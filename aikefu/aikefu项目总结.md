# AI客服系统项目总结

## 项目概述

aikefu是一个智能客服系统，集成了传统人工客服与AI智能客服的功能。系统支持多渠道客户服务，包括在线聊天、知识库查询和智能问答。通过结合大语言模型(LLM)技术，系统能够提供24/7的自动化客服支持，同时保留人工干预的能力，为用户提供无缝的服务体验。

## 系统架构

系统采用前后端分离的架构，主要由三个核心组件构成：

1. **后端服务(kefu-service)**：基于Java开发的核心业务逻辑层，负责会话管理、用户认证、数据存储等功能。

2. **前端界面(kefu-service-frontend)**：基于Vue.js开发的用户界面，包含客服工作台和用户聊天界面。

3. **AI语言模型服务(APILLM)**：基于Python开发的智能问答服务，集成了大语言模型，提供智能对话、知识库检索和FAQ聚类等功能。

系统通过WebSocket实现实时通信，支持消息的即时传递，同时使用RESTful API进行数据交互。

## 主要功能

### 会话管理
- 多会话并行处理
- 会话排队与分配
- 会话状态跟踪（待处理、进行中、已结束）
- 会话转接功能

### 消息处理
- 实时消息收发
- 图片消息支持
- 已读状态跟踪
- 历史消息查询

### AI智能客服
- 智能问答
- 知识库检索
- 多轮对话支持
- 人机协作，智能转人工

### 客服工具
- 快捷回复管理
- 解决方案记录
- FAQ管理与聚类
- 用户信息查看

### 用户管理
- 客服状态管理（在线/离线）
- 用户信息采集
- 订单信息关联

## 技术栈

### 后端
- **语言**：Java
- **框架**：Spring Boot
- **数据库**：MySQL
- **通信**：WebSocket、RESTful API

### 前端
- **框架**：Vue.js
- **UI组件**：Element Plus
- **状态管理**：Vuex/Pinia
- **路由**：Vue Router

### AI服务
- **语言**：Python
- **框架**：FastAPI
- **模型**：大语言模型(LLM) qwen3:1.7b, 知识库嵌入模型 BAAI-bge-m3
- **向量数据库**：用于语义检索

## 模块说明

### kefu-service（后端服务）
- **controller**：提供API接口
- **service**：实现业务逻辑
- **entity**：数据模型定义
- **mapper**：数据库操作
- **websocket**：实时通信服务

### kefu-service-frontend（前端界面）
- **views**：页面视图组件
- **components**：可复用UI组件
- **api**：后端接口调用
- **store**：状态管理
- **utils**：工具函数

### APILLM（AI服务）
- **InformationManager**：知识库管理添加、删除、更新、查询文档
- **LLMHandler**：大语言模型交互
- **EmbeddingGenerator**：文本嵌入生成
- **聊天API**：提供智能对话服务

## 特色功能

1. **混合客服模式**：支持AI自动回复与人工客服无缝切换

2. **多维度会话管理**：按状态（待处理、进行中、AI会话中）分类管理会话

3. **智能知识库**：基于语义检索的智能问答系统

4. **用户信息整合**：关联用户订单信息，提供全方位用户视图

5. **实时通信**：WebSocket确保消息即时传递，支持在线状态监控

## 总结

aikefu客服系统是一个融合了传统客服系统和AI技术的现代智能客服平台。通过前后端分离架构和AI语言模型的集成，系统能够提供高效、智能的客户服务体验。系统既支持AI自动回复降低人工成本，又保留了人工干预的能力，确保服务质量。未来可进一步优化AI模型性能，增强多渠道集成能力，提升整体服务体验。 