# ConnectController.onLine 方法分析

## 方法概述

`onLine` 方法是在线客服系统的核心接口，负责处理用户发送的消息并返回相应的回复。该方法根据不同的用户输入模式，可以触发不同的功能路径：

1. 普通消息处理：将消息交给AI处理并返回回复
2. 查询订单信息：根据订单号查询订单详情，用户需要在消息中包含HS开头的订单号（至少18位数字）。系统会自动识别并返回订单详情。
3. 查询物流信息：根据订单号查询物流状态和轨迹，用户需要在消息中包含"物流"关键词和HS开头的订单号。
4. 取消订单：根据订单号取消订单（功能开发中），用户需要在消息中包含"取消"和"订单"关键词，以及HS开头的订单号。
5. 取消物流：根据订单号取消物流（功能开发中），用户需要在消息中包含"取消"和"物流"关键词，以及HS开头的订单号。

## 注意事项
1. 订单号必须是以HS开头，后面跟至少18位数字
2. 要触发特殊功能（查询订单、查询物流等），必须设置`group_type`参数
3. 消息关键词和订单号的顺序可以灵活，系统会自动识别
4. 查询结果中的内容取决于系统中实际存储的数据
5. 系统使用处理器链模式，会选择第一个能处理消息的处理器

## 数据处理设计

系统采用了策略模式和处理器链模式，通过以下组件实现功能：

1. **MessageHandler接口**：定义消息处理器的通用接口
2. **消息处理器实现类**：针对不同类型的消息实现专门的处理逻辑
   - OrderQueryHandler：处理订单查询请求
   - ExpressQueryHandler：处理物流查询请求
   - CancelOrderHandler：处理取消订单请求
   - CancelExpressHandler：处理取消物流请求
3. **MessageHandlerFactory**：管理消息处理器，实现处理器链
4. **MessageConstants**：统一管理消息类型和响应文本的常量
5. **AssertUtils**：提供参数验证工具方法


## 用户输入指南

### 1. 查询订单信息
要查询订单信息，用户需要在消息中包含HS开头的订单号（至少18位数字）。系统会自动识别并返回订单详情。

**输入示例：**
```
你好，请查询我的订单 HS20250522210136341563331
```
或简单地：
```
订单 HS20250522210136341563331
```

**返回内容：**
```
您好，查询结果：
---------------------------
回收单号：HS20250522210136341563331
三方单号：xxxxx
订单类型：xxxxx
下单渠道：xxxxx
下单时间：xxxx-xx-xx xx:xx:xx
商品名称：品牌-商品名
回收状态：xxxxx
回收报价：xxx.xx
快递公司：xxxxx
物流单号：xxxxx
物流状态：xxxxx
```

### 2. 查询物流信息
要查询物流信息，用户需要在消息中包含"物流"关键词和HS开头的订单号。

**输入示例：**
```
请查询物流 HS20250522210136341563331
```
或：
```
HS20250522210136341563331 物流查询
```
或简单地：
```
物流 HS20250522210136341563331
```

**返回内容：**
```
您好，物流查询结果：
---------------------------
订单号：HS20250522210136341563331

快递公司：xxxxx
物流单号：xxxxx
物流状态：xxxxx

物流轨迹：
xxxx-xx-xx xx:xx:xx
物流详情1

xxxx-xx-xx xx:xx:xx
物流详情2

xxxx-xx-xx xx:xx:xx
物流详情3

xxxx-xx-xx xx:xx:xx
物流详情4

xxxx-xx-xx xx:xx:xx
物流详情5

...
共xx条物流记录，仅显示最新5条
```

### 3. 取消订单（功能开发中）
要申请取消订单，用户需要在消息中包含"取消"和"订单"关键词，以及HS开头的订单号。

**输入示例：**
```
请取消订单 HS20250522210136341563331
```
或：
```
订单取消 HS20250522210136341563331
```

**返回内容：**
```
您好，取消订单功能正抓紧在开发中...
```

### 4. 取消物流（功能开发中）
要申请取消物流，用户需要在消息中包含"取消"和"物流"关键词，以及HS开头的订单号。

**输入示例：**
```
请取消物流 HS20250522210136341563331
```
或：
```
物流取消 HS20250522210136341563331
```

**返回内容：**
```
您好，取消物流功能正在抓紧开发中...
```

### 5. 普通对话
如果消息中不包含特定的关键词和订单号，或者没有设置`group_type`参数，系统会将消息交给AI处理，并返回AI的回复。

**输入示例：**
```
你好，请问如何使用你们的服务？
```

**返回内容：**
AI回复的内容。

## 注意事项
1. 订单号必须是以HS开头，后面跟至少18位数字
2. 要触发特殊功能（查询订单、查询物流等），必须设置`group_type`参数
3. 消息关键词和订单号的顺序可以灵活，系统会自动识别
4. 查询结果中的内容取决于系统中实际存储的数据
5. 系统使用处理器链模式，会选择第一个能处理消息的处理器

## 错误处理
- 如果`session_id`为空，返回"session_id不能为空"
- 如果`messages`为空，返回空字符串
- 如果发生系统错误，返回"服务异常"
- 如果找不到处理器处理消息，且不是微信群消息，则使用AI服务处理



## 详细处理流程

1. **接收请求参数**：方法接收一个 `ConnectEntiy` 类型的参数，包含消息内容、会话ID等信息
2. **参数验证**：使用 `AssertUtils` 工具类检查 `session_id` 和 `messages` 是否为空
3. **消息类型判断**：判断是否是微信群消息（根据 `group_type` 是否非空）
4. **消息处理器工厂**：如果是微信群消息，使用 `MessageHandlerFactory` 处理特殊消息
    - 工厂内部维护一个处理器链，包含所有消息处理器
    - 遍历处理器链，找到第一个能够处理该消息的处理器
    - 调用找到的处理器的 `handle` 方法处理消息
5. **各种处理器的处理逻辑**：
    - **OrderQueryHandler**: 处理订单查询请求，调用API并格式化订单信息
    - **ExpressQueryHandler**: 处理物流查询请求，调用API并格式化物流信息
    - **CancelOrderHandler**: 处理取消订单请求，显示"功能开发中"提示
    - **CancelExpressHandler**: 处理取消物流请求，显示"功能开发中"提示
6. **普通消息处理**：如果没有找到合适的处理器或不是微信群消息，调用 AI 服务处理消息
    - 获取用户ID（不存在则创建）
    - 查询或创建会话
    - 异步保存用户消息
    - 调用 AI 服务获取回复
    - 异步保存 AI 回复
7. **返回结果**：包装处理结果并返回


## 接口参数说明

方法签名：
```java
public Result<String> onLine(@RequestBody ConnectEntiy connectEntiy)
```

`ConnectEntiy` 参数主要字段：

| 参数名 | 类型 | 必填 | 说明 |
|-------|-----|-----|------|
| session_id | String | 是 | 会话ID，用于标识用户 |
| messages | String | 是 | 用户发送的消息内容 |
| group_type | String | 否 | 分组类型，如果有值表示是微信群消息 |
| datasource | String | 否 | 数据源 |
| collection_name | String | 否 | 集合名称 |
| scene | String | 否 | 场景信息 |
| user_id | String | 否 | 用户ID |
| userName | String | 否 | 用户名 |
| avatar | String | 否 | 用户头像 |
| channel | String | 否 | 渠道 |
| isMerchant | Integer | 否 | 是否是商家 |
| isTechnician | Integer | 否 | 是否是技术人员 |

## 返回结果说明

方法返回 `Result<String>` 类型的结果：

- 成功情况：`Result.success(MessageConstants.RESPONSE_SUCCESS, response)`
  - `code`: 200
  - `message`: "响应正常"
  - `data`: 响应内容字符串
- 失败情况：`Result.error(ResultCode.ERROR, MessageConstants.ERROR_SERVICE_EXCEPTION)`
  - `code`: 错误码
  - `message`: 错误描述
  - `data`: null
