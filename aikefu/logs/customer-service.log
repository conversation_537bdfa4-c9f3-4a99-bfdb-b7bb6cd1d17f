2025-08-22 00:00:24.265 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 00:05:24.278 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 00:10:24.271 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 00:15:24.279 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 00:20:24.266 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 00:25:24.268 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 00:30:24.274 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 00:35:24.267 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 00:40:24.278 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 00:45:24.270 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 00:50:24.276 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 00:55:24.267 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 01:00:24.267 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 01:05:24.269 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 01:10:24.269 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 01:15:24.269 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 01:20:24.272 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 01:25:24.274 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 01:30:24.265 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 01:35:24.272 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 01:40:24.272 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 01:45:24.275 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 01:50:24.277 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 01:55:24.271 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 02:00:24.268 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 02:05:24.273 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 02:10:24.273 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 02:15:24.278 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 02:20:24.272 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 02:25:24.272 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 02:30:24.272 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 02:35:24.267 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 02:40:24.265 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 02:45:24.271 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 02:50:24.277 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 02:55:24.267 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 03:00:24.270 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 03:05:24.275 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 03:10:24.272 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 03:15:24.276 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 03:20:24.265 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 03:25:24.272 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 03:30:24.270 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 03:35:24.266 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 03:40:24.269 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 03:45:24.271 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 03:50:24.279 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 03:55:24.269 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 04:00:24.275 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 04:05:24.272 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 04:10:24.279 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 04:15:24.273 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 04:20:24.264 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 04:25:24.264 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 04:30:24.276 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 04:35:24.267 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 04:40:24.268 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 04:45:24.273 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 04:50:24.271 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 04:55:24.278 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 05:00:24.272 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 05:05:24.265 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 05:10:24.272 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 05:15:24.269 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 05:20:24.266 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 05:25:24.267 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 05:30:24.272 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 05:35:24.269 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 05:40:24.279 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 05:45:24.265 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 05:50:24.275 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 05:55:24.275 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 06:00:24.268 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 06:05:24.273 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 06:10:24.269 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 06:15:24.278 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 06:20:24.273 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 06:25:24.271 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 06:30:24.274 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 06:35:24.265 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 06:40:24.279 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 06:45:24.270 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 06:50:24.267 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 06:55:24.278 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 07:00:24.269 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 07:05:24.279 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 07:10:24.264 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 07:15:24.265 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 07:20:24.279 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 07:25:24.278 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 07:30:24.268 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 07:35:24.265 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 07:40:24.270 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 07:45:24.271 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 07:50:24.269 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 07:55:24.272 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 08:00:24.267 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 08:05:24.273 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 08:10:24.278 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 08:15:24.275 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 08:20:24.277 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 08:25:24.270 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 08:30:24.277 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 08:35:24.267 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 08:40:24.264 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 08:45:24.270 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 08:50:24.268 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 08:55:24.277 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 09:00:24.272 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 09:01:30.402 [http-nio-8080-exec-1] ERROR com.kefang.websocket.WebSocketServer - 发生错误：Connection reset, Session ID: 14
2025-08-22 09:01:30.402 [http-nio-8080-exec-6] ERROR com.kefang.websocket.WebSocketServer - 发生错误：Connection reset, Session ID: 13
2025-08-22 09:01:30.441 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 283 有一用户连接关闭！
2025-08-22 09:01:30.441 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：1
2025-08-22 09:01:30.441 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-22 09:01:30.441 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 09:01:30.442 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 4 有一客服连接关闭！
2025-08-22 09:01:30.456 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：0
2025-08-22 09:01:30.457 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-22 09:01:30.457 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-22 09:05:02.959 [http-nio-8080-exec-4] INFO  com.kefang.controller.FaqController - 查询所有常见问题
2025-08-22 09:05:11.868 [http-nio-8080-exec-3] INFO  com.kefang.controller.FaqController - 查询所有常见问题
2025-08-22 09:05:24.266 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 09:07:40.979 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: agent, id: 4, sessionId: 15
2025-08-22 09:07:40.980 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 客服 4 的WebSocket连接已存储，连接ID: 15
2025-08-22 09:07:40.980 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：1
2025-08-22 09:07:40.980 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-22 09:07:40.980 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 09:07:47.476 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 4 有一客服连接关闭！
2025-08-22 09:07:47.481 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：0
2025-08-22 09:07:47.481 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-22 09:07:47.481 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-22 09:07:50.267 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: agent, id: 4, sessionId: 16
2025-08-22 09:07:50.267 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 客服 4 的WebSocket连接已存储，连接ID: 16
2025-08-22 09:07:50.267 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：1
2025-08-22 09:07:50.267 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-22 09:07:50.267 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 09:10:01.002 [scheduling-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 当前端口不是8819，不执行定时回收商数据统计任务
2025-08-22 09:10:24.270 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 09:15:01.004 [scheduling-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 当前端口不是8819，不执行定时微信群来源统计任务
2025-08-22 09:15:24.274 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 09:20:24.279 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 09:25:24.264 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 09:30:24.269 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 09:35:24.278 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 09:40:24.269 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 09:45:24.266 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 09:50:24.265 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 09:55:24.274 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 09:59:41.853 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 4 有一客服连接关闭！
2025-08-22 09:59:41.859 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：0
2025-08-22 09:59:41.859 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-22 09:59:41.859 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-22 09:59:41.877 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-08-22 09:59:41.877 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-08-22 09:59:41.878 [SpringApplicationShutdownHook] INFO  com.kefang.config.OssConfig - 正在关闭OSS客户端...
2025-08-22 09:59:41.878 [SpringApplicationShutdownHook] INFO  com.kefang.config.OssConfig - OSS客户端已关闭
2025-08-22 09:59:41.878 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-22 09:59:41.883 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-22 09:59:47.270 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-22 09:59:47.275 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 14416 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-08-22 09:59:47.277 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "prod"
2025-08-22 09:59:48.609 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8819 (http)
2025-08-22 09:59:48.617 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8819"]
2025-08-22 09:59:48.617 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-22 09:59:48.617 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-08-22 09:59:48.734 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-22 09:59:48.734 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1418 ms
2025-08-22 09:59:49.130 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-22 09:59:49.138 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-22 09:59:49.151 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-22 09:59:49.193 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-22 09:59:49.196 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-22 09:59:49.200 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-08-22 09:59:49.202 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-08-22 09:59:49.202 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-22 09:59:49.322 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-08-22 09:59:49.346 [main] INFO  com.kefang.config.OssConfig - 正在初始化OSS客户端...
2025-08-22 09:59:49.346 [main] INFO  com.kefang.config.OssConfig - OSS配置信息 - endpoint: http://oss-cn-beijing.aliyuncs.com, bucketName: juranfile
2025-08-22 09:59:49.530 [main] INFO  com.kefang.config.OssConfig - OSS客户端初始化成功
2025-08-22 09:59:49.963 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-22 09:59:49.997 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8819"]
2025-08-22 09:59:50.012 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8819 (http) with context path ''
2025-08-22 09:59:50.029 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 3.181 seconds (JVM running for 4.032)
2025-08-22 09:59:50.030 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-22 09:59:56.107 [scheduling-1] ERROR com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:265)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:400)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:373)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:595)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:382)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.kefang.job.AutoSessionTransferTask$$EnhancerBySpringCGLIB$$fad485c1.transferInactiveSessionsToAI(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:582)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:537)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:424)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1428)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:134)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 33 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:576)
	... 39 common frames omitted
2025-08-22 09:59:56.108 [scheduling-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.transaction.CannotCreateTransactionException: Could not open JDBC Connection for transaction; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:309)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:400)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:373)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:595)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:382)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.kefang.job.AutoSessionTransferTask$$EnhancerBySpringCGLIB$$fad485c1.transferInactiveSessionsToAI(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:265)
	... 22 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:582)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:537)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:424)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1428)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:134)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 33 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:576)
	... 39 common frames omitted
2025-08-22 10:00:17.482 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-08-22 10:00:17.482 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-08-22 10:00:17.483 [SpringApplicationShutdownHook] INFO  com.kefang.config.OssConfig - 正在关闭OSS客户端...
2025-08-22 10:00:17.483 [SpringApplicationShutdownHook] INFO  com.kefang.config.OssConfig - OSS客户端已关闭
2025-08-22 10:00:19.412 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-22 10:00:19.421 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 22420 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-08-22 10:00:19.422 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "dev"
2025-08-22 10:00:20.482 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-22 10:00:20.489 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-22 10:00:20.489 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-22 10:00:20.489 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-08-22 10:00:20.581 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-22 10:00:20.582 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1124 ms
2025-08-22 10:00:20.969 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-22 10:00:20.976 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-22 10:00:20.988 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-22 10:00:21.025 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-22 10:00:21.027 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-22 10:00:21.031 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-08-22 10:00:21.033 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-08-22 10:00:21.033 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-22 10:00:21.146 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-08-22 10:00:21.162 [main] INFO  com.kefang.config.OssConfig - 正在初始化OSS客户端...
2025-08-22 10:00:21.162 [main] INFO  com.kefang.config.OssConfig - OSS配置信息 - endpoint: http://oss-cn-beijing.aliyuncs.com, bucketName: juranfile
2025-08-22 10:00:21.294 [main] INFO  com.kefang.config.OssConfig - OSS客户端初始化成功
2025-08-22 10:00:21.717 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-22 10:00:21.761 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-22 10:00:21.780 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-22 10:00:21.801 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-22 10:00:21.802 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.79 seconds (JVM running for 3.407)
2025-08-22 10:00:21.958 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-22 10:00:21.986 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 10:00:27.056 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22 10:00:27.056 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-22 10:00:27.057 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-22 10:00:27.088 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: agent, id: 4, sessionId: 0
2025-08-22 10:00:27.088 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 客服 4 的WebSocket连接已存储，连接ID: 0
2025-08-22 10:00:27.088 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：1
2025-08-22 10:00:27.088 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-22 10:00:27.088 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 10:01:41.226 [http-nio-8080-exec-5] INFO  com.kefang.controller.ConnectController - 手动触发微信群来源统计任务
2025-08-22 10:01:41.228 [http-nio-8080-exec-5] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 当前端口不是8819，不执行定时微信群来源统计任务
2025-08-22 10:01:54.582 [http-nio-8080-exec-6] INFO  com.kefang.controller.ConnectController - 手动触发微信群来源统计任务
2025-08-22 10:01:54.582 [http-nio-8080-exec-6] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 当前端口不是8819，不执行定时微信群来源统计任务
2025-08-22 10:01:57.260 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 4 有一客服连接关闭！
2025-08-22 10:01:57.264 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：0
2025-08-22 10:01:57.265 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-22 10:01:57.265 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-22 10:01:57.269 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-08-22 10:01:57.269 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-08-22 10:01:57.270 [SpringApplicationShutdownHook] INFO  com.kefang.config.OssConfig - 正在关闭OSS客户端...
2025-08-22 10:01:57.272 [SpringApplicationShutdownHook] INFO  com.kefang.config.OssConfig - OSS客户端已关闭
2025-08-22 10:01:57.273 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-22 10:01:57.277 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-22 10:02:01.245 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-22 10:02:01.254 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 47652 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-08-22 10:02:01.254 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "dev"
2025-08-22 10:02:02.220 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-22 10:02:02.229 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-22 10:02:02.229 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-22 10:02:02.229 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-08-22 10:02:02.319 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-22 10:02:02.319 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1017 ms
2025-08-22 10:02:02.705 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-22 10:02:02.713 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-22 10:02:02.727 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-22 10:02:02.772 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-22 10:02:02.776 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-22 10:02:02.779 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-08-22 10:02:02.784 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-08-22 10:02:02.784 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-22 10:02:02.902 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-08-22 10:02:02.929 [main] INFO  com.kefang.config.OssConfig - 正在初始化OSS客户端...
2025-08-22 10:02:02.929 [main] INFO  com.kefang.config.OssConfig - OSS配置信息 - endpoint: http://oss-cn-beijing.aliyuncs.com, bucketName: juranfile
2025-08-22 10:02:03.063 [main] INFO  com.kefang.config.OssConfig - OSS客户端初始化成功
2025-08-22 10:02:03.484 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-22 10:02:03.523 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-22 10:02:03.537 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-22 10:02:03.553 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.769 seconds (JVM running for 3.374)
2025-08-22 10:02:03.554 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-22 10:02:03.662 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-22 10:02:03.683 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 10:02:04.069 [http-nio-8080-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22 10:02:04.069 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-22 10:02:04.070 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-22 10:02:04.102 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: agent, id: 4, sessionId: 0
2025-08-22 10:02:04.102 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 客服 4 的WebSocket连接已存储，连接ID: 0
2025-08-22 10:02:04.102 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：1
2025-08-22 10:02:04.102 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-22 10:02:04.102 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 10:02:07.834 [http-nio-8080-exec-1] INFO  com.kefang.controller.ConnectController - 手动触发微信群来源统计任务
2025-08-22 10:02:07.836 [http-nio-8080-exec-1] INFO  com.kefang.api.ExecuteSqlQueryApi - 设置SSO认证信息: [Content-Type:"application/json", privateKey:"SK98wL2h", apitoken:"28074dea233e7d60fad0a06380f229af"]
2025-08-22 10:02:07.836 [http-nio-8080-exec-1] INFO  com.kefang.api.ExecuteSqlQueryApi - 发送SQL查询请求: URL=https://api.bearhome.cn/hsapi/recovery/order//statistics/executeSqlQuery, SQL=  SELECT
    ma.merchant_id AS wxgroup,
    ma.source,
    ma.name, 
    COUNT(CASE 
              WHEN ro.create_time >= CURDATE() - INTERVAL 1 DAY AND ro.create_time < CURDATE() 
              THEN ro.id 
          END) AS orderCount, 
    COUNT(CASE 
              WHEN ro.finish_time >= CURDATE() - INTERVAL 1 DAY AND ro.finish_time < CURDATE() AND ro.status IN (70, 80) 
              THEN ro.id 
          END) AS cancelCount, 
    COUNT(CASE 
              WHEN ro.finish_time >= CURDATE() - INTERVAL 1 DAY AND ro.finish_time < CURDATE() AND ro.status = 100 
              THEN ro.id 
          END) AS completedCount
          
FROM 
    juran_platform.mer_appkey ma
LEFT JOIN
    juran_order.recover_order ro ON ma.source = ro.source
WHERE 
    ma.source IN (65, 59, 84, 87, 90, 72, 97, 98, 105)
GROUP BY
    ma.merchant_id,
    ma.source,
    ma.name
ORDER BY
    ma.source;
2025-08-22 10:02:07.838 [http-nio-8080-exec-1] INFO  com.kefang.api.ExecuteSqlQueryApi - 请求体JSON: {"sql":"  SELECT\n    ma.merchant_id AS wxgroup,\n    ma.source,\n    ma.name, \n    COUNT(CASE \n              WHEN ro.create_time >= CURDATE() - INTERVAL 1 DAY AND ro.create_time < CURDATE() \n              THEN ro.id \n          END) AS orderCount, \n    COUNT(CASE \n              WHEN ro.finish_time >= CURDATE() - INTERVAL 1 DAY AND ro.finish_time < CURDATE() AND ro.status IN (70, 80) \n              THEN ro.id \n          END) AS cancelCount, \n    COUNT(CASE \n              WHEN ro.finish_time >= CURDATE() - INTERVAL 1 DAY AND ro.finish_time < CURDATE() AND ro.status = 100 \n              THEN ro.id \n          END) AS completedCount\n          \nFROM \n    juran_platform.mer_appkey ma\nLEFT JOIN\n    juran_order.recover_order ro ON ma.source = ro.source\nWHERE \n    ma.source IN (65, 59, 84, 87, 90, 72, 97, 98, 105)\nGROUP BY\n    ma.merchant_id,\n    ma.source,\n    ma.name\nORDER BY\n    ma.source;"}
2025-08-22 10:02:20.872 [http-nio-8080-exec-1] INFO  com.kefang.api.ExecuteSqlQueryApi - SQL查询响应成功
2025-08-22 10:02:20.873 [http-nio-8080-exec-1] INFO  com.kefang.api.ExecuteSqlQueryApi - 响应数据: {"statusCode":200,"data":{"data":[{"wxgroup":4390,"cancelCount":2,"name":"时代鸟平台","orderCount":27,"source":59,"completedCount":24},{"wxgroup":4442,"cancelCount":20,"name":"飞蚂蚁","orderCount":146,"source":65,"completedCount":103},{"wxgroup":4573,"cancelCount":16,"name":"爱裹","orderCount":43,"source":72,"completedCount":14},{"wxgroup":4846,"cancelCount":2,"name":"湖北省再生资源集团","orderCount":4,"source":84,"completedCount":1},{"wxgroup":4925,"cancelCount":8,"name":"爱博绿","orderCount":24,"source":87,"completedCount":5},{"wxgroup":4946,"cancelCount":2,"name":"汪回收","orderCount":7,"source":90,"completedCount":2},{"wxgroup":5166,"cancelCount":0,"name":"海鲸回收","orderCount":0,"source":97,"completedCount":0},{"wxgroup":5257,"cancelCount":1,"name":"上海绿袋环保","orderCount":6,"source":98,"completedCount":4},{"wxgroup":5996,"cancelCount":0,"name":"蓝鲸鱼","orderCount":4,"source":105,"completedCount":1}],"recordCount":9,"executionTime":12831,"status":"SUCCESS","message":"查询执行成功","executedSql":"SELECT\n    ma.merchant_id AS wxgroup,\n    ma.source,\n    ma.name, \n    COUNT(CASE \n              WHEN ro.create_time >= CURDATE() - INTERVAL 1 DAY AND ro.create_time < CURDATE() \n              THEN ro.id \n          END) AS orderCount, \n    COUNT(CASE \n              WHEN ro.finish_time >= CURDATE() - INTERVAL 1 DAY AND ro.finish_time < CURDATE() AND ro.status IN (70, 80) \n              THEN ro.id \n          END) AS cancelCount, \n    COUNT(CASE \n              WHEN ro.finish_time >= CURDATE() - INTERVAL 1 DAY AND ro.finish_time < CURDATE() AND ro.status = 100 \n              THEN ro.id \n          END) AS completedCount\n          \nFROM \n    juran_platform.mer_appkey ma\nLEFT JOIN\n    juran_order.recover_order ro ON ma.source = ro.source\nWHERE \n    ma.source IN (65, 59, 84, 87, 90, 72, 97, 98, 105)\nGROUP BY\n    ma.merchant_id,\n    ma.source,\n    ma.name\nORDER BY\n    ma.source LIMIT 1000","queryTimestamp":1755828143489,"userId":null,"columns":null},"errorInfo":null}
2025-08-22 10:02:20.873 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 获取企业微信群昨日订单统计数据成功: {"statusCode":200,"data":{"data":[{"wxgroup":4390,"cancelCount":2,"name":"时代鸟平台","orderCount":27,"source":59,"completedCount":24},{"wxgroup":4442,"cancelCount":20,"name":"飞蚂蚁","orderCount":146,"source":65,"completedCount":103},{"wxgroup":4573,"cancelCount":16,"name":"爱裹","orderCount":43,"source":72,"completedCount":14},{"wxgroup":4846,"cancelCount":2,"name":"湖北省再生资源集团","orderCount":4,"source":84,"completedCount":1},{"wxgroup":4925,"cancelCount":8,"name":"爱博绿","orderCount":24,"source":87,"completedCount":5},{"wxgroup":4946,"cancelCount":2,"name":"汪回收","orderCount":7,"source":90,"completedCount":2},{"wxgroup":5166,"cancelCount":0,"name":"海鲸回收","orderCount":0,"source":97,"completedCount":0},{"wxgroup":5257,"cancelCount":1,"name":"上海绿袋环保","orderCount":6,"source":98,"completedCount":4},{"wxgroup":5996,"cancelCount":0,"name":"蓝鲸鱼","orderCount":4,"source":105,"completedCount":1}],"recordCount":9,"executionTime":12831,"status":"SUCCESS","message":"查询执行成功","executedSql":"SELECT\n    ma.merchant_id AS wxgroup,\n    ma.source,\n    ma.name, \n    COUNT(CASE \n              WHEN ro.create_time >= CURDATE() - INTERVAL 1 DAY AND ro.create_time < CURDATE() \n              THEN ro.id \n          END) AS orderCount, \n    COUNT(CASE \n              WHEN ro.finish_time >= CURDATE() - INTERVAL 1 DAY AND ro.finish_time < CURDATE() AND ro.status IN (70, 80) \n              THEN ro.id \n          END) AS cancelCount, \n    COUNT(CASE \n              WHEN ro.finish_time >= CURDATE() - INTERVAL 1 DAY AND ro.finish_time < CURDATE() AND ro.status = 100 \n              THEN ro.id \n          END) AS completedCount\n          \nFROM \n    juran_platform.mer_appkey ma\nLEFT JOIN\n    juran_order.recover_order ro ON ma.source = ro.source\nWHERE \n    ma.source IN (65, 59, 84, 87, 90, 72, 97, 98, 105)\nGROUP BY\n    ma.merchant_id,\n    ma.source,\n    ma.name\nORDER BY\n    ma.source LIMIT 1000","queryTimestamp":1755828143489,"userId":null,"columns":null},"errorInfo":null}
2025-08-22 10:02:20.873 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 开始获取近一个月订单统计数据
2025-08-22 10:02:20.874 [http-nio-8080-exec-1] INFO  com.kefang.api.SourceStatisticsApi - 设置SSO认证信息: [Content-Type:"application/json", privateKey:"rFoeWGuL", apitoken:"d247e22d852180e2576e5a54ca524ea4"]
2025-08-22 10:02:20.874 [http-nio-8080-exec-1] INFO  com.kefang.api.SourceStatisticsApi - 发送月度订单统计请求: URL=https://api.bearhome.cn/hsapi/recovery/order//statistics/monthlyOrder, 请求体={"sourceList":[65,59,84,87,90,72,97,98,105],"endDate":"2025-08-22","startDate":"2025-07-23"}
2025-08-22 10:02:21.122 [http-nio-8080-exec-1] INFO  com.kefang.api.SourceStatisticsApi - 月度订单统计任务响应: {"statusCode":200,"data":[{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"10","statusName":"等待签收","count":79},{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"15","statusName":"回收处理中","count":3},{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"30","statusName":"验机确认","count":36},{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"70","statusName":"回收取消","count":91},{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"80","statusName":"回收退单","count":45},{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"90","statusName":"订单异常","count":4},{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"100","statusName":"回收完成","count":936},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"10","statusName":"等待签收","count":281},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"15","statusName":"回收处理中","count":2},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"20","statusName":"等待验机","count":1},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"30","statusName":"验机确认","count":98},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"60","statusName":"等待退回","count":16},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"70","statusName":"回收取消","count":303},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"80","statusName":"回收退单","count":530},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"90","statusName":"订单异常","count":23},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"100","statusName":"回收完成","count":3746},{"merchantId":4573,"name":"爱裹","source":72,"status":"10","statusName":"等待签收","count":77},{"merchantId":4573,"name":"爱裹","source":72,"status":"15","statusName":"回收处理中","count":1},{"merchantId":4573,"name":"爱裹","source":72,"status":"20","statusName":"等待验机","count":1},{"merchantId":4573,"name":"爱裹","source":72,"status":"30","statusName":"验机确认","count":24},{"merchantId":4573,"name":"爱裹","source":72,"status":"60","statusName":"等待退回","count":2},{"merchantId":4573,"name":"爱裹","source":72,"status":"70","statusName":"回收取消","count":116},{"merchantId":4573,"name":"爱裹","source":72,"status":"80","statusName":"回收退单","count":132},{"merchantId":4573,"name":"爱裹","source":72,"status":"90","statusName":"订单异常","count":3},{"merchantId":4573,"name":"爱裹","source":72,"status":"100","statusName":"回收完成","count":811},{"merchantId":4846,"name":"湖北省再生资源集团","source":84,"status":"10","statusName":"等待签收","count":42},{"merchantId":4846,"name":"湖北省再生资源集团","source":84,"status":"70","statusName":"回收取消","count":115},{"merchantId":4846,"name":"湖北省再生资源集团","source":84,"status":"80","statusName":"回收退单","count":1},{"merchantId":4846,"name":"湖北省再生资源集团","source":84,"status":"90","statusName":"订单异常","count":1},{"merchantId":4846,"name":"湖北省再生资源集团","source":84,"status":"100","statusName":"回收完成","count":151},{"merchantId":4925,"name":"爱博绿","source":87,"status":"10","statusName":"等待签收","count":72},{"merchantId":4925,"name":"爱博绿","source":87,"status":"15","statusName":"回收处理中","count":43},{"merchantId":4925,"name":"爱博绿","source":87,"status":"60","statusName":"等待退回","count":2},{"merchantId":4925,"name":"爱博绿","source":87,"status":"70","statusName":"回收取消","count":281},{"merchantId":4925,"name":"爱博绿","source":87,"status":"80","statusName":"回收退单","count":94},{"merchantId":4925,"name":"爱博绿","source":87,"status":"90","statusName":"订单异常","count":4},{"merchantId":4925,"name":"爱博绿","source":87,"status":"100","statusName":"回收完成","count":196},{"merchantId":4946,"name":"汪回收","source":90,"status":"10","statusName":"等待签收","count":16},{"merchantId":4946,"name":"汪回收","source":90,"status":"15","statusName":"回收处理中","count":5},{"merchantId":4946,"name":"汪回收","source":90,"status":"30","statusName":"验机确认","count":7},{"merchantId":4946,"name":"汪回收","source":90,"status":"70","statusName":"回收取消","count":46},{"merchantId":4946,"name":"汪回收","source":90,"status":"80","statusName":"回收退单","count":26},{"merchantId":4946,"name":"汪回收","source":90,"status":"90","statusName":"订单异常","count":2},{"merchantId":4946,"name":"汪回收","source":90,"status":"100","statusName":"回收完成","count":160},{"merchantId":5166,"name":"海鲸回收","source":97,"status":"10","statusName":"等待签收","count":1},{"merchantId":5166,"name":"海鲸回收","source":97,"status":"70","statusName":"回收取消","count":1},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"5","statusName":"等待寄回","count":1},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"10","statusName":"等待签收","count":25},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"15","statusName":"回收处理中","count":3},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"30","statusName":"验机确认","count":1},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"60","statusName":"等待退回","count":1},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"70","statusName":"回收取消","count":47},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"80","statusName":"回收退单","count":26},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"100","statusName":"回收完成","count":161},{"merchantId":5996,"name":"蓝鲸鱼","source":105,"status":"10","statusName":"等待签收","count":3},{"merchantId":5996,"name":"蓝鲸鱼","source":105,"status":"30","statusName":"验机确认","count":1},{"merchantId":5996,"name":"蓝鲸鱼","source":105,"status":"70","statusName":"回收取消","count":3},{"merchantId":5996,"name":"蓝鲸鱼","source":105,"status":"80","statusName":"回收退单","count":1},{"merchantId":5996,"name":"蓝鲸鱼","source":105,"status":"100","statusName":"回收完成","count":2}],"errorInfo":null}
2025-08-22 10:02:21.123 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 获取近一个月订单统计数据成功: {"statusCode":200,"data":[{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"10","statusName":"等待签收","count":79},{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"15","statusName":"回收处理中","count":3},{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"30","statusName":"验机确认","count":36},{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"70","statusName":"回收取消","count":91},{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"80","statusName":"回收退单","count":45},{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"90","statusName":"订单异常","count":4},{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"100","statusName":"回收完成","count":936},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"10","statusName":"等待签收","count":281},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"15","statusName":"回收处理中","count":2},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"20","statusName":"等待验机","count":1},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"30","statusName":"验机确认","count":98},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"60","statusName":"等待退回","count":16},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"70","statusName":"回收取消","count":303},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"80","statusName":"回收退单","count":530},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"90","statusName":"订单异常","count":23},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"100","statusName":"回收完成","count":3746},{"merchantId":4573,"name":"爱裹","source":72,"status":"10","statusName":"等待签收","count":77},{"merchantId":4573,"name":"爱裹","source":72,"status":"15","statusName":"回收处理中","count":1},{"merchantId":4573,"name":"爱裹","source":72,"status":"20","statusName":"等待验机","count":1},{"merchantId":4573,"name":"爱裹","source":72,"status":"30","statusName":"验机确认","count":24},{"merchantId":4573,"name":"爱裹","source":72,"status":"60","statusName":"等待退回","count":2},{"merchantId":4573,"name":"爱裹","source":72,"status":"70","statusName":"回收取消","count":116},{"merchantId":4573,"name":"爱裹","source":72,"status":"80","statusName":"回收退单","count":132},{"merchantId":4573,"name":"爱裹","source":72,"status":"90","statusName":"订单异常","count":3},{"merchantId":4573,"name":"爱裹","source":72,"status":"100","statusName":"回收完成","count":811},{"merchantId":4846,"name":"湖北省再生资源集团","source":84,"status":"10","statusName":"等待签收","count":42},{"merchantId":4846,"name":"湖北省再生资源集团","source":84,"status":"70","statusName":"回收取消","count":115},{"merchantId":4846,"name":"湖北省再生资源集团","source":84,"status":"80","statusName":"回收退单","count":1},{"merchantId":4846,"name":"湖北省再生资源集团","source":84,"status":"90","statusName":"订单异常","count":1},{"merchantId":4846,"name":"湖北省再生资源集团","source":84,"status":"100","statusName":"回收完成","count":151},{"merchantId":4925,"name":"爱博绿","source":87,"status":"10","statusName":"等待签收","count":72},{"merchantId":4925,"name":"爱博绿","source":87,"status":"15","statusName":"回收处理中","count":43},{"merchantId":4925,"name":"爱博绿","source":87,"status":"60","statusName":"等待退回","count":2},{"merchantId":4925,"name":"爱博绿","source":87,"status":"70","statusName":"回收取消","count":281},{"merchantId":4925,"name":"爱博绿","source":87,"status":"80","statusName":"回收退单","count":94},{"merchantId":4925,"name":"爱博绿","source":87,"status":"90","statusName":"订单异常","count":4},{"merchantId":4925,"name":"爱博绿","source":87,"status":"100","statusName":"回收完成","count":196},{"merchantId":4946,"name":"汪回收","source":90,"status":"10","statusName":"等待签收","count":16},{"merchantId":4946,"name":"汪回收","source":90,"status":"15","statusName":"回收处理中","count":5},{"merchantId":4946,"name":"汪回收","source":90,"status":"30","statusName":"验机确认","count":7},{"merchantId":4946,"name":"汪回收","source":90,"status":"70","statusName":"回收取消","count":46},{"merchantId":4946,"name":"汪回收","source":90,"status":"80","statusName":"回收退单","count":26},{"merchantId":4946,"name":"汪回收","source":90,"status":"90","statusName":"订单异常","count":2},{"merchantId":4946,"name":"汪回收","source":90,"status":"100","statusName":"回收完成","count":160},{"merchantId":5166,"name":"海鲸回收","source":97,"status":"10","statusName":"等待签收","count":1},{"merchantId":5166,"name":"海鲸回收","source":97,"status":"70","statusName":"回收取消","count":1},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"5","statusName":"等待寄回","count":1},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"10","statusName":"等待签收","count":25},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"15","statusName":"回收处理中","count":3},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"30","statusName":"验机确认","count":1},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"60","statusName":"等待退回","count":1},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"70","statusName":"回收取消","count":47},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"80","statusName":"回收退单","count":26},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"100","statusName":"回收完成","count":161},{"merchantId":5996,"name":"蓝鲸鱼","source":105,"status":"10","statusName":"等待签收","count":3},{"merchantId":5996,"name":"蓝鲸鱼","source":105,"status":"30","statusName":"验机确认","count":1},{"merchantId":5996,"name":"蓝鲸鱼","source":105,"status":"70","statusName":"回收取消","count":3},{"merchantId":5996,"name":"蓝鲸鱼","source":105,"status":"80","statusName":"回收退单","count":1},{"merchantId":5996,"name":"蓝鲸鱼","source":105,"status":"100","statusName":"回收完成","count":2}],"errorInfo":null}
2025-08-22 10:02:21.125 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 近一个月订单统计任务执行成功
2025-08-22 10:02:21.125 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 获取到 59 条月度订单统计数据
2025-08-22 10:02:21.125 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 获取近一个月订单统计数据完成: {"statusCode":200,"data":[{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"10","statusName":"等待签收","count":79},{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"15","statusName":"回收处理中","count":3},{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"30","statusName":"验机确认","count":36},{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"70","statusName":"回收取消","count":91},{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"80","statusName":"回收退单","count":45},{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"90","statusName":"订单异常","count":4},{"merchantId":4390,"name":"时代鸟平台","source":59,"status":"100","statusName":"回收完成","count":936},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"10","statusName":"等待签收","count":281},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"15","statusName":"回收处理中","count":2},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"20","statusName":"等待验机","count":1},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"30","statusName":"验机确认","count":98},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"60","statusName":"等待退回","count":16},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"70","statusName":"回收取消","count":303},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"80","statusName":"回收退单","count":530},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"90","statusName":"订单异常","count":23},{"merchantId":4442,"name":"飞蚂蚁","source":65,"status":"100","statusName":"回收完成","count":3746},{"merchantId":4573,"name":"爱裹","source":72,"status":"10","statusName":"等待签收","count":77},{"merchantId":4573,"name":"爱裹","source":72,"status":"15","statusName":"回收处理中","count":1},{"merchantId":4573,"name":"爱裹","source":72,"status":"20","statusName":"等待验机","count":1},{"merchantId":4573,"name":"爱裹","source":72,"status":"30","statusName":"验机确认","count":24},{"merchantId":4573,"name":"爱裹","source":72,"status":"60","statusName":"等待退回","count":2},{"merchantId":4573,"name":"爱裹","source":72,"status":"70","statusName":"回收取消","count":116},{"merchantId":4573,"name":"爱裹","source":72,"status":"80","statusName":"回收退单","count":132},{"merchantId":4573,"name":"爱裹","source":72,"status":"90","statusName":"订单异常","count":3},{"merchantId":4573,"name":"爱裹","source":72,"status":"100","statusName":"回收完成","count":811},{"merchantId":4846,"name":"湖北省再生资源集团","source":84,"status":"10","statusName":"等待签收","count":42},{"merchantId":4846,"name":"湖北省再生资源集团","source":84,"status":"70","statusName":"回收取消","count":115},{"merchantId":4846,"name":"湖北省再生资源集团","source":84,"status":"80","statusName":"回收退单","count":1},{"merchantId":4846,"name":"湖北省再生资源集团","source":84,"status":"90","statusName":"订单异常","count":1},{"merchantId":4846,"name":"湖北省再生资源集团","source":84,"status":"100","statusName":"回收完成","count":151},{"merchantId":4925,"name":"爱博绿","source":87,"status":"10","statusName":"等待签收","count":72},{"merchantId":4925,"name":"爱博绿","source":87,"status":"15","statusName":"回收处理中","count":43},{"merchantId":4925,"name":"爱博绿","source":87,"status":"60","statusName":"等待退回","count":2},{"merchantId":4925,"name":"爱博绿","source":87,"status":"70","statusName":"回收取消","count":281},{"merchantId":4925,"name":"爱博绿","source":87,"status":"80","statusName":"回收退单","count":94},{"merchantId":4925,"name":"爱博绿","source":87,"status":"90","statusName":"订单异常","count":4},{"merchantId":4925,"name":"爱博绿","source":87,"status":"100","statusName":"回收完成","count":196},{"merchantId":4946,"name":"汪回收","source":90,"status":"10","statusName":"等待签收","count":16},{"merchantId":4946,"name":"汪回收","source":90,"status":"15","statusName":"回收处理中","count":5},{"merchantId":4946,"name":"汪回收","source":90,"status":"30","statusName":"验机确认","count":7},{"merchantId":4946,"name":"汪回收","source":90,"status":"70","statusName":"回收取消","count":46},{"merchantId":4946,"name":"汪回收","source":90,"status":"80","statusName":"回收退单","count":26},{"merchantId":4946,"name":"汪回收","source":90,"status":"90","statusName":"订单异常","count":2},{"merchantId":4946,"name":"汪回收","source":90,"status":"100","statusName":"回收完成","count":160},{"merchantId":5166,"name":"海鲸回收","source":97,"status":"10","statusName":"等待签收","count":1},{"merchantId":5166,"name":"海鲸回收","source":97,"status":"70","statusName":"回收取消","count":1},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"5","statusName":"等待寄回","count":1},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"10","statusName":"等待签收","count":25},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"15","statusName":"回收处理中","count":3},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"30","statusName":"验机确认","count":1},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"60","statusName":"等待退回","count":1},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"70","statusName":"回收取消","count":47},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"80","statusName":"回收退单","count":26},{"merchantId":5257,"name":"上海绿袋环保","source":98,"status":"100","statusName":"回收完成","count":161},{"merchantId":5996,"name":"蓝鲸鱼","source":105,"status":"10","statusName":"等待签收","count":3},{"merchantId":5996,"name":"蓝鲸鱼","source":105,"status":"30","statusName":"验机确认","count":1},{"merchantId":5996,"name":"蓝鲸鱼","source":105,"status":"70","statusName":"回收取消","count":3},{"merchantId":5996,"name":"蓝鲸鱼","source":105,"status":"80","statusName":"回收退单","count":1},{"merchantId":5996,"name":"蓝鲸鱼","source":105,"status":"100","statusName":"回收完成","count":2}],"errorInfo":null}
2025-08-22 10:02:21.125 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 开始获取近一个月物流订单统计数据
2025-08-22 10:02:21.126 [http-nio-8080-exec-1] INFO  com.kefang.api.SourceStatisticsApi - 设置SSO认证信息: [Content-Type:"application/json", privateKey:"dxzQJThc", apitoken:"3f46a13069ea240af1c48e011c5b1e3a"]
2025-08-22 10:02:21.126 [http-nio-8080-exec-1] INFO  com.kefang.api.SourceStatisticsApi - 发送物流订单统计请求: URL=https://api.bearhome.cn/hsapi/recovery/order//statistics/expressOrder, 请求体={"sourceList":[65,59,84,87,90,72,97,98,105],"endDate":"2025-08-22","startDate":"2025-07-23"}
2025-08-22 10:02:21.336 [http-nio-8080-exec-1] INFO  com.kefang.api.SourceStatisticsApi - 物流订单统计任务响应: {"statusCode":200,"data":[{"source":"59","name":"时代鸟平台","pendingPickup":21,"pendingReceive":5},{"source":"65","name":"飞蚂蚁","pendingPickup":78,"pendingReceive":92},{"source":"72","name":"爱裹","pendingPickup":24,"pendingReceive":28},{"source":"84","name":"湖北省再生资源集团","pendingPickup":4,"pendingReceive":3},{"source":"87","name":"爱博绿","pendingPickup":13,"pendingReceive":4},{"source":"90","name":"汪回收","pendingPickup":3,"pendingReceive":5},{"source":"97","name":"海鲸回收","pendingPickup":0,"pendingReceive":0},{"source":"98","name":"上海绿袋环保","pendingPickup":0,"pendingReceive":9},{"source":"105","name":"蓝鲸鱼","pendingPickup":1,"pendingReceive":0}],"errorInfo":null}
2025-08-22 10:02:21.336 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 获取近一个月物流订单统计数据成功: {"statusCode":200,"data":[{"source":"59","name":"时代鸟平台","pendingPickup":21,"pendingReceive":5},{"source":"65","name":"飞蚂蚁","pendingPickup":78,"pendingReceive":92},{"source":"72","name":"爱裹","pendingPickup":24,"pendingReceive":28},{"source":"84","name":"湖北省再生资源集团","pendingPickup":4,"pendingReceive":3},{"source":"87","name":"爱博绿","pendingPickup":13,"pendingReceive":4},{"source":"90","name":"汪回收","pendingPickup":3,"pendingReceive":5},{"source":"97","name":"海鲸回收","pendingPickup":0,"pendingReceive":0},{"source":"98","name":"上海绿袋环保","pendingPickup":0,"pendingReceive":9},{"source":"105","name":"蓝鲸鱼","pendingPickup":1,"pendingReceive":0}],"errorInfo":null}
2025-08-22 10:02:21.336 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 近一个月物流订单统计任务执行成功
2025-08-22 10:02:21.336 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 获取到 9 条物流订单统计数据
2025-08-22 10:02:21.336 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 获取近一个月物流订单统计数据完成: {"statusCode":200,"data":[{"source":"59","name":"时代鸟平台","pendingPickup":21,"pendingReceive":5},{"source":"65","name":"飞蚂蚁","pendingPickup":78,"pendingReceive":92},{"source":"72","name":"爱裹","pendingPickup":24,"pendingReceive":28},{"source":"84","name":"湖北省再生资源集团","pendingPickup":4,"pendingReceive":3},{"source":"87","name":"爱博绿","pendingPickup":13,"pendingReceive":4},{"source":"90","name":"汪回收","pendingPickup":3,"pendingReceive":5},{"source":"97","name":"海鲸回收","pendingPickup":0,"pendingReceive":0},{"source":"98","name":"上海绿袋环保","pendingPickup":0,"pendingReceive":9},{"source":"105","name":"蓝鲸鱼","pendingPickup":1,"pendingReceive":0}],"errorInfo":null}
2025-08-22 10:02:21.337 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 按商户ID分组解析月度订单统计数据: {4925={等待签收=72, 订单异常=4, 回收退单=94, 回收完成=196, 等待退回=2, 回收取消=281, 回收处理中=43}, 5166={等待签收=1, 回收取消=1}, 4573={等待签收=77, 订单异常=3, 验机确认=24, 回收退单=132, 回收完成=811, 等待退回=2, 回收取消=116, 等待验机=1, 回收处理中=1}, 4442={等待签收=281, 订单异常=23, 验机确认=98, 回收退单=530, 回收完成=3746, 等待退回=16, 回收取消=303, 等待验机=1, 回收处理中=2}, 5257={等待签收=25, 验机确认=1, 回收退单=26, 回收完成=161, 等待退回=1, 回收取消=47, 等待寄回=1, 回收处理中=3}, 5996={等待签收=3, 验机确认=1, 回收退单=1, 回收完成=2, 回收取消=3}, 4390={等待签收=79, 订单异常=4, 验机确认=36, 回收退单=45, 回收完成=936, 回收取消=91, 回收处理中=3}, 4846={等待签收=42, 订单异常=1, 回收退单=1, 回收完成=151, 回收取消=115}, 4946={等待签收=16, 订单异常=2, 验机确认=7, 回收退单=26, 回收完成=160, 回收取消=46, 回收处理中=5}}
2025-08-22 10:02:21.338 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 按商户ID分组解析物流订单统计数据: {4925={"pendingReceive":4,"name":"爱博绿","source":"87","pendingPickup":13}, 5166={"pendingReceive":0,"name":"海鲸回收","source":"97","pendingPickup":0}, 4573={"pendingReceive":28,"name":"爱裹","source":"72","pendingPickup":24}, 4442={"pendingReceive":92,"name":"飞蚂蚁","source":"65","pendingPickup":78}, 5257={"pendingReceive":9,"name":"上海绿袋环保","source":"98","pendingPickup":0}, 5996={"pendingReceive":0,"name":"蓝鲸鱼","source":"105","pendingPickup":1}, 4390={"pendingReceive":5,"name":"时代鸟平台","source":"59","pendingPickup":21}, 4846={"pendingReceive":3,"name":"湖北省再生资源集团","source":"84","pendingPickup":4}, 4946={"pendingReceive":5,"name":"汪回收","source":"90","pendingPickup":3}}
2025-08-22 10:02:21.338 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 微信群来源统计任务执行成功
2025-08-22 10:02:21.338 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 获取到 9 条统计数据
2025-08-22 10:02:21.338 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 按wxgroup分组后有 9 个不同的wxgroup
2025-08-22 10:02:21.338 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 开始为商户ID 4925 推送消息...
2025-08-22 10:02:28.481 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 商户ID 4925 推送消息内容:   【爱博绿】
──────────────
昨日订单统计（2025-08-21）
──────────────
 📝 - 下单量：24
 
 ↩️ - 取消单量：8
 
 ✅ - 回收完成：5
──────────────
近30天订单统计
──────────────
 ⬅️ - 待派单单量：0
 
 📦 - 待揽收单量：13
 
 🚚 - 待签收单量：4
 
 ⏳ - 待验机单量：0
 
 🕵️ - 验机待确认单量：0
 
 🔄 - 待退回单量：2
 

2025-08-22 10:02:33.070 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 开始为商户ID 5166 推送消息...
2025-08-22 10:02:36.293 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 商户ID 5166 推送消息内容:   【海鲸回收】
──────────────
昨日订单统计（2025-08-21）
──────────────
 📝 - 下单量：0
 
 ↩️ - 取消单量：0
 
 ✅ - 回收完成：0
──────────────
近30天订单统计
──────────────
 ⬅️ - 待派单单量：0
 
 📦 - 待揽收单量：0
 
 🚚 - 待签收单量：0
 
 ⏳ - 待验机单量：0
 
 🕵️ - 验机待确认单量：0
 
 🔄 - 待退回单量：0
 

2025-08-22 10:02:43.422 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 开始为商户ID 4573 推送消息...
2025-08-22 10:02:46.462 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 商户ID 4573 推送消息内容:   【爱裹】
──────────────
昨日订单统计（2025-08-21）
──────────────
 📝 - 下单量：43
 
 ↩️ - 取消单量：16
 
 ✅ - 回收完成：14
──────────────
近30天订单统计
──────────────
 ⬅️ - 待派单单量：0
 
 📦 - 待揽收单量：24
 
 🚚 - 待签收单量：28
 
 ⏳ - 待验机单量：1
 
 🕵️ - 验机待确认单量：24
 
 🔄 - 待退回单量：2
 

2025-08-22 10:02:46.463 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 开始为商户ID 4442 推送消息...
2025-08-22 10:02:53.577 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 商户ID 4442 推送消息内容:   【飞蚂蚁】
──────────────
昨日订单统计（2025-08-21）
──────────────
 📝 - 下单量：146
 
 ↩️ - 取消单量：20
 
 ✅ - 回收完成：103
──────────────
近30天订单统计
──────────────
 ⬅️ - 待派单单量：0
 
 📦 - 待揽收单量：78
 
 🚚 - 待签收单量：92
 
 ⏳ - 待验机单量：1
 
 🕵️ - 验机待确认单量：98
 
 🔄 - 待退回单量：16
 

2025-08-22 10:02:53.579 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 开始为商户ID 5257 推送消息...
2025-08-22 10:02:53.579 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 商户ID 5257 推送消息内容:   【上海绿袋环保】
──────────────
昨日订单统计（2025-08-21）
──────────────
 📝 - 下单量：6
 
 ↩️ - 取消单量：1
 
 ✅ - 回收完成：4
──────────────
近30天订单统计
──────────────
 ⬅️ - 待派单单量：1
 
 📦 - 待揽收单量：0
 
 🚚 - 待签收单量：9
 
 ⏳ - 待验机单量：0
 
 🕵️ - 验机待确认单量：1
 
 🔄 - 待退回单量：1
 

2025-08-22 10:02:53.579 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 开始为商户ID 5996 推送消息...
2025-08-22 10:02:53.580 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 商户ID 5996 推送消息内容:   【蓝鲸鱼】
──────────────
昨日订单统计（2025-08-21）
──────────────
 📝 - 下单量：4
 
 ↩️ - 取消单量：0
 
 ✅ - 回收完成：1
──────────────
近30天订单统计
──────────────
 ⬅️ - 待派单单量：0
 
 📦 - 待揽收单量：1
 
 🚚 - 待签收单量：0
 
 ⏳ - 待验机单量：0
 
 🕵️ - 验机待确认单量：1
 
 🔄 - 待退回单量：0
 

2025-08-22 10:02:53.580 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 开始为商户ID 4390 推送消息...
2025-08-22 10:02:53.580 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 商户ID 4390 推送消息内容:   【时代鸟平台】
──────────────
昨日订单统计（2025-08-21）
──────────────
 📝 - 下单量：27
 
 ↩️ - 取消单量：2
 
 ✅ - 回收完成：24
──────────────
近30天订单统计
──────────────
 ⬅️ - 待派单单量：0
 
 📦 - 待揽收单量：21
 
 🚚 - 待签收单量：5
 
 ⏳ - 待验机单量：0
 
 🕵️ - 验机待确认单量：36
 
 🔄 - 待退回单量：0
 

2025-08-22 10:02:53.581 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 开始为商户ID 4846 推送消息...
2025-08-22 10:02:53.581 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 商户ID 4846 推送消息内容:   【湖北省再生资源集团】
──────────────
昨日订单统计（2025-08-21）
──────────────
 📝 - 下单量：4
 
 ↩️ - 取消单量：2
 
 ✅ - 回收完成：1
──────────────
近30天订单统计
──────────────
 ⬅️ - 待派单单量：0
 
 📦 - 待揽收单量：4
 
 🚚 - 待签收单量：3
 
 ⏳ - 待验机单量：0
 
 🕵️ - 验机待确认单量：0
 
 🔄 - 待退回单量：0
 

2025-08-22 10:02:53.582 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 开始为商户ID 4946 推送消息...
2025-08-22 10:02:53.582 [http-nio-8080-exec-1] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 商户ID 4946 推送消息内容:   【汪回收】
──────────────
昨日订单统计（2025-08-21）
──────────────
 📝 - 下单量：7
 
 ↩️ - 取消单量：2
 
 ✅ - 回收完成：2
──────────────
近30天订单统计
──────────────
 ⬅️ - 待派单单量：0
 
 📦 - 待揽收单量：3
 
 🚚 - 待签收单量：5
 
 ⏳ - 待验机单量：0
 
 🕵️ - 验机待确认单量：7
 
 🔄 - 待退回单量：0
 

2025-08-22 10:07:03.552 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 10:12:03.567 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 10:14:27.002 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 4 有一客服连接关闭！
2025-08-22 10:14:27.006 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：0
2025-08-22 10:14:27.007 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-22 10:14:27.007 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-22 10:14:27.014 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-08-22 10:14:27.015 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-08-22 10:14:27.016 [SpringApplicationShutdownHook] INFO  com.kefang.config.OssConfig - 正在关闭OSS客户端...
2025-08-22 10:14:27.016 [SpringApplicationShutdownHook] INFO  com.kefang.config.OssConfig - OSS客户端已关闭
2025-08-22 10:14:27.016 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-22 10:14:27.021 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-22 10:14:31.083 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-22 10:14:31.092 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 51648 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-08-22 10:14:31.093 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "dev"
2025-08-22 10:14:32.092 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-22 10:14:32.101 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-22 10:14:32.101 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-22 10:14:32.101 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-08-22 10:14:32.209 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-22 10:14:32.210 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1080 ms
2025-08-22 10:14:32.617 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-22 10:14:32.622 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-22 10:14:32.633 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-22 10:14:32.672 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-22 10:14:32.675 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-22 10:14:32.681 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-08-22 10:14:32.683 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-08-22 10:14:32.683 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-22 10:14:32.798 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-08-22 10:14:32.821 [main] INFO  com.kefang.config.OssConfig - 正在初始化OSS客户端...
2025-08-22 10:14:32.822 [main] INFO  com.kefang.config.OssConfig - OSS配置信息 - endpoint: http://oss-cn-beijing.aliyuncs.com, bucketName: juranfile
2025-08-22 10:14:32.990 [main] INFO  com.kefang.config.OssConfig - OSS客户端初始化成功
2025-08-22 10:14:33.424 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-22 10:14:33.458 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-22 10:14:33.475 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-22 10:14:33.489 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.792 seconds (JVM running for 3.372)
2025-08-22 10:14:33.489 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-22 10:14:33.560 [http-nio-8080-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22 10:14:33.560 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-22 10:14:33.561 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-22 10:14:33.607 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: agent, id: 4, sessionId: 0
2025-08-22 10:14:33.608 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 客服 4 的WebSocket连接已存储，连接ID: 0
2025-08-22 10:14:33.608 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：1
2025-08-22 10:14:33.608 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-22 10:14:33.608 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 10:14:33.635 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-22 10:14:33.659 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 10:14:54.425 [http-nio-8080-exec-3] INFO  com.kefang.controller.ConnectController - 手动触发微信群来源统计任务
2025-08-22 10:14:54.425 [http-nio-8080-exec-3] INFO  com.kefang.job.SourceWxGroupStatisticsTask - 当前端口不是8819，不执行定时微信群来源统计任务
2025-08-22 10:19:33.493 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 10:24:33.501 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 10:29:33.497 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 10:34:33.487 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 10:39:33.501 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 10:44:33.501 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 10:49:33.490 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 10:54:33.495 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 10:59:33.504 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 11:04:33.492 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 11:09:33.492 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 11:14:33.497 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 11:19:33.492 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 11:24:33.497 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 11:29:33.501 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 11:34:33.491 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 11:39:33.494 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 11:44:33.495 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 11:49:33.501 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 11:54:33.499 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 11:59:33.497 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 12:04:33.497 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 12:09:33.487 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 12:14:33.490 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 12:19:33.487 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 12:24:33.497 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 12:29:33.495 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 12:34:33.490 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 12:39:33.487 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 12:44:33.496 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 12:49:33.487 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 12:54:33.487 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 12:59:33.500 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 13:04:33.501 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 13:09:33.497 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 13:14:33.487 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 13:15:27.755 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: user, id: 283, sessionId: 1
2025-08-22 13:15:27.756 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 用户 283 的WebSocket连接已存储，连接ID: 1
2025-08-22 13:15:27.756 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：2
2025-08-22 13:15:27.756 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [283]
2025-08-22 13:15:27.756 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 13:15:28.770 [Thread-3] INFO  com.kefang.websocket.WebSocketServer - 开始发送欢迎消息给用户: 283
2025-08-22 13:15:28.773 [Thread-3] INFO  com.kefang.websocket.WebSocketServer - 为欢迎消息设置会话ID: 457
2025-08-22 13:15:28.787 [Thread-3] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已保存到数据库，ID: 5739
2025-08-22 13:15:28.787 [Thread-3] INFO  com.kefang.websocket.WebSocketServer - 发送欢迎消息JSON: {"content":"您好，请问有什么可以帮您，可描述您想咨询的问题点击发送给我们。人工客服工作时间为09:00-21:00","from":2,"id":5739,"receiverId":283,"senderId":6,"sessionId":457,"timestamp":1755839728770,"type":1}
2025-08-22 13:15:28.788 [Thread-3] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已成功发送给用户: 283
2025-08-22 13:19:33.487 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 13:21:57.276 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 283 有一用户连接关闭！
2025-08-22 13:21:57.277 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：1
2025-08-22 13:21:57.277 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-22 13:21:57.277 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 13:23:29.996 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 4 有一客服连接关闭！
2025-08-22 13:23:30.001 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：0
2025-08-22 13:23:30.001 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-22 13:23:30.001 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-22 13:24:33.488 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 13:25:00.656 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: agent, id: 4, sessionId: 2
2025-08-22 13:25:00.656 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 客服 4 的WebSocket连接已存储，连接ID: 2
2025-08-22 13:25:00.656 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：1
2025-08-22 13:25:00.656 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-22 13:25:00.656 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 13:25:08.102 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: user, id: 283, sessionId: 3
2025-08-22 13:25:08.103 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 用户 283 的WebSocket连接已存储，连接ID: 3
2025-08-22 13:25:08.103 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：2
2025-08-22 13:25:08.103 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [283]
2025-08-22 13:25:08.103 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 13:25:09.111 [Thread-5] INFO  com.kefang.websocket.WebSocketServer - 开始发送欢迎消息给用户: 283
2025-08-22 13:25:09.113 [Thread-5] INFO  com.kefang.websocket.WebSocketServer - 为欢迎消息设置会话ID: 457
2025-08-22 13:25:09.118 [Thread-5] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已保存到数据库，ID: 5740
2025-08-22 13:25:09.118 [Thread-5] INFO  com.kefang.websocket.WebSocketServer - 发送欢迎消息JSON: {"content":"您好，请问有什么可以帮您，可描述您想咨询的问题点击发送给我们。人工客服工作时间为09:00-21:00","from":2,"id":5740,"receiverId":283,"senderId":6,"sessionId":457,"timestamp":1755840309112,"type":1}
2025-08-22 13:25:09.119 [Thread-5] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已成功发送给用户: 283
2025-08-22 13:25:26.897 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 尝试发送转接人工通知 - 会话ID: 457, 用户ID: 283, 客服ID: 4
2025-08-22 13:25:26.899 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 已通过WebSocket发送转接通知给用户: 283
2025-08-22 13:25:26.900 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 尝试发送转接通知给客服 - 会话ID: 457, 用户ID: 283, 客服ID: 4
2025-08-22 13:25:26.901 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 已通过WebSocket发送转接通知给客服: 4
2025-08-22 13:25:30.074 [http-nio-8080-exec-7] INFO  com.kefang.controller.FaqController - 查询所有常见问题
2025-08-22 13:25:33.539 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 尝试发送转接人工通知 - 会话ID: 457, 用户ID: 283, 客服ID: 4
2025-08-22 13:25:33.539 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 已通过WebSocket发送转接通知给用户: 283
2025-08-22 13:25:37.322 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: 您好，很高兴为您服务 您好，很高兴为您服务
  发送者ID: 4 发送方: 客服 数据源: 【默认】
  接收方ID: 283  会话ID: 457 类型: 1
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:25:40.637 [http-nio-8080-exec-5] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: 你好
  发送者ID: 283 发送方: 用户 数据源: 【默认数据源】
  接收方ID: 4  会话ID: 457 类型: 1
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:25:40.646 [http-nio-8080-exec-5] INFO  com.kefang.websocket.WebSocketServer - 发送消息给指定客服: 4
2025-08-22 13:25:45.409 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/upfile/2025/08-22/82e8a47bd90b47c0b79e4f93ff259cc5.png
  发送者ID: 4 发送方: 客服 数据源: 【默认数据源】
  接收方ID: 283  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:25:51.028 [http-nio-8080-exec-4] INFO  com.kefang.controller.FileUploadController - 开始上传文件：recraft-v3_视觉模型_的logo_的__3_-removebg-preview.png, 大小：204521 bytes
2025-08-22 13:25:51.358 [http-nio-8080-exec-4] INFO  com.kefang.utils.OssFileUploadUtil - 文件上传成功：原文件名=recraft-v3_视觉模型_的logo_的__3_-removebg-preview.png, OSS路径=dismantling/2025/08/22/48d3142637d844e7b94b43d10ec53f25_1755840351045.png, 访问URL=https://file.juranguanjia.com/dismantling/2025/08/22/48d3142637d844e7b94b43d10ec53f25_1755840351045.png
2025-08-22 13:25:51.358 [http-nio-8080-exec-4] INFO  com.kefang.controller.FileUploadController - 文件上传成功：https://file.juranguanjia.com/dismantling/2025/08/22/48d3142637d844e7b94b43d10ec53f25_1755840351045.png
2025-08-22 13:25:51.367 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/dismantling/2025/08/22/48d3142637d844e7b94b43d10ec53f25_1755840351045.png
  发送者ID: 283 发送方: 用户 数据源: 【null】
  接收方ID: 4  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:25:51.376 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 发送消息给指定客服: 4
2025-08-22 13:26:06.140 [http-nio-8080-exec-2] INFO  com.kefang.controller.FileUploadController - 开始上传文件：66ae0039ba7a7993d273eeaf903711e2.mp4, 大小：4475551 bytes
2025-08-22 13:26:06.404 [http-nio-8080-exec-2] INFO  com.kefang.utils.OssFileUploadUtil - 文件上传成功：原文件名=66ae0039ba7a7993d273eeaf903711e2.mp4, OSS路径=dismantling/2025/08/22/3500d177afe04e93953cca39139cad56_1755840366140.mp4, 访问URL=https://file.juranguanjia.com/dismantling/2025/08/22/3500d177afe04e93953cca39139cad56_1755840366140.mp4
2025-08-22 13:26:06.404 [http-nio-8080-exec-2] INFO  com.kefang.controller.FileUploadController - 文件上传成功：https://file.juranguanjia.com/dismantling/2025/08/22/3500d177afe04e93953cca39139cad56_1755840366140.mp4
2025-08-22 13:29:26.197 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 283 有一用户连接关闭！
2025-08-22 13:29:26.198 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：1
2025-08-22 13:29:26.198 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-22 13:29:26.198 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 13:29:26.198 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 4 有一客服连接关闭！
2025-08-22 13:29:26.202 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：0
2025-08-22 13:29:26.202 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-22 13:29:26.202 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-22 13:29:26.208 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-08-22 13:29:26.209 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-08-22 13:29:26.209 [SpringApplicationShutdownHook] INFO  com.kefang.config.OssConfig - 正在关闭OSS客户端...
2025-08-22 13:29:26.209 [SpringApplicationShutdownHook] INFO  com.kefang.config.OssConfig - OSS客户端已关闭
2025-08-22 13:29:26.210 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-22 13:29:26.212 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-22 13:29:31.747 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-22 13:29:31.753 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 45164 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-08-22 13:29:31.754 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "prod"
2025-08-22 13:29:32.691 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8819 (http)
2025-08-22 13:29:32.700 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8819"]
2025-08-22 13:29:32.701 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-22 13:29:32.701 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-08-22 13:29:32.833 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-22 13:29:32.833 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1047 ms
2025-08-22 13:29:33.241 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-22 13:29:33.250 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-22 13:29:33.262 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-22 13:29:33.317 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-22 13:29:33.323 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-22 13:29:33.331 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-08-22 13:29:33.335 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-08-22 13:29:33.335 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-22 13:29:33.493 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-08-22 13:29:33.517 [main] INFO  com.kefang.config.OssConfig - 正在初始化OSS客户端...
2025-08-22 13:29:33.518 [main] INFO  com.kefang.config.OssConfig - OSS配置信息 - endpoint: http://oss-cn-beijing.aliyuncs.com, bucketName: juranfile
2025-08-22 13:29:33.743 [main] INFO  com.kefang.config.OssConfig - OSS客户端初始化成功
2025-08-22 13:29:34.237 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-22 13:29:34.279 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8819"]
2025-08-22 13:29:34.317 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8819 (http) with context path ''
2025-08-22 13:29:34.353 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.941 seconds (JVM running for 3.881)
2025-08-22 13:29:34.354 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-22 13:29:40.573 [scheduling-1] ERROR com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:265)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:400)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:373)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:595)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:382)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.kefang.job.AutoSessionTransferTask$$EnhancerBySpringCGLIB$$a938c38a.transferInactiveSessionsToAI(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:582)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:537)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:424)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1428)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:134)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 33 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:576)
	... 39 common frames omitted
2025-08-22 13:29:40.574 [scheduling-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.transaction.CannotCreateTransactionException: Could not open JDBC Connection for transaction; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:309)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:400)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:373)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:595)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:382)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.kefang.job.AutoSessionTransferTask$$EnhancerBySpringCGLIB$$a938c38a.transferInactiveSessionsToAI(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:265)
	... 22 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:582)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:537)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:424)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1428)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:134)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 33 common frames omitted
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:576)
	... 39 common frames omitted
2025-08-22 13:30:14.906 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-08-22 13:30:14.907 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-08-22 13:30:14.908 [SpringApplicationShutdownHook] INFO  com.kefang.config.OssConfig - 正在关闭OSS客户端...
2025-08-22 13:30:14.908 [SpringApplicationShutdownHook] INFO  com.kefang.config.OssConfig - OSS客户端已关闭
2025-08-22 13:30:17.285 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-22 13:30:17.296 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 45744 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-08-22 13:30:17.297 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "dev"
2025-08-22 13:30:18.328 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-22 13:30:18.335 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-22 13:30:18.336 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-22 13:30:18.336 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-08-22 13:30:18.435 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-22 13:30:18.435 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1102 ms
2025-08-22 13:30:18.860 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-22 13:30:18.865 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-22 13:30:18.875 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-22 13:30:18.911 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-22 13:30:18.914 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-22 13:30:18.918 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-08-22 13:30:18.920 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-08-22 13:30:18.921 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-22 13:30:19.094 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-08-22 13:30:19.125 [main] INFO  com.kefang.config.OssConfig - 正在初始化OSS客户端...
2025-08-22 13:30:19.125 [main] INFO  com.kefang.config.OssConfig - OSS配置信息 - endpoint: http://oss-cn-beijing.aliyuncs.com, bucketName: juranfile
2025-08-22 13:30:19.270 [main] INFO  com.kefang.config.OssConfig - OSS客户端初始化成功
2025-08-22 13:30:19.692 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-22 13:30:19.735 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-22 13:30:19.750 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-22 13:30:19.767 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 3.037 seconds (JVM running for 3.757)
2025-08-22 13:30:19.767 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-22 13:30:19.897 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-22 13:30:19.926 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 13:30:20.054 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22 13:30:20.054 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-22 13:30:20.055 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-22 13:30:20.089 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: agent, id: 4, sessionId: 1
2025-08-22 13:30:20.089 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: user, id: 283, sessionId: 0
2025-08-22 13:30:20.090 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 客服 4 的WebSocket连接已存储，连接ID: 1
2025-08-22 13:30:20.090 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 用户 283 的WebSocket连接已存储，连接ID: 0
2025-08-22 13:30:20.090 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：1
2025-08-22 13:30:20.090 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：2
2025-08-22 13:30:20.090 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [283]
2025-08-22 13:30:20.090 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [283]
2025-08-22 13:30:20.090 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 13:30:20.090 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 13:30:21.093 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 开始发送欢迎消息给用户: 283
2025-08-22 13:30:21.096 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 为欢迎消息设置会话ID: 457
2025-08-22 13:30:21.105 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已保存到数据库，ID: 5747
2025-08-22 13:30:21.105 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 发送欢迎消息JSON: {"content":"您好，请问有什么可以帮您，可描述您想咨询的问题点击发送给我们。人工客服工作时间为09:00-21:00","from":2,"id":5747,"receiverId":283,"senderId":6,"sessionId":457,"timestamp":1755840621093,"type":1}
2025-08-22 13:30:21.106 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已成功发送给用户: 283
2025-08-22 13:31:02.357 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: 你好
  发送者ID: 283 发送方: 用户 数据源: 【默认数据源】
  接收方ID: 4  会话ID: 457 类型: 1
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:31:02.374 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 发送消息给指定客服: 4
2025-08-22 13:31:09.903 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: 您好，很高兴为您服务 您好，很高兴为您服务
  发送者ID: 4 发送方: 客服 数据源: 【默认】
  接收方ID: 283  会话ID: 457 类型: 1
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:31:14.196 [http-nio-8080-exec-6] INFO  com.kefang.controller.FileUploadController - 开始上传文件：recraft-v3_个_视觉模型_的logo_的 (2) (1) - 副本.png, 大小：547869 bytes
2025-08-22 13:31:14.401 [http-nio-8080-exec-6] INFO  com.kefang.utils.OssFileUploadUtil - 文件上传成功：原文件名=recraft-v3_个_视觉模型_的logo_的 (2) (1) - 副本.png, OSS路径=dismantling/2025/08/22/8edb598ec36f46a0b1bd1db13558765c_1755840674210.png, 访问URL=https://file.juranguanjia.com/dismantling/2025/08/22/8edb598ec36f46a0b1bd1db13558765c_1755840674210.png
2025-08-22 13:31:14.402 [http-nio-8080-exec-6] INFO  com.kefang.controller.FileUploadController - 文件上传成功：https://file.juranguanjia.com/dismantling/2025/08/22/8edb598ec36f46a0b1bd1db13558765c_1755840674210.png
2025-08-22 13:31:14.410 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/dismantling/2025/08/22/8edb598ec36f46a0b1bd1db13558765c_1755840674210.png
  发送者ID: 283 发送方: 用户 数据源: 【null】
  接收方ID: 4  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:31:14.423 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 发送消息给指定客服: 4
2025-08-22 13:31:21.169 [http-nio-8080-exec-7] INFO  com.kefang.controller.FileUploadController - 开始上传文件：66ae0039ba7a7993d273eeaf903711e2.mp4, 大小：4475551 bytes
2025-08-22 13:31:21.467 [http-nio-8080-exec-7] INFO  com.kefang.utils.OssFileUploadUtil - 文件上传成功：原文件名=66ae0039ba7a7993d273eeaf903711e2.mp4, OSS路径=dismantling/2025/08/22/9f88a27f828a4532953add40c2e3b59e_1755840681169.mp4, 访问URL=https://file.juranguanjia.com/dismantling/2025/08/22/9f88a27f828a4532953add40c2e3b59e_1755840681169.mp4
2025-08-22 13:31:21.467 [http-nio-8080-exec-7] INFO  com.kefang.controller.FileUploadController - 文件上传成功：https://file.juranguanjia.com/dismantling/2025/08/22/9f88a27f828a4532953add40c2e3b59e_1755840681169.mp4
2025-08-22 13:35:14.556 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: 你好
  发送者ID: 283 发送方: 用户 数据源: 【默认数据源】
  接收方ID: 4  会话ID: 457 类型: 1
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:35:14.566 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 发送消息给指定客服: 4
2025-08-22 13:35:19.776 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 13:35:21.385 [http-nio-8080-exec-8] INFO  com.kefang.controller.FileUploadController - 开始上传文件：recraft-v3_个_视觉模型_的logo_的 (2) (1) - 副本.png, 大小：547869 bytes
2025-08-22 13:35:21.668 [http-nio-8080-exec-8] INFO  com.kefang.utils.OssFileUploadUtil - 文件上传成功：原文件名=recraft-v3_个_视觉模型_的logo_的 (2) (1) - 副本.png, OSS路径=dismantling/2025/08/22/0a4a2ddb4dfb49cb92cf8136730c3c99_1755840921386.png, 访问URL=https://file.juranguanjia.com/dismantling/2025/08/22/0a4a2ddb4dfb49cb92cf8136730c3c99_1755840921386.png
2025-08-22 13:35:21.669 [http-nio-8080-exec-8] INFO  com.kefang.controller.FileUploadController - 文件上传成功：https://file.juranguanjia.com/dismantling/2025/08/22/0a4a2ddb4dfb49cb92cf8136730c3c99_1755840921386.png
2025-08-22 13:35:21.671 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/dismantling/2025/08/22/0a4a2ddb4dfb49cb92cf8136730c3c99_1755840921386.png
  发送者ID: 283 发送方: 用户 数据源: 【null】
  接收方ID: 4  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:35:21.678 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 发送消息给指定客服: 4
2025-08-22 13:35:27.431 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/upfile/2025/08-22/1b44fa92125f4cd2a989013dc9a7307b.png
  发送者ID: 4 发送方: 客服 数据源: 【默认数据源】
  接收方ID: 283  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:35:30.158 [http-nio-8080-exec-5] INFO  com.kefang.controller.FileUploadController - 开始上传文件：66ae0039ba7a7993d273eeaf903711e2.mp4, 大小：4475551 bytes
2025-08-22 13:35:31.282 [http-nio-8080-exec-5] INFO  com.kefang.utils.OssFileUploadUtil - 文件上传成功：原文件名=66ae0039ba7a7993d273eeaf903711e2.mp4, OSS路径=dismantling/2025/08/22/71de3671dda64b00acd7de7a388f2971_1755840930158.mp4, 访问URL=https://file.juranguanjia.com/dismantling/2025/08/22/71de3671dda64b00acd7de7a388f2971_1755840930158.mp4
2025-08-22 13:35:31.282 [http-nio-8080-exec-5] INFO  com.kefang.controller.FileUploadController - 文件上传成功：https://file.juranguanjia.com/dismantling/2025/08/22/71de3671dda64b00acd7de7a388f2971_1755840930158.mp4
2025-08-22 13:35:31.285 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/dismantling/2025/08/22/71de3671dda64b00acd7de7a388f2971_1755840930158.mp4
  发送者ID: 283 发送方: 用户 数据源: 【null】
  接收方ID: 4  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:35:31.291 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 发送消息给指定客服: 4
2025-08-22 13:35:39.519 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: agent, id: 4, sessionId: 2
2025-08-22 13:35:39.521 [http-nio-8080-exec-4] WARN  com.kefang.websocket.WebSocketServer - 客服 4 已有连接, 关闭旧连接
2025-08-22 13:35:39.521 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 4 有一客服连接关闭！
2025-08-22 13:35:39.525 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：1
2025-08-22 13:35:39.525 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [283]
2025-08-22 13:35:39.525 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-22 13:35:39.525 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 客服 4 的WebSocket连接已存储，连接ID: 2
2025-08-22 13:35:39.525 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：2
2025-08-22 13:35:39.525 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [283]
2025-08-22 13:35:39.525 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 13:35:39.824 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 4 有一客服连接关闭！
2025-08-22 13:35:39.828 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：1
2025-08-22 13:35:39.828 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [283]
2025-08-22 13:35:39.828 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-22 13:35:44.531 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: agent, id: 4, sessionId: 3
2025-08-22 13:35:44.532 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 客服 4 的WebSocket连接已存储，连接ID: 3
2025-08-22 13:35:44.532 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：2
2025-08-22 13:35:44.532 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [283]
2025-08-22 13:35:44.532 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 13:37:47.285 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 283 有一用户连接关闭！
2025-08-22 13:37:47.285 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：1
2025-08-22 13:37:47.285 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-22 13:37:47.285 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 13:37:47.285 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 4 有一客服连接关闭！
2025-08-22 13:37:47.290 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：0
2025-08-22 13:37:47.290 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-22 13:37:47.290 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-22 13:37:47.298 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-08-22 13:37:47.298 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-08-22 13:37:47.298 [SpringApplicationShutdownHook] INFO  com.kefang.config.OssConfig - 正在关闭OSS客户端...
2025-08-22 13:37:47.298 [SpringApplicationShutdownHook] INFO  com.kefang.config.OssConfig - OSS客户端已关闭
2025-08-22 13:37:47.299 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-22 13:37:47.303 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-22 13:37:49.228 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-22 13:37:49.235 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 50764 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-08-22 13:37:49.235 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "dev"
2025-08-22 13:37:50.158 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-22 13:37:50.166 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-22 13:37:50.166 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-22 13:37:50.166 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-08-22 13:37:50.260 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-22 13:37:50.260 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 993 ms
2025-08-22 13:37:50.617 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-22 13:37:50.625 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-22 13:37:50.642 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-22 13:37:50.680 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-22 13:37:50.682 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-22 13:37:50.686 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-08-22 13:37:50.689 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-08-22 13:37:50.689 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-22 13:37:50.801 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-08-22 13:37:50.819 [main] INFO  com.kefang.config.OssConfig - 正在初始化OSS客户端...
2025-08-22 13:37:50.819 [main] INFO  com.kefang.config.OssConfig - OSS配置信息 - endpoint: http://oss-cn-beijing.aliyuncs.com, bucketName: juranfile
2025-08-22 13:37:50.949 [main] INFO  com.kefang.config.OssConfig - OSS客户端初始化成功
2025-08-22 13:37:51.359 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-22 13:37:51.394 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-22 13:37:51.409 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-22 13:37:51.425 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.593 seconds (JVM running for 3.223)
2025-08-22 13:37:51.425 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-22 13:37:51.534 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-22 13:37:51.556 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 13:37:53.037 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22 13:37:53.037 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-22 13:37:53.038 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-22 13:37:53.069 [http-nio-8080-exec-5] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: agent, id: 4, sessionId: 1
2025-08-22 13:37:53.069 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: user, id: 283, sessionId: 0
2025-08-22 13:37:53.069 [http-nio-8080-exec-5] INFO  com.kefang.websocket.WebSocketServer - 客服 4 的WebSocket连接已存储，连接ID: 1
2025-08-22 13:37:53.069 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 用户 283 的WebSocket连接已存储，连接ID: 0
2025-08-22 13:37:53.069 [http-nio-8080-exec-5] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：1
2025-08-22 13:37:53.069 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：2
2025-08-22 13:37:53.069 [http-nio-8080-exec-5] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [283]
2025-08-22 13:37:53.069 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [283]
2025-08-22 13:37:53.069 [http-nio-8080-exec-5] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 13:37:53.069 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 13:37:54.080 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 开始发送欢迎消息给用户: 283
2025-08-22 13:37:54.083 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 为欢迎消息设置会话ID: 457
2025-08-22 13:37:54.091 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已保存到数据库，ID: 5755
2025-08-22 13:37:54.091 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 发送欢迎消息JSON: {"content":"您好，请问有什么可以帮您，可描述您想咨询的问题点击发送给我们。人工客服工作时间为09:00-21:00","from":2,"id":5755,"receiverId":283,"senderId":6,"sessionId":457,"timestamp":1755841074080,"type":1}
2025-08-22 13:37:54.091 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已成功发送给用户: 283
2025-08-22 13:38:00.603 [http-nio-8080-exec-6] INFO  com.kefang.controller.FileUploadController - 开始上传文件：recraft-v3_o_的 (2) (1).png, 大小：624674 bytes
2025-08-22 13:38:00.812 [http-nio-8080-exec-6] INFO  com.kefang.utils.OssFileUploadUtil - 文件上传成功：原文件名=recraft-v3_o_的 (2) (1).png, OSS路径=kefu/2025/08/22/21a71ce938fb42e88a811557a702e964_1755841080616.png, 访问URL=https://file.juranguanjia.com/kefu/2025/08/22/21a71ce938fb42e88a811557a702e964_1755841080616.png
2025-08-22 13:38:00.813 [http-nio-8080-exec-6] INFO  com.kefang.controller.FileUploadController - 文件上传成功：https://file.juranguanjia.com/kefu/2025/08/22/21a71ce938fb42e88a811557a702e964_1755841080616.png
2025-08-22 13:38:00.822 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/kefu/2025/08/22/21a71ce938fb42e88a811557a702e964_1755841080616.png
  发送者ID: 283 发送方: 用户 数据源: 【null】
  接收方ID: 4  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:38:00.849 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 发送消息给指定客服: 4
2025-08-22 13:38:34.232 [http-nio-8080-exec-9] INFO  com.kefang.controller.FileUploadController - 开始上传文件：66ae0039ba7a7993d273eeaf903711e2.mp4, 大小：4475551 bytes
2025-08-22 13:38:34.652 [http-nio-8080-exec-9] INFO  com.kefang.utils.OssFileUploadUtil - 文件上传成功：原文件名=66ae0039ba7a7993d273eeaf903711e2.mp4, OSS路径=kefu/2025/08/22/30093d911c7e482a8cf326f70e2d2686_1755841114232.mp4, 访问URL=https://file.juranguanjia.com/kefu/2025/08/22/30093d911c7e482a8cf326f70e2d2686_1755841114232.mp4
2025-08-22 13:38:34.652 [http-nio-8080-exec-9] INFO  com.kefang.controller.FileUploadController - 文件上传成功：https://file.juranguanjia.com/kefu/2025/08/22/30093d911c7e482a8cf326f70e2d2686_1755841114232.mp4
2025-08-22 13:38:34.655 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/kefu/2025/08/22/30093d911c7e482a8cf326f70e2d2686_1755841114232.mp4
  发送者ID: 283 发送方: 用户 数据源: 【null】
  接收方ID: 4  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:38:34.672 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 发送消息给指定客服: 4
2025-08-22 13:41:43.658 [http-nio-8080-exec-9] INFO  com.kefang.controller.FileUploadController - 开始上传文件：recraft-v3_个_视觉模型_的logo_的 (2) (1) - 副本.png, 大小：547869 bytes
2025-08-22 13:41:43.780 [http-nio-8080-exec-9] INFO  com.kefang.utils.OssFileUploadUtil - 文件上传成功：原文件名=recraft-v3_个_视觉模型_的logo_的 (2) (1) - 副本.png, OSS路径=kefu/2025/08/22/55b6b902323042cc88e8757b2a15b682_1755841303659.png, 访问URL=https://file.juranguanjia.com/kefu/2025/08/22/55b6b902323042cc88e8757b2a15b682_1755841303659.png
2025-08-22 13:41:43.780 [http-nio-8080-exec-9] INFO  com.kefang.controller.FileUploadController - 文件上传成功：https://file.juranguanjia.com/kefu/2025/08/22/55b6b902323042cc88e8757b2a15b682_1755841303659.png
2025-08-22 13:41:43.784 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/kefu/2025/08/22/55b6b902323042cc88e8757b2a15b682_1755841303659.png
  发送者ID: 283 发送方: 用户 数据源: 【null】
  接收方ID: 4  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:41:43.793 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 发送消息给指定客服: 4
2025-08-22 13:41:51.257 [http-nio-8080-exec-6] INFO  com.kefang.controller.FileUploadController - 开始上传文件：66ae0039ba7a7993d273eeaf903711e2.mp4, 大小：4475551 bytes
2025-08-22 13:41:51.701 [http-nio-8080-exec-6] INFO  com.kefang.utils.OssFileUploadUtil - 文件上传成功：原文件名=66ae0039ba7a7993d273eeaf903711e2.mp4, OSS路径=kefu/2025/08/22/c62e6b16812b4f01b9c94a3d9dbe52aa_1755841311257.mp4, 访问URL=https://file.juranguanjia.com/kefu/2025/08/22/c62e6b16812b4f01b9c94a3d9dbe52aa_1755841311257.mp4
2025-08-22 13:41:51.701 [http-nio-8080-exec-6] INFO  com.kefang.controller.FileUploadController - 文件上传成功：https://file.juranguanjia.com/kefu/2025/08/22/c62e6b16812b4f01b9c94a3d9dbe52aa_1755841311257.mp4
2025-08-22 13:41:51.704 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/kefu/2025/08/22/c62e6b16812b4f01b9c94a3d9dbe52aa_1755841311257.mp4
  发送者ID: 283 发送方: 用户 数据源: 【null】
  接收方ID: 4  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:41:51.713 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 发送消息给指定客服: 4
2025-08-22 13:42:51.436 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 13:45:28.188 [http-nio-8080-exec-3] INFO  com.kefang.controller.FileUploadController - 开始上传文件：66ae0039ba7a7993d273eeaf903711e2.mp4, 大小：4475551 bytes
2025-08-22 13:45:28.883 [http-nio-8080-exec-3] INFO  com.kefang.utils.OssFileUploadUtil - 文件上传成功：原文件名=66ae0039ba7a7993d273eeaf903711e2.mp4, OSS路径=kefu/2025/08/22/90a729de3ccf472f9ff20c706db17608_1755841528189.mp4, 访问URL=https://file.juranguanjia.com/kefu/2025/08/22/90a729de3ccf472f9ff20c706db17608_1755841528189.mp4
2025-08-22 13:45:28.883 [http-nio-8080-exec-3] INFO  com.kefang.controller.FileUploadController - 文件上传成功：https://file.juranguanjia.com/kefu/2025/08/22/90a729de3ccf472f9ff20c706db17608_1755841528189.mp4
2025-08-22 13:45:28.887 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/kefu/2025/08/22/90a729de3ccf472f9ff20c706db17608_1755841528189.mp4
  发送者ID: 283 发送方: 用户 数据源: 【null】
  接收方ID: 4  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:45:28.896 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 发送消息给指定客服: 4
2025-08-22 13:45:38.888 [http-nio-8080-exec-7] INFO  com.kefang.controller.FileUploadController - 开始上传文件：recraft-v3_个_视觉模型_的logo_的 (2) (1) - 副本.png, 大小：547869 bytes
2025-08-22 13:45:39.010 [http-nio-8080-exec-7] INFO  com.kefang.utils.OssFileUploadUtil - 文件上传成功：原文件名=recraft-v3_个_视觉模型_的logo_的 (2) (1) - 副本.png, OSS路径=kefu/2025/08/22/3061a5957f0347669a166d890cfdbb93_1755841538889.png, 访问URL=https://file.juranguanjia.com/kefu/2025/08/22/3061a5957f0347669a166d890cfdbb93_1755841538889.png
2025-08-22 13:45:39.010 [http-nio-8080-exec-7] INFO  com.kefang.controller.FileUploadController - 文件上传成功：https://file.juranguanjia.com/kefu/2025/08/22/3061a5957f0347669a166d890cfdbb93_1755841538889.png
2025-08-22 13:45:39.012 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/kefu/2025/08/22/3061a5957f0347669a166d890cfdbb93_1755841538889.png
  发送者ID: 283 发送方: 用户 数据源: 【null】
  接收方ID: 4  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:45:39.020 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 发送消息给指定客服: 4
2025-08-22 13:45:43.495 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/upfile/2025/08-22/db31c76815f94f588408a605a415c53d.png
  发送者ID: 4 发送方: 客服 数据源: 【默认数据源】
  接收方ID: 283  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:47:51.425 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 13:52:51.425 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 13:56:02.483 [http-nio-8080-exec-9] INFO  com.kefang.controller.FaqController - 查询所有常见问题
2025-08-22 13:56:19.384 [http-nio-8080-exec-7] INFO  com.kefang.controller.FaqController - 查询所有常见问题
2025-08-22 13:57:21.130 [http-nio-8080-exec-2] INFO  com.kefang.controller.FaqController - 查询所有常见问题
2025-08-22 13:57:51.425 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 13:58:25.814 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 4 有一客服连接关闭！
2025-08-22 13:58:25.823 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：1
2025-08-22 13:58:25.824 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [283]
2025-08-22 13:58:25.824 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-22 13:58:28.618 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: agent, id: 4, sessionId: 2
2025-08-22 13:58:28.618 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 客服 4 的WebSocket连接已存储，连接ID: 2
2025-08-22 13:58:28.618 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：2
2025-08-22 13:58:28.618 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [283]
2025-08-22 13:58:28.618 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 13:58:29.170 [http-nio-8080-exec-10] INFO  com.kefang.controller.FaqController - 查询所有常见问题
2025-08-22 13:58:29.614 [http-nio-8080-exec-9] INFO  com.kefang.websocket.WebSocketServer - 283 有一用户连接关闭！
2025-08-22 13:58:29.614 [http-nio-8080-exec-9] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：1
2025-08-22 13:58:29.614 [http-nio-8080-exec-9] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-22 13:58:29.614 [http-nio-8080-exec-9] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 13:58:32.409 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: user, id: 283, sessionId: 3
2025-08-22 13:58:32.409 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 用户 283 的WebSocket连接已存储，连接ID: 3
2025-08-22 13:58:32.409 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：2
2025-08-22 13:58:32.409 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [283]
2025-08-22 13:58:32.409 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 13:58:33.312 [http-nio-8080-exec-10] INFO  com.kefang.controller.QuestionAnalysisController - 获取问题分类统计，场景：null，数据源：null，开始日期：2025-07-25，结束日期：2025-08-22，限制：20
2025-08-22 13:58:33.313 [http-nio-8080-exec-6] INFO  com.kefang.controller.QuestionAnalysisController - 获取问题关键词统计，场景：，数据源：，开始日期：2025-07-25，结束日期：2025-08-22，限制：100
2025-08-22 13:58:33.320 [http-nio-8080-exec-6] INFO  c.kefang.service.impl.QuestionAnalysisServiceImpl - 过滤后的有效关键词数: 4
2025-08-22 13:58:33.422 [Thread-4] INFO  com.kefang.websocket.WebSocketServer - 开始发送欢迎消息给用户: 283
2025-08-22 13:58:33.425 [Thread-4] INFO  com.kefang.websocket.WebSocketServer - 为欢迎消息设置会话ID: 457
2025-08-22 13:58:33.430 [Thread-4] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已保存到数据库，ID: 5763
2025-08-22 13:58:33.430 [Thread-4] INFO  com.kefang.websocket.WebSocketServer - 发送欢迎消息JSON: {"content":"您好，请问有什么可以帮您，可描述您想咨询的问题点击发送给我们。人工客服工作时间为09:00-21:00","from":2,"id":5763,"receiverId":283,"senderId":6,"sessionId":457,"timestamp":1755842313422,"type":1}
2025-08-22 13:58:33.430 [Thread-4] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已成功发送给用户: 283
2025-08-22 13:58:33.570 [http-nio-8080-exec-1] INFO  com.kefang.controller.QuestionAnalysisController - 获取问题趋势，时间单位：week，场景：，数据源：，开始日期：2025-07-25，结束日期：2025-08-22
2025-08-22 13:58:34.262 [http-nio-8080-exec-2] INFO  com.kefang.controller.FaqController - 查询所有常见问题
2025-08-22 13:58:39.153 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: 您好，很高兴为您服务 您好，很高兴为您服务
  发送者ID: 4 发送方: 客服 数据源: 【默认】
  接收方ID: 283  会话ID: 457 类型: 1
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:58:43.059 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: 你好
  发送者ID: 283 发送方: 用户 数据源: 【默认数据源】
  接收方ID: 4  会话ID: 457 类型: 1
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:58:43.067 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 发送消息给指定客服: 4
2025-08-22 13:58:53.159 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/upfile/2025/08-22/cc083c98767d4af4945fc47b52474458.webp
  发送者ID: 4 发送方: 客服 数据源: 【默认数据源】
  接收方ID: 283  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 13:59:02.705 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/upfile/2025/08-22/f3de42d860a749bb9d3f0143ff752ec3.mp4
  发送者ID: 4 发送方: 客服 数据源: 【默认数据源】
  接收方ID: 283  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 14:00:21.735 [http-nio-8080-exec-5] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/upfile/2025/08-22/e56c3730fe7f4c2ebe39e5699813cb62.jpg
  发送者ID: 4 发送方: 客服 数据源: 【默认数据源】
  接收方ID: 283  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 14:00:52.972 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/upfile/2025/08-22/f23ef14593f94ec6973be9044b936df4.mp4
  发送者ID: 4 发送方: 客服 数据源: 【默认数据源】
  接收方ID: 283  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 14:02:51.425 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 14:07:51.431 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 14:12:51.421 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 14:17:51.431 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 会话自动转换任务完成：已将 1 个最后活跃时间超过15分钟的人工会话转为AI会话, 更新了 1 条人工呼叫记录
2025-08-22 14:22:51.422 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 14:27:51.424 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 14:32:32.804 [http-nio-8080-exec-10] INFO  com.kefang.controller.FaqController - 查询所有常见问题
2025-08-22 14:32:34.179 [http-nio-8080-exec-2] INFO  com.kefang.controller.FaqController - 查询所有常见问题
2025-08-22 14:32:36.370 [http-nio-8080-exec-8] INFO  com.kefang.controller.FaqController - 查询所有常见问题
2025-08-22 14:32:39.891 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 283 有一用户连接关闭！
2025-08-22 14:32:39.891 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：1
2025-08-22 14:32:39.891 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-22 14:32:39.891 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 14:32:42.672 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: user, id: 283, sessionId: 4
2025-08-22 14:32:42.672 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 用户 283 的WebSocket连接已存储，连接ID: 4
2025-08-22 14:32:42.672 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：2
2025-08-22 14:32:42.672 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [283]
2025-08-22 14:32:42.672 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-22 14:32:43.678 [Thread-5] INFO  com.kefang.websocket.WebSocketServer - 开始发送欢迎消息给用户: 283
2025-08-22 14:32:43.679 [Thread-5] INFO  com.kefang.websocket.WebSocketServer - 为欢迎消息设置会话ID: 457
2025-08-22 14:32:43.683 [Thread-5] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已保存到数据库，ID: 5770
2025-08-22 14:32:43.683 [Thread-5] INFO  com.kefang.websocket.WebSocketServer - 发送欢迎消息JSON: {"content":"您好，请问有什么可以帮您，可描述您想咨询的问题点击发送给我们。人工客服工作时间为09:00-21:00","from":2,"id":5770,"receiverId":283,"senderId":6,"sessionId":457,"timestamp":1755844363678,"type":1}
2025-08-22 14:32:43.683 [Thread-5] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已成功发送给用户: 283
2025-08-22 14:32:46.988 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 尝试发送转接人工通知 - 会话ID: 457, 用户ID: 283, 客服ID: 4
2025-08-22 14:32:46.990 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 已通过WebSocket发送转接通知给用户: 283
2025-08-22 14:32:46.991 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 尝试发送转接通知给客服 - 会话ID: 457, 用户ID: 283, 客服ID: 4
2025-08-22 14:32:46.991 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 已通过WebSocket发送转接通知给客服: 4
2025-08-22 14:32:51.429 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 14:32:52.959 [http-nio-8080-exec-8] INFO  com.kefang.controller.FaqController - 查询所有常见问题
2025-08-22 14:32:59.290 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 尝试发送转接人工通知 - 会话ID: 457, 用户ID: 283, 客服ID: 4
2025-08-22 14:32:59.290 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 已通过WebSocket发送转接通知给用户: 283
2025-08-22 14:33:05.231 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/upfile/2025/08-22/7918c68a9f14495fb13bd208056cf777.png
  发送者ID: 4 发送方: 客服 数据源: 【默认数据源】
  接收方ID: 283  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 14:33:14.061 [http-nio-8080-exec-9] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/upfile/2025/08-22/617edb27e9894e48aa386fc3906cb422.mp4
  发送者ID: 4 发送方: 客服 数据源: 【默认数据源】
  接收方ID: 283  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 14:34:13.967 [http-nio-8080-exec-3] INFO  com.kefang.controller.FileUploadController - 开始上传文件：66ae0039ba7a7993d273eeaf903711e2.mp4, 大小：4475551 bytes
2025-08-22 14:34:14.601 [http-nio-8080-exec-3] INFO  com.kefang.utils.OssFileUploadUtil - 文件上传成功：原文件名=66ae0039ba7a7993d273eeaf903711e2.mp4, OSS路径=kefu/2025/08/22/17f26739d9aa47d5ba89bfd519a1b651_1755844453968.mp4, 访问URL=https://file.juranguanjia.com/kefu/2025/08/22/17f26739d9aa47d5ba89bfd519a1b651_1755844453968.mp4
2025-08-22 14:34:14.601 [http-nio-8080-exec-3] INFO  com.kefang.controller.FileUploadController - 文件上传成功：https://file.juranguanjia.com/kefu/2025/08/22/17f26739d9aa47d5ba89bfd519a1b651_1755844453968.mp4
2025-08-22 14:34:14.609 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/kefu/2025/08/22/17f26739d9aa47d5ba89bfd519a1b651_1755844453968.mp4
  发送者ID: 283 发送方: 用户 数据源: 【null】
  接收方ID: 4  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 14:34:14.621 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 发送消息给指定客服: 4
2025-08-22 14:35:26.091 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/upfile/2025/08-22/fd2b7aaa063f406aa48efa8e57e6cba3.mp4
  发送者ID: 4 发送方: 客服 数据源: 【默认数据源】
  接收方ID: 283  会话ID: 457 类型: 2
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 14:36:17.635 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: 您好，很高兴为您服务 您好，很高兴为您服务
  发送者ID: 4 发送方: 客服 数据源: 【默认数据源】
  接收方ID: 283  会话ID: 457 类型: 1
  在线状态： 在线人数：2  在线用户ID：[283] 在线客服ID：[4]
2025-08-22 14:37:51.436 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 14:42:51.424 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-22 14:47:51.423 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
