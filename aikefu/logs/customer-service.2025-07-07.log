2025-07-07 10:10:37.183 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-07 10:10:37.191 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 42520 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-07-07 10:10:37.192 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "dev"
2025-07-07 10:10:37.872 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.kefang.mapper.secondary.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-07 10:10:37.873 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.kefang.mapper.secondary]' package. Please check your configuration.
2025-07-07 10:10:38.177 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-07 10:10:38.185 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-07 10:10:38.185 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 10:10:38.185 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-07 10:10:38.271 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 10:10:38.271 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1049 ms
2025-07-07 10:10:38.745 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-07-07 10:10:38.783 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'secondarySqlSessionFactory' defined in class path resource [com/kefang/config/SecondaryMyBatisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'secondarySqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes\mapper\secondary\MessageMapper.xml]'
2025-07-07 10:10:38.783 [main] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-07-07 10:10:38.783 [main] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-07-07 10:10:38.786 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-07 10:10:38.796 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-07 10:10:38.809 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'secondarySqlSessionFactory' defined in class path resource [com/kefang/config/SecondaryMyBatisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'secondarySqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes\mapper\secondary\MessageMapper.xml]'
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:658)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:638)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.kefang.CustomerServiceApplication.main(CustomerServiceApplication.java:17)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'secondarySqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes\mapper\secondary\MessageMapper.xml]'
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 19 common frames omitted
Caused by: java.io.IOException: Failed to parse mapping resource: 'file [D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes\mapper\secondary\MessageMapper.xml]'
	at org.mybatis.spring.SqlSessionFactoryBean.buildSqlSessionFactory(SqlSessionFactoryBean.java:615)
	at org.mybatis.spring.SqlSessionFactoryBean.afterPropertiesSet(SqlSessionFactoryBean.java:492)
	at org.mybatis.spring.SqlSessionFactoryBean.getObject(SqlSessionFactoryBean.java:635)
	at com.kefang.config.SecondaryMyBatisConfig.secondarySqlSessionFactory(SecondaryMyBatisConfig.java:55)
	at com.kefang.config.SecondaryMyBatisConfig$$EnhancerBySpringCGLIB$$c1980340.CGLIB$secondarySqlSessionFactory$1(<generated>)
	at com.kefang.config.SecondaryMyBatisConfig$$EnhancerBySpringCGLIB$$c1980340$$FastClassBySpringCGLIB$$4a312c0b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.kefang.config.SecondaryMyBatisConfig$$EnhancerBySpringCGLIB$$c1980340.secondarySqlSessionFactory(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 20 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes\mapper\secondary\MessageMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving JdbcType. Cause: java.lang.IllegalArgumentException: No enum constant org.apache.ibatis.type.JdbcType.TEXT
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:123)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.parse(XMLMapperBuilder.java:95)
	at org.mybatis.spring.SqlSessionFactoryBean.buildSqlSessionFactory(SqlSessionFactoryBean.java:613)
	... 33 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error resolving JdbcType. Cause: java.lang.IllegalArgumentException: No enum constant org.apache.ibatis.type.JdbcType.TEXT
	at org.apache.ibatis.builder.BaseBuilder.resolveJdbcType(BaseBuilder.java:73)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.buildResultMappingFromContext(XMLMapperBuilder.java:392)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:280)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:254)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElements(XMLMapperBuilder.java:246)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:119)
	... 35 common frames omitted
Caused by: java.lang.IllegalArgumentException: No enum constant org.apache.ibatis.type.JdbcType.TEXT
	at java.base/java.lang.Enum.valueOf(Enum.java:273)
	at org.apache.ibatis.type.JdbcType.valueOf(JdbcType.java:25)
	at org.apache.ibatis.builder.BaseBuilder.resolveJdbcType(BaseBuilder.java:71)
	... 40 common frames omitted
2025-07-07 10:15:33.712 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-07 10:15:33.722 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 21200 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-07-07 10:15:33.722 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "dev"
2025-07-07 10:15:34.570 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-07 10:15:34.579 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-07 10:15:34.579 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 10:15:34.579 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-07 10:15:34.661 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 10:15:34.662 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 902 ms
2025-07-07 10:15:34.969 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-07 10:15:34.974 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-07-07 10:15:34.984 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-07 10:15:35.017 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-07 10:15:35.019 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-07-07 10:15:35.022 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-07 10:15:35.024 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-07 10:15:35.025 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-07 10:15:35.145 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-07-07 10:15:35.527 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-07 10:15:35.563 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-07 10:15:35.577 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-07 10:15:35.590 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.232 seconds (JVM running for 2.755)
2025-07-07 10:15:35.592 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 10:15:35.696 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 10:15:35.719 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 10:16:47.189 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-07-07 10:16:47.190 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-07-07 10:16:47.191 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 10:16:47.195 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 10:22:56.887 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-07 10:22:56.895 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 51120 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-07-07 10:22:56.895 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "dev"
2025-07-07 10:22:57.782 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-07 10:22:57.787 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-07 10:22:57.789 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 10:22:57.789 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-07 10:22:57.877 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 10:22:57.877 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 949 ms
2025-07-07 10:22:58.180 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-07 10:22:58.186 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-07-07 10:22:58.194 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-07 10:22:58.227 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-07 10:22:58.229 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-07-07 10:22:58.233 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-07 10:22:58.235 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-07 10:22:58.235 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-07 10:22:58.334 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-07-07 10:22:58.705 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-07 10:22:58.738 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-07 10:22:58.752 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-07 10:22:58.765 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.214 seconds (JVM running for 2.73)
2025-07-07 10:22:58.766 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 10:22:58.866 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 10:22:58.890 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 10:24:41.374 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 10:24:41.375 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-07 10:24:41.376 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-07 10:25:16.766 [http-nio-8080-exec-2] INFO  com.kefang.controller.EvaluationController - 请求控制传送带，参数: ConveyorControlEntity(city=null, warehouse_name=null, orderId=JD1234567890, weight=8.0, timestamp=2025-06-17 12:45:27, conveyorId=设备编号, remark=null, operator=null, deviceId=null)
2025-07-07 10:25:16.771 [http-nio-8080-exec-2] INFO  com.kefang.controller.EvaluationController - 开始查询订单信息，物流单号: JD1234567890, 查询类型: express
2025-07-07 10:25:16.771 [http-nio-8080-exec-2] INFO  com.kefang.api.ReAppOrder - 第三方订单号Token不存在或已过期，重新获取token
2025-07-07 10:25:17.268 [http-nio-8080-exec-2] INFO  com.kefang.api.ReAppOrder - 第三方订单号获取新token成功: 52e07e2b0f8a4e3160727801a863a8d5, 过期时间: 1751858717268
2025-07-07 10:25:17.372 [http-nio-8080-exec-2] INFO  com.kefang.api.ReAppOrder - {"total":0,"in_page":2,"page_no":1,"page_max":1,"list":[],"page_size":10}
2025-07-07 10:25:17.372 [http-nio-8080-exec-2] ERROR com.kefang.api.ReAppOrder - 未找到订单号信息: JD1234567890
2025-07-07 10:25:17.372 [http-nio-8080-exec-2] WARN  com.kefang.controller.EvaluationController - 订单信息查询失败: 未找到订单信息
2025-07-07 10:25:17.372 [http-nio-8080-exec-2] INFO  com.kefang.controller.EvaluationController - 传送带控制结果: 传送带向右滚动
2025-07-07 10:27:58.774 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 10:28:54.390 [http-nio-8080-exec-9] INFO  com.kefang.controller.EvaluationController - 请求控制传送带，参数: ConveyorControlEntity(city=null, warehouse_name=null, orderId=JDVE07532536896, weight=8.0, timestamp=2025-06-17 12:45:27, conveyorId=设备编号, remark=null, operator=null, deviceId=null)
2025-07-07 10:28:54.390 [http-nio-8080-exec-9] INFO  com.kefang.controller.EvaluationController - 开始查询订单信息，物流单号: JDVE07532536896, 查询类型: express
2025-07-07 10:28:54.488 [http-nio-8080-exec-9] INFO  com.kefang.api.ReAppOrder - {"total":0,"in_page":2,"page_no":1,"page_max":1,"list":[],"page_size":10}
2025-07-07 10:28:54.489 [http-nio-8080-exec-9] ERROR com.kefang.api.ReAppOrder - 未找到订单号信息: JDVE07532536896
2025-07-07 10:28:54.489 [http-nio-8080-exec-9] WARN  com.kefang.controller.EvaluationController - 订单信息查询失败: 未找到订单信息
2025-07-07 10:28:54.489 [http-nio-8080-exec-9] INFO  com.kefang.controller.EvaluationController - 传送带控制结果: 传送带向右滚动
2025-07-07 10:29:19.427 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-07-07 10:29:19.427 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-07-07 10:29:19.430 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 10:29:19.438 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 10:29:53.688 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-07 10:29:53.692 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 43808 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-07-07 10:29:53.692 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "dev"
2025-07-07 10:29:54.572 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-07 10:29:54.577 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-07 10:29:54.579 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 10:29:54.579 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-07 10:29:54.668 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 10:29:54.668 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 934 ms
2025-07-07 10:29:54.841 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'hsAppOrder': Unsatisfied dependency expressed through field 'ssoLogin'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'ssoLogin' defined in file [D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes\com\kefang\api\SsoLogin.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ssoServiceImpl': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'sso.api.url' in value "${sso.api.url}"
2025-07-07 10:29:54.844 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-07 10:29:54.856 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-07 10:29:54.870 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'hsAppOrder': Unsatisfied dependency expressed through field 'ssoLogin'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'ssoLogin' defined in file [D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes\com\kefang\api\SsoLogin.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ssoServiceImpl': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'sso.api.url' in value "${sso.api.url}"
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:660)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.kefang.CustomerServiceApplication.main(CustomerServiceApplication.java:14)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'ssoLogin' defined in file [D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes\com\kefang\api\SsoLogin.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ssoServiceImpl': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'sso.api.url' in value "${sso.api.url}"
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:657)
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ssoServiceImpl': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'sso.api.url' in value "${sso.api.url}"
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:405)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 33 common frames omitted
Caused by: java.lang.IllegalArgumentException: Could not resolve placeholder 'sso.api.url' in value "${sso.api.url}"
	at org.springframework.util.PropertyPlaceholderHelper.parseStringValue(PropertyPlaceholderHelper.java:180)
	at org.springframework.util.PropertyPlaceholderHelper.replacePlaceholders(PropertyPlaceholderHelper.java:126)
	at org.springframework.core.env.AbstractPropertyResolver.doResolvePlaceholders(AbstractPropertyResolver.java:239)
	at org.springframework.core.env.AbstractPropertyResolver.resolveRequiredPlaceholders(AbstractPropertyResolver.java:210)
	at org.springframework.context.support.PropertySourcesPlaceholderConfigurer.lambda$processProperties$0(PropertySourcesPlaceholderConfigurer.java:191)
	at org.springframework.beans.factory.support.AbstractBeanFactory.resolveEmbeddedValue(AbstractBeanFactory.java:936)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1332)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:657)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	... 45 common frames omitted
2025-07-07 10:30:11.641 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-07 10:30:11.650 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 22364 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-07-07 10:30:11.650 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "dev"
2025-07-07 10:30:12.481 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-07 10:30:12.489 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-07 10:30:12.489 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 10:30:12.489 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-07 10:30:12.582 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 10:30:12.582 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 898 ms
2025-07-07 10:30:12.896 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-07 10:30:12.902 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-07-07 10:30:12.911 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-07 10:30:12.947 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-07 10:30:12.949 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-07-07 10:30:12.952 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-07 10:30:12.954 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-07 10:30:12.954 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-07 10:30:13.050 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-07-07 10:30:13.389 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-07 10:30:13.424 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-07 10:30:13.437 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-07 10:30:13.451 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.149 seconds (JVM running for 2.587)
2025-07-07 10:30:13.452 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 10:30:13.570 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 10:30:13.589 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 10:30:18.866 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 10:30:18.867 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-07 10:30:18.868 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-07-07 10:30:18.939 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 请求控制传送带，参数: ConveyorControlEntity(city=null, warehouse_name=null, orderId=JDX041137914551, weight=8.0, timestamp=2025-06-17 12:45:27, conveyorId=设备编号, remark=null, operator=null, deviceId=null)
2025-07-07 10:30:18.944 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 开始查询订单信息，物流单号: JDX041137914551, 查询类型: express
2025-07-07 10:30:18.944 [http-nio-8080-exec-1] INFO  com.kefang.api.ReAppOrder - 第三方订单号Token不存在或已过期，重新获取token
2025-07-07 10:30:19.346 [http-nio-8080-exec-1] INFO  com.kefang.api.ReAppOrder - 第三方订单号获取新token成功: ab340521fab190515b0a29763f281828, 过期时间: 1751859019346
2025-07-07 10:30:19.466 [http-nio-8080-exec-1] INFO  com.kefang.api.ReAppOrder - {"total":1,"in_page":2,"page_no":1,"page_max":1,"list":[{"area_name":"十里堡镇","new_item":"","code":"HS20250705224025740001970","source":48,"type":1,"area_id":110118,"vaild_time":"2025-07-07 10:28:46","city_name":"密云区","sale_order_code":"RE0125070500938551","price":"0.00","id":11332406,"express_number":"JDX041137914551","order_type":"回收订单","source_name":"小智换新 - 京东平台","yws_order_express":0,"address":"北京密云区十里堡镇十里堡镇程家庄村村西文明街78号","is_follow":0,"prov_id":110000,"create_time":"2025-07-05 22:40:26","status_name":"回收完成","mobile":"","yws_order_id":0,"sign_time":"2025-07-07 10:28:46","finish_time":"2025-07-07 10:28:46","recover_item":{"item_code":"002222","item_model":"无","item_cates":"电饭煲","item_price":"0.00","item_name":"其他品牌-电饭煲-无","goods_type":0,"item_brand":"其他品牌","info":"商品不存在"},"prov_name":"北京","phone":"18400785718,,2879#","name":"任立国","buy_price":"0.00","detail":"销售订单号:[324922554146] 期望上门时间:[2025-07-06 00:00:00] 品牌:[其他品牌] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-06 00:00:00","city_id":110100,"status":100}],"page_size":10}
2025-07-07 10:30:19.467 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 订单信息查询成功: {"area_name":"十里堡镇","new_item":"","code":"HS20250705224025740001970","source":48,"type":1,"area_id":110118,"vaild_time":"2025-07-07 10:28:46","city_name":"密云区","sale_order_code":"RE0125070500938551","price":"0.00","id":11332406,"express_number":"JDX041137914551","order_type":"回收订单","source_name":"小智换新 - 京东平台","yws_order_express":0,"address":"北京密云区十里堡镇十里堡镇程家庄村村西文明街78号","is_follow":0,"prov_id":110000,"create_time":"2025-07-05 22:40:26","status_name":"回收完成","mobile":"","yws_order_id":0,"sign_time":"2025-07-07 10:28:46","finish_time":"2025-07-07 10:28:46","re_code":"JDX041137914551","recover_item":{"item_code":"002222","item_model":"无","item_cates":"电饭煲","item_price":"0.00","item_name":"其他品牌-电饭煲-无","goods_type":0,"item_brand":"其他品牌","info":"商品不存在"},"prov_name":"北京","phone":"18400785718,,2879#","name":"任立国","buy_price":"0.00","detail":"销售订单号:[324922554146] 期望上门时间:[2025-07-06 00:00:00] 品牌:[其他品牌] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-06 00:00:00","city_id":110100,"status":100}
2025-07-07 10:30:19.467 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 传送带控制结果: 传送带向右滚动
2025-07-07 10:32:54.070 [http-nio-8080-exec-3] INFO  com.kefang.controller.EvaluationController - 请求控制传送带，参数: ConveyorControlEntity(city=null, warehouse_name=null, orderId=JDX041117968543, weight=8.0, timestamp=2025-06-17 12:45:27, conveyorId=设备编号, remark=null, operator=null, deviceId=null)
2025-07-07 10:32:54.070 [http-nio-8080-exec-3] INFO  com.kefang.controller.EvaluationController - 开始查询订单信息，物流单号: JDX041117968543, 查询类型: express
2025-07-07 10:32:54.207 [http-nio-8080-exec-3] INFO  com.kefang.api.ReAppOrder - {"total":1,"in_page":2,"page_no":1,"page_max":1,"list":[{"area_name":"霍营街道","new_item":"","code":"HS20250705112003351017861","source":48,"type":1,"area_id":110114,"city_name":"昌平区","sale_order_code":"RE0125070500254791","price":"10.00","id":11322410,"express_number":"JDX041117968543","order_type":"回收订单","source_name":"小智换新 - 京东平台","yws_order_express":0,"address":"北京昌平区霍营街道回龙观和谐家园一区10号楼一单元301","is_follow":0,"prov_id":110000,"create_time":"2025-07-05 11:20:03","status_name":"等待验机","mobile":"13261997570","yws_order_id":0,"sign_time":"2025-07-07 10:32:43","recover_item":{"item_code":"002222","item_model":"无","item_cates":"电烤箱","item_price":"10.00","item_name":"美的（MIDEA）-电烤箱-无","goods_type":0,"item_brand":"美的（MIDEA）","info":"商品不存在"},"prov_name":"北京","phone":"","name":"隗晨光","buy_price":"0.00","detail":"销售订单号:[324886556326] 期望上门时间:[] 品牌:[美的（Midea）] 工单备注:[] 用户备注:[]","city_id":110100,"status":20}],"page_size":10}
2025-07-07 10:32:54.208 [http-nio-8080-exec-3] INFO  com.kefang.controller.EvaluationController - 订单信息查询成功: {"area_name":"霍营街道","new_item":"","code":"HS20250705112003351017861","source":48,"type":1,"area_id":110114,"city_name":"昌平区","sale_order_code":"RE0125070500254791","price":"10.00","id":11322410,"express_number":"JDX041117968543","order_type":"回收订单","source_name":"小智换新 - 京东平台","yws_order_express":0,"address":"北京昌平区霍营街道回龙观和谐家园一区10号楼一单元301","is_follow":0,"prov_id":110000,"create_time":"2025-07-05 11:20:03","status_name":"等待验机","mobile":"13261997570","yws_order_id":0,"sign_time":"2025-07-07 10:32:43","re_code":"JDX041117968543","recover_item":{"item_code":"002222","item_model":"无","item_cates":"电烤箱","item_price":"10.00","item_name":"美的（MIDEA）-电烤箱-无","goods_type":0,"item_brand":"美的（MIDEA）","info":"商品不存在"},"prov_name":"北京","phone":"","name":"隗晨光","buy_price":"0.00","detail":"销售订单号:[324886556326] 期望上门时间:[] 品牌:[美的（Midea）] 工单备注:[] 用户备注:[]","city_id":110100,"status":20}
2025-07-07 10:32:54.208 [http-nio-8080-exec-3] INFO  com.kefang.controller.EvaluationController - 传送带控制结果: 传送带向右滚动
2025-07-07 10:35:13.462 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 10:38:43.865 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-07-07 10:38:43.865 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-07-07 10:38:43.866 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 10:38:43.870 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 10:38:46.828 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 31560 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-07-07 10:38:46.828 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-07 10:38:46.831 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "dev"
2025-07-07 10:38:47.667 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-07 10:38:47.674 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-07 10:38:47.674 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 10:38:47.674 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-07 10:38:47.752 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 10:38:47.753 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 891 ms
2025-07-07 10:38:48.068 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-07 10:38:48.073 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-07-07 10:38:48.083 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-07 10:38:48.121 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-07 10:38:48.123 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-07-07 10:38:48.126 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-07 10:38:48.129 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-07 10:38:48.129 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-07 10:38:48.229 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-07-07 10:38:48.572 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-07 10:38:48.602 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-07 10:38:48.618 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-07 10:38:48.632 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.137 seconds (JVM running for 2.619)
2025-07-07 10:38:48.632 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 10:38:48.743 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 10:38:48.764 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 10:38:54.749 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 10:38:54.749 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-07 10:38:54.750 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-07 10:38:54.813 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 请求控制传送带，参数: ConveyorControlEntity(city=null, warehouse_name=null, orderId=JDX041117968543, weight=8.0, timestamp=2025-06-17 12:45:27, conveyorId=设备编号, remark=null, operator=null, deviceId=null)
2025-07-07 10:38:54.819 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 开始查询订单信息，物流单号: JDX041117968543, 查询类型: express
2025-07-07 10:38:54.819 [http-nio-8080-exec-1] INFO  com.kefang.api.ReAppOrder - 第三方订单号Token不存在或已过期，重新获取token
2025-07-07 10:38:55.155 [http-nio-8080-exec-1] INFO  com.kefang.api.ReAppOrder - 第三方订单号获取新token成功: 7f3933164332302bff277a99bfc72eb4, 过期时间: 1751859535155
2025-07-07 10:38:55.324 [http-nio-8080-exec-1] INFO  com.kefang.api.ReAppOrder - {"total":1,"in_page":2,"page_no":1,"page_max":1,"list":[{"area_name":"霍营街道","new_item":"","code":"HS20250705112003351017861","source":48,"type":1,"area_id":110114,"city_name":"昌平区","sale_order_code":"RE0125070500254791","price":"10.00","id":11322410,"express_number":"JDX041117968543","order_type":"回收订单","source_name":"小智换新 - 京东平台","yws_order_express":0,"address":"北京昌平区霍营街道回龙观和谐家园一区10号楼一单元301","is_follow":0,"prov_id":110000,"create_time":"2025-07-05 11:20:03","status_name":"等待验机","mobile":"13261997570","yws_order_id":0,"sign_time":"2025-07-07 10:32:43","recover_item":{"item_code":"002222","item_model":"无","item_cates":"电烤箱","item_price":"10.00","item_name":"美的（MIDEA）-电烤箱-无","goods_type":0,"item_brand":"美的（MIDEA）","info":"商品不存在"},"prov_name":"北京","phone":"","name":"隗晨光","buy_price":"0.00","detail":"销售订单号:[324886556326] 期望上门时间:[] 品牌:[美的（Midea）] 工单备注:[] 用户备注:[]","city_id":110100,"status":20}],"page_size":10}
2025-07-07 10:38:55.325 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 订单信息查询成功: {"area_name":"霍营街道","new_item":"","code":"HS20250705112003351017861","source":48,"type":1,"area_id":110114,"city_name":"昌平区","sale_order_code":"RE0125070500254791","price":"10.00","id":11322410,"express_number":"JDX041117968543","order_type":"回收订单","source_name":"小智换新 - 京东平台","yws_order_express":0,"address":"北京昌平区霍营街道回龙观和谐家园一区10号楼一单元301","is_follow":0,"prov_id":110000,"create_time":"2025-07-05 11:20:03","status_name":"等待验机","mobile":"13261997570","yws_order_id":0,"sign_time":"2025-07-07 10:32:43","re_code":"JDX041117968543","recover_item":{"item_code":"002222","item_model":"无","item_cates":"电烤箱","item_price":"10.00","item_name":"美的（MIDEA）-电烤箱-无","goods_type":0,"item_brand":"美的（MIDEA）","info":"商品不存在"},"prov_name":"北京","phone":"","name":"隗晨光","buy_price":"0.00","detail":"销售订单号:[324886556326] 期望上门时间:[] 品牌:[美的（Midea）] 工单备注:[] 用户备注:[]","city_id":110100,"status":20}
2025-07-07 10:38:55.325 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 提取订单字段 - source: 48, price: 10.00
2025-07-07 10:38:55.325 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 业务规则判断：source(48) != 48 && price(10.0) > 5.0 = false，传送带方向向右
2025-07-07 10:38:55.325 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 传送带控制结果: 传送带向右滚动
2025-07-07 10:42:13.482 [http-nio-8080-exec-3] INFO  com.kefang.controller.EvaluationController - 请求控制传送带，参数: ConveyorControlEntity(city=null, warehouse_name=null, orderId=JDX041117968543, weight=8.0, timestamp=2025-06-17 12:45:27, conveyorId=设备编号, remark=null, operator=null, deviceId=null)
2025-07-07 10:42:13.483 [http-nio-8080-exec-3] INFO  com.kefang.controller.EvaluationController - 开始查询订单信息，物流单号: JDX041117968543, 查询类型: express
2025-07-07 10:42:13.700 [http-nio-8080-exec-3] INFO  com.kefang.api.ReAppOrder - {"total":1,"in_page":2,"page_no":1,"page_max":1,"list":[{"area_name":"霍营街道","new_item":"","code":"HS20250705112003351017861","source":48,"type":1,"area_id":110114,"city_name":"昌平区","sale_order_code":"RE0125070500254791","price":"10.00","id":11322410,"express_number":"JDX041117968543","order_type":"回收订单","source_name":"小智换新 - 京东平台","yws_order_express":0,"address":"北京昌平区霍营街道回龙观和谐家园一区10号楼一单元301","is_follow":0,"prov_id":110000,"create_time":"2025-07-05 11:20:03","status_name":"等待验机","mobile":"13261997570","yws_order_id":0,"sign_time":"2025-07-07 10:32:43","recover_item":{"item_code":"002222","item_model":"无","item_cates":"电烤箱","item_price":"10.00","item_name":"美的（MIDEA）-电烤箱-无","goods_type":0,"item_brand":"美的（MIDEA）","info":"商品不存在"},"prov_name":"北京","phone":"","name":"隗晨光","buy_price":"0.00","detail":"销售订单号:[324886556326] 期望上门时间:[] 品牌:[美的（Midea）] 工单备注:[] 用户备注:[]","city_id":110100,"status":20}],"page_size":10}
2025-07-07 10:42:13.700 [http-nio-8080-exec-3] INFO  com.kefang.controller.EvaluationController - 订单信息查询成功: {"area_name":"霍营街道","new_item":"","code":"HS20250705112003351017861","source":48,"type":1,"area_id":110114,"city_name":"昌平区","sale_order_code":"RE0125070500254791","price":"10.00","id":11322410,"express_number":"JDX041117968543","order_type":"回收订单","source_name":"小智换新 - 京东平台","yws_order_express":0,"address":"北京昌平区霍营街道回龙观和谐家园一区10号楼一单元301","is_follow":0,"prov_id":110000,"create_time":"2025-07-05 11:20:03","status_name":"等待验机","mobile":"13261997570","yws_order_id":0,"sign_time":"2025-07-07 10:32:43","re_code":"JDX041117968543","recover_item":{"item_code":"002222","item_model":"无","item_cates":"电烤箱","item_price":"10.00","item_name":"美的（MIDEA）-电烤箱-无","goods_type":0,"item_brand":"美的（MIDEA）","info":"商品不存在"},"prov_name":"北京","phone":"","name":"隗晨光","buy_price":"0.00","detail":"销售订单号:[324886556326] 期望上门时间:[] 品牌:[美的（Midea）] 工单备注:[] 用户备注:[]","city_id":110100,"status":20}
2025-07-07 10:42:13.701 [http-nio-8080-exec-3] INFO  com.kefang.controller.EvaluationController - 提取订单字段 - source: 48, price: 10.00
2025-07-07 10:42:13.701 [http-nio-8080-exec-3] INFO  com.kefang.controller.EvaluationController - 业务规则判断：source(48) != 48 && price(10.0) > 5.0 = false，传送带方向向右
2025-07-07 10:42:13.701 [http-nio-8080-exec-3] INFO  com.kefang.controller.EvaluationController - 传送带控制结果: 传送带向右滚动
2025-07-07 10:42:29.233 [http-nio-8080-exec-9] INFO  com.kefang.controller.EvaluationController - 请求控制传送带，参数: ConveyorControlEntity(city=null, warehouse_name=null, orderId=JDX041119652166, weight=8.0, timestamp=2025-06-17 12:45:27, conveyorId=设备编号, remark=null, operator=null, deviceId=null)
2025-07-07 10:42:29.234 [http-nio-8080-exec-9] INFO  com.kefang.controller.EvaluationController - 开始查询订单信息，物流单号: JDX041119652166, 查询类型: express
2025-07-07 10:42:29.340 [http-nio-8080-exec-9] INFO  com.kefang.api.ReAppOrder - {"total":1,"in_page":2,"page_no":1,"page_max":1,"list":[{"area_name":"北新桥街道","new_item":"","code":"HS20250705114502021062289","source":48,"type":1,"area_id":110101,"vaild_time":"2025-07-07 10:41:41","city_name":"东城区","sale_order_code":"RE0125070500281961","price":"0.00","id":11322832,"express_number":"JDX041119652166","order_type":"回收订单","source_name":"小智换新 - 京东平台","yws_order_express":0,"address":"北京东城区北新桥街道北门仓胡同7-11-501","is_follow":0,"prov_id":110000,"create_time":"2025-07-05 11:45:02","status_name":"回收完成","mobile":"13601088505","yws_order_id":0,"sign_time":"2025-07-07 10:41:41","finish_time":"2025-07-07 10:41:41","recover_item":{"item_code":"002222","item_model":"无","item_cates":"电饭煲","item_price":"0.00","item_name":"美的（MIDEA）-电饭煲-无","goods_type":0,"item_brand":"美的（MIDEA）","info":"商品不存在"},"prov_name":"北京","phone":"","name":"张云云","buy_price":"0.00","detail":"销售订单号:[324877435905] 期望上门时间:[] 品牌:[美的（Midea）] 工单备注:[] 用户备注:[]","city_id":110100,"status":100}],"page_size":10}
2025-07-07 10:42:29.341 [http-nio-8080-exec-9] INFO  com.kefang.controller.EvaluationController - 订单信息查询成功: {"area_name":"北新桥街道","new_item":"","code":"HS20250705114502021062289","source":48,"type":1,"area_id":110101,"vaild_time":"2025-07-07 10:41:41","city_name":"东城区","sale_order_code":"RE0125070500281961","price":"0.00","id":11322832,"express_number":"JDX041119652166","order_type":"回收订单","source_name":"小智换新 - 京东平台","yws_order_express":0,"address":"北京东城区北新桥街道北门仓胡同7-11-501","is_follow":0,"prov_id":110000,"create_time":"2025-07-05 11:45:02","status_name":"回收完成","mobile":"13601088505","yws_order_id":0,"sign_time":"2025-07-07 10:41:41","finish_time":"2025-07-07 10:41:41","re_code":"JDX041119652166","recover_item":{"item_code":"002222","item_model":"无","item_cates":"电饭煲","item_price":"0.00","item_name":"美的（MIDEA）-电饭煲-无","goods_type":0,"item_brand":"美的（MIDEA）","info":"商品不存在"},"prov_name":"北京","phone":"","name":"张云云","buy_price":"0.00","detail":"销售订单号:[324877435905] 期望上门时间:[] 品牌:[美的（Midea）] 工单备注:[] 用户备注:[]","city_id":110100,"status":100}
2025-07-07 10:42:29.341 [http-nio-8080-exec-9] INFO  com.kefang.controller.EvaluationController - 提取订单字段 - source: 48, price: 0.00
2025-07-07 10:42:29.341 [http-nio-8080-exec-9] INFO  com.kefang.controller.EvaluationController - 业务规则判断：source(48) != 48 && price(0.0) > 5.0 = false，传送带方向向右
2025-07-07 10:42:29.341 [http-nio-8080-exec-9] INFO  com.kefang.controller.EvaluationController - 传送带控制结果: 传送带向右滚动
2025-07-07 10:43:48.641 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 10:45:33.402 [http-nio-8080-exec-2] INFO  com.kefang.controller.EvaluationController - 请求控制传送带，参数: ConveyorControlEntity(city=null, warehouse_name=null, orderId=JDX041119652166, weight=8.0, timestamp=2025-06-17 12:45:27, conveyorId=设备编号, remark=null, operator=null, deviceId=null)
2025-07-07 10:45:33.402 [http-nio-8080-exec-2] INFO  com.kefang.controller.EvaluationController - 开始查询订单信息，物流单号: JDX041119652166, 查询类型: express
2025-07-07 10:45:33.536 [http-nio-8080-exec-2] INFO  com.kefang.api.ReAppOrder - {"total":1,"in_page":2,"page_no":1,"page_max":1,"list":[{"area_name":"北新桥街道","new_item":"","code":"HS20250705114502021062289","source":48,"type":1,"area_id":110101,"vaild_time":"2025-07-07 10:41:41","city_name":"东城区","sale_order_code":"RE0125070500281961","price":"0.00","id":11322832,"express_number":"JDX041119652166","order_type":"回收订单","source_name":"小智换新 - 京东平台","yws_order_express":0,"address":"北京东城区北新桥街道北门仓胡同7-11-501","is_follow":0,"prov_id":110000,"create_time":"2025-07-05 11:45:02","status_name":"回收完成","mobile":"13601088505","yws_order_id":0,"sign_time":"2025-07-07 10:41:41","finish_time":"2025-07-07 10:41:41","recover_item":{"item_code":"002222","item_model":"无","item_cates":"电饭煲","item_price":"0.00","item_name":"美的（MIDEA）-电饭煲-无","goods_type":0,"item_brand":"美的（MIDEA）","info":"商品不存在"},"prov_name":"北京","phone":"","name":"张云云","buy_price":"0.00","detail":"销售订单号:[324877435905] 期望上门时间:[] 品牌:[美的（Midea）] 工单备注:[] 用户备注:[]","city_id":110100,"status":100}],"page_size":10}
2025-07-07 10:45:33.536 [http-nio-8080-exec-2] INFO  com.kefang.controller.EvaluationController - 订单信息查询成功: {"area_name":"北新桥街道","new_item":"","code":"HS20250705114502021062289","source":48,"type":1,"area_id":110101,"vaild_time":"2025-07-07 10:41:41","city_name":"东城区","sale_order_code":"RE0125070500281961","price":"0.00","id":11322832,"express_number":"JDX041119652166","order_type":"回收订单","source_name":"小智换新 - 京东平台","yws_order_express":0,"address":"北京东城区北新桥街道北门仓胡同7-11-501","is_follow":0,"prov_id":110000,"create_time":"2025-07-05 11:45:02","status_name":"回收完成","mobile":"13601088505","yws_order_id":0,"sign_time":"2025-07-07 10:41:41","finish_time":"2025-07-07 10:41:41","re_code":"JDX041119652166","recover_item":{"item_code":"002222","item_model":"无","item_cates":"电饭煲","item_price":"0.00","item_name":"美的（MIDEA）-电饭煲-无","goods_type":0,"item_brand":"美的（MIDEA）","info":"商品不存在"},"prov_name":"北京","phone":"","name":"张云云","buy_price":"0.00","detail":"销售订单号:[324877435905] 期望上门时间:[] 品牌:[美的（Midea）] 工单备注:[] 用户备注:[]","city_id":110100,"status":100}
2025-07-07 10:45:33.537 [http-nio-8080-exec-2] INFO  com.kefang.controller.EvaluationController - 提取订单字段 - source: 48, price: 0.00
2025-07-07 10:45:33.537 [http-nio-8080-exec-2] INFO  com.kefang.controller.EvaluationController - 业务规则判断：source(48) != 48 && price(0.0) > 5.0 = false，传送带方向向右
2025-07-07 10:45:33.537 [http-nio-8080-exec-2] INFO  com.kefang.controller.EvaluationController - 传送带控制结果: 传送带向右滚动
2025-07-07 10:45:43.633 [http-nio-8080-exec-7] INFO  com.kefang.controller.EvaluationController - 请求控制传送带，参数: ConveyorControlEntity(city=null, warehouse_name=null, orderId=JDX041115489943, weight=8.0, timestamp=2025-06-17 12:45:27, conveyorId=设备编号, remark=null, operator=null, deviceId=null)
2025-07-07 10:45:43.633 [http-nio-8080-exec-7] INFO  com.kefang.controller.EvaluationController - 开始查询订单信息，物流单号: JDX041115489943, 查询类型: express
2025-07-07 10:45:43.745 [http-nio-8080-exec-7] INFO  com.kefang.api.ReAppOrder - {"total":1,"in_page":2,"page_no":1,"page_max":1,"list":[{"area_name":"朝阳区","new_item":"","code":"HS20250705095405153052660","source":65,"type":1,"area_id":110105,"vaild_time":"2025-07-07 10:40:11","city_name":"北京市","sale_order_code":"F20250705095057957076","price":"2.00","id":11320608,"express_number":"JDX041115489943","order_type":"回收订单","source_name":"小智回收 - 飞蚂蚁平台","yws_order_express":0,"address":"太阳宫火星园7号楼二单元2184（18层）","is_follow":0,"prov_id":110000,"create_time":"2025-07-05 09:54:05","status_name":"回收完成","mobile":"15901099203","yws_order_id":0,"sign_time":"2025-07-07 10:40:11","finish_time":"2025-07-07 10:40:11","recover_item":{"item_code":"002222","item_model":"无","item_cates":"电陶炉","item_price":"3.00","item_name":"其他品牌-电陶炉-无","goods_type":0,"item_brand":"其他品牌","info":"商品不存在"},"prov_name":"北京","phone":"","name":"沈女士","buy_price":"0.00","detail":"","city_id":110100,"status":100}],"page_size":10}
2025-07-07 10:45:43.746 [http-nio-8080-exec-7] INFO  com.kefang.controller.EvaluationController - 订单信息查询成功: {"area_name":"朝阳区","new_item":"","code":"HS20250705095405153052660","source":65,"type":1,"area_id":110105,"vaild_time":"2025-07-07 10:40:11","city_name":"北京市","sale_order_code":"F20250705095057957076","price":"2.00","id":11320608,"express_number":"JDX041115489943","order_type":"回收订单","source_name":"小智回收 - 飞蚂蚁平台","yws_order_express":0,"address":"太阳宫火星园7号楼二单元2184（18层）","is_follow":0,"prov_id":110000,"create_time":"2025-07-05 09:54:05","status_name":"回收完成","mobile":"15901099203","yws_order_id":0,"sign_time":"2025-07-07 10:40:11","finish_time":"2025-07-07 10:40:11","re_code":"JDX041115489943","recover_item":{"item_code":"002222","item_model":"无","item_cates":"电陶炉","item_price":"3.00","item_name":"其他品牌-电陶炉-无","goods_type":0,"item_brand":"其他品牌","info":"商品不存在"},"prov_name":"北京","phone":"","name":"沈女士","buy_price":"0.00","detail":"","city_id":110100,"status":100}
2025-07-07 10:45:43.746 [http-nio-8080-exec-7] INFO  com.kefang.controller.EvaluationController - 提取订单字段 - source: 65, price: 2.00
2025-07-07 10:45:43.746 [http-nio-8080-exec-7] INFO  com.kefang.controller.EvaluationController - 业务规则判断：source(65) != 48 && price(2.0) > 5.0 = false，传送带方向向右
2025-07-07 10:45:43.746 [http-nio-8080-exec-7] INFO  com.kefang.controller.EvaluationController - 传送带控制结果: 传送带向右滚动
2025-07-07 10:47:09.573 [http-nio-8080-exec-4] INFO  com.kefang.controller.EvaluationController - 请求控制传送带，参数: ConveyorControlEntity(city=null, warehouse_name=null, orderId=JDX041010225820, weight=8.0, timestamp=2025-06-17 12:45:27, conveyorId=设备编号, remark=null, operator=null, deviceId=null)
2025-07-07 10:47:09.573 [http-nio-8080-exec-4] INFO  com.kefang.controller.EvaluationController - 开始查询订单信息，物流单号: JDX041010225820, 查询类型: express
2025-07-07 10:47:09.698 [http-nio-8080-exec-4] INFO  com.kefang.api.ReAppOrder - {"total":1,"in_page":2,"page_no":1,"page_max":1,"list":[{"area_name":"香坊区","new_item":"","code":"HS20250701161527031219686","source":48,"type":1,"area_id":230110,"vaild_time":"2025-07-07 10:40:03","city_name":"哈尔滨市","sale_order_code":"RE0125070100356731","price":"0.00","id":11278263,"express_number":"JDX041010225820","order_type":"回收订单","source_name":"小智换新 - 京东平台","yws_order_express":0,"address":"黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402","is_follow":0,"prov_id":230000,"create_time":"2025-07-01 16:15:27","status_name":"回收完成","mobile":"","yws_order_id":0,"sign_time":"2025-07-07 10:40:03","finish_time":"2025-07-07 10:40:03","recover_item":{"item_code":"002222","item_model":"无","item_cates":"冷风扇","item_price":"0.00","item_name":"其他-冷风扇-无","goods_type":0,"item_brand":"其他","info":"商品不存在"},"prov_name":"黑龙江","phone":"18446426229,,8674#","name":"张","buy_price":"0.00","detail":"销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00","city_id":230100,"status":100}],"page_size":10}
2025-07-07 10:47:09.698 [http-nio-8080-exec-4] INFO  com.kefang.controller.EvaluationController - 订单信息查询成功: {"area_name":"香坊区","new_item":"","code":"HS20250701161527031219686","source":48,"type":1,"area_id":230110,"vaild_time":"2025-07-07 10:40:03","city_name":"哈尔滨市","sale_order_code":"RE0125070100356731","price":"0.00","id":11278263,"express_number":"JDX041010225820","order_type":"回收订单","source_name":"小智换新 - 京东平台","yws_order_express":0,"address":"黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402","is_follow":0,"prov_id":230000,"create_time":"2025-07-01 16:15:27","status_name":"回收完成","mobile":"","yws_order_id":0,"sign_time":"2025-07-07 10:40:03","finish_time":"2025-07-07 10:40:03","re_code":"JDX041010225820","recover_item":{"item_code":"002222","item_model":"无","item_cates":"冷风扇","item_price":"0.00","item_name":"其他-冷风扇-无","goods_type":0,"item_brand":"其他","info":"商品不存在"},"prov_name":"黑龙江","phone":"18446426229,,8674#","name":"张","buy_price":"0.00","detail":"销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00","city_id":230100,"status":100}
2025-07-07 10:47:09.698 [http-nio-8080-exec-4] INFO  com.kefang.controller.EvaluationController - 提取订单字段 - source: 48, price: 0.00
2025-07-07 10:47:09.699 [http-nio-8080-exec-4] INFO  com.kefang.controller.EvaluationController - 业务规则判断：source(48) != 48 && price(0.0) > 5.0 = false，传送带方向向右
2025-07-07 10:47:09.699 [http-nio-8080-exec-4] INFO  com.kefang.controller.EvaluationController - 传送带控制结果: 传送带向右滚动
2025-07-07 10:48:48.639 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 10:53:48.634 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 10:58:48.643 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 11:03:48.640 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 11:08:48.641 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 11:13:48.632 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 11:18:48.646 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 11:23:48.636 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 11:28:48.642 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 11:33:48.636 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 11:38:48.639 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 11:43:48.633 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 11:48:48.640 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 11:53:48.642 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 11:58:48.637 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 12:03:48.639 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 12:08:48.645 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 12:13:48.630 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 12:18:48.640 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 12:23:48.643 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 12:28:48.641 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 12:33:48.638 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 12:38:48.630 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 12:43:48.640 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 12:48:48.640 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 12:53:48.638 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 12:58:48.644 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 13:03:48.632 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 13:08:48.630 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 13:13:48.636 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 13:18:48.645 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 13:23:48.630 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 13:28:48.635 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 13:33:48.635 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 13:38:48.636 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 13:43:48.638 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 13:48:48.631 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 13:51:24.890 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-07-07 13:51:24.890 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-07-07 13:51:24.893 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 13:51:24.898 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 13:51:31.437 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-07 13:51:31.442 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 27116 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-07-07 13:51:31.442 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "dev"
2025-07-07 13:51:32.321 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-07 13:51:32.326 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-07 13:51:32.328 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 13:51:32.328 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-07 13:51:32.407 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 13:51:32.408 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 931 ms
2025-07-07 13:51:32.780 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-07 13:51:32.785 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-07-07 13:51:32.794 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-07 13:51:32.830 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-07 13:51:32.833 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-07-07 13:51:32.836 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-07 13:51:32.838 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-07 13:51:32.838 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-07 13:51:32.943 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-07-07 13:51:33.277 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-07 13:51:33.304 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-07 13:51:33.321 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-07 13:51:33.336 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.245 seconds (JVM running for 3.15)
2025-07-07 13:51:33.337 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 13:51:33.457 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 13:51:33.478 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 13:52:18.306 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 13:52:18.306 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-07 13:52:18.307 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-07 13:52:18.372 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 请求控制传送带，参数: ConveyorControlEntity(city=null, warehouse_name=null, orderId=JDX041010225820, weight=8.0, timestamp=2025-06-17 12:45:27, conveyorId=设备编号, remark=null, operator=null, deviceId=null)
2025-07-07 13:52:18.377 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 开始查询订单信息，物流单号: JDX041010225820, 查询类型: express
2025-07-07 13:52:18.377 [http-nio-8080-exec-1] INFO  com.kefang.api.ReAppOrder - 第三方订单号Token不存在或已过期，重新获取token
2025-07-07 13:52:18.808 [http-nio-8080-exec-1] INFO  com.kefang.api.ReAppOrder - 第三方订单号获取新token成功: 17fe93bf5aabdf3578fa1cafaebebcfb, 过期时间: 1751871138808
2025-07-07 13:52:18.928 [http-nio-8080-exec-1] INFO  com.kefang.api.ReAppOrder - {"total":1,"in_page":2,"page_no":1,"page_max":1,"list":[{"area_name":"香坊区","new_item":"","code":"HS20250701161527031219686","source":48,"type":1,"area_id":230110,"vaild_time":"2025-07-07 10:40:03","city_name":"哈尔滨市","sale_order_code":"RE0125070100356731","price":"0.00","id":11278263,"express_number":"JDX041010225820","order_type":"回收订单","source_name":"小智换新 - 京东平台","yws_order_express":0,"address":"黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402","is_follow":0,"prov_id":230000,"create_time":"2025-07-01 16:15:27","status_name":"回收完成","mobile":"","yws_order_id":0,"sign_time":"2025-07-07 10:40:03","finish_time":"2025-07-07 10:40:03","recover_item":{"item_code":"002222","item_model":"无","item_cates":"冷风扇","item_price":"0.00","item_name":"其他-冷风扇-无","goods_type":0,"item_brand":"其他","info":"商品不存在"},"prov_name":"黑龙江","phone":"18446426229,,8674#","name":"张","buy_price":"0.00","detail":"销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00","city_id":230100,"status":100}],"page_size":10}
2025-07-07 13:52:18.929 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 订单信息查询成功: {"area_name":"香坊区","new_item":"","code":"HS20250701161527031219686","source":48,"type":1,"area_id":230110,"vaild_time":"2025-07-07 10:40:03","city_name":"哈尔滨市","sale_order_code":"RE0125070100356731","price":"0.00","id":11278263,"express_number":"JDX041010225820","order_type":"回收订单","source_name":"小智换新 - 京东平台","yws_order_express":0,"address":"黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402","is_follow":0,"prov_id":230000,"create_time":"2025-07-01 16:15:27","status_name":"回收完成","mobile":"","yws_order_id":0,"sign_time":"2025-07-07 10:40:03","finish_time":"2025-07-07 10:40:03","re_code":"JDX041010225820","recover_item":{"item_code":"002222","item_model":"无","item_cates":"冷风扇","item_price":"0.00","item_name":"其他-冷风扇-无","goods_type":0,"item_brand":"其他","info":"商品不存在"},"prov_name":"黑龙江","phone":"18446426229,,8674#","name":"张","buy_price":"0.00","detail":"销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00","city_id":230100,"status":100}
2025-07-07 13:52:18.929 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 提取订单字段 - source: 48, price: 0.00
2025-07-07 13:52:18.929 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 业务规则判断：source(48) != 48 && price(0.0) > 5.0 = false，传送带方向向右
2025-07-07 13:52:18.929 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 传送带控制结果: 传送带向右滚动
2025-07-07 13:53:14.230 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-07-07 13:53:14.230 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-07-07 13:53:14.231 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 13:53:14.236 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 13:53:17.403 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-07 13:53:17.408 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 18428 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-07-07 13:53:17.409 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "prod"
2025-07-07 13:53:18.264 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8819 (http)
2025-07-07 13:53:18.271 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8819"]
2025-07-07 13:53:18.271 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 13:53:18.271 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-07 13:53:18.354 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 13:53:18.354 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 913 ms
2025-07-07 13:53:18.675 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-07 13:53:18.682 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-07-07 13:53:18.690 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-07 13:53:18.724 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-07 13:53:18.728 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-07-07 13:53:18.732 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-07 13:53:18.735 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-07 13:53:18.735 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-07 13:53:18.832 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-07-07 13:53:19.181 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-07 13:53:19.205 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8819"]
2025-07-07 13:53:19.220 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8819 (http) with context path ''
2025-07-07 13:53:19.232 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.182 seconds (JVM running for 2.728)
2025-07-07 13:53:19.234 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 13:53:22.161 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-07-07 13:53:22.162 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-07-07 13:53:34.915 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-07 13:53:34.920 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 40964 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-07-07 13:53:34.920 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "dev"
2025-07-07 13:53:35.757 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-07 13:53:35.763 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-07 13:53:35.763 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 13:53:35.763 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-07 13:53:35.837 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 13:53:35.837 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 883 ms
2025-07-07 13:53:36.167 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-07 13:53:36.173 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-07-07 13:53:36.182 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-07 13:53:36.216 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-07 13:53:36.218 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-07-07 13:53:36.223 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-07 13:53:36.225 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-07 13:53:36.225 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-07 13:53:36.327 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-07-07 13:53:36.651 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-07 13:53:36.680 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-07 13:53:36.694 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-07 13:53:36.709 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.111 seconds (JVM running for 2.631)
2025-07-07 13:53:36.709 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 13:53:36.818 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 13:53:36.838 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 13:53:41.409 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 13:53:41.409 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-07 13:53:41.411 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-07 13:53:41.481 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 请求控制传送带，参数: ConveyorControlEntity(city=null, warehouse_name=null, orderId=JDX041010225820, weight=8.0, timestamp=2025-06-17 12:45:27, conveyorId=设备编号, remark=null, operator=null, deviceId=null)
2025-07-07 13:53:41.486 [http-nio-8080-exec-1] INFO  com.kefang.api.ReAppOrder - 第三方订单号Token不存在或已过期，重新获取token
2025-07-07 13:53:41.848 [http-nio-8080-exec-1] INFO  com.kefang.api.ReAppOrder - 第三方订单号获取新token成功: 0808d84b7787d9c20b09449a8eb30926, 过期时间: 1751871221848
2025-07-07 13:53:41.970 [http-nio-8080-exec-1] INFO  com.kefang.api.ReAppOrder - {"total":1,"in_page":2,"page_no":1,"page_max":1,"list":[{"area_name":"香坊区","new_item":"","code":"HS20250701161527031219686","source":48,"type":1,"area_id":230110,"vaild_time":"2025-07-07 10:40:03","city_name":"哈尔滨市","sale_order_code":"RE0125070100356731","price":"0.00","id":11278263,"express_number":"JDX041010225820","order_type":"回收订单","source_name":"小智换新 - 京东平台","yws_order_express":0,"address":"黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402","is_follow":0,"prov_id":230000,"create_time":"2025-07-01 16:15:27","status_name":"回收完成","mobile":"","yws_order_id":0,"sign_time":"2025-07-07 10:40:03","finish_time":"2025-07-07 10:40:03","recover_item":{"item_code":"002222","item_model":"无","item_cates":"冷风扇","item_price":"0.00","item_name":"其他-冷风扇-无","goods_type":0,"item_brand":"其他","info":"商品不存在"},"prov_name":"黑龙江","phone":"18446426229,,8674#","name":"张","buy_price":"0.00","detail":"销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00","city_id":230100,"status":100}],"page_size":10}
2025-07-07 13:53:41.970 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 提取订单字段 - source: 48, price: 0.00
2025-07-07 13:53:41.970 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 业务规则判断：source(48) != 48 && price(0.0) > 5.0 = false，传送带方向向右
2025-07-07 13:53:41.970 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 传送带控制结果: 传送带向右滚动
2025-07-07 13:56:30.818 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-07-07 13:56:30.818 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-07-07 13:56:30.819 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 13:56:30.825 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 13:56:33.812 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-07 13:56:33.819 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 18428 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-07-07 13:56:33.820 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "dev"
2025-07-07 13:56:34.736 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-07 13:56:34.743 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-07 13:56:34.743 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 13:56:34.743 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-07 13:56:34.820 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 13:56:34.820 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 964 ms
2025-07-07 13:56:35.131 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-07 13:56:35.137 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-07-07 13:56:35.147 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-07 13:56:35.181 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-07 13:56:35.183 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-07-07 13:56:35.187 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-07 13:56:35.189 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-07 13:56:35.189 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-07 13:56:35.288 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-07-07 13:56:35.612 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-07 13:56:35.640 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-07 13:56:35.659 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-07 13:56:35.674 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.22 seconds (JVM running for 2.706)
2025-07-07 13:56:35.677 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 13:56:35.786 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 13:56:35.805 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-07-07 13:56:38.204 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 13:56:38.205 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-07 13:56:38.206 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-07 13:56:38.271 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 请求控制传送带，参数: ConveyorControlEntity(city=null, warehouse_name=null, orderId=JDX041010225820, weight=8.0, timestamp=2025-06-17 12:45:27, conveyorId=设备编号, remark=null, operator=null, deviceId=null)
2025-07-07 13:56:38.276 [http-nio-8080-exec-1] INFO  com.kefang.api.ReAppOrder - 第三方订单号Token不存在或已过期，重新获取token
2025-07-07 13:56:38.648 [http-nio-8080-exec-1] INFO  com.kefang.api.ReAppOrder - 第三方订单号获取新token成功: 3a5f57e477a9bced73dad1a1eca3c09b, 过期时间: 1751871398648
2025-07-07 13:56:38.760 [http-nio-8080-exec-1] INFO  com.kefang.api.ReAppOrder - {"total":1,"in_page":2,"page_no":1,"page_max":1,"list":[{"area_name":"香坊区","new_item":"","code":"HS20250701161527031219686","source":48,"type":1,"area_id":230110,"vaild_time":"2025-07-07 10:40:03","city_name":"哈尔滨市","sale_order_code":"RE0125070100356731","price":"0.00","id":11278263,"express_number":"JDX041010225820","order_type":"回收订单","source_name":"小智换新 - 京东平台","yws_order_express":0,"address":"黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402","is_follow":0,"prov_id":230000,"create_time":"2025-07-01 16:15:27","status_name":"回收完成","mobile":"","yws_order_id":0,"sign_time":"2025-07-07 10:40:03","finish_time":"2025-07-07 10:40:03","recover_item":{"item_code":"002222","item_model":"无","item_cates":"冷风扇","item_price":"0.00","item_name":"其他-冷风扇-无","goods_type":0,"item_brand":"其他","info":"商品不存在"},"prov_name":"黑龙江","phone":"18446426229,,8674#","name":"张","buy_price":"0.00","detail":"销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00","city_id":230100,"status":100}],"page_size":10}
2025-07-07 13:56:38.760 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 提取订单字段 - source: 48, price: 0.00
2025-07-07 13:56:38.760 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 业务规则判断：source(48) != 48 && price(0.0) > 5.0 = false，传送带方向向右
2025-07-07 13:56:38.760 [http-nio-8080-exec-1] INFO  com.kefang.controller.EvaluationController - 传送带控制结果: message: 传送带向右滚动 - data : 1
2025-07-07 14:00:50.018 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-07-07 14:00:50.019 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-07-07 14:00:50.020 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 14:00:50.023 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 14:00:52.893 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-07 14:00:52.896 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 50008 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-07-07 14:00:52.897 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "prod"
2025-07-07 14:00:53.719 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8819 (http)
2025-07-07 14:00:53.725 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8819"]
2025-07-07 14:00:53.725 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 14:00:53.725 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-07-07 14:00:53.808 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 14:00:53.808 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 880 ms
2025-07-07 14:00:54.176 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-07 14:00:54.182 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-07-07 14:00:54.191 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-07 14:00:54.223 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-07 14:00:54.226 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-07-07 14:00:54.229 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-07 14:00:54.231 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-07 14:00:54.231 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-07 14:00:54.348 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-07-07 14:00:54.693 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-07 14:00:54.720 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8819"]
2025-07-07 14:00:54.734 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8819 (http) with context path ''
2025-07-07 14:00:54.748 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.185 seconds (JVM running for 2.658)
2025-07-07 14:00:54.749 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 14:01:16.943 [scheduling-1] ERROR com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:265)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:400)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:373)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:595)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:382)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.kefang.job.AutoSessionTransferTask$$EnhancerBySpringCGLIB$$b168bc70.transferInactiveSessionsToAI(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 33 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 36 common frames omitted
2025-07-07 14:01:16.944 [scheduling-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.transaction.CannotCreateTransactionException: Could not open JDBC Connection for transaction; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:309)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:400)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:373)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:595)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:382)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.kefang.job.AutoSessionTransferTask$$EnhancerBySpringCGLIB$$b168bc70.transferInactiveSessionsToAI(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:265)
	... 22 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 33 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 36 common frames omitted
2025-07-07 14:01:17.314 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-07-07 14:01:17.314 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
