2025-08-19 15:14:48.100 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-19 15:14:48.108 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 43588 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-08-19 15:14:48.108 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "test"
2025-08-19 15:14:48.984 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8812 (http)
2025-08-19 15:14:48.992 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8812"]
2025-08-19 15:14:48.992 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-19 15:14:48.992 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-08-19 15:14:49.071 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-19 15:14:49.071 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 928 ms
2025-08-19 15:14:49.458 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-19 15:14:49.466 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-19 15:14:49.487 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-19 15:14:49.533 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-19 15:14:49.538 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-19 15:14:49.545 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-08-19 15:14:49.548 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-08-19 15:14:49.550 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-19 15:14:49.690 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-08-19 15:14:50.118 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-19 15:14:50.149 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8812"]
2025-08-19 15:14:50.167 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8812 (http) with context path ''
2025-08-19 15:14:50.213 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.481 seconds (JVM running for 3.085)
2025-08-19 15:14:50.214 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-19 15:14:53.807 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-08-19 15:14:53.807 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
