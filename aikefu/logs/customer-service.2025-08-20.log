2025-08-20 16:16:27.890 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-20 16:16:27.895 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 50428 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-08-20 16:16:27.896 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "prod"
2025-08-20 16:16:28.929 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8819 (http)
2025-08-20 16:16:28.936 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8819"]
2025-08-20 16:16:28.937 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-20 16:16:28.938 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-08-20 16:16:29.049 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-20 16:16:29.049 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1117 ms
2025-08-20 16:16:29.506 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-20 16:16:29.513 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-20 16:16:29.522 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-20 16:16:29.563 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-20 16:16:29.565 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-20 16:16:29.568 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-08-20 16:16:29.570 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-08-20 16:16:29.570 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-20 16:16:29.703 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-08-20 16:16:30.078 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-20 16:16:30.109 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8819"]
2025-08-20 16:16:30.124 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8819 (http) with context path ''
2025-08-20 16:16:30.138 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.641 seconds (JVM running for 3.61)
2025-08-20 16:16:30.139 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-20 16:16:33.463 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-08-20 16:16:33.463 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-08-20 16:16:48.074 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-20 16:16:48.082 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 46648 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-08-20 16:16:48.082 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "dev"
2025-08-20 16:16:48.962 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-20 16:16:48.970 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-20 16:16:48.970 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-20 16:16:48.970 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-08-20 16:16:49.052 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-20 16:16:49.053 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 935 ms
2025-08-20 16:16:49.414 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-20 16:16:49.420 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-20 16:16:49.435 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-20 16:16:49.479 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-20 16:16:49.481 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-20 16:16:49.484 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-08-20 16:16:49.487 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-08-20 16:16:49.487 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-20 16:16:49.591 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-08-20 16:16:49.920 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-20 16:16:49.947 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-20 16:16:49.962 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-20 16:16:49.976 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.302 seconds (JVM running for 2.897)
2025-08-20 16:16:49.976 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-20 16:16:50.088 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-20 16:16:50.128 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 16:16:57.702 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-20 16:16:57.703 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-20 16:16:57.703 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-08-20 16:16:57.797 [http-nio-8080-exec-1] INFO  com.kefang.controller.AgentController - SSO API Token 未缓存，正在获取...
2025-08-20 16:16:57.800 [http-nio-8080-exec-1] INFO  com.kefang.controller.AgentController - SSO API Token 刷新成功
2025-08-20 16:16:57.800 [http-nio-8080-exec-1] INFO  com.kefang.controller.AgentController - SSO API Token 缓存中已存在，正在返回...
2025-08-20 16:16:58.349 [http-nio-8080-exec-1] INFO  com.kefang.controller.AgentController - SSO用户登录成功，响应数据: SsoUserLoginDTO.Response(code=202, data=null, msg=登录帐号必须是手机号码)
2025-08-20 16:16:59.938 [http-nio-8080-exec-7] INFO  com.kefang.controller.QuestionAnalysisController - 获取问题关键词统计，场景：null，数据源：null，开始日期：2025-08-14，结束日期：2025-08-20，限制：100
2025-08-20 16:16:59.972 [http-nio-8080-exec-7] INFO  c.kefang.service.impl.QuestionAnalysisServiceImpl - 过滤后的有效关键词数: 0
2025-08-20 16:17:05.452 [http-nio-8080-exec-10] INFO  com.kefang.controller.FaqController - 查询所有常见问题
2025-08-20 16:18:09.765 [http-nio-8080-exec-7] INFO  com.kefang.controller.FaqController - 查询所有常见问题
2025-08-20 16:20:12.211 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: agent, id: 4, sessionId: 0
2025-08-20 16:20:12.212 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 客服 4 的WebSocket连接已存储，连接ID: 0
2025-08-20 16:20:12.212 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：1
2025-08-20 16:20:12.212 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-20 16:20:12.212 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 16:21:49.977 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 16:21:51.470 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: user, id: 282, sessionId: 1
2025-08-20 16:21:51.470 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 用户 282 的WebSocket连接已存储，连接ID: 1
2025-08-20 16:21:51.470 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：2
2025-08-20 16:21:51.471 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [282]
2025-08-20 16:21:51.471 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 16:21:52.475 [Thread-3] INFO  com.kefang.websocket.WebSocketServer - 开始发送欢迎消息给用户: 282
2025-08-20 16:21:52.477 [Thread-3] INFO  com.kefang.websocket.WebSocketServer - 为欢迎消息设置会话ID: 456
2025-08-20 16:21:52.481 [Thread-3] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已保存到数据库，ID: 5626
2025-08-20 16:21:52.481 [Thread-3] INFO  com.kefang.websocket.WebSocketServer - 发送欢迎消息JSON: {"content":"您好，请问有什么可以帮您，可描述您想咨询的问题点击发送给我们。人工客服工作时间为09:00-21:00","from":2,"id":5626,"receiverId":282,"senderId":6,"sessionId":456,"timestamp":1755678112475,"type":1}
2025-08-20 16:21:52.481 [Thread-3] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已成功发送给用户: 282
2025-08-20 16:22:02.903 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: 你好
  发送者ID: 282 发送方: 用户 数据源: 【默认数据源】
  接收方ID: 6  会话ID: 456 类型: 1
  在线状态： 在线人数：2  在线用户ID：[282] 在线客服ID：[4]
2025-08-20 16:22:02.909 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 用户发送给AI客服的消息，跳过WebSocket的AI回复处理
2025-08-20 16:22:13.962 [http-nio-8080-exec-5] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/upfile/2025/08-20/035da4433a424fadb1830968e839b0fa.png
  发送者ID: 282 发送方: 用户 数据源: 【null】
  接收方ID: 6  会话ID: 456 类型: 2
  在线状态： 在线人数：2  在线用户ID：[282] 在线客服ID：[4]
2025-08-20 16:22:13.967 [http-nio-8080-exec-5] WARN  com.kefang.websocket.WebSocketServer - 指定的客服(ID:6)不在线，消息无法发送
2025-08-20 16:22:13.967 [http-nio-8080-exec-5] INFO  com.kefang.websocket.WebSocketServer - 客服不在线，消息已保存但未转发
2025-08-20 16:22:22.516 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 尝试发送转接人工通知 - 会话ID: 456, 用户ID: 282, 客服ID: 4
2025-08-20 16:22:22.517 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 已通过WebSocket发送转接通知给用户: 282
2025-08-20 16:22:22.517 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 尝试发送转接通知给客服 - 会话ID: 456, 用户ID: 282, 客服ID: 4
2025-08-20 16:22:22.517 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 已通过WebSocket发送转接通知给客服: 4
2025-08-20 16:22:27.157 [http-nio-8080-exec-1] INFO  com.kefang.controller.FaqController - 查询所有常见问题
2025-08-20 16:22:32.176 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 尝试发送转接人工通知 - 会话ID: 456, 用户ID: 282, 客服ID: 4
2025-08-20 16:22:32.177 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 已通过WebSocket发送转接通知给用户: 282
2025-08-20 16:22:37.552 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: 您好，很高兴为您服务 您好，很高兴为您服务
  发送者ID: 4 发送方: 客服 数据源: 【默认】
  接收方ID: 282  会话ID: 456 类型: 1
  在线状态： 在线人数：2  在线用户ID：[282] 在线客服ID：[4]
2025-08-20 16:22:47.390 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: 你好
  发送者ID: 282 发送方: 用户 数据源: 【默认数据源】
  接收方ID: 4  会话ID: 456 类型: 1
  在线状态： 在线人数：2  在线用户ID：[282] 在线客服ID：[4]
2025-08-20 16:22:47.399 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 发送消息给指定客服: 4
2025-08-20 16:23:35.879 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/upfile/2025/08-20/2c8c846a991c460398e76130734b6c9c.png
  发送者ID: 282 发送方: 用户 数据源: 【null】
  接收方ID: 4  会话ID: 456 类型: 2
  在线状态： 在线人数：2  在线用户ID：[282] 在线客服ID：[4]
2025-08-20 16:23:35.887 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 发送消息给指定客服: 4
2025-08-20 16:26:49.981 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 16:31:49.976 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 16:36:49.983 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 16:41:49.982 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 会话自动转换任务完成：已将 1 个最后活跃时间超过15分钟的人工会话转为AI会话, 更新了 1 条人工呼叫记录
2025-08-20 16:46:49.981 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 16:51:49.979 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 16:56:49.983 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 17:01:49.983 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 17:02:53.965 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 282 有一用户连接关闭！
2025-08-20 17:02:53.965 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：1
2025-08-20 17:02:53.965 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-20 17:02:53.965 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 17:02:53.965 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 4 有一客服连接关闭！
2025-08-20 17:02:53.970 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：0
2025-08-20 17:02:53.970 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-20 17:02:53.970 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-20 17:02:53.979 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-08-20 17:02:53.980 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-08-20 17:02:53.981 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-20 17:02:53.984 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-20 17:03:01.808 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-20 17:03:01.816 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 41556 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-08-20 17:03:01.817 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "dev"
2025-08-20 17:03:02.981 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-20 17:03:02.992 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-20 17:03:02.993 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-20 17:03:02.993 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-08-20 17:03:03.107 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-20 17:03:03.107 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1249 ms
2025-08-20 17:03:03.562 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-20 17:03:03.569 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-20 17:03:03.581 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-20 17:03:03.622 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-20 17:03:03.624 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-20 17:03:03.627 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-08-20 17:03:03.629 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-08-20 17:03:03.629 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-20 17:03:03.743 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-08-20 17:03:03.761 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'fileUploadController': Unsatisfied dependency expressed through field 'ossFileUploadUtil'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'ossFileUploadUtil': Unsatisfied dependency expressed through field 'ossClient'; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.aliyun.oss.OSS' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
2025-08-20 17:03:03.762 [main] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-08-20 17:03:03.762 [main] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-08-20 17:03:03.764 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-20 17:03:03.775 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-20 17:03:03.791 [main] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Field ossClient in com.kefang.utils.OssFileUploadUtil required a bean of type 'com.aliyun.oss.OSS' that could not be found.

The injection point has the following annotations:
	- @org.springframework.beans.factory.annotation.Autowired(required=true)


Action:

Consider defining a bean of type 'com.aliyun.oss.OSS' in your configuration.

2025-08-20 17:10:47.094 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-20 17:10:47.103 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 48324 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-08-20 17:10:47.103 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "dev"
2025-08-20 17:10:48.077 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-20 17:10:48.091 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-20 17:10:48.091 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-20 17:10:48.091 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-08-20 17:10:48.186 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-20 17:10:48.186 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1048 ms
2025-08-20 17:10:48.602 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-20 17:10:48.607 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-20 17:10:48.618 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-20 17:10:48.659 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-20 17:10:48.664 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-20 17:10:48.671 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-08-20 17:10:48.675 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-08-20 17:10:48.675 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-20 17:10:48.807 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-08-20 17:10:48.830 [main] INFO  com.kefang.config.OssConfig - 正在初始化OSS客户端...
2025-08-20 17:10:48.831 [main] INFO  com.kefang.config.OssConfig - OSS配置信息 - endpoint: http://oss-cn-beijing.aliyuncs.com, bucketName: juranfile
2025-08-20 17:10:48.990 [main] INFO  com.kefang.config.OssConfig - OSS客户端初始化成功
2025-08-20 17:10:49.452 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-20 17:10:49.490 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-20 17:10:49.505 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-20 17:10:49.523 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-20 17:10:49.523 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.831 seconds (JVM running for 3.436)
2025-08-20 17:10:49.644 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-20 17:10:49.665 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 17:10:53.177 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-20 17:10:53.177 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-20 17:10:53.178 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-20 17:10:53.964 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: user, id: 282, sessionId: 0
2025-08-20 17:10:53.964 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 用户 282 的WebSocket连接已存储，连接ID: 0
2025-08-20 17:10:53.964 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：1
2025-08-20 17:10:53.964 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [282]
2025-08-20 17:10:53.964 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-20 17:10:54.973 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 开始发送欢迎消息给用户: 282
2025-08-20 17:10:54.975 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 为欢迎消息设置会话ID: 456
2025-08-20 17:10:54.982 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已保存到数据库，ID: 5635
2025-08-20 17:10:54.982 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 发送欢迎消息JSON: {"content":"您好，请问有什么可以帮您，可描述您想咨询的问题点击发送给我们。人工客服工作时间为09:00-21:00","from":2,"id":5635,"receiverId":282,"senderId":6,"sessionId":456,"timestamp":1755681054973,"type":1}
2025-08-20 17:10:54.982 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已成功发送给用户: 282
2025-08-20 17:10:55.146 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: agent, id: 4, sessionId: 1
2025-08-20 17:10:55.148 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 客服 4 的WebSocket连接已存储，连接ID: 1
2025-08-20 17:10:55.148 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：2
2025-08-20 17:10:55.148 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [282]
2025-08-20 17:10:55.148 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 17:12:51.891 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 282 有一用户连接关闭！
2025-08-20 17:12:51.891 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：1
2025-08-20 17:12:51.891 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-20 17:12:51.891 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 17:12:51.891 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 4 有一客服连接关闭！
2025-08-20 17:12:51.897 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：0
2025-08-20 17:12:51.897 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-20 17:12:51.897 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-20 17:12:51.902 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-08-20 17:12:51.902 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-08-20 17:12:51.902 [SpringApplicationShutdownHook] INFO  com.kefang.config.OssConfig - 正在关闭OSS客户端...
2025-08-20 17:12:51.902 [SpringApplicationShutdownHook] INFO  com.kefang.config.OssConfig - OSS客户端已关闭
2025-08-20 17:12:51.904 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-20 17:12:51.911 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-20 17:12:54.755 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-20 17:12:54.763 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 49328 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-08-20 17:12:54.763 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "dev"
2025-08-20 17:12:55.690 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-20 17:12:55.696 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-20 17:12:55.696 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-20 17:12:55.696 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-08-20 17:12:55.779 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-20 17:12:55.779 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 978 ms
2025-08-20 17:12:56.097 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-20 17:12:56.104 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-20 17:12:56.124 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-20 17:12:56.163 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-20 17:12:56.166 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-20 17:12:56.170 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-08-20 17:12:56.172 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-08-20 17:12:56.173 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-20 17:12:56.287 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-08-20 17:12:56.304 [main] INFO  com.kefang.config.OssConfig - 正在初始化OSS客户端...
2025-08-20 17:12:56.304 [main] INFO  com.kefang.config.OssConfig - OSS配置信息 - endpoint: http://oss-cn-beijing.aliyuncs.com, bucketName: juranfile
2025-08-20 17:12:56.450 [main] INFO  com.kefang.config.OssConfig - OSS客户端初始化成功
2025-08-20 17:12:56.858 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-20 17:12:56.888 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-20 17:12:56.903 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-20 17:12:56.917 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.519 seconds (JVM running for 3.048)
2025-08-20 17:12:56.918 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-20 17:12:57.039 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-20 17:12:57.060 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 17:12:57.173 [http-nio-8080-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-20 17:12:57.173 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-20 17:12:57.175 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-20 17:12:57.206 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: user, id: 282, sessionId: 0
2025-08-20 17:12:57.207 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: agent, id: 4, sessionId: 1
2025-08-20 17:12:57.207 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 用户 282 的WebSocket连接已存储，连接ID: 0
2025-08-20 17:12:57.207 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 客服 4 的WebSocket连接已存储，连接ID: 1
2025-08-20 17:12:57.207 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：2
2025-08-20 17:12:57.207 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：1
2025-08-20 17:12:57.207 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [282]
2025-08-20 17:12:57.207 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [282]
2025-08-20 17:12:57.207 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 17:12:57.207 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 17:12:58.212 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 开始发送欢迎消息给用户: 282
2025-08-20 17:12:58.215 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 为欢迎消息设置会话ID: 456
2025-08-20 17:12:58.221 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已保存到数据库，ID: 5636
2025-08-20 17:12:58.221 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 发送欢迎消息JSON: {"content":"您好，请问有什么可以帮您，可描述您想咨询的问题点击发送给我们。人工客服工作时间为09:00-21:00","from":2,"id":5636,"receiverId":282,"senderId":6,"sessionId":456,"timestamp":1755681178212,"type":1}
2025-08-20 17:12:58.221 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已成功发送给用户: 282
2025-08-20 17:17:56.924 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 17:22:56.921 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 17:23:58.657 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 282 有一用户连接关闭！
2025-08-20 17:23:58.657 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：1
2025-08-20 17:23:58.657 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-20 17:23:58.657 [http-nio-8080-exec-3] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 17:24:01.449 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: user, id: 282, sessionId: 2
2025-08-20 17:24:01.449 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 用户 282 的WebSocket连接已存储，连接ID: 2
2025-08-20 17:24:01.449 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：2
2025-08-20 17:24:01.449 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [282]
2025-08-20 17:24:01.449 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 17:24:02.451 [Thread-4] INFO  com.kefang.websocket.WebSocketServer - 开始发送欢迎消息给用户: 282
2025-08-20 17:24:02.453 [Thread-4] INFO  com.kefang.websocket.WebSocketServer - 为欢迎消息设置会话ID: 456
2025-08-20 17:24:02.457 [Thread-4] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已保存到数据库，ID: 5637
2025-08-20 17:24:02.457 [Thread-4] INFO  com.kefang.websocket.WebSocketServer - 发送欢迎消息JSON: {"content":"您好，请问有什么可以帮您，可描述您想咨询的问题点击发送给我们。人工客服工作时间为09:00-21:00","from":2,"id":5637,"receiverId":282,"senderId":6,"sessionId":456,"timestamp":1755681842451,"type":1}
2025-08-20 17:24:02.457 [Thread-4] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已成功发送给用户: 282
2025-08-20 17:24:09.103 [http-nio-8080-exec-9] INFO  com.kefang.websocket.WebSocketServer - 4 有一客服连接关闭！
2025-08-20 17:24:09.111 [http-nio-8080-exec-9] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：1
2025-08-20 17:24:09.111 [http-nio-8080-exec-9] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [282]
2025-08-20 17:24:09.111 [http-nio-8080-exec-9] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-20 17:24:11.849 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: agent, id: 4, sessionId: 3
2025-08-20 17:24:11.849 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 客服 4 的WebSocket连接已存储，连接ID: 3
2025-08-20 17:24:11.849 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：2
2025-08-20 17:24:11.849 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [282]
2025-08-20 17:24:11.849 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 17:24:12.462 [http-nio-8080-exec-7] INFO  com.kefang.controller.FaqController - 查询所有常见问题
2025-08-20 17:24:18.745 [http-nio-8080-exec-2] INFO  com.kefang.controller.FaqController - 查询所有常见问题
2025-08-20 17:24:28.409 [http-nio-8080-exec-5] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: 你好
  发送者ID: 282 发送方: 用户 数据源: 【默认数据源】
  接收方ID: 6  会话ID: 456 类型: 1
  在线状态： 在线人数：2  在线用户ID：[282] 在线客服ID：[4]
2025-08-20 17:24:28.418 [http-nio-8080-exec-5] INFO  com.kefang.websocket.WebSocketServer - 用户发送给AI客服的消息，跳过WebSocket的AI回复处理
2025-08-20 17:24:33.998 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 尝试发送转接人工通知 - 会话ID: 456, 用户ID: 282, 客服ID: 4
2025-08-20 17:24:33.999 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 已通过WebSocket发送转接通知给用户: 282
2025-08-20 17:24:34.000 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 尝试发送转接通知给客服 - 会话ID: 456, 用户ID: 282, 客服ID: 4
2025-08-20 17:24:34.000 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 已通过WebSocket发送转接通知给客服: 4
2025-08-20 17:24:40.182 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 尝试发送转接人工通知 - 会话ID: 456, 用户ID: 282, 客服ID: 4
2025-08-20 17:24:40.183 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 已通过WebSocket发送转接通知给用户: 282
2025-08-20 17:24:44.522 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: 您好，很高兴为您服务 您好，很高兴为您服务
  发送者ID: 4 发送方: 客服 数据源: 【默认数据源】
  接收方ID: 282  会话ID: 456 类型: 1
  在线状态： 在线人数：2  在线用户ID：[282] 在线客服ID：[4]
2025-08-20 17:24:54.053 [http-nio-8080-exec-10] ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is org.springframework.web.multipart.MaxUploadSizeExceededException: Maximum upload size exceeded; nested exception is java.lang.IllegalStateException: org.apache.tomcat.util.http.fileupload.impl.FileSizeLimitExceededException: The field file exceeds its maximum permitted size of 1048576 bytes.] with root cause
org.apache.tomcat.util.http.fileupload.impl.FileSizeLimitExceededException: The field file exceeds its maximum permitted size of 1048576 bytes.
	at org.apache.tomcat.util.http.fileupload.impl.FileItemStreamImpl$1.raiseError(FileItemStreamImpl.java:117)
	at org.apache.tomcat.util.http.fileupload.util.LimitedInputStream.checkLimit(LimitedInputStream.java:76)
	at org.apache.tomcat.util.http.fileupload.util.LimitedInputStream.read(LimitedInputStream.java:135)
	at java.base/java.io.FilterInputStream.read(FilterInputStream.java:106)
	at org.apache.tomcat.util.http.fileupload.util.Streams.copy(Streams.java:97)
	at org.apache.tomcat.util.http.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:317)
	at org.apache.catalina.connector.Request.parseParts(Request.java:2940)
	at org.apache.catalina.connector.Request.getParts(Request.java:2834)
	at org.apache.catalina.connector.RequestFacade.getParts(RequestFacade.java:1098)
	at org.springframework.web.multipart.support.StandardMultipartHttpServletRequest.parseRequest(StandardMultipartHttpServletRequest.java:95)
	at org.springframework.web.multipart.support.StandardMultipartHttpServletRequest.<init>(StandardMultipartHttpServletRequest.java:88)
	at org.springframework.web.multipart.support.StandardServletMultipartResolver.resolveMultipart(StandardServletMultipartResolver.java:122)
	at org.springframework.web.servlet.DispatcherServlet.checkMultipart(DispatcherServlet.java:1209)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1043)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-08-20 17:27:56.931 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 17:32:56.921 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 17:37:56.917 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 17:42:56.931 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 会话自动转换任务完成：已将 1 个最后活跃时间超过15分钟的人工会话转为AI会话, 更新了 1 条人工呼叫记录
2025-08-20 17:47:56.926 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 17:52:38.875 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 4 有一客服连接关闭！
2025-08-20 17:52:38.879 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：1
2025-08-20 17:52:38.879 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [282]
2025-08-20 17:52:38.879 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-20 17:52:38.880 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 282 有一用户连接关闭！
2025-08-20 17:52:38.880 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：0
2025-08-20 17:52:38.880 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-20 17:52:38.880 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-20 17:52:38.888 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-08-20 17:52:38.888 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-08-20 17:52:38.888 [SpringApplicationShutdownHook] INFO  com.kefang.config.OssConfig - 正在关闭OSS客户端...
2025-08-20 17:52:38.888 [SpringApplicationShutdownHook] INFO  com.kefang.config.OssConfig - OSS客户端已关闭
2025-08-20 17:52:38.889 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-20 17:52:38.892 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-20 17:52:41.376 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-20 17:52:41.384 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 46736 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-08-20 17:52:41.384 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "dev"
2025-08-20 17:52:42.301 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-20 17:52:42.307 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-20 17:52:42.308 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-20 17:52:42.308 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-08-20 17:52:42.392 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-20 17:52:42.393 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 970 ms
2025-08-20 17:52:42.743 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-20 17:52:42.749 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-20 17:52:42.760 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-20 17:52:42.794 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-20 17:52:42.796 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-20 17:52:42.799 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-08-20 17:52:42.802 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-08-20 17:52:42.802 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-20 17:52:42.909 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-08-20 17:52:42.924 [main] INFO  com.kefang.config.OssConfig - 正在初始化OSS客户端...
2025-08-20 17:52:42.925 [main] INFO  com.kefang.config.OssConfig - OSS配置信息 - endpoint: http://oss-cn-beijing.aliyuncs.com, bucketName: juranfile
2025-08-20 17:52:43.048 [main] INFO  com.kefang.config.OssConfig - OSS客户端初始化成功
2025-08-20 17:52:43.475 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-20 17:52:43.503 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-20 17:52:43.517 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-20 17:52:43.532 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.543 seconds (JVM running for 3.21)
2025-08-20 17:52:43.533 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-20 17:52:43.638 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-20 17:52:43.662 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 17:52:44.162 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-20 17:52:44.162 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-20 17:52:44.163 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-20 17:52:44.193 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: user, id: 282, sessionId: 0
2025-08-20 17:52:44.193 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: agent, id: 4, sessionId: 1
2025-08-20 17:52:44.193 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 客服 4 的WebSocket连接已存储，连接ID: 1
2025-08-20 17:52:44.193 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 用户 282 的WebSocket连接已存储，连接ID: 0
2025-08-20 17:52:44.193 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：1
2025-08-20 17:52:44.193 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：2
2025-08-20 17:52:44.193 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [282]
2025-08-20 17:52:44.193 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [282]
2025-08-20 17:52:44.193 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 17:52:44.193 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 17:52:45.195 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 开始发送欢迎消息给用户: 282
2025-08-20 17:52:45.214 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 为欢迎消息设置会话ID: 456
2025-08-20 17:52:45.222 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已保存到数据库，ID: 5643
2025-08-20 17:52:45.222 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 发送欢迎消息JSON: {"content":"您好，请问有什么可以帮您，可描述您想咨询的问题点击发送给我们。人工客服工作时间为09:00-21:00","from":2,"id":5643,"receiverId":282,"senderId":6,"sessionId":456,"timestamp":1755683565195,"type":1}
2025-08-20 17:52:45.224 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已成功发送给用户: 282
2025-08-20 17:52:45.488 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 282 有一用户连接关闭！
2025-08-20 17:52:45.489 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：1
2025-08-20 17:52:45.489 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-20 17:52:45.490 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 17:52:45.495 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 4 有一客服连接关闭！
2025-08-20 17:52:45.500 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：0
2025-08-20 17:52:45.500 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-20 17:52:45.500 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-20 17:52:48.357 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: user, id: 282, sessionId: 2
2025-08-20 17:52:48.357 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 用户 282 的WebSocket连接已存储，连接ID: 2
2025-08-20 17:52:48.358 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：1
2025-08-20 17:52:48.358 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [282]
2025-08-20 17:52:48.358 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-20 17:52:48.368 [http-nio-8080-exec-9] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: agent, id: 4, sessionId: 3
2025-08-20 17:52:48.368 [http-nio-8080-exec-9] INFO  com.kefang.websocket.WebSocketServer - 客服 4 的WebSocket连接已存储，连接ID: 3
2025-08-20 17:52:48.369 [http-nio-8080-exec-9] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：2
2025-08-20 17:52:48.369 [http-nio-8080-exec-9] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [282]
2025-08-20 17:52:48.369 [http-nio-8080-exec-9] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 17:52:49.378 [Thread-4] INFO  com.kefang.websocket.WebSocketServer - 开始发送欢迎消息给用户: 282
2025-08-20 17:52:49.388 [Thread-4] INFO  com.kefang.websocket.WebSocketServer - 为欢迎消息设置会话ID: 456
2025-08-20 17:52:49.400 [Thread-4] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已保存到数据库，ID: 5644
2025-08-20 17:52:49.400 [Thread-4] INFO  com.kefang.websocket.WebSocketServer - 发送欢迎消息JSON: {"content":"您好，请问有什么可以帮您，可描述您想咨询的问题点击发送给我们。人工客服工作时间为09:00-21:00","from":2,"id":5644,"receiverId":282,"senderId":6,"sessionId":456,"timestamp":1755683569380,"type":1}
2025-08-20 17:52:49.401 [Thread-4] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已成功发送给用户: 282
2025-08-20 17:52:49.671 [http-nio-8080-exec-10] INFO  com.kefang.controller.FaqController - 查询所有常见问题
2025-08-20 17:52:56.489 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: 你好
  发送者ID: 282 发送方: 用户 数据源: 【默认数据源】
  接收方ID: 6  会话ID: 456 类型: 1
  在线状态： 在线人数：2  在线用户ID：[282] 在线客服ID：[4]
2025-08-20 17:52:56.499 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 用户发送给AI客服的消息，跳过WebSocket的AI回复处理
2025-08-20 17:55:40.254 [http-nio-8080-exec-5] INFO  com.kefang.websocket.WebSocketServer - 282 有一用户连接关闭！
2025-08-20 17:55:40.254 [http-nio-8080-exec-5] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：1
2025-08-20 17:55:40.254 [http-nio-8080-exec-5] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-20 17:55:40.254 [http-nio-8080-exec-5] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 17:55:40.267 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 4 有一客服连接关闭！
2025-08-20 17:55:40.273 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：0
2025-08-20 17:55:40.273 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-20 17:55:40.273 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-20 17:55:43.020 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: agent, id: 4, sessionId: 4
2025-08-20 17:55:43.020 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 客服 4 的WebSocket连接已存储，连接ID: 4
2025-08-20 17:55:43.020 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：1
2025-08-20 17:55:43.020 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-20 17:55:43.021 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 17:55:43.053 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: user, id: 282, sessionId: 5
2025-08-20 17:55:43.053 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 用户 282 的WebSocket连接已存储，连接ID: 5
2025-08-20 17:55:43.053 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：2
2025-08-20 17:55:43.053 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [282]
2025-08-20 17:55:43.053 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 17:55:43.702 [http-nio-8080-exec-7] INFO  com.kefang.controller.FaqController - 查询所有常见问题
2025-08-20 17:55:44.069 [Thread-7] INFO  com.kefang.websocket.WebSocketServer - 开始发送欢迎消息给用户: 282
2025-08-20 17:55:44.071 [Thread-7] INFO  com.kefang.websocket.WebSocketServer - 为欢迎消息设置会话ID: 456
2025-08-20 17:55:44.075 [Thread-7] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已保存到数据库，ID: 5647
2025-08-20 17:55:44.075 [Thread-7] INFO  com.kefang.websocket.WebSocketServer - 发送欢迎消息JSON: {"content":"您好，请问有什么可以帮您，可描述您想咨询的问题点击发送给我们。人工客服工作时间为09:00-21:00","from":2,"id":5647,"receiverId":282,"senderId":6,"sessionId":456,"timestamp":1755683744070,"type":1}
2025-08-20 17:55:44.076 [Thread-7] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已成功发送给用户: 282
2025-08-20 17:57:43.537 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 17:59:25.841 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 4 有一客服连接关闭！
2025-08-20 17:59:25.845 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：1
2025-08-20 17:59:25.845 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [282]
2025-08-20 17:59:25.845 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-20 17:59:25.846 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 282 有一用户连接关闭！
2025-08-20 17:59:25.846 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：0
2025-08-20 17:59:25.846 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-20 17:59:25.846 [SpringApplicationShutdownHook] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-20 17:59:25.852 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 正在关闭聊天消息处理线程池...
2025-08-20 17:59:25.852 [SpringApplicationShutdownHook] INFO  com.kefang.service.impl.ConnectServiceImpl - 聊天消息处理线程池已关闭
2025-08-20 17:59:25.853 [SpringApplicationShutdownHook] INFO  com.kefang.config.OssConfig - 正在关闭OSS客户端...
2025-08-20 17:59:25.853 [SpringApplicationShutdownHook] INFO  com.kefang.config.OssConfig - OSS客户端已关闭
2025-08-20 17:59:25.853 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-20 17:59:25.858 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-20 17:59:28.926 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-20 17:59:28.934 [main] INFO  com.kefang.CustomerServiceApplication - Starting CustomerServiceApplication using Java 17.0.12 on xd with PID 32180 (D:\a_project\aliyun\xiondon\aikefu\kefu-service\target\classes started by Administrator in D:\a_project\aliyun\xiondon\aikefu)
2025-08-20 17:59:28.935 [main] INFO  com.kefang.CustomerServiceApplication - The following 1 profile is active: "dev"
2025-08-20 17:59:29.951 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-20 17:59:29.957 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-20 17:59:29.959 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-20 17:59:29.959 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.71]
2025-08-20 17:59:30.047 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-20 17:59:30.047 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1079 ms
2025-08-20 17:59:30.379 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-20 17:59:30.386 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-20 17:59:30.397 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatCallManualRecordMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-20 17:59:30.435 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-08-20 17:59:30.437 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-08-20 17:59:30.442 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-08-20 17:59:30.443 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-08-20 17:59:30.444 [main] WARN  c.b.mybatisplus.core.injector.AbstractMethod - [com.kefang.mapper.ChatSessionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-08-20 17:59:30.564 [main] INFO  com.kefang.handler.MessageHandlerFactory - 消息处理器链初始化完成，共加载4个处理器
2025-08-20 17:59:30.584 [main] INFO  com.kefang.config.OssConfig - 正在初始化OSS客户端...
2025-08-20 17:59:30.584 [main] INFO  com.kefang.config.OssConfig - OSS配置信息 - endpoint: http://oss-cn-beijing.aliyuncs.com, bucketName: juranfile
2025-08-20 17:59:30.718 [main] INFO  com.kefang.config.OssConfig - OSS客户端初始化成功
2025-08-20 17:59:31.090 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-20 17:59:31.119 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-20 17:59:31.135 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-20 17:59:31.149 [main] INFO  com.kefang.CustomerServiceApplication - Started CustomerServiceApplication in 2.586 seconds (JVM running for 3.109)
2025-08-20 17:59:31.149 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-20 17:59:31.173 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-20 17:59:31.173 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-20 17:59:31.174 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-20 17:59:31.206 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: user, id: 282, sessionId: 0
2025-08-20 17:59:31.206 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 用户 282 的WebSocket连接已存储，连接ID: 0
2025-08-20 17:59:31.206 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：1
2025-08-20 17:59:31.206 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: agent, id: 4, sessionId: 1
2025-08-20 17:59:31.206 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [282]
2025-08-20 17:59:31.206 [http-nio-8080-exec-2] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 17:59:31.206 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 客服 4 的WebSocket连接已存储，连接ID: 1
2025-08-20 17:59:31.206 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：2
2025-08-20 17:59:31.206 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [282]
2025-08-20 17:59:31.206 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 17:59:31.282 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-20 17:59:31.304 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 17:59:32.212 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 开始发送欢迎消息给用户: 282
2025-08-20 17:59:32.216 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 为欢迎消息设置会话ID: 456
2025-08-20 17:59:32.221 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已保存到数据库，ID: 5648
2025-08-20 17:59:32.222 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 发送欢迎消息JSON: {"content":"您好，请问有什么可以帮您，可描述您想咨询的问题点击发送给我们。人工客服工作时间为09:00-21:00","from":2,"id":5648,"receiverId":282,"senderId":6,"sessionId":456,"timestamp":1755683972213,"type":1}
2025-08-20 17:59:32.222 [Thread-2] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已成功发送给用户: 282
2025-08-20 17:59:34.977 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 282 有一用户连接关闭！
2025-08-20 17:59:34.977 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：1
2025-08-20 17:59:34.977 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-20 17:59:34.977 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 17:59:34.982 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 4 有一客服连接关闭！
2025-08-20 17:59:34.987 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：0
2025-08-20 17:59:34.987 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-20 17:59:34.987 [http-nio-8080-exec-7] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-20 17:59:37.724 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: agent, id: 4, sessionId: 2
2025-08-20 17:59:37.724 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 客服 4 的WebSocket连接已存储，连接ID: 2
2025-08-20 17:59:37.724 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：1
2025-08-20 17:59:37.724 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-20 17:59:37.724 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 17:59:37.790 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 新WebSocket连接 - role: user, id: 282, sessionId: 3
2025-08-20 17:59:37.790 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 用户 282 的WebSocket连接已存储，连接ID: 3
2025-08-20 17:59:37.791 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 有新连接加入！当前在线人数为：2
2025-08-20 17:59:37.791 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: [282]
2025-08-20 17:59:37.791 [http-nio-8080-exec-10] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 17:59:38.333 [http-nio-8080-exec-6] INFO  com.kefang.controller.FaqController - 查询所有常见问题
2025-08-20 17:59:38.801 [Thread-5] INFO  com.kefang.websocket.WebSocketServer - 开始发送欢迎消息给用户: 282
2025-08-20 17:59:38.803 [Thread-5] INFO  com.kefang.websocket.WebSocketServer - 为欢迎消息设置会话ID: 456
2025-08-20 17:59:38.806 [Thread-5] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已保存到数据库，ID: 5649
2025-08-20 17:59:38.806 [Thread-5] INFO  com.kefang.websocket.WebSocketServer - 发送欢迎消息JSON: {"content":"您好，请问有什么可以帮您，可描述您想咨询的问题点击发送给我们。人工客服工作时间为09:00-21:00","from":2,"id":5649,"receiverId":282,"senderId":6,"sessionId":456,"timestamp":1755683978801,"type":1}
2025-08-20 17:59:38.806 [Thread-5] INFO  com.kefang.websocket.WebSocketServer - 欢迎消息已成功发送给用户: 282
2025-08-20 17:59:43.558 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: 你好
  发送者ID: 282 发送方: 用户 数据源: 【默认数据源】
  接收方ID: 6  会话ID: 456 类型: 1
  在线状态： 在线人数：2  在线用户ID：[282] 在线客服ID：[4]
2025-08-20 17:59:43.565 [http-nio-8080-exec-6] INFO  com.kefang.websocket.WebSocketServer - 用户发送给AI客服的消息，跳过WebSocket的AI回复处理
2025-08-20 17:59:53.870 [http-nio-8080-exec-8] INFO  com.kefang.controller.FileUploadController - 开始上传文件：agentAvatarPic2.png, 大小：1235395 bytes
2025-08-20 17:59:54.356 [http-nio-8080-exec-8] INFO  com.kefang.utils.OssFileUploadUtil - 文件上传成功：原文件名=agentAvatarPic2.png, OSS路径=dismantling/2025/08/20/53669e1a25964b7dbdad3cba34bf26fe_1755683993884.png, 访问URL=https://file.juranguanjia.com/dismantling/2025/08/20/53669e1a25964b7dbdad3cba34bf26fe_1755683993884.png
2025-08-20 17:59:54.357 [http-nio-8080-exec-8] INFO  com.kefang.controller.FileUploadController - 文件上传成功：https://file.juranguanjia.com/dismantling/2025/08/20/53669e1a25964b7dbdad3cba34bf26fe_1755683993884.png
2025-08-20 17:59:54.365 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 
------- 收到websocket消息 -------
  消息: https://file.juranguanjia.com/dismantling/2025/08/20/53669e1a25964b7dbdad3cba34bf26fe_1755683993884.png
  发送者ID: 282 发送方: 用户 数据源: 【null】
  接收方ID: 6  会话ID: 456 类型: 2
  在线状态： 在线人数：2  在线用户ID：[282] 在线客服ID：[4]
2025-08-20 17:59:54.374 [http-nio-8080-exec-4] WARN  com.kefang.websocket.WebSocketServer - 指定的客服(ID:6)不在线，消息无法发送
2025-08-20 17:59:54.374 [http-nio-8080-exec-4] INFO  com.kefang.websocket.WebSocketServer - 客服不在线，消息已保存但未转发
2025-08-20 18:04:31.149 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 18:09:31.157 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 18:14:31.153 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 18:19:31.149 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 18:24:31.161 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 18:29:31.150 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 18:34:31.158 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 18:39:31.151 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 18:44:31.154 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 18:49:31.147 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 18:54:31.159 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 18:59:31.153 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 19:04:31.157 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 19:09:31.153 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 19:14:31.149 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 19:19:31.155 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 19:24:31.156 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 19:29:31.159 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 19:34:31.151 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 19:39:31.151 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 19:44:31.149 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 19:49:31.156 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 19:54:31.149 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 19:59:31.157 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 20:04:31.147 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 20:09:31.155 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 20:14:31.147 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 20:19:31.147 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 20:24:31.152 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 20:29:31.147 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 20:34:31.146 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 20:39:31.160 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 20:44:31.154 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 20:49:31.156 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 20:54:31.150 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 20:59:31.161 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 21:01:22.064 [http-nio-8080-exec-8] ERROR com.kefang.websocket.WebSocketServer - 发生错误：Connection reset, Session ID: 3
2025-08-20 21:01:22.064 [http-nio-8080-exec-1] ERROR com.kefang.websocket.WebSocketServer - 发生错误：Connection reset, Session ID: 2
2025-08-20 21:01:22.072 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 282 有一用户连接关闭！
2025-08-20 21:01:22.072 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 4 有一客服连接关闭！
2025-08-20 21:01:22.072 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：1
2025-08-20 21:01:22.072 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-20 21:01:22.072 [http-nio-8080-exec-8] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: [4]
2025-08-20 21:01:22.091 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线人数为：0
2025-08-20 21:01:22.092 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线用户ID: []
2025-08-20 21:01:22.092 [http-nio-8080-exec-1] INFO  com.kefang.websocket.WebSocketServer - 当前在线客服ID: []
2025-08-20 21:04:31.158 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 21:09:31.161 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 21:14:31.155 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 21:19:31.154 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 21:24:31.153 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 21:29:31.155 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 21:34:31.155 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 21:39:31.153 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 21:44:31.155 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 21:49:31.148 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 21:54:31.160 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 21:59:31.146 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 22:04:31.156 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 22:09:31.159 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 22:14:31.146 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 22:19:31.150 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 22:24:31.150 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 22:29:31.148 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 22:34:31.153 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 22:39:31.153 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 22:44:31.157 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 22:49:31.155 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 22:54:31.149 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 22:59:31.149 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 23:04:31.147 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 23:09:31.154 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 23:14:31.150 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 23:19:31.148 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 23:24:31.156 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 23:29:31.149 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 23:34:31.150 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 23:39:31.156 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 23:44:31.148 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 23:49:31.151 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 23:54:31.161 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
2025-08-20 23:59:31.150 [scheduling-1] INFO  com.kefang.job.AutoSessionTransferTask - 没有需要自动转换的会话
