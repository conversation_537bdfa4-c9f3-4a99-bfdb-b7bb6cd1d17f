# 企业微信群处理业务指南 —— 熊小智客服

## 一、方法概述

`ConnectController.onLine` 方法是客服系统的核心入口，
负责处理用户发送的消息并返回相应的回复。
该方法根据不同的用户输入模式，可以触发不同的功能路径：主要负责处理来自各渠道的用户消息，包括企业微信群消息。
该方法具有以下功能：

1. **统一入口处理**：处理来自企业微信群、个人消息等多种渠道的用户请求
2. **消息路由分发**：根据消息类型将请求分发给专门的处理器或AI服务
3. **智能回复**：对于非特定业务消息，通过AI服务提供智能回复

如果是企业微信群消息，系统会自动识别并调用对应的处理器进行处理。
1. 普通消息处理：将消息交给AI处理并返回回复
2. 查询订单信息：
   订单号格式：以 HS 开头，后接至少18位数字（如 HS20250522210136341563331）。
   第三方订单需使用固定格式：第三方订单 [订单号]（如 第三方订单 906237106）。
3. 查询物流信息：
    消息中需包含订单号或第三方订单号。
    需明确提及"物流"关键词，或使用"物流"相关指令词。
    支持格式：物流 [订单号] 或 第三方物流 [订单号]。
4. 取消订单：
   需明确提及"取消订单"关键词。
   支持格式：取消订单 [订单号] 或 取消订单 第三方订单 [订单号]。
5. 取消物流：
   需明确提及"取消物流"关键词。
   支持格式：取消物流 [订单号] 或 取消物流 第三方订单 [订单号]。

## 企业微信群业务触发总结

| 功能类型 | 触发关键词 | 支持格式示例 |
|---------|----------|------------|
| 查询订单信息 | 无/"查询订单" | HS20250522210136341563331 或 第三方订单 906237106 |
| 查询物流信息 | "物流" | 物流 HS20250522210136341563331 或 第三方物流 906237106 |
| 取消订单 | "取消订单" | 取消订单 HS20250522210136341563331 或 取消订单 第三方订单 1927893997958008833 |
| 取消物流 | "取消物流" | 取消物流 第三方订单 906237106 或 取消物流 HS20250522210136341563331 |

## 二、用户输入指南


### 2.1 用户输入指南

#### 请在涉及第三方订单的相关内容前统一添加"第三方订单"前缀，
```
例如：
第三方订单 XXXXX
第三方订单物流 XXXXXX
```

#### 1. 查询订单信息

要查询订单信息，用户需要在消息中包含HS开头的订单号或者第三方订单号。系统会自动识别并返回订单详情。

**输入示例：**

```
查询订单 HS20250522210136341563331
```

或简单地：

```
HS20250522210136341563331
```
或第三方订单号：

```
第三方订单 906237106
```
**返回内容：**

```
您好，查询结果：
---------------------------
回收单号：HS20250522210136341563331
三方单号：xxxxx
订单类型：xxxxx
下单渠道：xxxxx
下单时间：xxxx-xx-xx xx:xx:xx
商品名称：品牌-商品名
回收状态：xxxxx
回收报价：xxx.xx
快递公司：xxxxx
物流单号：xxxxx
物流状态：xxxxx
```

#### 2. 查询物流信息

要查询物流信息，用户需要在消息中包含订单号 或者第三方订单号，并明确表示查询物流。

**输入示例：**

```
第三方物流  906237106
```

或者：

```
物流 HS20250522210136341563331
```

**返回内容：**

```
您好，物流查询结果：
---------------------------
订单号：HS20250522210136341563331

快递公司：xxxxx
物流单号：xxxxx
物流状态：xxxxx

物流轨迹：
xxxx-xx-xx xx:xx:xx
物流详情1

xxxx-xx-xx xx:xx:xx
物流详情2

xxxx-xx-xx xx:xx:xx
物流详情3

xxxx-xx-xx xx:xx:xx
物流详情4

xxxx-xx-xx xx:xx:xx
物流详情5

...
共xx条物流记录，仅显示最新5条
```

#### 3. 取消订单

要取消订单，用户需要在消息中明确表示取消意图并包含订单号或者第三方订单号。

**输入示例：**

```
取消订单 HS20250522210136341563331
```

或者：

```
取消订单 第三方订单 1927893997958008833 
```

**返回内容：**

```
您好，订单取消请求已受理：
---------------------------
订单号：HS20250522210136341563331
取消状态：已受理
处理结果：订单已成功取消
备注：如有疑问，请联系客服人工处理
```

#### 4. 取消物流

要取消已安排的物流，用户需要在消息中表明取消物流的意图并包含订单号或物流单号。

**输入示例：**

```
取消物流  第三方订单 1927893997958008833 
```

或者：

```
取消物流 HS20250522210136341563331
```

**返回内容：**

```
您好，物流取消请求已处理：
---------------------------
订单号：HS20250522210136341563331
物流单号：SF1234567890123
取消状态：处理成功
处理结果：物流取消成功，上门取件已取消
备注：如已经取件，取消失败，请联系客服
```

#### 5. 普通对话

对于不属于以上几类特定业务处理的消息，系统将通过AI智能回复进行处理。

**输入示例：**

```
你是谁？
```

**返回内容：**

系统会根据AI模型和知识库返回相应的回答，例如：

```
我是熊小智，很高兴帮助你。
```

## 三、数据处理设计处理流程

### 3.1 整体处理流程

```
┌─────────────┐     ┌───────────────┐     ┌───────────────────────┐     ┌─────────────┐
│ 接收用户请求 │ ──→ │ 参数有效性验证 │ ──→ │ 微信群消息特殊处理判断 │ ──→ │ 消息处理分发 │
└─────────────┘     └───────────────┘     └───────────────────────┘     └─────────────┘
                                                                             │
┌─────────────────┐     ┌───────────────────────┐     ┌─────────────────┐    ↓
│ 返回处理结果给用户 │ ←── │ 结果格式化和错误处理 │ ←── │ AI服务或业务处理 │ ←──┘
└─────────────────┘     └───────────────────────┘     └─────────────────┘
```

### 3.2 企业微信群消息处理流程

当系统接收到企业微信群消息时（即`group_type`不为空）：

1. **消息识别与分发**
   - 系统将消息传递给`MessageHandlerFactory`进行处理
   - 工厂类遍历所有已注册的消息处理器
   - 每个处理器通过`canHandle`方法判断是否能处理该消息

2. **特定业务处理器**
   - **查询订单处理器**(`QueryOrderHandler`)：识别并处理含订单号的查询请求
   - **查询物流处理器**(`QueryExpressHandler`)：处理物流查询请求
   - **取消订单处理器**(`CancelOrderHandler`)：处理订单取消请求
   - **取消物流处理器**(`CancelExpressHandler`)：处理物流取消请求

3. **处理器执行流程**
   - 提取关键信息（如订单号、物流信息等）
   - 调用相关业务服务（如订单服务、物流服务）
   - 格式化处理结果
   - 返回处理结果

4. **无匹配处理器情况**
   - 如果没有特定处理器能处理该消息，`handleMessage`方法返回`null`
   - 系统会调用`ConnectService.onLine`方法，交由AI服务处理

### 3.3 AI服务处理流程

当企业微信群消息无法被特定业务处理器处理时：

1. **用户身份处理**
   - 系统根据`session_id`查询用户信息
   - 如用户不存在，则创建新用户

2. **会话管理**
   - 查询用户现有会话
   - 如无会话，创建新会话
   - 记录用户消息到数据库（异步）

3. **AI智能回复**
   - 调用`AIService.getAIResponse`获取AI回复
   - 记录AI回复到数据库（异步）
   - 返回AI回复结果给用户

