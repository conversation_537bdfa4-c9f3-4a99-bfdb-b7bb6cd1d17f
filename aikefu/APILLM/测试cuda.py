import torch
print("torch 模块路径:", torch.__file__)
# 查看torch版本.
print("查看torch版本: " + torch.__version__)

# 检查使用设备.
# 根据环境选择使用cuda、mps还是cpu。
print("根据环境选择使用cuda、mps还是cpu。")
device = (
    "cuda"
    if torch.cuda.is_available()
    else "mps"
    if torch.backends.mps.is_available()
    else "cpu"
)
print(f"使用 {device} 设备")

# 查看cuda是否可用.
print("查看cuda是否可用: " + str(torch.cuda.is_available()))

# 查看cuda版本.
print("查看cuda版本: " + torch.version.cuda)

# 查看GPU数量
print("查看GPU数量: " + str(torch.cuda.device_count()))

# 查看设备名称.
print("查看设备名称: " + torch.cuda.get_device_name(0))


import torch
import numpy as np
import time
print("测试对比NumPy和PyTorch在CPU上的计算性能，以及PyTorch在CPU和GPU上的计算性能：")
# 测试对比NumPy和PyTorch在CPU上的计算性能，以及PyTorch在CPU和GPU上的计算性能：
class Benchmark:
    """For measuring running time."""
    print("开始测试...")
    def __init__(self, description='Done'):
        self.description = description

    def __enter__(self):
        self.start_time = time.time()
        return self

    def __exit__(self, *args):
        self.stop_time = time.time()
        print(f'{self.description}: {self.stop_time - self.start_time:.4f} sec')


# GPU计算热身
size = 6000

# 使用numpy进行矩阵运算作为基线对比
# print("使用numpy进行矩阵运算作为基线对比，正在进行热身...")
# with Benchmark('numpy'):
#     for _ in range(10):
#         a = np.random.normal(size=(size, size))
#         b = np.dot(a, a)
#
# # 使用PyTorch在CPU上进行矩阵运算
# print("使用PyTorch在CPU上进行矩阵运算，正在进行热身...")
# device = torch.device('cpu')
# with Benchmark('torch-cpu'):
#     for _ in range(10):
#         a = torch.randn(size=(size, size), device=device)
#         b = torch.mm(a, a)
#
#
# # 使用PyTorch在GPU上进行矩阵运算，展示GPU加速的优势
# print("使用PyTorch在GPU上进行矩阵运算，正在进行热身...")
# device = torch.device('cuda')
# with Benchmark('torch-gpu'):
#     for _ in range(10):
#         a = torch.randn(size=(size, size), device=device)
#         b = torch.mm(a, a)
