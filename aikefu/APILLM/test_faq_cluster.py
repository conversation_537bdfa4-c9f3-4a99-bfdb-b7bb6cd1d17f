#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json
import time

# 测试数据
test_data = [
    {"count": 112, "keyword": "你好"},
    {"count": 49, "keyword": "维修报价怎么计算?"},
    {"count": 27, "keyword": "怎么申请退款怎么处理?"},
    {"count": 20, "keyword": "123"},
    {"count": 15, "keyword": "工程师知识库的编号是多少？"},
    {"count": 14, "keyword": "你好↵"},
    {"count": 9, "keyword": "11"},
    {"count": 8, "keyword": "1↵"},
    {"count": 7, "keyword": "1"},
    {"count": 6, "keyword": "转人工↵"},
    {"count": 6, "keyword": "人工↵"},
    {"count": 6, "keyword": "人工服务↵"},
    {"count": 5, "keyword": "绑定微信提现↵"},
    {"count": 4, "keyword": "回收"},
    {"count": 4, "keyword": "介绍一下熊洞智家"},
    {"count": 3, "keyword": "【熊洞服务】您的订单还未支付，请在24小时内完成支付哦~点 1j.cn/efea50d7 立即支付↵"},
    {"count": 3, "keyword": "可以上门回收吗?"},
    {"count": 5, "keyword": "怎么提现↵"},
    {"count": 5, "keyword": "怎么提现怎么支付↵"},
    {"count": 2, "keyword": "什么是电动窗帘"},
    {"count": 2, "keyword": "熊洞智家是什么?"},
    {"count": 3, "keyword": "您好，请问有什么可以帮您?"},
    {"count": 4, "keyword": "我想咨询退款问题"},
    {"count": 3, "keyword": "退款怎么处理"},
    {"count": 2, "keyword": "上门回收费用是多少"}
]

def main():
    """测试FAQ聚类接口"""
    print("开始测试FAQ聚类接口...")
    
    # API地址
    api_url = "http://localhost:8000/faq/cluster"
    
    # 构建请求数据
    request_data = {
        "data": test_data,
        "n_clusters": 5,
        "similarity_threshold": 0.85
    }
    
    try:
        # 发送请求
        start_time = time.time()
        response = requests.post(api_url, json=request_data)
        time_cost = round(time.time() - start_time, 2)
        
        # 检查响应状态
        if response.status_code == 200:
            result = response.json()
            print(f"FAQ聚类成功，请求耗时: {time_cost} 秒，服务器处理耗时: {result['time_cost']} 秒")
            print("\n===== 聚类结果 =====")
            print(json.dumps(result["results"], ensure_ascii=False, indent=2))
        else:
            print(f"请求失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
    
    except Exception as e:
        print(f"发生错误: {str(e)}")

if __name__ == "__main__":
    main() 