import soundfile as sf
from transformers import Qwen2_5OmniModel, Qwen2_5OmniProcessor
from qwen_omni_utils import process_mm_info

# 本地模型路径
MODEL_PATH = "E:/models/Qwen2.5-Omni-7B"

# 加载本地模型（自动选择设备）
model = Qwen2_5OmniModel.from_pretrained(
    MODEL_PATH,  # 直接指定本地路径
    torch_dtype="auto",
    device_map="auto",
    # 可选：启用 flash_attention_2 加速
    # attn_implementation="flash_attention_2",
)

# 加载本地 Processor
processor = Qwen2_5OmniProcessor.from_pretrained(MODEL_PATH)

# 多模态对话示例
conversation = [
    {
        "role": "system",
        "content": "You are <PERSON><PERSON>, a virtual human developed by the Qwen Team, Alibaba Group, capable of perceiving auditory and visual inputs, as well as generating text and speech.",
    },
    {
        "role": "user",
        "content": [
            {"type": "video", "video": "https://qianwen-res.oss-cn-beijing.aliyuncs.com/Qwen2.5-Omni/draw.mp4"},
        ],
    },
]

# 是否使用视频中的音频
USE_AUDIO_IN_VIDEO = True

# 预处理输入
text = processor.apply_chat_template(conversation, add_generation_prompt=True, tokenize=False)
audios, images, videos = process_mm_info(conversation, use_audio_in_video=USE_AUDIO_IN_VIDEO)
inputs = processor(
    text=text,
    audios=audios,
    images=images,
    videos=videos,
    return_tensors="pt",
    padding=True,
    use_audio_in_video=USE_AUDIO_IN_VIDEO,
)
inputs = inputs.to(model.device).to(model.dtype)

# 生成文本和音频
text_ids, audio = model.generate(**inputs, use_audio_in_video=USE_AUDIO_IN_VIDEO)

# 解码输出
text = processor.batch_decode(text_ids, skip_special_tokens=True, clean_up_tokenization_spaces=False)
print(text)

# 保存生成的音频
sf.write(
    "output.wav",
    audio.reshape(-1).detach().cpu().numpy(),
    samplerate=24000,
)