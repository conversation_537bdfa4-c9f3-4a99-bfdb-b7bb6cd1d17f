#!/usr/bin/env python
# -*- coding: utf-8 -*-

import numpy as np
from sklearn.cluster import AgglomerativeClustering
from sklearn.metrics.pairwise import cosine_similarity
import logging
import re

class FAQClusterTool:
    """FAQ聚类工具类，用于将相似问题聚合在一起"""
    
    def __init__(self, n_clusters=5, similarity_threshold=0.85):
        """
        初始化FAQ聚类工具
        
        参数:
        n_clusters (int): 聚类的目标类别数（已废弃，保留参数是为了兼容性）
        similarity_threshold (float): 相似度阈值，用于判断两个问题是否相似
        """
        self.n_clusters = n_clusters  # 保留但不再使用
        self.similarity_threshold = similarity_threshold
        self.embedding_func = None
        self.logger = logging.getLogger(__name__)
    
    def set_embedding_func(self, embedding_func):
        """
        设置用于生成文本嵌入的函数
        
        参数:
        embedding_func (callable): 接收文本列表并返回嵌入向量的函数
        """
        self.embedding_func = embedding_func
    
    def _preprocess_text(self, text):
        """
        预处理文本，移除换行符和多余空白，统一标点符号
        
        参数:
        text (str): 原始文本
        
        返回:
        str: 预处理后的文本
        """
        if not text:
            return ""
        
        # 移除换行符和多余空白
        text = text.strip()
        text = re.sub(r'\r\n|\r|\n', '', text)
        text = re.sub(r'\s+', ' ', text)
        
        # 常见的替换，如全角转半角，统一标点符号
        text = text.replace('，', ',').replace('。', '.').replace('？', '?').replace('！', '!')
        
        return text
    
    def _exact_match_clustering(self, questions, counts):
        """
        在进行语义聚类前，先基于精确匹配进行初步聚类
        
        参数:
        questions (List[str]): 问题列表
        counts (List[int]): 每个问题的出现次数
        
        返回:
        Tuple[List[str], List[int], Dict]: 预处理后的问题列表、计数列表和精确匹配的聚类结果
        """
        # 预处理所有问题
        processed_questions = [self._preprocess_text(q) for q in questions]
        
        # 创建问题映射，合并完全相同的问题
        question_map = {}
        for i, (q, count) in enumerate(zip(processed_questions, counts)):
            if not q:  # 跳过空文本
                continue
                
            lower_q = q.lower()  # 转小写进行比较
            if lower_q in question_map:
                # 合并相同问题的计数
                question_map[lower_q]["count"] += count
                question_map[lower_q]["indices"].append(i)
            else:
                question_map[lower_q] = {
                    "original": q,  # 保留原始大小写
                    "count": count,
                    "indices": [i]
                }
        
        # 构建预处理后的问题列表和计数列表
        new_questions = []
        new_counts = []
        exact_match_results = {}
        
        for q_info in question_map.values():
            new_questions.append(q_info["original"])
            new_counts.append(q_info["count"])
            
            # 如果有多个索引，说明有精确匹配合并
            if len(q_info["indices"]) > 1:
                exact_match_results[q_info["original"]] = {
                    "count": q_info["count"],
                    "merged_indices": q_info["indices"],
                    "merged_questions": [questions[i] for i in q_info["indices"]]
                }
                #self.logger.info(f"精确匹配合并: '{q_info['original']}' 合并了 {len(q_info['indices'])} 个问题，总计数: {q_info['count']}")
        
        return new_questions, new_counts, exact_match_results
    
    def cluster(self, questions, counts=None):
        """
        对问题进行聚类
        
        参数:
        questions (List[str]): 问题列表
        counts (List[int], optional): 每个问题的出现次数
        
        返回:
        List[Dict]: 聚类结果列表，每个元素包含代表问题和成员问题
        """
        if not questions:
            return []
        
        if not self.embedding_func:
            raise ValueError("请先使用set_embedding_func设置嵌入函数")
        
        # 如果没有提供counts，默认每个问题出现一次
        if counts is None:
            counts = [1] * len(questions)
        
        # 确保questions和counts长度一致
        if len(questions) != len(counts):
            raise ValueError("问题列表和计数列表长度不一致")
        
        # 预处理和精确匹配聚类
        self.logger.info(f"开始FAQ聚类处理，原始问题数: {len(questions)}")
        questions, counts, exact_matches = self._exact_match_clustering(questions, counts)
        self.logger.info(f"精确匹配预处理后，问题数: {len(questions)}")
        
        # 如果只有一个问题，直接返回
        if len(questions) == 1:
            return [{
                "representative": questions[0],
                "total_count": counts[0],
                "members": [{"keyword": questions[0], "count": counts[0]}]
            }]
        
        # 生成嵌入向量
        try:
            embeddings = self.embedding_func(questions)
            # self.logger.info(f"成功生成 {len(embeddings)} 个嵌入向量")
        except Exception as e:
            self.logger.error(f"生成嵌入向量时出错: {str(e)}")
            raise
        
        # 计算相似度矩阵
        similarity_matrix = cosine_similarity(embeddings)
        
        # 记录相似度信息，便于调试
        if self.logger.isEnabledFor(logging.DEBUG):
            top_similarities = []
            for i in range(len(questions)):
                for j in range(i+1, len(questions)):
                    sim = similarity_matrix[i, j]
                    if sim > self.similarity_threshold:
                        top_similarities.append((questions[i], questions[j], sim))
            
            # 按相似度降序排序
            top_similarities.sort(key=lambda x: x[2], reverse=True)
            
            # 记录前10个最相似的对
            for q1, q2, sim in top_similarities[:10]:
                self.logger.debug(f"高相似度对: '{q1}' - '{q2}' = {sim:.4f}")
        
        # 使用基于距离阈值的层次聚类，不再使用固定的聚类数量
        distance_threshold = 1.0 - self.similarity_threshold  # 将相似度阈值转换为距离阈值
        # self.logger.info(f"使用基于阈值的层次聚类，相似度阈值: {self.similarity_threshold}, 距离阈值: {distance_threshold:.4f}")
        
        try:
            clustering = AgglomerativeClustering(
                distance_threshold=distance_threshold,
                n_clusters=None,  # 设置为None时，会根据distance_threshold自动确定聚类数
                metric='precomputed',
                linkage='average'
            )
            
            # 将相似度矩阵转换为距离矩阵
            distance_matrix = 1 - similarity_matrix
            
            # 执行聚类
            cluster_labels = clustering.fit_predict(distance_matrix)
            
            # 记录聚类标签分布
            label_counts = {}
            for label in cluster_labels:
                label_counts[label] = label_counts.get(label, 0) + 1
            # self.logger.info(f"聚类结果：自动形成 {len(label_counts)} 个聚类, 聚类标签分布: {label_counts}")
            
        except Exception as e:
            # 如果基于阈值的聚类失败（可能是由于sklearn版本问题），回退到传统方式
            self.logger.warning(f"基于阈值的聚类失败: {str(e)}，回退到传统固定聚类数方式")
            n_clusters = min(self.n_clusters, len(questions))
            self.logger.info(f"回退方案：使用固定聚类数 {n_clusters}")
            
            clustering = AgglomerativeClustering(
                n_clusters=n_clusters,
                metric='precomputed',
                linkage='average'
            )
            
            # 将相似度矩阵转换为距离矩阵
            distance_matrix = 1 - similarity_matrix
            
            cluster_labels = clustering.fit_predict(distance_matrix)
        
        # 整理聚类结果
        clusters = {}
        for i, (question, count, label) in enumerate(zip(questions, counts, cluster_labels)):
            if label not in clusters:
                clusters[label] = {
                    "members": [],
                    "total_count": 0,
                    "representative_idx": -1,
                    "max_count": -1
                }
            
            clusters[label]["members"].append({"keyword": question, "count": count})
            clusters[label]["total_count"] += count
            
            # 更新代表问题（选择出现次数最多的）
            if count > clusters[label]["max_count"]:
                clusters[label]["max_count"] = count
                clusters[label]["representative_idx"] = len(clusters[label]["members"]) - 1
        
        # 转换为最终结果格式
        results = []
        for label, cluster in clusters.items():
            representative = cluster["members"][cluster["representative_idx"]]["keyword"]
            results.append({
                "representative": representative,
                "total_count": cluster["total_count"],
                "members": cluster["members"]
            })
            
            # 记录每个聚类的详情
            if self.logger.isEnabledFor(logging.DEBUG):
                member_info = [f"'{m['keyword']}' ({m['count']})" for m in cluster["members"]]
                self.logger.debug(f"聚类 {label}: 代表问题 '{representative}', 总计数 {cluster['total_count']}, 成员: {', '.join(member_info)}")
        
        # 按总数量降序排序
        results.sort(key=lambda x: x["total_count"], reverse=True)
        
        self.logger.info(f"FAQ聚类完成，原始问题数: {len(questions) + sum(len(info['merged_indices'])-1 for info in exact_matches.values())}, 聚类后: {len(results)} 类")
        
        return results 