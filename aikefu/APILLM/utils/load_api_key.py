def load_api_key(file_path=r"D:\a_project\xiondon\APILLM\utils\api_key.txt"):
    """
    从指定文件中加载 API Key。

    :param file_path: 包含 API Key 的文件路径
    :return: API Key 字符串
    """
    try:
        with open(file_path, "r") as file:
            for line in file:
                # 去除空格并检查是否以 ARK_API_KEY 开头
                if line.strip().startswith("ARK_API_KEY"):
                    # 提取等号后面的内容
                    key = line.split("=")[1].strip()
                    return key
        raise ValueError("未找到有效的 API Key")
    except FileNotFoundError:
        print(f"文件 {file_path} 不存在，请检查路径是否正确！")
    except Exception as e:
        print(f"读取 API Key 时发生错误: {e}")


# 测试读取 API Key
if __name__ == "__main__":
    api_key = load_api_key()
    if api_key:
        print("API Key 已成功加载:", api_key)