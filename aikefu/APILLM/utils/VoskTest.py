from fastapi import FastAPI, File, UploadFile
from vosk import Model, KaldiRecognizer
import wave
import json
import os
import subprocess

# 初始化 FastAPI 应用
app = FastAPI()

# 加载 Vosk 模型
MODEL_PATH = "../models/vosk-model-small-cn-0.22"  # 替换为你的模型路径
model = Model(MODEL_PATH)

def convert_to_wav(input_file: str, output_file: str):
    """
    使用 FFmpeg 将输入文件转换为单声道、16kHz 采样率的 WAV 文件。
    """
    try:
        subprocess.run([
            "ffmpeg",
            "-i", input_file,          # 输入文件
            "-ar", "16000",            # 设置采样率为 16kHz
            "-ac", "1",                # 设置为单声道
            output_file                # 输出文件
        ], check=True)
    except Exception as e:
        raise RuntimeError(f"音频文件转换失败: {str(e)}")

@app.post("/speech-to-text")
async def speech_to_text(file: UploadFile = File(...)):
    """
    接收上传的音频文件并返回语音转文字的结果。
    支持 MP3 和 WAV 格式。
    """
    try:
        # 确保上传的是支持的音频格式
        if not (file.filename.endswith(".wav") or file.filename.endswith(".mp3")):
            return {"code": 400, "error": "仅支持 WAV 或 MP3 格式的音频文件"}

        # 保存上传的文件到临时位置
        temp_input_path = f"./temp_input_{file.filename}"
        temp_output_path = f"./temp_output.wav"
        with open(temp_input_path, "wb") as temp_file:
            temp_file.write(await file.read())

        # 如果是 MP3 文件，先转换为 WAV
        if file.filename.endswith(".mp3"):
            convert_to_wav(temp_input_path, temp_output_path)
            os.remove(temp_input_path)  # 删除原始 MP3 文件
            audio_path = temp_output_path
        else:
            audio_path = temp_input_path

        # 打开音频文件
        wf = wave.open(audio_path, "rb")

        # 初始化识别器
        rec = KaldiRecognizer(model, wf.getframerate())
        result_text = ""

        # 逐帧读取音频数据并进行识别
        while True:
            data = wf.readframes(4000)
            if len(data) == 0:
                break
            if rec.AcceptWaveform(data):
                res = json.loads(rec.Result())
                result_text += res.get("text", "")

        # 获取最终结果
        final_result = json.loads(rec.FinalResult())
        result_text += final_result.get("text", "")

        # 删除临时文件
        wf.close()
        os.remove(audio_path)

        # 成功返回
        return {"code": 200, "data": result_text.strip()}

    except Exception as e:
        # 错误返回
        return {"code": 500, "error": f"处理音频文件时出错: {str(e)}"}

# 启动服务时指定端口
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8826)