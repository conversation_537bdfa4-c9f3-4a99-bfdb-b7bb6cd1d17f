import torch

# 验证 PyTorch 和 CUDA 版本
print("PyTorch 版本:", torch.__version__)  # 应为 2.6.0+cu126
print("CUDA 版本:", torch.version.cuda)    # 应为 12.6

# 确保 CUDA 可用
assert torch.cuda.is_available(), "CUDA 未启用"

# 配置矩阵乘法的 FP16 支持
torch.backends.cuda.matmul.allow_tf32 = False
torch.backends.cuda.matmul.allow_fp16_reduced_precision_reduction = True

device = torch.device("cuda")

# 测试矩阵乘法（应触发 FP16）
x = torch.randn(100, 100, device=device, dtype=torch.float32)
with torch.amp.autocast(device_type='cuda', dtype=torch.float16):
    y = x @ x.T
    print("矩阵乘法后的 dtype:", y.dtype)  # 应输出 torch.float16

# 测试简单计算（可能仍为 float32）
with torch.amp.autocast(device_type='cuda', dtype=torch.float16):
    a = torch.tensor([1.0], device=device)
    b = a * 2
    print("简单计算的 dtype:", b.dtype)  # 可能输出 torch.float32

# 验证显存占用（FP16 应减少显存）
print("原始显存占用:", torch.cuda.memory_allocated() / 1e6, "MB")
with torch.amp.autocast(device_type='cuda', dtype=torch.float16):
    z = x.to(dtype=torch.float16)  # 显式转换为 FP16
    print("转换后的显存占用:", torch.cuda.memory_allocated() / 1e6, "MB")