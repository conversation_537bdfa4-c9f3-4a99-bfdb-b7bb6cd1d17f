from openai import OpenAI
from utils.load_api_key import load_api_key

# 加载API Key
ARK_API_KEY = load_api_key()

# 初始化OpenAI客户端
client = OpenAI(
    base_url="https://ark.cn-beijing.volces.com/api/v3",  # 默认路径
    api_key=ARK_API_KEY,
)

def generate_response(model, messages, stream=False):
    """
    调用火山引擎API生成对话响应
    :param model: 模型名称，例如 "deepseek-v3-241226"
    :param messages: 对话历史，格式为 [{"role": "system/user/assistant", "content": "内容"}]
    :param stream: 是否启用流式响应，默认为False
    :return: 如果stream为True，返回生成器；否则返回完整响应
    """
    print("模型名称:" + model)
    print("messages" + str(messages))

    # 截断对话历史，最多保留10条消息
    max_history_length = 10
    if len(messages) > max_history_length:
        # 保留系统角色的消息（第一条），并截取最近的9条用户和助手消息
        system_message = [msg for msg in messages if msg["role"] == "system"]
        recent_messages = messages[-max_history_length + 1:]
        messages = system_message + recent_messages

    try:
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            stream=stream,
        )
        if stream:
            return (chunk.choices[0].delta.content for chunk in response if chunk.choices)
        else:
            return response.choices[0].message.content
    except Exception as e:
        raise Exception(f"API Error: {str(e)}")