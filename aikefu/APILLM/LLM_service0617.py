import os
import uuid
import time
import sys
import logging
from logging.handlers import TimedRotatingFileHandler
from datetime import datetime
import numpy as np
import torch
from transformers import AutoTokenizer, AutoModel
import chromadb
from fastapi import FastAPI, HTTPException, Query, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any, AsyncGenerator, Union
from langchain.chains import <PERSON><PERSON>hain
from langchain_core.prompts import PromptTemplate
from langchain_ollama import OllamaLLM
from contextlib import asynccontextmanager
import uvicorn
import re
from utils.faq_cluster import FAQClusterTool

# ========== 全局常量定义 ==========
DEFAULT_COLLECTION_NAME = "information"  # 默认集合名称
DEFAULT_DB_PATH = "xiondon_information"  # 默认数据库路径
DEFAULT_MODEL_NAME = "BAAI/bge-m3"  # 默认模型名称
DEFAULT_LOCAL_MODEL_PATH = "./models/BAAI-bge-m3"  # 本地模型路径
DEFAULT_BATCH_SIZE = 20  # 默认批处理大小
DEFAULT_PAGE = 1  # 默认页码
DEFAULT_PAGE_SIZE = 10  # 默认每页数量
MAX_PAGE_SIZE = 100  # 最大每页数量
OLLAMA_URL = "http://localhost:11434/api/generate"  # Ollama API URL
# OLLAMA_MODEL = "qwen2.5:3b"  # Ollama 模型名称
# OLLAMA_MODEL = "deepseek-r1:7b"  # Ollama 模型名称
OLLAMA_MODEL = "qwen3:1.7b"  # Ollama 模型名称
# OLLAMA_MODEL = "qwen3:4b"  # Ollama 模型名称

# ========== 日志系统配置 ==========
LOG_DIR = "logs"  # 日志目录
LOG_FILE = "llm_service.log"  # 基础日志文件名
LOG_LEVEL = logging.INFO  # 日志级别
LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"  # 日志格式
LOG_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"  # 日期格式

# 全局标志，用于跟踪日志系统是否已初始化
_LOGGING_INITIALIZED = False

# 确保日志目录存在
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)


# 用于重定向stdout和stderr到logger的类
class LoggerWriter:
    def __init__(self, logger, level):
        self.logger = logger
        self.level = level
        self.buf = ""
        self._writing = False  # 添加标志防止递归

    def write(self, message):
        # 如果正在写入或消息为空，则跳过
        if self._writing or not message or not message.strip():
            return

        try:
            self._writing = True
            self.buf += message
            if self.buf.endswith("\n"):
                self.logger.log(self.level, self.buf.rstrip())
                self.buf = ""
        finally:
            self._writing = False

    def flush(self):
        if not self._writing and self.buf:
            try:
                self._writing = True
                self.logger.log(self.level, self.buf)
                self.buf = ""
            finally:
                self._writing = False


# 初始化日志系统
def setup_logging():
    global _LOGGING_INITIALIZED

    # 如果日志系统已经初始化，直接返回已有的 logger
    if _LOGGING_INITIALIZED:
        return logging.getLogger()

    # 创建logger
    logger = logging.getLogger()
    logger.setLevel(LOG_LEVEL)

    # 清除现有的handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # 创建文件处理器，每天轮转一次，保留7天
    log_file_path = os.path.join(LOG_DIR, LOG_FILE)
    file_handler = TimedRotatingFileHandler(
        log_file_path,
        when="midnight",
        interval=1,
        backupCount=7,
        encoding="utf-8"
    )
    file_handler.setLevel(LOG_LEVEL)

    # 创建格式器并添加到处理器
    formatter = logging.Formatter(LOG_FORMAT, LOG_DATE_FORMAT)
    file_handler.setFormatter(formatter)

    # 添加处理器到logger
    logger.addHandler(file_handler)

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(LOG_LEVEL)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 重定向stdout和stderr到logger
    sys.stdout = LoggerWriter(logger, logging.INFO)
    sys.stderr = LoggerWriter(logger, logging.ERROR)

    # 记录启动信息
    logging.info("=" * 50)
    logging.info("日志系统初始化完成")
    logging.info(f"日志文件路径: {log_file_path}")
    logging.info(f"日志级别: {logging.getLevelName(LOG_LEVEL)}")
    logging.info("=" * 50)

    # 设置初始化标志
    _LOGGING_INITIALIZED = True

    return logger


# ========== 数据模型定义 ==========
# 向量数据库管理相关模型
class Document(BaseModel):
    text: str
    id: Optional[str] = None  # 此字段将被忽略，服务器将自动生成唯一ID
    collection_name: str = DEFAULT_COLLECTION_NAME  # 集合名称


class QueryRequest(BaseModel):
    query_text: str
    n_results: int = 3
    collection_name: str = DEFAULT_COLLECTION_NAME  # 集合名称


class UpdateDocument(BaseModel):
    id: str
    text: str


class UpdateRequest(BaseModel):
    documents: List[UpdateDocument]
    collection_name: str = DEFAULT_COLLECTION_NAME  # 集合名称


class DeleteRequest(BaseModel):
    ids: List[str]
    collection_name: str = DEFAULT_COLLECTION_NAME  # 集合名称


class ListRequest(BaseModel):
    page: int = DEFAULT_PAGE
    page_size: int = DEFAULT_PAGE_SIZE
    collection_name: str = DEFAULT_COLLECTION_NAME  # 集合名称


class CreateCollectionRequest(BaseModel):
    collection_name: str  # 要创建的集合名称


# 聊天服务相关模型
class ConversationRequest(BaseModel):
    messages: str
    history: List[Dict[str, str]] = []
    model: str = OLLAMA_MODEL
    stream: bool = False
    collection_name: str = DEFAULT_COLLECTION_NAME  # 允许指定从哪个集合检索


class ConversationResponse(BaseModel):
    response: str
    time_cost: float


# FAQ聚类相关模型
class FAQItem(BaseModel):
    count: int
    keyword: str


class FAQClusterRequest(BaseModel):
    data: List[FAQItem]
    n_clusters: Optional[int] = 5  # 保留向后兼容性，但不再使用此参数
    similarity_threshold: float = 0.85  # 相似度阈值，用于确定自动聚类的距离


class FAQClusterResponse(BaseModel):
    results: List[FAQItem]
    time_cost: float


# ========== 向量数据库管理类 ==========
class InformationManager:
    def __init__(self, db_path=DEFAULT_DB_PATH, default_collection_name=DEFAULT_COLLECTION_NAME,
                 model_name=DEFAULT_MODEL_NAME, local_model_path=DEFAULT_LOCAL_MODEL_PATH,
                 batch_size=DEFAULT_BATCH_SIZE):
        """初始化信息管理器，负责向量数据库的操作和嵌入模型的管理"""
        self.CHROMA_DB_PATH = db_path
        self.DEFAULT_COLLECTION_NAME = default_collection_name
        self.BATCH_SIZE = batch_size
        self.MODEL_NAME = model_name
        self.LOCAL_MODEL_PATH = local_model_path
        self.collections = {}  # 缓存集合实例

        # 初始化Chroma客户端
        self.client = chromadb.PersistentClient(path=self.CHROMA_DB_PATH)
        # 初始化默认集合
        self.get_collection(self.DEFAULT_COLLECTION_NAME)

        # 加载模型和分词器（优先从本地路径加载）
        self._load_model()

    def _load_model(self):
        """加载模型和分词器，优先从本地路径加载"""
        import os
        local_path_exists = os.path.exists(self.LOCAL_MODEL_PATH)

        if local_path_exists:
            print(f"从本地路径加载模型: {self.LOCAL_MODEL_PATH}")
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(self.LOCAL_MODEL_PATH)
                self.model = AutoModel.from_pretrained(self.LOCAL_MODEL_PATH)

                # 初始化设备并迁移模型
                self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
                print(f"使用的设备为: {self.device} device")
                self.model.to(self.device)

                print("本地模型加载成功")
                return
            except Exception as e:
                print(f"从本地路径加载模型失败，错误信息: {e}")
                print(f"尝试从Hugging Face下载模型: {self.MODEL_NAME}")
        else:
            print(f"本地模型路径不存在: {self.LOCAL_MODEL_PATH}")
            print(f"尝试从Hugging Face下载模型: {self.MODEL_NAME}")

        # 从Hugging Face下载模型
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(self.MODEL_NAME)
            self.model = AutoModel.from_pretrained(self.MODEL_NAME)

            # 初始化设备并迁移模型
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            print(f"使用的设备为: {self.device} device")
            self.model.to(self.device)

            print("从Hugging Face下载模型成功")
        except Exception as e:
            raise RuntimeError(f"模型加载失败，错误信息: {e}")

    def get_collection(self, collection_name=None):
        """获取或创建指定名称的集合"""
        if collection_name is None:
            collection_name = self.DEFAULT_COLLECTION_NAME

        if collection_name not in self.collections:
            self.collections[collection_name] = self.client.get_or_create_collection(name=collection_name)
            print(f"获取或创建集合: {collection_name}")
        return self.collections[collection_name]

    def generate_embeddings(self, texts):
        """生成文本的嵌入向量"""
        inputs = self.tokenizer(
            texts,
            padding=True,
            truncation=True,
            return_tensors="pt",
            max_length=1024
        )
        # 将输入数据迁移到设备
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        with torch.no_grad():
            outputs = self.model(**inputs)
        return outputs.last_hidden_state[:, 0, :].cpu().numpy()

    def add_documents(self, texts, collection_name=None, ids=None):
        """添加文档到指定集合"""
        if collection_name is None:
            collection_name = self.DEFAULT_COLLECTION_NAME

        collection = self.get_collection(collection_name)

        if ids is None:
            ids = [str(uuid.uuid4()) for _ in texts]

        embeddings = self.generate_embeddings(texts)

        # 过滤无效数据
        valid_indices = [
            j for j, (emb, txt) in enumerate(zip(embeddings, texts))
            if isinstance(emb, np.ndarray) and txt.strip()
        ]

        if not valid_indices:
            return []

        valid_embeddings = [embeddings[j].tolist() for j in valid_indices]
        valid_texts = [texts[j] for j in valid_indices]
        valid_ids = [ids[j] for j in valid_indices]

        # 插入到指定集合
        collection.add(
            ids=valid_ids,
            embeddings=valid_embeddings,
            documents=valid_texts
        )
        print(f"插入 {len(valid_texts)} 个文档到集合 {collection_name}")
        return valid_ids

    def delete_documents(self, ids, collection_name=None):
        """从指定集合删除文档"""
        if collection_name is None:
            collection_name = self.DEFAULT_COLLECTION_NAME

        collection = self.get_collection(collection_name)

        # 先检查这些ID是否存在
        existing_ids = []
        non_existing_ids = []

        # 获取所有ID
        all_docs = collection.get()
        all_ids = set(all_docs["ids"]) if "ids" in all_docs and all_docs["ids"] else set()

        # 分类ID是否存在
        for doc_id in ids:
            if doc_id in all_ids:
                existing_ids.append(doc_id)
            else:
                non_existing_ids.append(doc_id)

        # 只删除存在的ID
        if existing_ids:
            collection.delete(ids=existing_ids)
            print(f"从集合 {collection_name} 删除 {len(existing_ids)} 个文档")

        if non_existing_ids:
            print(f"警告：集合 {collection_name} 中有 {len(non_existing_ids)} 个ID不存在")

        return {"deleted": existing_ids, "not_found": non_existing_ids}

    def update_documents(self, ids, texts, collection_name=None):
        """更新指定集合中的文档"""
        if collection_name is None:
            collection_name = self.DEFAULT_COLLECTION_NAME

        self.delete_documents(ids, collection_name)
        return self.add_documents(texts, collection_name, ids)

    def query_documents(self, query_text, n_results=3, collection_name=None):
        """查询指定集合中的文档"""
        if collection_name is None:
            collection_name = self.DEFAULT_COLLECTION_NAME

        collection = self.get_collection(collection_name)

        query_embedding = self.generate_embeddings([query_text])[0].tolist()
        results = collection.query(
            query_embeddings=[query_embedding],
            n_results=n_results,
            include=["documents", "distances"]  # 确保包含距离信息以便于排序和过滤
        )
        return results

    def get_top_documents(self, n=DEFAULT_PAGE_SIZE, page=DEFAULT_PAGE, collection_name=None):
        """获取指定集合中的文档，支持分页"""
        if collection_name is None:
            collection_name = self.DEFAULT_COLLECTION_NAME

        collection = self.get_collection(collection_name)

        # 获取所有ID以便于分页
        all_docs = collection.get()
        all_ids = all_docs.get("ids", [])

        total_documents = len(all_ids)

        # 计算分页索引
        start_idx = (page - 1) * n
        end_idx = min(start_idx + n, total_documents)

        # 如果请求的页码超出范围，返回空结果
        if start_idx >= total_documents:
            return {
                "ids": [],
                "documents": [],
                "metadatas": [],
                "embeddings": [],
                "pagination": {
                    "total": total_documents,
                    "page": page,
                    "page_size": n,
                    "total_pages": max(1, (total_documents + n - 1) // n)
                }
            }

        # 获取当前页的ID
        page_ids = all_ids[start_idx:end_idx]

        # 使用这些ID查询详细信息
        if page_ids:
            results = collection.get(ids=page_ids)
        else:
            results = {"ids": [], "documents": [], "metadatas": [], "embeddings": []}

        # 添加分页信息
        results["pagination"] = {
            "total": total_documents,
            "page": page,
            "page_size": n,
            "total_pages": max(1, (total_documents + n - 1) // n)
        }

        return results

    def list_collections(self):
        """列出所有可用的集合"""
        # 在ChromaDB v0.6.0版本中，list_collections直接返回名称列表
        return self.client.list_collections()

    def create_collection(self, collection_name):
        """创建新的集合"""
        try:
            if not collection_name or not collection_name.strip():
                raise ValueError("集合名称不能为空")

            print(f"尝试创建集合: {collection_name}")

            # 检查集合是否已存在
            try:
                # 在ChromaDB v0.6.0版本中，list_collections只返回集合名称的列表
                existing_collection_names = self.client.list_collections()
                print(f"现有集合: {existing_collection_names}")

                if collection_name in existing_collection_names:
                    print(f"集合已存在: {collection_name}")
                    return {"name": collection_name, "status": "already_exists"}
            except Exception as e:
                print(f"检查集合存在性时出错: {str(e)}")
                # 如果无法检查是否存在，尝试继续创建

            # 创建新集合
            collection = self.client.get_or_create_collection(name=collection_name)
            print(f"成功创建新集合: {collection_name}")

            # 更新缓存
            self.collections[collection_name] = collection

            return {"name": collection_name, "status": "created"}
        except Exception as e:
            print(f"创建集合时发生异常: {str(e)}")
            raise

    # 供聊天服务使用的简化检索方法
    def retrieve_for_chat(self, query_text, top_n=1, collection_name=None):
        """为聊天功能进行文档检索，返回格式化的参考资料列表"""
        start_time = time.time()
        results = self.query_documents(query_text, top_n, collection_name)

        # 获取距离和文档内容
        distances = results.get("distances", [[]])[0]
        documents = results.get("documents", [[]])[0]

        references = []
        print(f"\n=== 参考资料（仅后台可见）=== ")
        for d, doc in zip(distances, documents):
            print(f"- 文档: {doc}\n  相似度: {d:.4f}")
            references.append(f"{doc}")
        print("============================\n")

        end_time = time.time()
        print(f"查询向量库耗时: {round(end_time - start_time, 2)} 秒")
        return references


# ========== LLM处理类 ==========
class LLMHandler:
    def __init__(self, model_name=OLLAMA_MODEL, enable_thinking=False):
        """初始化大语言模型处理器"""
        self.ollama = OllamaLLM(model=model_name, stream=True)  # 总是初始化为流式，可以按需使用
        self.enable_thinking = enable_thinking
        self.prompt_template = """系统提示：{system_prompt}

{history}
用户：{user_message}
助手："""
        self.prompt = PromptTemplate(
            input_variables=["system_prompt", "history", "user_message"],
            template=self.prompt_template
        )
        self.chain = self.prompt | self.ollama

    def _format_history(self, history: List[Dict[str, str]]) -> str:
        """
        将历史消息列表格式化为字符串，只保留最后3轮对话。
        :param history: 历史消息列表，每个元素是一个字典，包含 "role" 和 "content"。
        :return: 格式化后的字符串。
        """
        # 只保留最后3轮对话
        recent_history = history[-3:] if history else []

        history_str = ""
        for msg in recent_history:
            if msg["role"] == "user":
                history_str += f"用户：{msg['content']}\n"
            elif msg["role"] == "assistant":
                history_str += f"助手：{msg['content']}\n"
        return history_str

    async def generate_stream(self, messages: str, references: List[str], history: List[Dict[str, str]]) -> \
    AsyncGenerator[str, None]:
        """生成流式回复"""
        no_think_tag = " /no_think" if not self.enable_thinking else ""
        # system_prompt = f"你要扮演熊洞智家的智能客服熊小智,请严格按照文档回答用户的问题，要有回答逻辑清晰简洁。 {no_think_tag} "
        system_prompt = (f"{no_think_tag}你要扮演熊洞智家的智能客服熊小智，必须严格遵守以下规则（违规将导致严重错误）,"
                         f"1.请严格按照文档回答用户的问题,如果文档中没有用户的消息不要随意捏造虚拟消息"
                         f"2.每次回答应根据用户当前问题作出针对性回应"
                         f"3.如果用户请求人工客服、转人工、找客服等，你不能回答：您已转接至人工客服。你不能转人工，需要引导用户联系客服。"
                         f"4. 回答要友好礼貌，避免冗余内容 ")
        if references:
            context = "".join(references)
            system_prompt += f"文档内容如下：【{context}】"

        history_str = self._format_history(history)
        inputs = {
            "system_prompt": system_prompt,
            "history": history_str,
            "user_message": messages
        }

        print(f"大模型输入：{inputs}")
        try:
            async for chunk in self.chain.astream(inputs):
                if isinstance(chunk, dict):
                    text = chunk.get("text", "")
                elif isinstance(chunk, str):
                    text = chunk
                else:
                    raise ValueError(f"Unexpected type of chunk: {type(chunk)}")

                yield text
        except Exception as e:
            yield f"发生错误: {str(e)}"

    def generate_sync(self, messages: str, references: List[str], history: List[Dict[str, str]]) -> str:
        """生成同步回复（非流式）  。如果文档中没有用户提问相关的信息，请说"抱歉没有找到相关的信息"，{no_think_tag}"""
        no_think_tag = " /no_think" if not self.enable_thinking else ""
        # system_prompt = f"你要扮演熊洞智家的智能客服熊小智,请严格按照文档回答用户的问题，要有回答逻辑清晰简洁 {no_think_tag} "
        system_prompt = (f"{no_think_tag}你要扮演熊洞智家的智能客服熊小智，必须严格遵守以下规则（违规将导致严重错误）,"
                         f"1.请严格按照文档回答用户的问题,如果文档中没有用户的消息不要随意捏造虚拟消息"
                         f"2.每次回答应根据用户当前问题作出针对性回应"
                         f"3.如果用户请求人工客服、转人工、找客服等，你不能回答：您已转接至人工客服。你不能转人工，需要引导用户联系客服。"
                         f"4. 回答要友好礼貌，避免冗余内容 ")
        if references:
            context = "".join(references)
            system_prompt += f"文档内容如下：【{context}】"
        if references:
            context = "".join(references)
            system_prompt += f"根据以下文档回答问题：【{context}】"

        history_str = self._format_history(history)
        inputs = {
            "system_prompt": system_prompt,
            "history": history_str,
            "user_message": messages
        }
        print(f"大模型输入：{inputs}")

        try:
            # 使用 invoke 方法进行同步调用
            response = self.chain.invoke(inputs)
            return response
        except Exception as e:
            return f"发生错误: {str(e)}"

    def generate(self, messages: str, references: List[str], history: List[Dict[str, str]], stream: bool) -> Union[
        str, AsyncGenerator[str, None]]:
        """根据stream参数选择流式或非流式生成方式"""
        if stream:
            return self.generate_stream(messages, references, history)
        else:
            return self.generate_sync(messages, references, history)


# ========== 嵌入向量生成类 ==========
class EmbeddingGenerator:
    """用于生成文本embedding的类"""

    def __init__(self, model_path=DEFAULT_LOCAL_MODEL_PATH):
        """初始化embedding生成器"""
        print(f"从本地路径加载模型: {model_path}")
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = AutoModel.from_pretrained(model_path)

        # 初始化设备并迁移模型
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"使用的设备为: {self.device}")
        self.model.to(self.device)

    def generate_embeddings(self, texts):
        """生成文本的嵌入向量"""
        inputs = self.tokenizer(
            texts,
            padding=True,
            truncation=True,
            return_tensors="pt",
            max_length=1024
        )
        # 将输入数据迁移到设备
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        with torch.no_grad():
            outputs = self.model(**inputs)
        return outputs.last_hidden_state[:, 0, :].cpu().numpy()


# ========== 应用生命周期管理 ==========
@asynccontextmanager
async def lifespan(app: FastAPI):
    """管理应用的生命周期，负责资源的初始化和清理"""
    # 应用启动时初始化 InformationManager
    app.state.manager = InformationManager(
        db_path=DEFAULT_DB_PATH,
        default_collection_name=DEFAULT_COLLECTION_NAME,
        model_name=DEFAULT_MODEL_NAME,
        local_model_path=DEFAULT_LOCAL_MODEL_PATH
    )
    print("模型使用的是：" + OLLAMA_MODEL)
    # 初始化LLM处理器，禁用思考模式
    app.state.llm_handler = LLMHandler(model_name=OLLAMA_MODEL, enable_thinking=False)

    # 初始化嵌入向量生成器(用于FAQ聚类)
    app.state.embedding_generator = EmbeddingGenerator(model_path=DEFAULT_LOCAL_MODEL_PATH)

    print("服务初始化完成，开始提供API服务...")
    yield
    # 应用关闭时可以进行清理工作
    print("应用关闭，正在释放资源...")

    # 1. 释放模型资源
    if hasattr(app.state.manager, 'model'):
        try:
            # 释放PyTorch模型资源
            del app.state.manager.model
            del app.state.manager.tokenizer
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
            print("成功释放嵌入模型资源")
        except Exception as e:
            print(f"释放模型资源时出错: {str(e)}")

    # 释放FAQ聚类模型资源
    if hasattr(app.state, 'embedding_generator'):
        try:
            del app.state.embedding_generator.model
            del app.state.embedding_generator.tokenizer
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
            print("成功释放FAQ聚类模型资源")
        except Exception as e:
            print(f"释放FAQ聚类模型资源时出错: {str(e)}")

    # 2. 关闭向量数据库连接
    if hasattr(app.state.manager, 'client'):
        try:
            # 释放ChromaDB连接
            app.state.manager.client = None
            print("成功关闭向量数据库连接")
        except Exception as e:
            print(f"关闭向量数据库连接时出错: {str(e)}")

    # 3. 关闭LLM处理器
    if hasattr(app.state, 'llm_handler'):
        try:
            # 关闭Ollama连接
            app.state.llm_handler.ollama = None
            print("成功关闭LLM处理器连接")
        except Exception as e:
            print(f"关闭LLM处理器时出错: {str(e)}")

    # 4. 关闭日志系统
    print("正在关闭日志系统...")
    sys.stdout = sys.__stdout__
    sys.stderr = sys.__stderr__
    logging.shutdown()


# ========== 依赖函数 ==========
def get_manager():
    """获取InformationManager实例的依赖函数"""
    return app.state.manager


def get_llm_handler():
    """获取LLMHandler实例的依赖函数"""
    return app.state.llm_handler


def get_embedding_generator():
    """获取EmbeddingGenerator实例的依赖函数"""
    return app.state.embedding_generator


# ========== 创建FastAPI应用 ==========
# 初始化日志系统（如果不是通过主入口启动）
logger = setup_logging()

app = FastAPI(
    title="智能客服与知识库管理系统",
    description="基于向量数据库的智能客服系统和知识库管理API",
    version="1.0.0",
    lifespan=lifespan,
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# ========== API路由定义 ==========
# 1. 基础路由
@app.post("/")
async def read_root():
    """欢迎页面"""
    return {"message": "欢迎使用智能客服与知识库管理系统"}


# 2. 集合管理路由
@app.post("/collections/list")
async def list_collections(manager: InformationManager = Depends(get_manager)):
    """列出所有可用的集合"""
    print("接收到集合列表请求")
    collections = manager.list_collections()
    return {"collections": collections}


@app.post("/collections/create", response_model=Dict[str, Any])
async def create_collection(request: CreateCollectionRequest, manager: InformationManager = Depends(get_manager)):
    """
    创建新的知识库集合

    - collection_name: 要创建的集合名称，不能为空
    """
    print("接收到创建集合请求")
    try:
        print(f"接收到创建集合请求: {request.collection_name}")
        result = manager.create_collection(request.collection_name)
        print(f"创建集合成功: {result}")
        return result
    except ValueError as e:
        error_msg = str(e)
        print(f"创建集合参数错误: {error_msg}")
        raise HTTPException(status_code=400, detail=error_msg)
    except Exception as e:
        error_msg = str(e)
        print(f"创建集合失败，未知错误: {error_msg}")
        raise HTTPException(status_code=500, detail=f"创建集合失败: {error_msg}")


# 3. 文档管理路由
@app.post("/documents/add", response_model=Dict[str, Any])
async def add_documents(documents: List[Document], manager: InformationManager = Depends(get_manager)):
    """
    添加文档到数据库

    注意：
    - 即使提供了id字段，系统也会自动为每个文档生成新的唯一ID
    - 可以通过collection_name字段指定要添加到的集合
    """
    print("接收到添加文档请求")
    # 检查是否有文档需要处理
    if not documents:
        # 如果没有文档，返回一个空的字典，其中包含一个空的列表
        return {"ids": []}

    # 按集合名称分组
    docs_by_collection = {}
    for doc in documents:
        # 如果文档的集合名称不在字典中，则初始化该集合名称的列表
        if doc.collection_name not in docs_by_collection:
            docs_by_collection[doc.collection_name] = []
        # 将文档的文本添加到相应集合名称的列表中
        docs_by_collection[doc.collection_name].append(doc.text)

    # 分别处理每个集合的文档
    all_ids = {}
    for collection_name, texts in docs_by_collection.items():
        # 调用manager的add_documents方法，将文档文本列表和集合名称作为参数传递
        # 结果存储在result_ids中
        result_ids = manager.add_documents(texts, collection_name=collection_name, ids=None)
        # 将结果ID列表添加到all_ids字典中，键为集合名称
        all_ids[collection_name] = result_ids

    return {"results": all_ids}


@app.post("/documents/delete", response_model=Dict[str, Any])
async def delete_documents(request: DeleteRequest, manager: InformationManager = Depends(get_manager)):
    """
    从数据库中删除文档

    可以通过collection_name字段指定要操作的集合
    """
    print("接收到删除文档请求")
    result = manager.delete_documents(request.ids, collection_name=request.collection_name)

    # 如果没有成功删除任何文档，返回404状态码
    if not result["deleted"] and result["not_found"]:
        raise HTTPException(
            status_code=404,
            detail=f"在集合 {request.collection_name} 中未找到要删除的文档: {result['not_found']}"
        )

    return result


@app.post("/documents/update", response_model=Dict[str, Any])
async def update_documents(request: UpdateRequest, manager: InformationManager = Depends(get_manager)):
    """
    更新数据库中的文档

    可以通过collection_name字段指定要操作的集合
    """
    print("接收到更新文档请求")
    ids = [doc.id for doc in request.documents]
    texts = [doc.text for doc in request.documents]
    updated_ids = manager.update_documents(ids, texts, collection_name=request.collection_name)
    return {"collection": request.collection_name, "updated": updated_ids}


@app.post("/query", response_model=Dict[str, Any])
async def query_documents(query: QueryRequest, manager: InformationManager = Depends(get_manager)):
    """
    查询数据库中的文档

    可以通过collection_name字段指定要查询的集合
    """
    print("接收到查询文档请求")
    results = manager.query_documents(query.query_text, query.n_results, collection_name=query.collection_name)
    return {"collection": query.collection_name, "results": results}


@app.post("/documents/list", response_model=Dict[str, Any])
async def get_documents(request: ListRequest = None, manager: InformationManager = Depends(get_manager)):
    """
    获取数据库中的文档，支持分页

    - page: 页码，从1开始
    - page_size: 每页记录数，范围1-100
    - collection_name: 集合名称，默认为DEFAULT_COLLECTION_NAME
    """
    print("接收到获取文档请求")
    page = DEFAULT_PAGE
    page_size = DEFAULT_PAGE_SIZE
    collection_name = DEFAULT_COLLECTION_NAME

    if request:
        page = request.page
        page_size = request.page_size
        collection_name = request.collection_name

    if page < 1:
        raise HTTPException(status_code=400, detail="页码必须大于或等于1")

    if page_size < 1 or page_size > MAX_PAGE_SIZE:
        raise HTTPException(status_code=400, detail=f"每页记录数必须在1到{MAX_PAGE_SIZE}之间")

    results = manager.get_top_documents(n=page_size, page=page, collection_name=collection_name)
    return {"collection": collection_name, "results": results}


# 4. 聊天服务路由
@app.post("/chat/stream")
async def chat_stream(
        request: ConversationRequest,
        manager: InformationManager = Depends(get_manager),
        llm_handler: LLMHandler = Depends(get_llm_handler)
):
    """
    流式聊天接口

    支持指定collection_name来从特定集合中检索信息
    """
    user_message = request.messages
    history = request.history
    collection_name = request.collection_name

    # 获取当前时间并格式化
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    # 打印美化后的消息
    print(f"\n--- 新请求 ---\n时间: {current_time}\n消息: {user_message}\n集合: {collection_name}")

    # 判断是否需要转人工
    def is_transfer_to_human(user_input):
        keywords = ["转人工", "找客服", "联系人工", "联系客服", "真人", "人工客服", "帮我转人工", "human"]
        return any(kw in user_input for kw in keywords)

    if is_transfer_to_human(user_message):
        if collection_name == "recycle_knowledge":
            reply = "您好，我是智能客服小熊，如您有任何问题，欢迎继续提问，我会尽力为您解答。 如需联系人工客服，请与AI客服完成至少5轮后，通过【页面右上角 → 转人工】按钮进行操作，感谢您的理解！"
        else:
            reply = """ 您好，我是智能客服小熊，目前暂不支持人工服务。
    如果您有常见问题需要咨询，我可以帮您快速解答。
    如需其他帮助，请尝试访问我们的官网或微信公众号获取更多信息哦~"""

        async def fake_stream():
            start_time = time.time()
            for char in reply:
                yield f"data: {char}\n\n"
            time_cost = round(time.time() - start_time, 2)
            print(f"总对话耗时: {time_cost} 秒")

        return StreamingResponse(fake_stream(), media_type="text/event-stream")

    #  正常流程：从指定集合检索相关信息
    references = manager.retrieve_for_chat(user_message, top_n=1, collection_name=collection_name)

    async def response_generator():
        start_time = time.time()
        try:
            async for chunk in llm_handler.generate_stream(user_message, references, ""):#  清空history
                if chunk.strip():
                    yield f"data: {chunk}\n\n"
            end_time = time.time()
            print(f"大模型返回耗时: {round(end_time - start_time, 2)} 秒")
        except Exception as e:
            error_msg = str(e)
            print(f"流式生成发生错误: {error_msg}")
            yield f"data: {error_msg}\n\n"

    return StreamingResponse(response_generator(), media_type="text/event-stream")


@app.post("/chat", response_model=ConversationResponse)
async def chat(
        request: ConversationRequest,
        manager: InformationManager = Depends(get_manager),
        llm_handler: LLMHandler = Depends(get_llm_handler)
):
    """
    非流式聊天接口

    支持指定collection_name来从特定集合中检索信息
    """
    start_time = time.time()
    user_message = request.messages
    history = request.history
    collection_name = request.collection_name

    # 获取当前时间并格式化
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    # 打印美化后的消息
    print(f"\n--- 新请求 ---\n时间: {current_time}\n消息: {user_message}\n集合: {collection_name}")

    # 如果user_message是转人工等特殊处理
    def is_transfer_to_human(user_input):
        keywords = ["转人工", "找客服", "联系人工", "联系客服", "真人", "人工客服", "帮我转人工", "human"]
        return any(kw in user_input for kw in keywords)

    if is_transfer_to_human(user_message):
        reply = """" 您好，我是智能客服小熊，目前暂不支持人工服务。
如果您有常见问题需要咨询，我可以帮您快速解答。
如需其他帮助，请尝试访问我们的官网或微信公众号获取更多信息哦~"""
        if collection_name == "recycle_knowledge":
            reply = "您好，我是智能客服小熊，如您有任何问题，欢迎继续提问，我会尽力为您解答。 如需联系人工客服，请与AI客服完成至少5轮后，通过【页面右上角 → 转人工】按钮进行操作，感谢您的理解！"

        time_cost = round(time.time() - start_time, 2)
        print(f"总对话耗时: {time_cost} 秒")
        return ConversationResponse(
            response=reply,
            time_cost=time_cost
        )

    # 从指定集合检索相关信息
    references = manager.retrieve_for_chat(user_message, top_n=1, collection_name=collection_name)

    try:
        # 非流式模式下直接调用 generate_sync
        ai_response = llm_handler.generate_sync(user_message, references, history)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

    time_cost = round(time.time() - start_time, 2)
    print(f"总对话耗时: {time_cost} 秒")
    # 使用正则表达式删除 <think> 标签及其内部的所有内容
    ai_response = re.sub(r"(?s)<think>.*?</think>\s*", "", ai_response)
    print(f"完整响应: {ai_response}")

    return ConversationResponse(
        response=ai_response,
        time_cost=time_cost
    )


# 5. FAQ聚类路由
@app.post("/faq/cluster", response_model=FAQClusterResponse)
async def cluster_faq(request: FAQClusterRequest,
                      embedding_generator: EmbeddingGenerator = Depends(get_embedding_generator)):
    """
    聚类常见问题
    
    将相似的问题聚合在一起，减少重复问题。现使用相似度阈值自动确定聚类数量，不再需要固定的n_clusters参数。
    """
    start_time = time.time()
    print("接收到FAQ聚类请求")

    try:
        # 提取问题文本和出现次数
        items = request.data
        questions = [item.keyword for item in items]
        counts = [item.count for item in items]

        # 初始化聚类工具（n_clusters参数保留但不再使用）
        cluster_tool = FAQClusterTool(
            n_clusters=request.n_clusters,
            similarity_threshold=request.similarity_threshold
        )
        cluster_tool.set_embedding_func(embedding_generator.generate_embeddings)

        # 执行聚类
        results = cluster_tool.cluster(questions, counts)

        # 将结果转换为简单数组格式
        simplified_results = [
            FAQItem(count=cluster["total_count"], keyword=cluster["representative"])
            for cluster in results
        ]

        # 计算处理时间
        time_cost = round(time.time() - start_time, 2)
        print(f"FAQ聚类处理完成，耗时: {time_cost} 秒，从 {len(items)} 个问题聚类为 {len(simplified_results)} 个类别")

        return FAQClusterResponse(
            results=simplified_results,
            time_cost=time_cost
        )
    except Exception as e:
        error_msg = str(e)
        print(f"FAQ聚类处理失败: {error_msg}")
        raise HTTPException(status_code=500, detail=f"聚类处理失败: {error_msg}")


# ========== 主入口 ==========
if __name__ == "__main__":
    # 日志系统在模块加载时已初始化，不需要再次初始化
    # 清理缓存
    torch.cuda.empty_cache()
    # 保存当前日志重定向
    logger_stderr = sys.stderr

    # 临时恢复原始 stdout/stderr，避免与 uvicorn 冲突
    sys.stderr = sys.__stderr__

    try:
        # 启动 uvicorn，禁用其内置日志配置
        uvicorn.run("LLM_service:app", host="0.0.0.0", port=8000, reload=False, log_config=None)
    finally:
        # 如果需要在 uvicorn 结束后恢复日志重定向，可以取消下面两行的注释
        # sys.stdout = logger_stdout
        # sys.stderr = logger_stderr
        pass
