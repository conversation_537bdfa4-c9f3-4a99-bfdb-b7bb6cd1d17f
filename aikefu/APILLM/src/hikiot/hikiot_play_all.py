import urllib
from http.server import BaseHTTPRequestHandler, HTTPServer
import urllib.parse as urlparse
import json
import base64
import threading
import webbrowser
import requests
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5

# ✅ 配置 - 替换为你自己的 AppToken & 私钥
APP_KEY = "1940314761871011903"
APP_ACCESS_TOKEN = "at-FaQEdtpGnabpjSwQtwkItJq-ExOF7sr079CFT_V-"
APP_SECRET_PEM = b"""-----BEGIN RSA PRIVATE KEY-----
MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBANhl684p9BpWffGv
eYm3Dcq/XR+hEOg23MbGAHvIU/DbUaSZ3KlCMXHS8n6/vNALYLXUdYFJTnGbrs0O
jEDUABar4yDXgLpqAKnZEICaokM+do7ngFwv4OfxrETonu6je3rSosxZBETtZzOs
IYxFTwwbx7DF7Qc81TILBhy0+zwVAgMBAAECgYBMgkSL4LNoeHoQ8EyVTz42F9CR
9T1oDLWkJa9YFGzxNGm6O5gT085tgvqvq5TTLaKkxxhAfK2aZ0O3RlR6OzpIItUs
zbgzDHmKgEkeAhn0YfnJHz6b52OjL1M9iYTFzX76GRanSfjE7UMrx0q+Ljc+Durz
Fb1+I9bOZFS0XVV4wQJBAP36kF9h/wd91wUGHCWD6ktPr3uMWWzGlH8TROthZQU5
0s3fID48unypiWTLPzkwfaj1HgmwCz5hjDLdcGJcbxECQQDaHssain0QovxC5HAz
DNd0GwzL9Gr94oPDIpBTqB3tcShMs9jSOYQe0+eoaacAxKGgaGCAjIiL8qU5Yf67
RYTFAkBF2G1xvbpr7gB+4jJFYuTA91YUDQHetzHJTJxMewlUCZXehfkRBJRoihs9
u+NUL/Cu0VIEagR9kEgFCP7KHg9xAkEApWX0XYvmilcNMyxGaG+0cTaR3ZbaCutZ
Pv9WshkpmIPWK1O1drRmm3nUkMCNAugYJ6r+gnwOjxDQeVL19swggQJBAJuCIXYm
1e4qhkeLG1uVjc8zMQQkVLDQ3wFCovcILPM6wWAFAR2CXl4GsZwLPIlSTWcoEd+0
gSWRpau6gwvoXoU=
-----END RSA PRIVATE KEY-----"""

PORT = 8121
REDIRECT_URL = f"http://localhost:{PORT}/callback"

# ✅ 全局设备参数（可由 Postman 修改）
device_serial = "*********"
channel_no = 11

# ----------- 加密/解密辅助函数 ------------
def rsa_encrypt(data: str, secret_key: bytes) -> str:
    key = RSA.import_key(secret_key)
    cipher = PKCS1_v1_5.new(key)
    encoded = urlparse.quote(data).encode("utf-8")
    encrypted = b''
    for i in range(0, len(encoded), 117):
        encrypted += cipher.encrypt(encoded[i:i + 117])
    return base64.b64encode(encrypted).decode("utf-8")

def rsa_decrypt(data: str, secret_key: bytes) -> str:
    key = RSA.import_key(secret_key)
    cipher = PKCS1_v1_5.new(key)
    decoded = base64.b64decode(data.encode("utf-8"))
    decrypted = b""
    for i in range(0, len(decoded), 128):
        decrypted += cipher.decrypt(decoded[i:i + 128], None)
    return urllib.parse.unquote(decrypted.decode("utf-8"))

# ----------- 授权后全流程拉流函数 ---------------
def run_full_flow(auth_code):
    print(f"✅ 收到 authCode: {auth_code}")

    if not device_serial:
        print("❌ 错误：未设置设备号，请先 POST /setDevice 设置 deviceSerial 和 channelNo")
        return

    # 1. 换userToken
    print("[1] 正在获取 userAccessToken...")
    q1 = f"authCode={auth_code}"
    qs1 = rsa_encrypt(q1, APP_SECRET_PEM)
    headers = {
        "Content-Type": "application/json",
        "App-Access-Token": APP_ACCESS_TOKEN
    }
    res1 = requests.get(
        "https://open-api.hikiot.com/auth/third/code2Token",
        headers=headers,
        params={"querySecret": qs1}
    ).json()

    if res1["code"] != 0:
        print("❌ 第一步失败:", res1)
        return

    user_data = json.loads(rsa_decrypt(res1["data"], APP_SECRET_PEM))
    user_token = user_data.get("userAccessToken")
    print("✅ userAccessToken 获取成功！")

    # 2. 获取设备 streamToken
    print("[2] 正在获取 streamLiveToken...")
    qs2 = rsa_encrypt(f"deviceSerial={device_serial}&channelNo={channel_no}", APP_SECRET_PEM)
    res2 = requests.get(
        "https://open-api.hikiot.com/device/v1/token/device/get",
        headers={
            "Content-Type": "application/json",
            "App-Access-Token": APP_ACCESS_TOKEN,
            "User-Access-Token": user_token
        },
        params={"querySecret": qs2}
    ).json()

    if res2["code"] != 0:
        print("❌ 获取设备Token失败：", res2)
        return

    stream_data = json.loads(rsa_decrypt(res2["data"], APP_SECRET_PEM))
    # 打印返回 数据
    print(stream_data.get("streamTokenPlayExpire"))
    stream_token = stream_data.get("streamLiveToken")
    if stream_token:
        hls_url = f"https://open-api.hikiot.com/video/v1/preview/live.m3u8?streamLiveToken={stream_token}"
        print("\n🎥 HLS 播放地址：", hls_url)
        webbrowser.open(hls_url)
    else:
        print("❌ 未获取到 streamLiveToken")


# ----------- HTTP 服务（支持授权回调 + 设置接口）-------------
class RequestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        parsed_url = urlparse.urlparse(self.path)
        path = parsed_url.path
        query = urlparse.parse_qs(parsed_url.query)

        if path == "/callback":
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()

            auth_code = query.get("authCode", [None])[0]
            html = "<html><body><h2>✅ 登录成功，请返回终端查看播放情况</h2></body></html>"

            if auth_code:
                print("\n🚀 授权成功，收到 authCode 🎉\n")
                threading.Thread(target=run_full_flow, args=(auth_code,), daemon=True).start()
            else:
                html = "<html><body><h2>❌ 授权失败，未获取到 authCode</h2></body></html>"
                print("❌ 未获取到 authCode")

            self.wfile.write(html.encode("utf-8"))
        else:
            self.send_response(404)
            self.end_headers()

    def do_POST(self):
        global device_serial, channel_no

        path = self.path
        content_length = int(self.headers.get('Content-Length', 0))
        body = self.rfile.read(content_length)

        if path == "/setDevice":
            try:
                data = json.loads(body)
                device_serial = data.get("deviceSerial")
                channel_no = int(data.get("channelNo", 1))
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                resp = {"msg": "设备参数设置成功！", "deviceSerial": device_serial, "channelNo": channel_no}
                print(f"📦 设置设备完成：{resp}")
                self.wfile.write(json.dumps(resp).encode("utf-8"))
            except Exception as e:
                self.send_response(400)
                self.end_headers()
                self.wfile.write(f"解析失败: {e}".encode("utf-8"))
        else:
            self.send_response(404)
            self.end_headers()


# ----------- 启动服务器并打印授权链接 -------------
def run_server():
    print(f"🚀 服务启动成功，监听端口：{PORT}")
    print(f"🔁 授权回调地址：{REDIRECT_URL}")
    encoded_redirect = urlparse.quote(REDIRECT_URL)
    auth_url = f"https://open.hikiot.com/oauth/thirdpart?state=test123&appKey={APP_KEY}&redirectUrl={encoded_redirect}"
    print("📱 请在浏览器中打开下方链接完成扫码授权登录：\n")
    print(auth_url)
    print("\n📮 使用 Postman 向以下接口发送 POST，设置拉流设备：")
    print(f"POST http://localhost:{PORT}/setDevice")
    print("Body 示例： { \"deviceSerial\": \"*********\", \"channelNo\": 1 }\n")

    server = HTTPServer(("", PORT), RequestHandler)
    server.serve_forever()


if __name__ == "__main__":
    run_server()