from http.server import BaseHTTPRequestHandler, HTTPServer
import urllib.parse as urlparse

PORT = 8121


class CallbackHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        # 获取完整的 URL 路径
        parsed_path = urlparse.urlparse(self.path)

        # 提取 query 参数
        params = urlparse.parse_qs(parsed_path.query)
        auth_code = params.get('authCode', [None])[0]
        state = params.get('state', [None])[0]

        # 返回提示信息到浏览器
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()

        if auth_code:
            message = f"""
            <html>
            <body>
            <h2>授权成功！</h2>
            <p><strong>授权码（authCode）:</strong> {auth_code}</p>
            <p>你现在可以关闭此页面，继续在终端中获取 userAccessToken。</p>
            </body>
            </html>
            """
            print(f"\n✅ 获取到 authCode：{auth_code}")
        else:
            message = "<html><body><h2>未获取到授权码</h2></body></html>"
            print("❌ 没有获取到 authCode 参数")

        self.wfile.write(message.encode('utf-8'))


def run():
    server_address = ('', PORT)
    httpd = HTTPServer(server_address, CallbackHandler)
    print(f"🚀 本地回调服务器已启动，监听端口 {PORT} ...")
    print(f"👉 请将 redirectUrl 设置为：http://localhost:{PORT}/callback")
    httpd.serve_forever()


if __name__ == '__main__':
    run()