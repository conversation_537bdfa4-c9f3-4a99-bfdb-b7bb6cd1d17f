import base64
import urllib
import urllib.parse as urlparse
import requests
from Crypto.Cipher import PKCS1_v1_5
from Crypto.PublicKey import RSA
import json

# 🔧 你的配置：
APP_ACCESS_TOKEN = "at-FaQEdtpGnabpjSwQtwkItJq-ExOF7sr079CFT_V-"
USER_ACCESS_TOKEN = "ut-a9c6adab-e9a2-40d0-b06e-5485b8eee1fe"
APP_SECRET_PEM = b"""-----BEGIN RSA PRIVATE KEY-----
MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBANhl684p9BpWffGv
eYm3Dcq/XR+hEOg23MbGAHvIU/DbUaSZ3KlCMXHS8n6/vNALYLXUdYFJTnGbrs0O
jEDUABar4yDXgLpqAKnZEICaokM+do7ngFwv4OfxrETonu6je3rSosxZBETtZzOs
IYxFTwwbx7DF7Qc81TILBhy0+zwVAgMBAAECgYBMgkSL4LNoeHoQ8EyVTz42F9CR
9T1oDLWkJa9YFGzxNGm6O5gT085tgvqvq5TTLaKkxxhAfK2aZ0O3RlR6OzpIItUs
zbgzDHmKgEkeAhn0YfnJHz6b52OjL1M9iYTFzX76GRanSfjE7UMrx0q+Ljc+Durz
Fb1+I9bOZFS0XVV4wQJBAP36kF9h/wd91wUGHCWD6ktPr3uMWWzGlH8TROthZQU5
0s3fID48unypiWTLPzkwfaj1HgmwCz5hjDLdcGJcbxECQQDaHssain0QovxC5HAz
DNd0GwzL9Gr94oPDIpBTqB3tcShMs9jSOYQe0+eoaacAxKGgaGCAjIiL8qU5Yf67
RYTFAkBF2G1xvbpr7gB+4jJFYuTA91YUDQHetzHJTJxMewlUCZXehfkRBJRoihs9
u+NUL/Cu0VIEagR9kEgFCP7KHg9xAkEApWX0XYvmilcNMyxGaG+0cTaR3ZbaCutZ
Pv9WshkpmIPWK1O1drRmm3nUkMCNAugYJ6r+gnwOjxDQeVL19swggQJBAJuCIXYm
1e4qhkeLG1uVjc8zMQQkVLDQ3wFCovcILPM6wWAFAR2CXl4GsZwLPIlSTWcoEd+0
gSWRpau6gwvoXoU=
-----END RSA PRIVATE KEY-----"""

device_serial = "*********"
channel_no = 11  # 通常为1


def rsa_encrypt_param(params: str, private_key_bytes: bytes) -> str:
    private_key = RSA.import_key(private_key_bytes)
    cipher = PKCS1_v1_5.new(private_key)
    encoded = urlparse.quote(params).encode("utf-8")
    encrypted_bytes = b''
    for i in range(0, len(encoded), 117):
        encrypted_bytes += cipher.encrypt(encoded[i:i+117])
    return base64.b64encode(encrypted_bytes).decode()

def rsa_decrypt(encrypted_data: str, private_key_bytes: bytes) -> str:
    private_key = RSA.import_key(private_key_bytes)
    cipher = PKCS1_v1_5.new(private_key)
    data_bytes = base64.b64decode(encrypted_data)
    decrypted = b""
    for i in range(0, len(data_bytes), 128):
        decrypted += cipher.decrypt(data_bytes[i:i+128], None)
    return urllib.parse.unquote(decrypted.decode())

# 加密请求参数
query_param = f"deviceSerial={device_serial}&channelNo={channel_no}"
query_secret = rsa_encrypt_param(query_param, APP_SECRET_PEM)

# 请求视频 token
headers = {
    "Content-Type": "application/json",
    "App-Access-Token": APP_ACCESS_TOKEN,
    "User-Access-Token": USER_ACCESS_TOKEN
}

url = "https://open-api.hikiot.com/device/v1/token/device/get"
resp = requests.get(url, params={"querySecret": query_secret}, headers=headers)
res = resp.json()

# 解密 data
if res["code"] == 0:
    decrypted_str = rsa_decrypt(res["data"], APP_SECRET_PEM)
    print("✅ 解密响应数据：")
    print(decrypted_str)

    try:
        data = json.loads(decrypted_str)
        stream_token = data.get("streamLiveToken")
        if stream_token:
            hls_url = f"https://open-api.hikiot.com/video/v1/preview/live.m3u8?streamLiveToken={stream_token}"
            print("📺 视频播放地址：", hls_url)
        else:
            print("❌ 未找到 streamLiveToken！")
    except Exception as e:
        print("解析失败：", e)
        print("原始：", decrypted_str)
else:
    print("❌ API 请求失败：", res)