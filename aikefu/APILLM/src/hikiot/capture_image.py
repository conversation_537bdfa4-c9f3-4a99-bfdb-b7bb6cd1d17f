import json
import urllib

import requests
import base64
import urllib.parse as urlparse
from Crypto.Cipher import PKCS1_v1_5
from Crypto.PublicKey import RSA
import webbrowser

# ✅ 替换为你自己的 tokens & 私钥
APP_KEY = "1940314761871011903"  # 海康威视应用Key
APP_ACCESS_TOKEN = "at--Wd6WSDkfVXH-GfNJH6h1GntOIsCaMteaXy8u8eS"  # 将被自动更新
USER_ACCESS_TOKEN = "ut-0b72aac1-0cc5-4d9b-8c82-35b11427f485"

# PEM格式私钥（用于加密解密）
APP_SECRET = b"""-----BEGIN RSA PRIVATE KEY-----
MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBANhl684p9BpWffGv
eYm3Dcq/XR+hEOg23MbGAHvIU/DbUaSZ3KlCMXHS8n6/vNALYLXUdYFJTnGbrs0O
jEDUABar4yDXgLpqAKnZEICaokM+do7ngFwv4OfxrETonu6je3rSosxZBETtZzOs
IYxFTwwbx7DF7Qc81TILBhy0+zwVAgMBAAECgYBMgkSL4LNoeHoQ8EyVTz42F9CR
9T1oDLWkJa9YFGzxNGm6O5gT085tgvqvq5TTLaKkxxhAfK2aZ0O3RlR6OzpIItUs
zbgzDHmKgEkeAhn0YfnJHz6b52OjL1M9iYTFzX76GRanSfjE7UMrx0q+Ljc+Durz
Fb1+I9bOZFS0XVV4wQJBAP36kF9h/wd91wUGHCWD6ktPr3uMWWzGlH8TROthZQU5
0s3fID48unypiWTLPzkwfaj1HgmwCz5hjDLdcGJcbxECQQDaHssain0QovxC5HAz
DNd0GwzL9Gr94oPDIpBTqB3tcShMs9jSOYQe0+eoaacAxKGgaGCAjIiL8qU5Yf67
RYTFAkBF2G1xvbpr7gB+4jJFYuTA91YUDQHetzHJTJxMewlUCZXehfkRBJRoihs9
u+NUL/Cu0VIEagR9kEgFCP7KHg9xAkEApWX0XYvmilcNMyxGaG+0cTaR3ZbaCutZ
Pv9WshkpmIPWK1O1drRmm3nUkMCNAugYJ6r+gnwOjxDQeVL19swggQJBAJuCIXYm
1e4qhkeLG1uVjc8zMQQkVLDQ3wFCovcILPM6wWAFAR2CXl4GsZwLPIlSTWcoEd+0
gSWRpau6gwvoXoU=
-----END RSA PRIVATE KEY-----"""

# 纯文本格式私钥（用于API认证）
APP_SECRET_TEXT = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBANhl684p9BpWffGveYm3Dcq/XR+hEOg23MbGAHvIU/DbUaSZ3KlCMXHS8n6/vNALYLXUdYFJTnGbrs0OjEDUABar4yDXgLpqAKnZEICaokM+do7ngFwv4OfxrETonu6je3rSosxZBETtZzOsIYxFTwwbx7DF7Qc81TILBhy0+zwVAgMBAAECgYBMgkSL4LNoeHoQ8EyVTz42F9CR9T1oDLWkJa9YFGzxNGm6O5gT085tgvqvq5TTLaKkxxhAfK2aZ0O3RlR6OzpIItUszbgzDHmKgEkeAhn0YfnJHz6b52OjL1M9iYTFzX76GRanSfjE7UMrx0q+Ljc+DurzFb1+I9bOZFS0XVV4wQJBAP36kF9h/wd91wUGHCWD6ktPr3uMWWzGlH8TROthZQU50s3fID48unypiWTLPzkwfaj1HgmwCz5hjDLdcGJcbxECQQDaHssain0QovxC5HAzDNd0GwzL9Gr94oPDIpBTqB3tcShMs9jSOYQe0+eoaacAxKGgaGCAjIiL8qU5Yf67RYTFAkBF2G1xvbpr7gB+4jJFYuTA91YUDQHetzHJTJxMewlUCZXehfkRBJRoihs9u+NUL/Cu0VIEagR9kEgFCP7KHg9xAkEApWX0XYvmilcNMyxGaG+0cTaR3ZbaCutZPv9WshkpmIPWK1O1drRmm3nUkMCNAugYJ6r+gnwOjxDQeVL19swggQJBAJuCIXYm1e4qhkeLG1uVjc8zMQQkVLDQ3wFCovcILPM6wWAFAR2CXl4GsZwLPIlSTWcoEd+0gSWRpau6gwvoXoU="

# ✅ 填入你要抓拍的设备参数
DEVICE_SERIAL = "*********"
CHANNEL_NO = 11

# 全局变量用于缓存token
_cached_app_token = None

def get_app_access_token():
    """
    获取有效的APP_ACCESS_TOKEN
    调用海康威视 /auth/exchangeAppToken 接口
    """
    global _cached_app_token

    print("🔑 正在获取新的APP_ACCESS_TOKEN...")

    try:
        # 准备请求数据，使用纯文本格式的私钥
        payload = {
            "appKey": APP_KEY,
            "appSecret": APP_SECRET_TEXT
        }

        # 调用海康威视API
        url = "https://open-api.hikiot.com/auth/exchangeAppToken"
        headers = {
            "Content-Type": "application/json"
        }

        response = requests.post(url, json=payload, headers=headers, timeout=10)

        if response.status_code == 200:
            resp_data = response.json()
            if resp_data.get('code') == 0:
                # 成功获取token
                token_data = resp_data.get('data', {})
                new_token = token_data.get('appAccessToken')

                if new_token:
                    _cached_app_token = new_token
                    print(f"✅ 成功获取新的APP_ACCESS_TOKEN: {new_token[:20]}...")
                    return new_token
                else:
                    print("❌ API响应中未找到appAccessToken字段")
                    print("响应数据:", resp_data)
            else:
                print(f"❌ API返回错误: {resp_data}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print("响应内容:", response.text)

    except Exception as e:
        print(f"❌ 获取APP_ACCESS_TOKEN时发生异常: {str(e)}")

    return None

# ✅ 请求抓图接口（含请求加密）
def encrypt_body(body_str: str, private_key_bytes: bytes) -> str:
    key = RSA.import_key(private_key_bytes)
    cipher = PKCS1_v1_5.new(key)
    encoded = urlparse.quote(body_str).encode("utf-8")
    encrypted = b''
    for i in range(0, len(encoded), 117):
        encrypted += cipher.encrypt(encoded[i:i + 117])
    return base64.b64encode(encrypted).decode("utf-8")

def decrypt_data(data_str: str, private_key_bytes: bytes) -> str:
    key = RSA.import_key(private_key_bytes)
    cipher = PKCS1_v1_5.new(key)
    decoded = base64.b64decode(data_str.encode("utf-8"))
    decrypted = b""
    for i in range(0, len(decoded), 128):
        decrypted += cipher.decrypt(decoded[i:i + 128], None)
    return urllib.parse.unquote(decrypted.decode("utf-8"))

def capture_image():
    """
    执行设备抓拍功能，支持自动token刷新
    """
    global _cached_app_token

    print("📸 正在抓拍...")

    # 使用缓存的token或原始token
    current_token = _cached_app_token if _cached_app_token else APP_ACCESS_TOKEN

    # 最多重试2次（原始token + 刷新后的token）
    for attempt in range(2):
        try:
            # 请求内容（结构必须匹配文档）
            payload = {
                "deviceSerial": DEVICE_SERIAL,
                "payload": {
                    "channelNo": CHANNEL_NO
                }
            }

            encrypted_body = encrypt_body(json.dumps(payload), APP_SECRET)
            headers = {
                "Content-Type": "application/json",
                "App-Access-Token": current_token,
                "User-Access-Token": USER_ACCESS_TOKEN
            }

            url = "https://open-api.hikiot.com/device/direct/v1/captureImage/captureImage"
            response = requests.post(url, json={"bodySecret": encrypted_body}, headers=headers)
            resp_json = response.json()

            if resp_json['code'] == 0:
                # 抓拍成功
                decrypted = decrypt_data(resp_json['data'], APP_SECRET)
                data = json.loads(decrypted)
                print("✅ 抓拍成功！图片地址：", data['captureUrl'])
                webbrowser.open(data['captureUrl'])
                return True

            elif resp_json['code'] == 400015:
                # APP_ACCESS_TOKEN无效，尝试获取新token
                print(f"⚠️ APP_ACCESS_TOKEN无效 (尝试 {attempt + 1}/2)")

                if attempt == 0:  # 第一次尝试，获取新token
                    new_token = get_app_access_token()
                    if new_token:
                        current_token = new_token
                        print("🔄 使用新token重试抓拍...")
                        continue
                    else:
                        print("❌ 无法获取新的APP_ACCESS_TOKEN")
                        break
                else:
                    print("❌ 即使使用新token仍然失败")
                    break
            else:
                # 其他错误
                print("❌ 抓拍失败：", resp_json)
                break

        except Exception as e:
            print(f"❌ 抓拍过程中发生异常: {str(e)}")
            break

    return False

if __name__ == "__main__":
    capture_image()