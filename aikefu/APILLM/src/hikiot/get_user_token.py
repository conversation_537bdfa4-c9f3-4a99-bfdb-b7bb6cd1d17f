import base64
import urllib
import urllib.parse as urlparse
import requests
from Crypto.Cipher import PKCS1_v1_5
from Crypto.PublicKey import RSA

# 🔑 步骤一：填写你自己的参数
APP_ACCESS_TOKEN = "at-FaQEdtpGnabpjSwQtwkItJq-ExOF7sr079CFT_V-"  # ✅ 你的 App Token
APP_SECRET_KEY = b"""-----BEGIN RSA PRIVATE KEY-----
MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBANhl684p9BpWffGv
eYm3Dcq/XR+hEOg23MbGAHvIU/DbUaSZ3KlCMXHS8n6/vNALYLXUdYFJTnGbrs0O
jEDUABar4yDXgLpqAKnZEICaokM+do7ngFwv4OfxrETonu6je3rSosxZBETtZzOs
IYxFTwwbx7DF7Qc81TILBhy0+zwVAgMBAAECgYBMgkSL4LNoeHoQ8EyVTz42F9CR
9T1oDLWkJa9YFGzxNGm6O5gT085tgvqvq5TTLaKkxxhAfK2aZ0O3RlR6OzpIItUs
zbgzDHmKgEkeAhn0YfnJHz6b52OjL1M9iYTFzX76GRanSfjE7UMrx0q+Ljc+Durz
Fb1+I9bOZFS0XVV4wQJBAP36kF9h/wd91wUGHCWD6ktPr3uMWWzGlH8TROthZQU5
0s3fID48unypiWTLPzkwfaj1HgmwCz5hjDLdcGJcbxECQQDaHssain0QovxC5HAz
DNd0GwzL9Gr94oPDIpBTqB3tcShMs9jSOYQe0+eoaacAxKGgaGCAjIiL8qU5Yf67
RYTFAkBF2G1xvbpr7gB+4jJFYuTA91YUDQHetzHJTJxMewlUCZXehfkRBJRoihs9
u+NUL/Cu0VIEagR9kEgFCP7KHg9xAkEApWX0XYvmilcNMyxGaG+0cTaR3ZbaCutZ
Pv9WshkpmIPWK1O1drRmm3nUkMCNAugYJ6r+gnwOjxDQeVL19swggQJBAJuCIXYm
1e4qhkeLG1uVjc8zMQQkVLDQ3wFCovcILPM6wWAFAR2CXl4GsZwLPIlSTWcoEd+0
gSWRpau6gwvoXoU=
-----END RSA PRIVATE KEY-----"""

AUTH_CODE = "4b93f62b824d3b161274ac6cf5f27c51"  # ✅ 你授权返回的 authCode

# 加密函数（authCode）
def rsa_encrypt(content: str, private_key_bytes: bytes) -> str:
    private_key = RSA.import_key(private_key_bytes)
    cipher = PKCS1_v1_5.new(private_key)
    encoded = urlparse.quote(content).encode("utf-8")

    encrypted_bytes = b""
    for i in range(0, len(encoded), 117):  # RSA 最大加密块
        block = encoded[i:i + 117]
        encrypted_block = cipher.encrypt(block)
        encrypted_bytes += encrypted_block

    return base64.b64encode(encrypted_bytes).decode("utf-8")

# 🔐 使用私钥加密 authCode，变为 querySecret
query_param = f"authCode={AUTH_CODE}"
query_secret = rsa_encrypt(query_param, APP_SECRET_KEY)

# 🌐 发起请求
url = "https://open-api.hikiot.com/auth/third/code2Token"
headers = {
    "Content-Type": "application/json",
    "App-Access-Token": APP_ACCESS_TOKEN
}

response = requests.get(url, params={"querySecret": query_secret}, headers=headers)

print("✅ API响应：")
print(response.status_code)
try:
    print(response.json())
except Exception:
    print(response.text)

def rsa_decrypt(encrypted_data: str, private_key_bytes: bytes) -> str:
    private_key = RSA.import_key(private_key_bytes)
    cipher = PKCS1_v1_5.new(private_key)
    data_bytes = base64.b64decode(encrypted_data.encode("utf-8"))

    decrypted = b""
    for i in range(0, len(data_bytes), 128):  # RSA分段解密（1024bit RSA，块大小128字节）
        part = data_bytes[i:i+128]
        decrypted += cipher.decrypt(part, None)

    plaintext = decrypted.decode("utf-8")
    return urllib.parse.unquote(plaintext)  # 解URL编码

# 解密
decrypted_data = rsa_decrypt(response.json()['data'], APP_SECRET_KEY)
print("🔓 解密后的授权数据：")
print(decrypted_data)