import requests
import json

# 我们将请求发送到 httpbin.org，它会返回请求的详细信息
URL = "http://httpbin.org/get"


def analyze_ip(response_json):
    """
    根据给定的Java代码逻辑，分析服务器最终会识别出的IP地址。
    """
    headers = response_json.get('headers', {})
    origin_ip = response_json.get('origin', 'N/A')

    print("-" * 20)
    print(f"服务器看到的直接连接IP (request.getRemoteAddr()): {origin_ip}")
    print(f"服务器收到的 'X-Forwarded-For' 头: {headers.get('X-Forwarded-For')}")
    print(f"服务器收到的 'Proxy-Client-IP' 头: {headers.get('Proxy-Client-Ip')}")  # 注意httpbin会将key转为title case
    print(f"服务器收到的 'WL-Proxy-Client-IP' 头: {headers.get('Wl-Proxy-Client-Ip')}")
    print("-" * 20)

    # 1. 检查 X-Forwarded-For
    ip = headers.get('X-Forwarded-For')
    if ip:
        final_ip = ip.split(',')[0].strip()
        print(f"✅ 规则1命中: 服务器会从 'X-Forwarded-For' 中获取IP。")
        print(f"   最终识别出的IP是: {final_ip}")
        return final_ip

    # 2. 检查 Proxy-Client-IP
    ip = headers.get('Proxy-Client-Ip')
    if ip:
        print(f"✅ 规则2命中: 服务器会从 'Proxy-Client-IP' 中获取IP。")
        print(f"   最终识别出的IP是: {ip}")
        return ip

    # 3. 检查 WL-Proxy-Client-IP
    ip = headers.get('Wl-Proxy-Client-Ip')
    if ip:
        print(f"✅ 规则3命中: 服务器会从 'WL-Proxy-Client-IP' 中获取IP。")
        print(f"   最终识别出的IP是: {ip}")
        return ip

    # 4. 使用 getRemoteAddr()
    print(f"✅ 规则4命中: 以上所有头都不存在，服务器使用 getRemoteAddr()。")
    print(f"   最终识别出的IP是: {origin_ip}")
    return origin_ip


def test_direct_connection():
    """
    测试场景1：直接从你的Linux访问，不设置任何代理头。
    这是最能反映你真实情况的测试。
    """
    print("\n========== [测试场景1: 直接连接] ==========")
    try:
        response = requests.get(URL)
        response.raise_for_status()  # 如果请求失败则抛出异常
        data = response.json()

        print("httpbin.org 返回的原始信息:")
        print(json.dumps(data, indent=2, ensure_ascii=False))

        print("\n--- 根据Java代码逻辑分析 ---")
        analyze_ip(data)

    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")


def test_with_simulated_proxy():
    """
    测试场景2：模拟你通过一个代理服务器访问。
    我们手动添加一个 'X-Forwarded-For' 头。
    """
    print("\n\n========== [测试场景2: 模拟经过代理] ==========")
    # 假设你的真实IP是 *******，代理服务器的IP是 *******
    # 代理服务器会添加这个头，httpbin.org的 origin 将会是你的IP (或者你脚本运行环境的出口IP)
    # 而不是代理IP
    fake_ip = "*******"
    headers = {
        'X-Forwarded-For': f"{fake_ip}, *******"
    }

    try:
        response = requests.get(URL, headers=headers)
        response.raise_for_status()
        data = response.json()

        print("httpbin.org 返回的原始信息:")
        print(json.dumps(data, indent=2, ensure_ascii=False))

        print("\n--- 根据Java代码逻辑分析 ---")
        analyze_ip(data)

    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")


if __name__ == "__main__":
    # --- 运行测试 ---
    # 你只需要关心这个测试的结果
    test_direct_connection()

    # 这个测试是为了演示Java代码的逻辑，可以帮助你理解
    test_with_simulated_proxy()