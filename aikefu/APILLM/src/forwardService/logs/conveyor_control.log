2025-07-08 11:15:14.166 [INFO] __main__ - 启动传送带控制API服务，端口: 5016
2025-07-08 11:16:13.058 [INFO] conveyor_control_api - 请求控制传送带，参数: city='武汉' warehouse_name='仓库名称' orderId='JDX041163068741-1-1-' weight=0.0 timestamp='2025-06-17 12:45:27' conveyorId='设备编号' remark=None operator=None deviceId=None
2025-07-08 11:16:13.059 [INFO] conveyor_control_api - 提取字段 - 城市: 武汉, 仓库名称: 仓库名称 , 传送带设备编号: 设备编号
2025-07-08 11:16:13.059 [INFO] conveyor_control_api - 天津规则：开始查询订单信息，物流单号: JDX041163068741-1-1-, 查询类型: express
2025-07-08 11:16:13.059 [INFO] conveyor_control_api - 订单号Token不存在或已过期，重新获取token
2025-07-08 11:16:13.377 [INFO] conveyor_control_api - 订单号获取新token成功: 089fd5bd1b289d600377f6e8d89f1fea, 过期时间: 1751948173377
2025-07-08 11:16:13.555 [INFO] conveyor_control_api - 查询响应数据: {'in_page': 2, 'total': 0, 'page_no': 1, 'page_size': 10, 'page_max': 1, 'list': []}
2025-07-08 11:16:13.555 [ERROR] conveyor_control_api - 未找到订单号信息: JDX041163068741-1-1-
2025-07-08 11:16:13.556 [WARNING] conveyor_control_api - 天津规则：订单信息查询失败，使用默认方向（向右）: 未找到订单信息
2025-07-08 11:16:13.556 [INFO] conveyor_control_api - 传送带控制结果- message: 传送带向右滚动  data : 1
2025-07-08 11:16:58.128 [INFO] __main__ - 启动传送带控制API服务，端口: 5016
2025-07-08 11:17:04.210 [INFO] conveyor_control_api - 请求控制传送带，参数: city='武汉' warehouse_name='仓库名称' orderId='JDX041163068741-1-1-' weight=0.0 timestamp='2025-06-17 12:45:27' conveyorId='设备编号' remark=None operator=None deviceId=None
2025-07-08 11:17:04.210 [INFO] conveyor_control_api - 提取字段 - 城市: 武汉, 仓库名称: 仓库名称 , 订单编号: JDX041163068741-1-1-, 传送带设备编号: 设备编号
2025-07-08 11:17:04.210 [INFO] conveyor_control_api - 天津规则：开始查询订单信息，物流单号: JDX041163068741-1-1-, 查询类型: express
2025-07-08 11:17:04.210 [INFO] conveyor_control_api - 订单号Token不存在或已过期，重新获取token
2025-07-08 11:17:04.498 [INFO] conveyor_control_api - 订单号获取新token成功: 8ad9ea4add71e5edfcc98a1289da17b5, 过期时间: 1751948224498
2025-07-08 11:17:04.633 [INFO] conveyor_control_api - 查询响应数据: {'in_page': 2, 'total': 0, 'page_no': 1, 'page_size': 10, 'page_max': 1, 'list': []}
2025-07-08 11:17:04.633 [ERROR] conveyor_control_api - 未找到订单号信息: JDX041163068741-1-1-
2025-07-08 11:17:04.634 [WARNING] conveyor_control_api - 天津规则：订单信息查询失败，使用默认方向（向右）: 未找到订单信息
2025-07-08 11:17:04.635 [INFO] conveyor_control_api - 传送带控制结果- message: 传送带向右滚动  data : 1
2025-07-08 11:19:07.240 [INFO] __main__ - 启动传送带控制API服务，端口: 5016
2025-07-08 11:19:09.193 [INFO] __main__ - 启动传送带控制API服务，端口: 5016
2025-07-08 11:19:13.535 [INFO] __main__ - 启动传送带控制API服务，端口: 5016
2025-07-08 11:19:16.876 [INFO] conveyor_control_api - 请求控制传送带，参数: city='武汉' warehouse_name='仓库名称' orderId='JDX041163068741-1-1-' weight=0.0 timestamp='2025-06-17 12:45:27' conveyorId='设备编号' remark=None operator=None deviceId=None
2025-07-08 11:19:16.876 [INFO] conveyor_control_api - 提取字段 - 城市: 武汉, 仓库名称: 仓库名称 , 订单编号: JDX041163068741, 传送带设备编号: 设备编号
2025-07-08 11:19:16.877 [INFO] conveyor_control_api - 天津规则：开始查询订单信息，物流单号: JDX041163068741, 查询类型: express
2025-07-08 11:19:16.877 [INFO] conveyor_control_api - 订单号Token不存在或已过期，重新获取token
2025-07-08 11:19:17.152 [INFO] conveyor_control_api - 订单号获取新token成功: aeb8ea3634fc94be32c0194ee1b8d955, 过期时间: 1751948357152
2025-07-08 11:19:17.327 [INFO] conveyor_control_api - 查询响应数据: {'in_page': 2, 'total': 1, 'page_no': 1, 'page_size': 10, 'page_max': 1, 'list': [{'id': 11344262, 'code': 'HS20250706182001181391051', 'source': 48, 'batch_code': None, 'type': 1, 'sale_order_code': 'RE0125070600679976', 'prov_id': 120000, 'city_id': 120100, 'area_id': 120115, 'address': '天津宝坻区宝平街道天馨家园小区9-3-1001', 'prov_name': '天津', 'city_name': '宝坻区', 'area_name': '宝平街道', 'name': '王元', 'phone': '', 'mobile': '15822026872', 'detail': '销售订单号:[324992647718] 期望上门时间:[] 品牌:[其他品牌] 工单备注:[] 用户备注:[]', 'price': '0.00', 'buy_price': '0.00', 'status': 100, 'vaild_time': '2025-07-08 09:03:57', 'sign_time': '2025-07-08 09:03:57', 'finish_time': '2025-07-08 09:03:57', 'status_name': '回收完成', 'express_number': 'JDX041163068741', 'refund_express_number': None, 'create_time': '2025-07-06 18:20:01', 'yws_order_id': 0, 'yws_order_express': 0, 'source_name': '小智换新 - 京东平台', 'order_type': '回收订单', 'recover_item': {'goods_type': 0, 'item_code': '002222', 'item_brand': '其他品牌', 'item_cates': '电饭煲', 'item_model': '无', 'item_name': '其他品牌-电饭煲-无', 'item_price': '0.00', 'info': '商品不存在'}, 'new_item': '', 'is_follow': 0, 'sale_tag': None}]}
2025-07-08 11:19:17.329 [INFO] conveyor_control_api - 天津规则判断：source(48) != 48 && price(0.0) > 5.0 = false，传送带方向向右
2025-07-08 11:19:17.329 [INFO] conveyor_control_api - 传送带控制结果- message: 传送带向右滚动  data : 1
