2025-07-07 16:39:36.200 [INFO] __main__ - 启动传送带控制API服务，端口: 5016
2025-07-07 16:41:11.126 [INFO] __main__ - 启动传送带控制API服务，端口: 5016
2025-07-07 16:41:27.523 [INFO] conveyor_control_api - 请求控制传送带，参数: city='城市' warehouse_name='仓库名称' orderId='JDX041010225820' weight=0.0 timestamp='2025-06-17 12:45:27' conveyorId='设备编号' remark=None operator=None deviceId=None
2025-07-07 16:41:27.523 [INFO] conveyor_control_api - 订单号Token不存在或已过期，重新获取token
2025-07-07 16:41:27.764 [INFO] conveyor_control_api - 订单号获取新token成功: df409919b77013db0e5101ce9e15646f, 过期时间: 1751881287764
2025-07-07 16:41:27.942 [INFO] conveyor_control_api - 查询响应数据: {'in_page': 2, 'total': 1, 'page_no': 1, 'page_size': 10, 'page_max': 1, 'list': [{'id': 11278263, 'code': 'HS20250701161527031219686', 'source': 48, 'batch_code': None, 'type': 1, 'sale_order_code': 'RE0125070100356731', 'prov_id': 230000, 'city_id': 230100, 'area_id': 230110, 'address': '黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402', 'prov_name': '黑龙江', 'city_name': '哈尔滨市', 'area_name': '香坊区', 'name': '张', 'phone': '18446426229,,8674#', 'mobile': '', 'detail': '销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00', 'price': '0.00', 'buy_price': '0.00', 'status': 100, 'vaild_time': '2025-07-07 10:40:03', 'sign_time': '2025-07-07 10:40:03', 'finish_time': '2025-07-07 10:40:03', 'status_name': '回收完成', 'express_number': 'JDX041010225820', 'refund_express_number': None, 'create_time': '2025-07-01 16:15:27', 'yws_order_id': 0, 'yws_order_express': 0, 'source_name': '小智换新 - 京东平台', 'order_type': '回收订单', 'recover_item': {'goods_type': 0, 'item_code': '002222', 'item_brand': '其他', 'item_cates': '冷风扇', 'item_model': '无', 'item_name': '其他-冷风扇-无', 'item_price': '0.00', 'info': '商品不存在'}, 'new_item': '', 'is_follow': 0, 'sale_tag': None}]}
2025-07-07 16:41:27.944 [INFO] conveyor_control_api - 业务规则判断：source(48) != 48 && price(0.0) > 5.0 = false，传送带方向向右
2025-07-07 16:41:27.944 [INFO] conveyor_control_api - 传送带控制结果- message: 传送带向右滚动  data : 1
2025-07-07 16:50:18.858 [INFO] __main__ - 启动传送带控制API服务，端口: 5016
2025-07-07 17:00:20.442 [INFO] __main__ - 启动传送带控制API服务，端口: 5016
2025-07-07 17:00:44.167 [INFO] conveyor_control_api - 请求控制传送带，参数: city='城市' warehouse_name='仓库名称' orderId='JDX041010225820' weight=0.0 timestamp='2025-06-17 12:45:27' conveyorId='设备编号' remark=None operator=None deviceId=None
2025-07-07 17:00:44.167 [INFO] conveyor_control_api - 仓库名称未匹配特定规则，使用默认订单信息进行判断
2025-07-07 17:00:44.167 [INFO] conveyor_control_api - 开始查询订单信息，物流单号: JDX041010225820, 查询类型: express
2025-07-07 17:00:44.168 [INFO] conveyor_control_api - 订单号Token不存在或已过期，重新获取token
2025-07-07 17:00:44.374 [INFO] conveyor_control_api - 订单号获取新token成功: 25c9d97b7d1309cc41f19c129d85e5e7, 过期时间: 1751882444374
2025-07-07 17:00:44.524 [INFO] conveyor_control_api - 查询响应数据: {'in_page': 2, 'total': 1, 'page_no': 1, 'page_size': 10, 'page_max': 1, 'list': [{'id': 11278263, 'code': 'HS20250701161527031219686', 'source': 48, 'batch_code': None, 'type': 1, 'sale_order_code': 'RE0125070100356731', 'prov_id': 230000, 'city_id': 230100, 'area_id': 230110, 'address': '黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402', 'prov_name': '黑龙江', 'city_name': '哈尔滨市', 'area_name': '香坊区', 'name': '张', 'phone': '18446426229,,8674#', 'mobile': '', 'detail': '销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00', 'price': '0.00', 'buy_price': '0.00', 'status': 100, 'vaild_time': '2025-07-07 10:40:03', 'sign_time': '2025-07-07 10:40:03', 'finish_time': '2025-07-07 10:40:03', 'status_name': '回收完成', 'express_number': 'JDX041010225820', 'refund_express_number': None, 'create_time': '2025-07-01 16:15:27', 'yws_order_id': 0, 'yws_order_express': 0, 'source_name': '小智换新 - 京东平台', 'order_type': '回收订单', 'recover_item': {'goods_type': 0, 'item_code': '002222', 'item_brand': '其他', 'item_cates': '冷风扇', 'item_model': '无', 'item_name': '其他-冷风扇-无', 'item_price': '0.00', 'info': '商品不存在'}, 'new_item': '', 'is_follow': 0, 'sale_tag': None}]}
2025-07-07 17:00:44.526 [INFO] conveyor_control_api - 订单信息查询成功: {'code': 'HS20250701161527031219686', 're_code': 'JDX041010225820', 'id': 11278263, 'source': 48, 'batch_code': None, 'type': 1, 'sale_order_code': 'RE0125070100356731', 'prov_id': 230000, 'city_id': 230100, 'area_id': 230110, 'address': '黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402', 'prov_name': '黑龙江', 'city_name': '哈尔滨市', 'area_name': '香坊区', 'name': '张', 'phone': '18446426229,,8674#', 'mobile': '', 'detail': '销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00', 'price': '0.00', 'buy_price': '0.00', 'status': 100, 'vaild_time': '2025-07-07 10:40:03', 'sign_time': '2025-07-07 10:40:03', 'finish_time': '2025-07-07 10:40:03', 'status_name': '回收完成', 'express_number': 'JDX041010225820', 'refund_express_number': None, 'create_time': '2025-07-01 16:15:27', 'yws_order_id': 0, 'yws_order_express': 0, 'source_name': '小智换新 - 京东平台', 'order_type': '回收订单', 'recover_item': {'goods_type': 0, 'item_code': '002222', 'item_brand': '其他', 'item_cates': '冷风扇', 'item_model': '无', 'item_name': '其他-冷风扇-无', 'item_price': '0.00', 'info': '商品不存在'}, 'new_item': '', 'is_follow': 0, 'sale_tag': None}
2025-07-07 17:00:44.526 [INFO] conveyor_control_api - 提取订单字段 - source: 48, price: 0.00
2025-07-07 17:00:44.526 [INFO] conveyor_control_api - 订单信息规则判断：source(48) != 48 && price(0.0) > 5.0 = false，传送带方向向右
2025-07-07 17:00:44.526 [INFO] conveyor_control_api - 传送带控制结果- message: 传送带向右滚动  data : 1
2025-07-07 17:01:07.351 [INFO] conveyor_control_api - 请求控制传送带，参数: city='城市' warehouse_name='仓库名称' orderId='JDX041010225820' weight=0.0 timestamp='2025-06-17 12:45:27' conveyorId='设备编号' remark=None operator=None deviceId=None
2025-07-07 17:01:07.351 [INFO] conveyor_control_api - 仓库名称未匹配特定规则，使用默认订单信息进行判断
2025-07-07 17:01:07.351 [INFO] conveyor_control_api - 开始查询订单信息，物流单号: JDX041010225820, 查询类型: express
2025-07-07 17:01:07.352 [INFO] conveyor_control_api - 订单号Token不存在或已过期，重新获取token
2025-07-07 17:01:07.562 [INFO] conveyor_control_api - 订单号获取新token成功: 7f0d63c63c05c7ab5b8320adc874697b, 过期时间: 1751882467562
2025-07-07 17:01:07.706 [INFO] conveyor_control_api - 查询响应数据: {'in_page': 2, 'total': 1, 'page_no': 1, 'page_size': 10, 'page_max': 1, 'list': [{'id': 11278263, 'code': 'HS20250701161527031219686', 'source': 48, 'batch_code': None, 'type': 1, 'sale_order_code': 'RE0125070100356731', 'prov_id': 230000, 'city_id': 230100, 'area_id': 230110, 'address': '黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402', 'prov_name': '黑龙江', 'city_name': '哈尔滨市', 'area_name': '香坊区', 'name': '张', 'phone': '18446426229,,8674#', 'mobile': '', 'detail': '销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00', 'price': '0.00', 'buy_price': '0.00', 'status': 100, 'vaild_time': '2025-07-07 10:40:03', 'sign_time': '2025-07-07 10:40:03', 'finish_time': '2025-07-07 10:40:03', 'status_name': '回收完成', 'express_number': 'JDX041010225820', 'refund_express_number': None, 'create_time': '2025-07-01 16:15:27', 'yws_order_id': 0, 'yws_order_express': 0, 'source_name': '小智换新 - 京东平台', 'order_type': '回收订单', 'recover_item': {'goods_type': 0, 'item_code': '002222', 'item_brand': '其他', 'item_cates': '冷风扇', 'item_model': '无', 'item_name': '其他-冷风扇-无', 'item_price': '0.00', 'info': '商品不存在'}, 'new_item': '', 'is_follow': 0, 'sale_tag': None}]}
2025-07-07 17:01:07.707 [INFO] conveyor_control_api - 订单信息查询成功: {'code': 'HS20250701161527031219686', 're_code': 'JDX041010225820', 'id': 11278263, 'source': 48, 'batch_code': None, 'type': 1, 'sale_order_code': 'RE0125070100356731', 'prov_id': 230000, 'city_id': 230100, 'area_id': 230110, 'address': '黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402', 'prov_name': '黑龙江', 'city_name': '哈尔滨市', 'area_name': '香坊区', 'name': '张', 'phone': '18446426229,,8674#', 'mobile': '', 'detail': '销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00', 'price': '0.00', 'buy_price': '0.00', 'status': 100, 'vaild_time': '2025-07-07 10:40:03', 'sign_time': '2025-07-07 10:40:03', 'finish_time': '2025-07-07 10:40:03', 'status_name': '回收完成', 'express_number': 'JDX041010225820', 'refund_express_number': None, 'create_time': '2025-07-01 16:15:27', 'yws_order_id': 0, 'yws_order_express': 0, 'source_name': '小智换新 - 京东平台', 'order_type': '回收订单', 'recover_item': {'goods_type': 0, 'item_code': '002222', 'item_brand': '其他', 'item_cates': '冷风扇', 'item_model': '无', 'item_name': '其他-冷风扇-无', 'item_price': '0.00', 'info': '商品不存在'}, 'new_item': '', 'is_follow': 0, 'sale_tag': None}
2025-07-07 17:01:07.707 [INFO] conveyor_control_api - 提取订单字段 - source: 48, price: 0.00
2025-07-07 17:01:07.707 [INFO] conveyor_control_api - 订单信息规则判断：source(48) != 48 && price(0.0) > 5.0 = false，传送带方向向右
2025-07-07 17:01:07.708 [INFO] conveyor_control_api - 传送带控制结果- message: 传送带向右滚动  data : 1
2025-07-07 17:10:37.307 [INFO] __main__ - 启动传送带控制API服务，端口: 5016
2025-07-07 17:10:45.888 [INFO] conveyor_control_api - 请求控制传送带，参数: city='天津' warehouse_name='天津武清仓库' orderId='JDX041010225820' weight=0.0 timestamp='2025-06-17 12:45:27' conveyorId='设备编号' remark=None operator=None deviceId=None
2025-07-07 17:10:45.888 [INFO] conveyor_control_api - 提取字段 - 城市: 天津, 仓库名称: 天津武清仓库
2025-07-07 17:10:45.889 [INFO] conveyor_control_api - 仓库规则判断：'天津武清仓库' -> 使用订单信息判断规则（source != 48 && price > 5）
2025-07-07 17:10:45.889 [INFO] conveyor_control_api - 开始查询订单信息，物流单号: JDX041010225820, 查询类型: express
2025-07-07 17:10:45.889 [INFO] conveyor_control_api - 订单号Token不存在或已过期，重新获取token
2025-07-07 17:10:46.093 [INFO] conveyor_control_api - 订单号获取新token成功: 10e6afa19890e8ec0291bca0cdb82923, 过期时间: 1751883046093
2025-07-07 17:10:46.251 [INFO] conveyor_control_api - 查询响应数据: {'in_page': 2, 'total': 1, 'page_no': 1, 'page_size': 10, 'page_max': 1, 'list': [{'id': 11278263, 'code': 'HS20250701161527031219686', 'source': 48, 'batch_code': None, 'type': 1, 'sale_order_code': 'RE0125070100356731', 'prov_id': 230000, 'city_id': 230100, 'area_id': 230110, 'address': '黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402', 'prov_name': '黑龙江', 'city_name': '哈尔滨市', 'area_name': '香坊区', 'name': '张', 'phone': '18446426229,,8674#', 'mobile': '', 'detail': '销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00', 'price': '0.00', 'buy_price': '0.00', 'status': 100, 'vaild_time': '2025-07-07 10:40:03', 'sign_time': '2025-07-07 10:40:03', 'finish_time': '2025-07-07 10:40:03', 'status_name': '回收完成', 'express_number': 'JDX041010225820', 'refund_express_number': None, 'create_time': '2025-07-01 16:15:27', 'yws_order_id': 0, 'yws_order_express': 0, 'source_name': '小智换新 - 京东平台', 'order_type': '回收订单', 'recover_item': {'goods_type': 0, 'item_code': '002222', 'item_brand': '其他', 'item_cates': '冷风扇', 'item_model': '无', 'item_name': '其他-冷风扇-无', 'item_price': '0.00', 'info': '商品不存在'}, 'new_item': '', 'is_follow': 0, 'sale_tag': None}]}
2025-07-07 17:10:46.253 [INFO] conveyor_control_api - 订单信息查询成功: {'code': 'HS20250701161527031219686', 're_code': 'JDX041010225820', 'id': 11278263, 'source': 48, 'batch_code': None, 'type': 1, 'sale_order_code': 'RE0125070100356731', 'prov_id': 230000, 'city_id': 230100, 'area_id': 230110, 'address': '黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402', 'prov_name': '黑龙江', 'city_name': '哈尔滨市', 'area_name': '香坊区', 'name': '张', 'phone': '18446426229,,8674#', 'mobile': '', 'detail': '销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00', 'price': '0.00', 'buy_price': '0.00', 'status': 100, 'vaild_time': '2025-07-07 10:40:03', 'sign_time': '2025-07-07 10:40:03', 'finish_time': '2025-07-07 10:40:03', 'status_name': '回收完成', 'express_number': 'JDX041010225820', 'refund_express_number': None, 'create_time': '2025-07-01 16:15:27', 'yws_order_id': 0, 'yws_order_express': 0, 'source_name': '小智换新 - 京东平台', 'order_type': '回收订单', 'recover_item': {'goods_type': 0, 'item_code': '002222', 'item_brand': '其他', 'item_cates': '冷风扇', 'item_model': '无', 'item_name': '其他-冷风扇-无', 'item_price': '0.00', 'info': '商品不存在'}, 'new_item': '', 'is_follow': 0, 'sale_tag': None}
2025-07-07 17:10:46.253 [INFO] conveyor_control_api - 提取订单字段 - source: 48, price: 0.00
2025-07-07 17:10:46.253 [INFO] conveyor_control_api - 订单信息规则判断：source(48) != 48 && price(0.0) > 5.0 = false，传送带方向向右
2025-07-07 17:10:46.253 [INFO] conveyor_control_api - 传送带控制结果- message: 传送带向右滚动  data : 1
2025-07-07 17:11:03.084 [INFO] conveyor_control_api - 请求控制传送带，参数: city='其他' warehouse_name='其他' orderId='JDX041010225820' weight=0.0 timestamp='2025-06-17 12:45:27' conveyorId='设备编号' remark=None operator=None deviceId=None
2025-07-07 17:11:03.084 [INFO] conveyor_control_api - 提取字段 - 城市: 其他, 仓库名称: 其他
2025-07-07 17:11:03.084 [INFO] conveyor_control_api - 仓库规则判断：'其他' -> 未匹配特定规则，使用默认订单信息判断
2025-07-07 17:11:03.085 [INFO] conveyor_control_api - 开始查询订单信息，物流单号: JDX041010225820, 查询类型: express
2025-07-07 17:11:03.255 [INFO] conveyor_control_api - 查询响应数据: {'in_page': 2, 'total': 1, 'page_no': 1, 'page_size': 10, 'page_max': 1, 'list': [{'id': 11278263, 'code': 'HS20250701161527031219686', 'source': 48, 'batch_code': None, 'type': 1, 'sale_order_code': 'RE0125070100356731', 'prov_id': 230000, 'city_id': 230100, 'area_id': 230110, 'address': '黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402', 'prov_name': '黑龙江', 'city_name': '哈尔滨市', 'area_name': '香坊区', 'name': '张', 'phone': '18446426229,,8674#', 'mobile': '', 'detail': '销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00', 'price': '0.00', 'buy_price': '0.00', 'status': 100, 'vaild_time': '2025-07-07 10:40:03', 'sign_time': '2025-07-07 10:40:03', 'finish_time': '2025-07-07 10:40:03', 'status_name': '回收完成', 'express_number': 'JDX041010225820', 'refund_express_number': None, 'create_time': '2025-07-01 16:15:27', 'yws_order_id': 0, 'yws_order_express': 0, 'source_name': '小智换新 - 京东平台', 'order_type': '回收订单', 'recover_item': {'goods_type': 0, 'item_code': '002222', 'item_brand': '其他', 'item_cates': '冷风扇', 'item_model': '无', 'item_name': '其他-冷风扇-无', 'item_price': '0.00', 'info': '商品不存在'}, 'new_item': '', 'is_follow': 0, 'sale_tag': None}]}
2025-07-07 17:11:03.257 [INFO] conveyor_control_api - 订单信息查询成功: {'code': 'HS20250701161527031219686', 're_code': 'JDX041010225820', 'id': 11278263, 'source': 48, 'batch_code': None, 'type': 1, 'sale_order_code': 'RE0125070100356731', 'prov_id': 230000, 'city_id': 230100, 'area_id': 230110, 'address': '黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402', 'prov_name': '黑龙江', 'city_name': '哈尔滨市', 'area_name': '香坊区', 'name': '张', 'phone': '18446426229,,8674#', 'mobile': '', 'detail': '销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00', 'price': '0.00', 'buy_price': '0.00', 'status': 100, 'vaild_time': '2025-07-07 10:40:03', 'sign_time': '2025-07-07 10:40:03', 'finish_time': '2025-07-07 10:40:03', 'status_name': '回收完成', 'express_number': 'JDX041010225820', 'refund_express_number': None, 'create_time': '2025-07-01 16:15:27', 'yws_order_id': 0, 'yws_order_express': 0, 'source_name': '小智换新 - 京东平台', 'order_type': '回收订单', 'recover_item': {'goods_type': 0, 'item_code': '002222', 'item_brand': '其他', 'item_cates': '冷风扇', 'item_model': '无', 'item_name': '其他-冷风扇-无', 'item_price': '0.00', 'info': '商品不存在'}, 'new_item': '', 'is_follow': 0, 'sale_tag': None}
2025-07-07 17:11:03.258 [INFO] conveyor_control_api - 提取订单字段 - source: 48, price: 0.00
2025-07-07 17:11:03.258 [INFO] conveyor_control_api - 订单信息规则判断：source(48) != 48 && price(0.0) > 5.0 = false，传送带方向向右
2025-07-07 17:11:03.258 [INFO] conveyor_control_api - 传送带控制结果- message: 传送带向右滚动  data : 1
2025-07-07 17:32:44.803 [INFO] __main__ - 启动传送带控制API服务，端口: 5016
2025-07-07 17:32:53.887 [INFO] conveyor_control_api - 请求控制传送带，参数: city='其他' warehouse_name='其他' orderId='JDX041010225820' weight=0.0 timestamp='2025-06-17 12:45:27' conveyorId='设备编号' remark=None operator=None deviceId=None
2025-07-07 17:32:53.887 [INFO] conveyor_control_api - 提取字段 - 城市: 其他, 仓库名称: 其他
2025-07-07 17:32:53.887 [WARNING] conveyor_control_api - 城市规则判断：'其他' -> 城市暂未开通
2025-07-07 17:32:59.631 [INFO] conveyor_control_api - 请求控制传送带，参数: city='武汉' warehouse_name='其他' orderId='JDX041010225820' weight=0.0 timestamp='2025-06-17 12:45:27' conveyorId='设备编号' remark=None operator=None deviceId=None
2025-07-07 17:32:59.631 [INFO] conveyor_control_api - 提取字段 - 城市: 武汉, 仓库名称: 其他
2025-07-07 17:32:59.631 [INFO] conveyor_control_api - 城市规则判断：'武汉' -> 武汉暂无特殊规则，传送带方向向右
2025-07-07 17:32:59.631 [INFO] conveyor_control_api - 传送带控制结果- message: 传送带向右滚动  data : 1
2025-07-07 17:35:08.543 [INFO] __main__ - 启动传送带控制API服务，端口: 5016
2025-07-07 17:35:16.919 [INFO] conveyor_control_api - 请求控制传送带，参数: city='武汉' warehouse_name='其他' orderId='JDX041010225820' weight=0.0 timestamp='2025-06-17 12:45:27' conveyorId='设备编号' remark=None operator=None deviceId=None
2025-07-07 17:35:16.919 [INFO] conveyor_control_api - 提取字段 - 城市: 武汉, 仓库名称: 其他
2025-07-07 17:35:16.920 [INFO] conveyor_control_api - 天津规则：开始查询订单信息，物流单号: JDX041010225820, 查询类型: express
2025-07-07 17:35:16.920 [INFO] conveyor_control_api - 订单号Token不存在或已过期，重新获取token
2025-07-07 17:35:17.221 [INFO] conveyor_control_api - 订单号获取新token成功: 2df6fb9cf5a7ebb8a7a77607554bd875, 过期时间: 1751884517221
2025-07-07 17:35:17.403 [INFO] conveyor_control_api - 查询响应数据: {'in_page': 2, 'total': 1, 'page_no': 1, 'page_size': 10, 'page_max': 1, 'list': [{'id': 11278263, 'code': 'HS20250701161527031219686', 'source': 48, 'batch_code': None, 'type': 1, 'sale_order_code': 'RE0125070100356731', 'prov_id': 230000, 'city_id': 230100, 'area_id': 230110, 'address': '黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402', 'prov_name': '黑龙江', 'city_name': '哈尔滨市', 'area_name': '香坊区', 'name': '张', 'phone': '18446426229,,8674#', 'mobile': '', 'detail': '销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00', 'price': '0.00', 'buy_price': '0.00', 'status': 100, 'vaild_time': '2025-07-07 10:40:03', 'sign_time': '2025-07-07 10:40:03', 'finish_time': '2025-07-07 10:40:03', 'status_name': '回收完成', 'express_number': 'JDX041010225820', 'refund_express_number': None, 'create_time': '2025-07-01 16:15:27', 'yws_order_id': 0, 'yws_order_express': 0, 'source_name': '小智换新 - 京东平台', 'order_type': '回收订单', 'recover_item': {'goods_type': 0, 'item_code': '002222', 'item_brand': '其他', 'item_cates': '冷风扇', 'item_model': '无', 'item_name': '其他-冷风扇-无', 'item_price': '0.00', 'info': '商品不存在'}, 'new_item': '', 'is_follow': 0, 'sale_tag': None}]}
2025-07-07 17:35:17.405 [INFO] conveyor_control_api - 天津规则：订单信息查询成功: {'code': 'HS20250701161527031219686', 're_code': 'JDX041010225820', 'id': 11278263, 'source': 48, 'batch_code': None, 'type': 1, 'sale_order_code': 'RE0125070100356731', 'prov_id': 230000, 'city_id': 230100, 'area_id': 230110, 'address': '黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402', 'prov_name': '黑龙江', 'city_name': '哈尔滨市', 'area_name': '香坊区', 'name': '张', 'phone': '18446426229,,8674#', 'mobile': '', 'detail': '销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00', 'price': '0.00', 'buy_price': '0.00', 'status': 100, 'vaild_time': '2025-07-07 10:40:03', 'sign_time': '2025-07-07 10:40:03', 'finish_time': '2025-07-07 10:40:03', 'status_name': '回收完成', 'express_number': 'JDX041010225820', 'refund_express_number': None, 'create_time': '2025-07-01 16:15:27', 'yws_order_id': 0, 'yws_order_express': 0, 'source_name': '小智换新 - 京东平台', 'order_type': '回收订单', 'recover_item': {'goods_type': 0, 'item_code': '002222', 'item_brand': '其他', 'item_cates': '冷风扇', 'item_model': '无', 'item_name': '其他-冷风扇-无', 'item_price': '0.00', 'info': '商品不存在'}, 'new_item': '', 'is_follow': 0, 'sale_tag': None}
2025-07-07 17:35:17.405 [INFO] conveyor_control_api - 天津规则：提取订单字段 - source: 48, price: 0.00
2025-07-07 17:35:17.405 [INFO] conveyor_control_api - 天津规则判断：source(48) != 48 && price(0.0) > 5.0 = false，传送带方向向右
2025-07-07 17:35:17.405 [INFO] conveyor_control_api - 传送带控制结果- message: 传送带向右滚动  data : 1
2025-07-07 17:36:46.067 [INFO] __main__ - 启动传送带控制API服务，端口: 5016
2025-07-07 17:36:53.863 [INFO] conveyor_control_api - 请求控制传送带，参数: city='天津' warehouse_name='仓库名称' orderId='JDX041010225820' weight=0.0 timestamp='2025-06-17 12:45:27' conveyorId='设备编号' remark=None operator=None deviceId=None
2025-07-07 17:36:53.863 [INFO] conveyor_control_api - 提取字段 - 城市: 天津, 仓库名称: 仓库名称
2025-07-07 17:36:53.863 [INFO] conveyor_control_api - 城市规则判断：'天津' -> 使用天津专属规则
2025-07-07 17:36:53.863 [INFO] conveyor_control_api - 天津规则：开始查询订单信息，物流单号: JDX041010225820, 查询类型: express
2025-07-07 17:36:53.863 [INFO] conveyor_control_api - 订单号Token不存在或已过期，重新获取token
2025-07-07 17:36:54.161 [INFO] conveyor_control_api - 订单号获取新token成功: c1a6adcd1a16896ec435a4feb5a41390, 过期时间: 1751884614161
2025-07-07 17:36:54.370 [INFO] conveyor_control_api - 查询响应数据: {'in_page': 2, 'total': 1, 'page_no': 1, 'page_size': 10, 'page_max': 1, 'list': [{'id': 11278263, 'code': 'HS20250701161527031219686', 'source': 48, 'batch_code': None, 'type': 1, 'sale_order_code': 'RE0125070100356731', 'prov_id': 230000, 'city_id': 230100, 'area_id': 230110, 'address': '黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402', 'prov_name': '黑龙江', 'city_name': '哈尔滨市', 'area_name': '香坊区', 'name': '张', 'phone': '18446426229,,8674#', 'mobile': '', 'detail': '销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00', 'price': '0.00', 'buy_price': '0.00', 'status': 100, 'vaild_time': '2025-07-07 10:40:03', 'sign_time': '2025-07-07 10:40:03', 'finish_time': '2025-07-07 10:40:03', 'status_name': '回收完成', 'express_number': 'JDX041010225820', 'refund_express_number': None, 'create_time': '2025-07-01 16:15:27', 'yws_order_id': 0, 'yws_order_express': 0, 'source_name': '小智换新 - 京东平台', 'order_type': '回收订单', 'recover_item': {'goods_type': 0, 'item_code': '002222', 'item_brand': '其他', 'item_cates': '冷风扇', 'item_model': '无', 'item_name': '其他-冷风扇-无', 'item_price': '0.00', 'info': '商品不存在'}, 'new_item': '', 'is_follow': 0, 'sale_tag': None}]}
2025-07-07 17:36:54.372 [INFO] conveyor_control_api - 天津规则：提取订单字段 - source: 48, price: 0.00
2025-07-07 17:36:54.372 [INFO] conveyor_control_api - 天津规则判断：source(48) != 48 && price(0.0) > 5.0 = false，传送带方向向右
2025-07-07 17:36:54.372 [INFO] conveyor_control_api - 传送带控制结果- message: 传送带向右滚动  data : 1
2025-07-07 17:37:28.422 [INFO] conveyor_control_api - 请求控制传送带，参数: city='武汉' warehouse_name='仓库名称' orderId='JDX041010225820' weight=0.0 timestamp='2025-06-17 12:45:27' conveyorId='设备编号' remark=None operator=None deviceId=None
2025-07-07 17:37:28.423 [INFO] conveyor_control_api - 提取字段 - 城市: 武汉, 仓库名称: 仓库名称
2025-07-07 17:37:28.423 [INFO] conveyor_control_api - 天津规则：开始查询订单信息，物流单号: JDX041010225820, 查询类型: express
2025-07-07 17:37:28.589 [INFO] conveyor_control_api - 查询响应数据: {'in_page': 2, 'total': 1, 'page_no': 1, 'page_size': 10, 'page_max': 1, 'list': [{'id': 11278263, 'code': 'HS20250701161527031219686', 'source': 48, 'batch_code': None, 'type': 1, 'sale_order_code': 'RE0125070100356731', 'prov_id': 230000, 'city_id': 230100, 'area_id': 230110, 'address': '黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402', 'prov_name': '黑龙江', 'city_name': '哈尔滨市', 'area_name': '香坊区', 'name': '张', 'phone': '18446426229,,8674#', 'mobile': '', 'detail': '销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00', 'price': '0.00', 'buy_price': '0.00', 'status': 100, 'vaild_time': '2025-07-07 10:40:03', 'sign_time': '2025-07-07 10:40:03', 'finish_time': '2025-07-07 10:40:03', 'status_name': '回收完成', 'express_number': 'JDX041010225820', 'refund_express_number': None, 'create_time': '2025-07-01 16:15:27', 'yws_order_id': 0, 'yws_order_express': 0, 'source_name': '小智换新 - 京东平台', 'order_type': '回收订单', 'recover_item': {'goods_type': 0, 'item_code': '002222', 'item_brand': '其他', 'item_cates': '冷风扇', 'item_model': '无', 'item_name': '其他-冷风扇-无', 'item_price': '0.00', 'info': '商品不存在'}, 'new_item': '', 'is_follow': 0, 'sale_tag': None}]}
2025-07-07 17:37:28.591 [INFO] conveyor_control_api - 天津规则：提取订单字段 - source: 48, price: 0.00
2025-07-07 17:37:28.592 [INFO] conveyor_control_api - 天津规则判断：source(48) != 48 && price(0.0) > 5.0 = false，传送带方向向右
2025-07-07 17:37:28.592 [INFO] conveyor_control_api - 传送带控制结果- message: 传送带向右滚动  data : 1
2025-07-07 18:18:01.680 [INFO] conveyor_control_api - 请求控制传送带，参数: city='武汉' warehouse_name='仓库名称' orderId='JDX041010225820' weight=0.0 timestamp='2025-06-17 12:45:27' conveyorId='设备编号' remark=None operator=None deviceId=None
2025-07-07 18:18:01.681 [INFO] conveyor_control_api - 提取字段 - 城市: 武汉, 仓库名称: 仓库名称
2025-07-07 18:18:01.681 [INFO] conveyor_control_api - 天津规则：开始查询订单信息，物流单号: JDX041010225820, 查询类型: express
2025-07-07 18:18:01.683 [INFO] conveyor_control_api - 订单号Token不存在或已过期，重新获取token
2025-07-07 18:18:01.999 [INFO] conveyor_control_api - 订单号获取新token成功: 99411940affeb07cf2c29378e97320c6, 过期时间: 1751887081999
2025-07-07 18:18:02.167 [INFO] conveyor_control_api - 查询响应数据: {'in_page': 2, 'total': 1, 'page_no': 1, 'page_size': 10, 'page_max': 1, 'list': [{'id': 11278263, 'code': 'HS20250701161527031219686', 'source': 48, 'batch_code': None, 'type': 1, 'sale_order_code': 'RE0125070100356731', 'prov_id': 230000, 'city_id': 230100, 'area_id': 230110, 'address': '黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402', 'prov_name': '黑龙江', 'city_name': '哈尔滨市', 'area_name': '香坊区', 'name': '张', 'phone': '18446426229,,8674#', 'mobile': '', 'detail': '销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00', 'price': '0.00', 'buy_price': '0.00', 'status': 100, 'vaild_time': '2025-07-07 10:40:03', 'sign_time': '2025-07-07 10:40:03', 'finish_time': '2025-07-07 10:40:03', 'status_name': '回收完成', 'express_number': 'JDX041010225820', 'refund_express_number': None, 'create_time': '2025-07-01 16:15:27', 'yws_order_id': 0, 'yws_order_express': 0, 'source_name': '小智换新 - 京东平台', 'order_type': '回收订单', 'recover_item': {'goods_type': 0, 'item_code': '002222', 'item_brand': '其他', 'item_cates': '冷风扇', 'item_model': '无', 'item_name': '其他-冷风扇-无', 'item_price': '0.00', 'info': '商品不存在'}, 'new_item': '', 'is_follow': 0, 'sale_tag': None}]}
2025-07-07 18:18:02.169 [INFO] conveyor_control_api - 天津规则判断：source(48) != 48 && price(0.0) > 5.0 = false，传送带方向向右
2025-07-07 18:18:02.169 [INFO] conveyor_control_api - 传送带控制结果- message: 传送带向右滚动  data : 1
2025-07-08 08:59:12.845 [INFO] conveyor_control_api - 提取字段 - 城市: 武汉, 仓库名称: 仓库名称
2025-07-08 08:59:12.845 [INFO] conveyor_control_api - 天津规则：开始查询订单信息，物流单号: JDX041010225820, 查询类型: express
2025-07-08 08:59:12.845 [INFO] conveyor_control_api - 订单号Token不存在或已过期，重新获取token
2025-07-08 08:59:13.033 [INFO] conveyor_control_api - 订单号获取新token成功: 2f751bfd863fb9d9247021fdc7551c92, 过期时间: 1751939953033
2025-07-08 08:59:13.194 [INFO] conveyor_control_api - 查询响应数据: {'in_page': 2, 'total': 1, 'page_no': 1, 'page_size': 10, 'page_max': 1, 'list': [{'id': 11278263, 'code': 'HS20250701161527031219686', 'source': 48, 'batch_code': None, 'type': 1, 'sale_order_code': 'RE0125070100356731', 'prov_id': 230000, 'city_id': 230100, 'area_id': 230110, 'address': '黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402', 'prov_name': '黑龙江', 'city_name': '哈尔滨市', 'area_name': '香坊区', 'name': '张', 'phone': '18446426229,,8674#', 'mobile': '', 'detail': '销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00', 'price': '0.00', 'buy_price': '0.00', 'status': 100, 'vaild_time': '2025-07-07 10:40:03', 'sign_time': '2025-07-07 10:40:03', 'finish_time': '2025-07-07 10:40:03', 'status_name': '回收完成', 'express_number': 'JDX041010225820', 'refund_express_number': None, 'create_time': '2025-07-01 16:15:27', 'yws_order_id': 0, 'yws_order_express': 0, 'source_name': '小智换新 - 京东平台', 'order_type': '回收订单', 'recover_item': {'goods_type': 0, 'item_code': '002222', 'item_brand': '其他', 'item_cates': '冷风扇', 'item_model': '无', 'item_name': '其他-冷风扇-无', 'item_price': '0.00', 'info': '商品不存在'}, 'new_item': '', 'is_follow': 0, 'sale_tag': None}]}
2025-07-08 08:59:13.196 [INFO] conveyor_control_api - 天津规则判断：source(48) != 48 && price(0.0) > 5.0 = false，传送带方向向右
2025-07-08 08:59:13.197 [INFO] conveyor_control_api - 传送带控制结果- message: 传送带向右滚动  data : 1
2025-07-08 08:59:55.956 [INFO] conveyor_control_api - 请求控制传送带，参数: city='武汉' warehouse_name='仓库名称' orderId='JDX041010225820' weight=0.0 timestamp='2025-06-17 12:45:27' conveyorId='设备编号' remark=None operator=None deviceId=None
2025-07-08 08:59:55.956 [INFO] conveyor_control_api - 提取字段 - 城市: 武汉, 仓库名称: 仓库名称
2025-07-08 08:59:55.956 [INFO] conveyor_control_api - 天津规则：开始查询订单信息，物流单号: JDX041010225820, 查询类型: express
2025-07-08 08:59:56.115 [INFO] conveyor_control_api - 查询响应数据: {'in_page': 2, 'total': 1, 'page_no': 1, 'page_size': 10, 'page_max': 1, 'list': [{'id': 11278263, 'code': 'HS20250701161527031219686', 'source': 48, 'batch_code': None, 'type': 1, 'sale_order_code': 'RE0125070100356731', 'prov_id': 230000, 'city_id': 230100, 'area_id': 230110, 'address': '黑龙江哈尔滨市香坊区黎明乡万科智慧未来城B区9栋2单元1402', 'prov_name': '黑龙江', 'city_name': '哈尔滨市', 'area_name': '香坊区', 'name': '张', 'phone': '18446426229,,8674#', 'mobile': '', 'detail': '销售订单号:[323616420141] 期望上门时间:[2025-07-02 00:00:00] 品牌:[其他] 是否为拆装一体:[否] 工单备注:[] 用户备注:[] | 预约时间为:2025-07-02 00:00:00', 'price': '0.00', 'buy_price': '0.00', 'status': 100, 'vaild_time': '2025-07-07 10:40:03', 'sign_time': '2025-07-07 10:40:03', 'finish_time': '2025-07-07 10:40:03', 'status_name': '回收完成', 'express_number': 'JDX041010225820', 'refund_express_number': None, 'create_time': '2025-07-01 16:15:27', 'yws_order_id': 0, 'yws_order_express': 0, 'source_name': '小智换新 - 京东平台', 'order_type': '回收订单', 'recover_item': {'goods_type': 0, 'item_code': '002222', 'item_brand': '其他', 'item_cates': '冷风扇', 'item_model': '无', 'item_name': '其他-冷风扇-无', 'item_price': '0.00', 'info': '商品不存在'}, 'new_item': '', 'is_follow': 0, 'sale_tag': None}]}
2025-07-08 08:59:56.115 [INFO] conveyor_control_api - 天津规则判断：source(48) != 48 && price(0.0) > 5.0 = false，传送带方向向右
2025-07-08 08:59:56.117 [INFO] conveyor_control_api - 传送带控制结果- message: 传送带向右滚动  data : 1
