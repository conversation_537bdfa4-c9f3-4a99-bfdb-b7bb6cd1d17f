#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
传送带控制API - Python FastAPI版本
根据Java controlConveyor方法完全复制实现
端口: 5016
"""

import asyncio
import os
import time
from datetime import datetime
from typing import Optional, Dict, Any, Generic, TypeVar
from threading import Lock

import httpx
import uvicorn
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
from loguru import logger

# 确保logs目录存在（与当前脚本文件同级）
script_dir = os.path.dirname(os.path.abspath(__file__))
logs_dir = os.path.join(script_dir, "logs")
if not os.path.exists(logs_dir):
    os.makedirs(logs_dir)

# 配置loguru日志系统
logger.remove()  # 移除默认处理器

# 添加控制台输出
logger.add(
    sink=lambda msg: print(msg, end=""),
    format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> [<level>{level}</level>] <cyan>{name}</cyan> - <level>{message}</level>",
    level="INFO"
)

# 添加文件输出，每日轮转，保留7天
logger.add(
    sink=os.path.join(logs_dir, "conveyor_control.log"),
    format="{time:YYYY-MM-DD HH:mm:ss.SSS} [{level}] {name} - {message}",
    level="INFO",
    rotation="00:00",  # 每天午夜轮转
    retention="7 days",  # 保留7天
    encoding="utf-8",
    enqueue=True  # 异步写入，提高性能
)

# 泛型类型变量
T = TypeVar('T')

# ==================== 数据模型定义 ====================

class ConveyorControlEntity(BaseModel):
    """传送带控制实体类 - 对应Java ConveyorControlEntity"""
    city: Optional[str] = Field(None, description="城市")
    warehouse_name: Optional[str] = Field(None, description="仓库名称")
    orderId: str = Field(..., description="订单编号")
    weight: float = Field(..., description="重量")
    timestamp: Optional[str] = Field(None, description="时间")
    conveyorId: Optional[str] = Field(None, description="传送带设备编号")
    remark: Optional[str] = Field(None, description="备注信息")
    operator: Optional[str] = Field(None, description="操作员")
    deviceId: Optional[str] = Field(None, description="设备ID")

class Result(BaseModel, Generic[T]):
    """通用响应结果类 - 对应Java Result<T>"""
    code: int = Field(description="响应码")
    message: str = Field(description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")
    success: bool = Field(description="是否成功")

    @classmethod
    def success_result(cls, message: str = "操作成功", data: T = None) -> 'Result[T]':
        """成功响应"""
        return cls(code=200, message=message, data=data, success=True)

    @classmethod
    def error_result(cls, message: str = "操作失败", code: int = 500) -> 'Result[T]':
        """错误响应"""
        return cls(code=code, message=message, data=None, success=False)

class OrderInfo(BaseModel):
    """订单信息模型"""
    source: Optional[int] = Field(None, description="订单来源")
    price: Optional[str] = Field(None, description="订单金额")
    code: Optional[str] = Field(None, description="HS订单号")
    re_code: Optional[str] = Field(None, description="原始订单号")

# ==================== 仓库规则方法 ====================

async def apply_tianjin_warehouse_rule(order_id: str, re_app_order_service) -> int:
    """
    天津专属仓库规则：订单来源（source）不等于48并且订单金额（price）大于5元的方向向左，其他所有情况方向向右

    Args:
        order_id: 订单编号
        re_app_order_service: 订单服务实例

    Returns:
        int: 0表示向左，1表示向右
    """
    direction = 1  # 默认向右

    try:
        key_code = order_id
        key_id = "express"
        logger.info(f"天津规则：开始查询订单信息，物流单号: {key_code}, 查询类型: {key_id}")

        order_result = await re_app_order_service.get_order_info_new(key_code, key_id)
        if order_result.success and order_result.data:
            order_info = order_result.data

            try:
                # 提取订单来源和金额
                source = order_info.get("source")
                price_str = order_info.get("price")

                if source is not None and price_str and price_str.strip():
                    # 将价格字符串转换为数值
                    price = float(price_str)

                    # 天津专属规则：订单来源不等于48并且订单金额大于5元的方向向左，其他情况向右
                    if source != 48 and price > 5.0:
                        direction = 0  # 向左
                        logger.info(f"天津规则判断：source({source}) != 48 && price({price}) > 5.0 = true，传送带方向向左")
                    else:
                        direction = 1  # 向右
                        logger.info(f"天津规则判断：source({source}) != 48 && price({price}) > 5.0 = false，传送带方向向右")
                else:
                    logger.warning(f"天津规则：订单信息中缺少必要字段，使用默认方向（向右）- source: {source}, price: {price_str}")
            except ValueError as e:
                logger.error(f"天津规则：价格字段格式错误，使用默认方向（向右）: {str(e)}")
            except Exception as e:
                logger.error(f"天津规则：处理订单信息异常，使用默认方向（向右）: {str(e)}")
        else:
            logger.warning(f"天津规则：订单信息查询失败，使用默认方向（向右）: {order_result.message}")
    except Exception as e:
        logger.error(f"天津规则：查询订单信息异常，使用默认方向（向右）: {str(e)}")

    return direction

# ==================== ReAppOrder 服务类 ====================

class ReAppOrderService:
    """订单接口服务类 - 对应Java ReAppOrder"""
    
    def __init__(self):
        self.base_url = "https://papi.bearhome.cn"
        self.mobile = "13300000000"
        self.password = "123456"
        self.cached_token = None
        self.token_expire_time = 0
        self.token_valid_duration = 60 * 60 * 1000  # 1小时(毫秒)
        self.token_lock = Lock()
        
    async def login(self) -> Result[str]:
        """登录并获取token - 对应Java login()方法"""
        try:
            # 检查是否有有效的缓存token
            current_time = int(time.time() * 1000)
            if (self.cached_token and 
                self.cached_token.strip() and 
                current_time < self.token_expire_time):
                logger.debug("使用订单号缓存的token")
                return Result.success_result("使用缓存token", self.cached_token)
            
            # 需要重新获取token，使用锁保护
            with self.token_lock:
                # 双重检查
                if (self.cached_token and 
                    self.cached_token.strip() and 
                    int(time.time() * 1000) < self.token_expire_time):
                    logger.debug("订单号双重检查后使用缓存的token")
                    return Result.success_result("使用缓存token", self.cached_token)
                
                # 重新获取token
                logger.info("订单号Token不存在或已过期，重新获取token")
                
                url = f"{self.base_url}/sys/test/login"
                headers = {"version": "1.0", "Content-Type": "application/json"}
                request_body = {"mobile": self.mobile, "password": self.password}
                
                async with httpx.AsyncClient() as client:
                    response = await client.post(url, json=request_body, headers=headers)
                    response_data = response.json()
                    
                logger.debug(f"订单号登录响应结果: {response_data}")
                
                code = response_data.get("code")
                if code == 200 and response_data.get("data"):
                    token = response_data["data"].get("admToken")
                    if not token:
                        logger.error("订单号登录成功但未获取到token")
                        return Result.error_result("登录成功但未获取到token")
                    
                    # 更新缓存
                    self.cached_token = token
                    self.token_expire_time = int(time.time() * 1000) + self.token_valid_duration
                    logger.info(f"订单号获取新token成功: {token}, 过期时间: {self.token_expire_time}")
                    
                    return Result.success_result("登录成功", token)
                else:
                    error_msg = response_data.get("msg", "登录失败")
                    logger.error(f"订单号登录失败: {error_msg}")
                    return Result.error_result(f"登录失败: {error_msg}")
                    
        except Exception as e:
            logger.error(f"订单号获取token异常: {str(e)}")
            return Result.error_result(f"登录异常: {str(e)}")
    
    async def get_valid_token(self) -> Optional[str]:
        """获取有效token - 对应Java getValidToken()方法"""
        try:
            login_result = await self.login()
            if login_result.success:
                return login_result.data
            return None
        except Exception as e:
            logger.error(f"订单号获取token异常: {str(e)}")
            return None
    
    async def get_order_info_new(self, key_code: str, key_id: str) -> Result[Dict[str, Any]]:
        """获取订单信息 - 对应Java getOrderInfoNew()方法"""
        try:
            # 参数校验
            if not key_code or not key_code.strip():
                return Result.error_result("keyCode不能为空")
            
            logger.debug(f"开始查询信息，单号: {key_code}")
            
            # 获取有效token
            token = await self.get_valid_token()
            if not token:
                return Result.error_result("获取订单号信息失败: 无法获取有效token")
            
            # 设置请求URL
            url = f"{self.base_url}/hs/admin/order"
            
            # 设置请求头
            headers = {
                "admtoken": token,
                "Content-Type": "application/json"
            }
            
            # 设置请求体
            request_body = {
                "page_size": 10,
                "page_no": 1,
                "key_id": key_id,
                "key_code": key_code
            }
            
            # 发送POST请求
            async with httpx.AsyncClient() as client:
                response = await client.post(url, json=request_body, headers=headers)
                response_data = response.json()
                
            logger.debug(f"单号查询结果: {response_data}")
            
            # 解析结果
            code = response_data.get("code")
            if code == 200 and response_data.get("data"):
                data = response_data["data"]
                logger.info(f"查询响应数据: {data}")
                
                if data.get("list") and len(data["list"]) > 0:
                    order = data["list"][0]
                    hs_order_code = order.get("code")
                    
                    # 构建结果
                    result = {
                        "code": hs_order_code,
                        "re_code": key_code,
                        **order  # 复制订单信息到结果中
                    }
                    
                    return Result.success_result("获取订单信息成功", result)
                else:
                    logger.error(f"未找到订单号信息: {key_code}")
                    return Result.error_result("未找到订单信息")
            else:
                error_msg = response_data.get("msg", "获取订单号信息失败")
                logger.error(f"获取单号信息失败: {error_msg}")
                return Result.error_result(f"获取订单号信息失败: {error_msg}")
                
        except Exception as e:
            logger.error(f"查询订单号信息异常: {str(e)}")
            return Result.error_result(f"查询订单号信息异常: {str(e)}")

# ==================== FastAPI 应用 ====================

app = FastAPI(
    title="传送带控制API",
    description="根据Java controlConveyor方法完全复制实现",
    version="1.0.0"
)

# 创建服务实例
re_app_order_service = ReAppOrderService()

@app.post("/controlConveyor", response_model=Result[int])
async def control_conveyor(conveyor_control_entity: ConveyorControlEntity) -> Result[int]:
    """
    传送带控制接口 - 完全对应Java controlConveyor方法
    根据传入参数控制传送带左右滚动方向
    返回值: 0表示向左滚动，1表示向右滚动
    天津判断规则：订单来源（source） 不等于 48 并且订单金额（price）大于5元的 方向向左
           其他所有情况 方向向右
    """
    # 参数验证 - 对应Java AssertUtils.assertNotEmpty
    if not conveyor_control_entity.orderId or not conveyor_control_entity.orderId.strip():
        logger.info("订单编号不能为空")
        return Result.error_result("订单编号不能为空")
    
    if conveyor_control_entity.weight is None:
        logger.info("重量不能为空")
        return Result.error_result("重量不能为空")
    
    logger.info(f"请求控制传送带，参数: {conveyor_control_entity}")

    # 提取字段
    city = conveyor_control_entity.city #  城市字段
    warehouse_name = conveyor_control_entity.warehouse_name # 仓库名称字段
    conveyor_id = conveyor_control_entity.conveyorId # 传送带设备编号字段
    order_id = conveyor_control_entity.orderId.split('-')[0]  # 订单编号字段
    logger.info(f"提取字段 - 城市: {city}, 仓库名称: {warehouse_name} , 订单编号: {order_id}, 传送带设备编号: {conveyor_id}")

    direction = 1  # 默认向右

    # 城市判断规则
    if not city or not city.strip():
        logger.error("城市字段为空")
        return Result.error_result("城市字段不能为空")

    city_clean = city.strip() # 去除空格

    # 天津：使用天津专属规则
    if city_clean == "天津" or "天津" in city_clean:
        logger.info(f"城市规则判断：'{city}' -> 使用天津专属规则")
        direction = await apply_tianjin_warehouse_rule(order_id, re_app_order_service)

    # 武汉：暂时没有规则
    elif city_clean == "武汉" or "武汉" in city_clean:
        # 使用天津仓库规则
        direction = 0 # 武汉向左滚动 0805修改

    # 其他城市：默认向右
    else:
        direction = 1  # 向右
        logger.info(f"其他城市：'{city}' -> 暂无特殊规则，默认向右")

    # 返回最终结果
    try:
        message = "传送带向左滚动" if direction == 0 else "传送带向右滚动"
        logger.info(f"传送带控制结果- message: {message}  data : {direction}")
        return Result.success_result(message, direction)
    except Exception as e:
        logger.error(f"传送带控制异常: {str(e)}")
        return Result.error_result("传送带控制异常，请联系管理员处理")

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "ok", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    logger.info("启动传送带控制API服务，端口: 5016")
    uvicorn.run(
        "conveyor_control_api:app",
        host="0.0.0.0",
        port=5016,
        reload=True,
        log_level="info"
    )
