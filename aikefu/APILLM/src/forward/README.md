# 传送带控制API转发服务

这是一个将传送带控制请求转发到目标服务器的FastAPI应用程序。该程序已打包为可执行文件(EXE)，可以直接运行，无需安装Python环境。

## 功能特点

- 转发传送带控制请求到指定的目标URL
- 支持通过配置文件修改目标URL、端口等参数
- 提供健康检查接口
- 提供配置查看接口
- 自动生成默认配置文件（如果不存在）
- **系统托盘运行** - 程序自动最小化到系统托盘，无窗口运行
- **开机自启动** - 首次运行时自动设置开机自启动，可通过托盘菜单控制
- **日志管理** - 每天自动生成新的日志文件，最多保留7天
- **静默运行** - 启动后无控制台窗口，不干扰正常工作

## 配置文件说明

配置文件`config.json`需要与可执行文件放在同一目录下，格式如下：

```json
{
    "target_url": "https://uat-chat.juranguanjia.com/api/evaluation/controlConveyor",
    "host": "127.0.0.1",
    "port": 8121,
    "api_path": "/api/controlConveyor",
    "city": "默认城市",
    "warehouse_name": "默认仓库",
    "log_level": "info",
    "timeout": 300.0,
    "verify_ssl": false
}
```

配置项说明：
- `target_url`：目标服务器URL，所有请求将被转发到此URL
- `host`：本地服务绑定的主机地址，默认为"127.0.0.1"
- `port`：本地服务绑定的端口，默认为8121
- `api_path`: 本地服务的API路径，默认为"/api/controlConveyor"
- `city`: 城市名称，将作为额外字段添加到转发的请求体中
- `warehouse_name`: 仓库名称，将作为额外字段添加到转发的请求体中
- `log_level`：日志级别，可选值为"debug"、"info"、"warning"、"error"、"critical"
- `timeout`：请求超时时间（秒），默认为300秒
- `verify_ssl`：是否验证SSL证书，默认为false（生产环境建议设置为true）

## 使用方法

### 运行可执行文件

1. 解压下载的压缩包到任意目录
2. 根据需要修改`config.json`文件中的配置
3. 双击运行`转发服务.exe`
4. 程序将自动最小化到系统托盘（屏幕右下角）
5. **首次运行时**将会自动:
   - 创建默认配置文件（如果不存在）
   - 创建日志目录
   - 设置开机自启动

### 系统托盘功能

右键点击系统托盘图标，可以:
1. **打开API文档** - 在默认浏览器中打开API文档页面
2. **开机自启动** - 勾选或取消设置开机自启动
3. **退出** - 完全关闭程序

### 日志管理

日志文件存储在程序所在目录下的`logs`文件夹中，按日期命名格式为`conveyor_api_YYYY-MM-DD.log`。系统会自动：
1. 每天生成新的日志文件
2. 最多保留7天的日志记录
3. 自动清理过期日志

### API接口

1. **传送带控制接口**
   - URL: `/api/controlConveyor` (可通过`config.json`中的`api_path`修改)
   - 方法: `POST`
   - 请求体示例:
     ```json
     {
       "conveyorId": "001",
       "orderId": "ORDER123456",
       "weight": 10.5,
       "timestamp": "2023-09-01T12:00:00"
     }
     ```

2. **健康检查接口**
   - URL: `/health`
   - 方法: `GET`

3. **配置查看接口**
   - URL: `/config`
   - 方法: `GET`

4. **根路径接口**
   - URL: `/`
   - 方法: `GET`

## 自行打包说明

如果需要自行打包程序，请按照以下步骤操作：

1. 确保已安装Python 3.7或更高版本
2. 安装所需依赖：`pip install -r requirements.txt` (需要先创建 `requirements.txt` 文件)
3. 运行打包脚本：`python build.py`
4. 打包完成后，可在`dist`目录中找到生成的可执行文件

**注意**: 打包命令已在 `build.py` 中配置为 `--noconsole`，这会在Windows上创建无控制台窗口的应用程序。

## 常见问题

1. **服务无法启动或托盘图标未显示**
   - 检查 `logs` 目录下的 `main_app` 和 `fastapi_app` 日志文件，查看是否有错误信息。
   - 确保端口未被其他程序占用。

2. **无法连接到目标服务器**
   - 检查网络连接和目标URL是否正确。
   - 查看 `fastapi_app` 日志文件，获取详细的请求错误信息。

3. **开机自启动不生效**
   - 此功能需要管理员权限才能正确设置。请尝试以管理员身份运行一次程序。
   - 检查Windows任务管理器的"启动"选项卡，确保应用没有被禁用。

