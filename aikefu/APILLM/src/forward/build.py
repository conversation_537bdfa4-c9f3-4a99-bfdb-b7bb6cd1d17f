import os
import sys
import subprocess
import shutil
import platform
from pathlib import Path

# --- Helper Functions ---

def clean_build():
    """清理旧的构建文件和目录"""
    print("正在清理旧的构建文件...")
    
    # 删除目录
    for dir_name in ['build', 'dist']:
        if os.path.isdir(dir_name):
            shutil.rmtree(dir_name)
            print(f"已删除目录: {dir_name}")
            
    # 删除spec文件
    for file in os.listdir('.'):
        if file.endswith('.spec'):
            os.remove(file)
            print(f"已删除spec文件: {file}")

def check_pyinstaller():
    """检查是否安装了PyInstaller"""
    try:
        subprocess.run([sys.executable, "-m", "pip", "show", "pyinstaller"], 
                      stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
        return True
    except subprocess.CalledProcessError:
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        return True
    except subprocess.CalledProcessError:
        return False

def check_dependencies():
    """检查所需依赖是否安装"""
    dependencies = ["pyside6", "pywin32"]
    missing = []
    
    for dep in dependencies:
        try:
            subprocess.run([sys.executable, "-m", "pip", "show", dep],
                         stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
        except subprocess.CalledProcessError:
            missing.append(dep)
    
    return missing
    
def install_dependencies(deps):
    """安装缺失的依赖"""
    for dep in deps:
        try:
            print(f"安装依赖: {dep}...")
            subprocess.run([sys.executable, "-m", "pip", "install", dep], check=True)
        except subprocess.CalledProcessError as e:
            print(f"安装 {dep} 失败: {e}")
            return False
    return True

# --- Main Build Logic ---

def build_exe():
    """构建EXE文件"""
    # 1. 清理环境
    clean_build()

    current_os = platform.system()
    if current_os != "Windows":
        print("错误: 此构建脚本仅配置为在Windows上运行。")
        return False
    
    print(f"当前操作系统: {current_os}")
    
    # 检查是否安装了PyInstaller
    if not check_pyinstaller():
        print("未检测到PyInstaller，尝试安装...")
        if not install_pyinstaller():
            print("安装PyInstaller失败，请手动安装后重试。")
            print("可以使用命令: pip install pyinstaller")
            return False
        print("PyInstaller安装成功！")
    
    # 检查依赖
    missing_deps = check_dependencies()
    if missing_deps:
        print(f"检测到缺失的依赖: {', '.join(missing_deps)}")
        print("尝试安装缺失的依赖...")
        if not install_dependencies(missing_deps):
            print("安装依赖失败，请手动安装后重试。")
            print(f"可以使用命令: pip install {' '.join(missing_deps)}")
            return False
        print("所有依赖安装成功！")
    
    # 2. 准备打包命令
    app_name = "熊洞科技-转发服务"
    script_to_package = "转发.py"
    
    icon_path = Path("icon.ico").resolve()
    icon_option = ["--icon", str(icon_path)] if icon_path.exists() else []
    if icon_option:
        print(f"使用图标: {icon_path}")
    else:
        print("警告: 找不到图标文件 'icon.ico'，将使用默认图标。")

    # 3. 执行打包命令
    print("开始打包...")
    cmd = [
        sys.executable, 
        "-m", "PyInstaller",
        f"--name={app_name}",
        "--onefile",
        "--noconsole",
        "--hidden-import=pkg_resources.py2_warn",
        f"--add-data={Path('config.json').resolve()};.",
        *icon_option,
        script_to_package
    ]
    
    try:
        # 强制使用UTF-8编码来捕获输出，避免在Windows上出现gbk解码错误
        result = subprocess.run(
            cmd, check=True, capture_output=True, encoding='utf-8'
        )
        print("打包命令执行完成。")
        print("\n--- PyInstaller 输出 ---")
        print(result.stdout)
        print("------------------------\n")
        
        # 4. 后处理
        dist_dir = Path("dist")
        exe_path = dist_dir / f"{app_name}.exe"

        if exe_path.exists():
            print(f"EXE文件已生成: {exe_path}")
            (dist_dir / "logs").mkdir(exist_ok=True)
            print(f"日志目录已创建: {dist_dir / 'logs'}")
            return True
        else:
            print(f"错误: 未找到生成的EXE文件: {exe_path}")
            return False
        
    except subprocess.CalledProcessError as e:
        print("\n打包过程中出错:")
        print("--- STDOUT ---")
        print(e.stdout)
        print("\n--- STDERR ---")
        print(e.stderr)
        print("--------------\n")
        return False

if __name__ == "__main__":
    print("====== 熊洞科技 - 传送带控制API转发服务打包工具 ======")
    
    success = build_exe()
    
    if success:
        print("\n===== 打包成功 =====")
        print("你可以在 'dist' 目录中找到生成的可执行文件。")
    else:
        print("\n===== 打包失败 =====")
        print("请检查上面的错误信息并重试。")
    
    input("\n按回车键退出...") 