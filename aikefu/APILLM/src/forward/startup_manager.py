"""
Windows开机自启动管理模块
支持注册表和启动文件夹两种方式
"""
import os
import sys
import logging
import winreg
import shutil
from pathlib import Path
from typing import Tuple, Optional


class StartupManager:
    """Windows开机自启动管理器"""
    
    def __init__(self, app_name: str = "熊洞科技-转发服务"):
        self.app_name = app_name
        self.exe_path = self._get_exe_path()
        self.logger = logging.getLogger("startup_manager")
        
        # 注册表路径
        self.registry_key = r"Software\Microsoft\Windows\CurrentVersion\Run"
        
        # 启动文件夹路径
        self.startup_folder = Path(os.path.expandvars(
            r"%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup"
        ))
    
    def _get_exe_path(self) -> str:
        """获取当前exe文件的完整路径"""
        if getattr(sys, 'frozen', False):
            # 打包后的exe文件
            return sys.executable
        else:
            # 开发环境
            return os.path.abspath(__file__)
    
    def check_startup_status(self) -> Tuple[bool, str]:
        """
        检查当前自启动状态
        
        Returns:
            Tuple[bool, str]: (是否已设置自启动, 设置方式描述)
        """
        # 检查注册表方式
        if self._check_registry_startup():
            return True, "注册表"
        
        # 检查启动文件夹方式
        if self._check_folder_startup():
            return True, "启动文件夹"
        
        return False, "未设置"
    
    def _check_registry_startup(self) -> bool:
        """检查注册表中是否已设置自启动"""
        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, self.registry_key) as key:
                value, _ = winreg.QueryValueEx(key, self.app_name)
                return value == self.exe_path
        except (FileNotFoundError, OSError):
            return False
    
    def _check_folder_startup(self) -> bool:
        """检查启动文件夹中是否存在快捷方式"""
        shortcut_path = self.startup_folder / f"{self.app_name}.lnk"
        return shortcut_path.exists()
    
    def set_startup(self, method: str = "auto") -> Tuple[bool, str]:
        """
        设置开机自启动
        
        Args:
            method: 设置方式 ("registry", "folder", "auto")
                   auto会优先尝试注册表，失败则使用启动文件夹
        
        Returns:
            Tuple[bool, str]: (是否成功, 结果描述)
        """
        if method == "registry":
            return self._set_registry_startup()
        elif method == "folder":
            return self._set_folder_startup()
        elif method == "auto":
            # 优先尝试注册表方式
            success, msg = self._set_registry_startup()
            if success:
                return success, msg
            
            # 注册表失败，尝试启动文件夹方式
            self.logger.warning(f"注册表方式失败: {msg}，尝试启动文件夹方式")
            return self._set_folder_startup()
        else:
            return False, f"不支持的设置方式: {method}"
    
    def _set_registry_startup(self) -> Tuple[bool, str]:
        """通过注册表设置自启动"""
        try:
            with winreg.OpenKey(
                winreg.HKEY_CURRENT_USER, 
                self.registry_key, 
                0, 
                winreg.KEY_SET_VALUE
            ) as key:
                winreg.SetValueEx(key, self.app_name, 0, winreg.REG_SZ, self.exe_path)
            
            self.logger.info(f"成功通过注册表设置自启动: {self.exe_path}")
            return True, "注册表方式设置成功"
            
        except PermissionError:
            msg = "权限不足，无法修改注册表"
            self.logger.error(msg)
            return False, msg
        except Exception as e:
            msg = f"注册表设置失败: {str(e)}"
            self.logger.error(msg)
            return False, msg
    
    def _set_folder_startup(self) -> Tuple[bool, str]:
        """通过启动文件夹设置自启动"""
        try:
            # 确保启动文件夹存在
            self.startup_folder.mkdir(parents=True, exist_ok=True)
            
            # 创建快捷方式
            shortcut_path = self.startup_folder / f"{self.app_name}.lnk"
            success = self._create_shortcut(str(shortcut_path), self.exe_path)
            
            if success:
                self.logger.info(f"成功通过启动文件夹设置自启动: {shortcut_path}")
                return True, "启动文件夹方式设置成功"
            else:
                msg = "创建快捷方式失败"
                self.logger.error(msg)
                return False, msg
                
        except Exception as e:
            msg = f"启动文件夹设置失败: {str(e)}"
            self.logger.error(msg)
            return False, msg
    
    def _create_shortcut(self, shortcut_path: str, target_path: str) -> bool:
        """创建Windows快捷方式"""
        try:
            import win32com.client
            
            shell = win32com.client.Dispatch("WScript.Shell")
            shortcut = shell.CreateShortCut(shortcut_path)
            shortcut.Targetpath = target_path
            shortcut.WorkingDirectory = os.path.dirname(target_path)
            shortcut.IconLocation = target_path
            shortcut.save()
            
            return True
        except ImportError:
            self.logger.error("缺少pywin32模块，无法创建快捷方式")
            return False
        except Exception as e:
            self.logger.error(f"创建快捷方式失败: {str(e)}")
            return False
    
    def remove_startup(self) -> Tuple[bool, str]:
        """移除开机自启动设置"""
        results = []
        
        # 移除注册表设置
        try:
            with winreg.OpenKey(
                winreg.HKEY_CURRENT_USER, 
                self.registry_key, 
                0, 
                winreg.KEY_SET_VALUE
            ) as key:
                winreg.DeleteValue(key, self.app_name)
            results.append("已移除注册表设置")
            self.logger.info("成功移除注册表自启动设置")
        except FileNotFoundError:
            # 注册表中没有该项，正常情况
            pass
        except Exception as e:
            results.append(f"移除注册表设置失败: {str(e)}")
            self.logger.error(f"移除注册表设置失败: {str(e)}")
        
        # 移除启动文件夹快捷方式
        try:
            shortcut_path = self.startup_folder / f"{self.app_name}.lnk"
            if shortcut_path.exists():
                shortcut_path.unlink()
                results.append("已移除启动文件夹快捷方式")
                self.logger.info("成功移除启动文件夹快捷方式")
        except Exception as e:
            results.append(f"移除启动文件夹快捷方式失败: {str(e)}")
            self.logger.error(f"移除启动文件夹快捷方式失败: {str(e)}")
        
        if results:
            return True, "; ".join(results)
        else:
            return True, "没有找到需要移除的自启动设置"
    
    def toggle_startup(self) -> Tuple[bool, str]:
        """切换自启动状态"""
        is_enabled, current_method = self.check_startup_status()
        
        if is_enabled:
            # 当前已启用，移除设置
            return self.remove_startup()
        else:
            # 当前未启用，设置自启动
            return self.set_startup("auto")


def is_first_run() -> bool:
    """
    检测是否为首次运行
    通过检查配置文件中的标记来判断
    """
    try:
        # 获取配置文件路径
        if getattr(sys, 'frozen', False):
            base_dir = os.path.dirname(sys.executable)
        else:
            base_dir = os.path.dirname(os.path.abspath(__file__))
        
        config_path = os.path.join(base_dir, "config.json")
        
        if not os.path.exists(config_path):
            return True
        
        # 读取配置文件检查是否有首次运行标记
        import json
        with open(config_path, "r", encoding="utf-8") as f:
            config = json.load(f)
        
        return not config.get("_first_run_completed", False)
        
    except Exception:
        # 如果出现任何错误，假设为首次运行
        return True


def mark_first_run_completed():
    """标记首次运行已完成"""
    try:
        # 获取配置文件路径
        if getattr(sys, 'frozen', False):
            base_dir = os.path.dirname(sys.executable)
        else:
            base_dir = os.path.dirname(os.path.abspath(__file__))
        
        config_path = os.path.join(base_dir, "config.json")
        
        # 读取现有配置
        import json
        if os.path.exists(config_path):
            with open(config_path, "r", encoding="utf-8") as f:
                config = json.load(f)
        else:
            config = {}
        
        # 添加首次运行完成标记
        config["_first_run_completed"] = True
        
        # 写回配置文件
        with open(config_path, "w", encoding="utf-8") as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
            
    except Exception as e:
        logging.getLogger("startup_manager").error(f"标记首次运行完成失败: {str(e)}")
