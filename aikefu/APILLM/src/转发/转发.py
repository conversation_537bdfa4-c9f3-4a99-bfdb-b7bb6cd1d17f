import logging
import logging.handlers
import json
import os
import sys
import multiprocessing
import webbrowser
import socket # Import socket for IP address
from datetime import datetime
from typing import Optional, Dict, Any

import httpx
import uvicorn
from fastapi import FastAPI, HTTPException, Depends
from pydantic import BaseModel, Field, validator
from PySide6 import QtWidgets, QtGui, QtCore

# --- 全局变量和配置 ---

BASE_DIR = os.path.dirname(os.path.abspath(__file__ if getattr(sys, 'frozen', False) is False else sys.executable))
LOG_DIR = os.path.join(BASE_DIR, "logs")
CONFIG_PATH = os.path.join(BASE_DIR, "config.json")

api_process = None # 用于持有子进程对象
main_logger = None # 主进程日志记录器

# --- 核心功能函数 ---

def get_local_ip():
    """获取本机局域网IP地址"""
    s = None
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        # 不需要真正发送数据，只需连接到一个外部地址即可获取本地接口IP
        s.connect(('*******', 80))
        ip = s.getsockname()[0]
    except Exception:
        ip = '127.0.0.1' # 如果获取失败，则返回环回地址
    finally:
        if s:
            s.close()
    return ip

def setup_logging(name, config):
    if not os.path.exists(LOG_DIR):
        os.makedirs(LOG_DIR, exist_ok=True)
    
    log_file = os.path.join(LOG_DIR, f"{name}_{datetime.now().strftime('%Y-%m-%d')}.log")
    
    handler = logging.handlers.TimedRotatingFileHandler(
        log_file, when='midnight', interval=1, backupCount=7, encoding='utf-8'
    )
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    
    logger = logging.getLogger(name)
    if logger.hasHandlers():
        logger.handlers.clear()
    logger.addHandler(handler)
    
    log_level = config.get("log_level", "info").upper()
    logger.setLevel(log_level)
    
    return logger

def load_config():
    default_config = {
        "target_url": "https://uat-chat.juranguanjia.com/api/evaluation/controlConveyor",
        "host": "127.0.0.1",
        "port": 8121,
        "api_path": "/api/controlConveyor",
        "city": "默认城市",
        "warehouse_name": "默认仓库",
        "log_level": "info",
        "timeout": 300.0,
        "verify_ssl": False
    }
    if os.path.exists(CONFIG_PATH):
        try:
            with open(CONFIG_PATH, "r", encoding="utf-8") as f:
                default_config.update(json.load(f))
        except json.JSONDecodeError:
            print("配置文件格式错误，将使用默认配置并覆盖。")
            # Fallback to default and overwrite
            with open(CONFIG_PATH, "w", encoding="utf-8") as f:
                json.dump(default_config, f, indent=4)
    else:
        with open(CONFIG_PATH, "w", encoding="utf-8") as f:
            json.dump(default_config, f, indent=4)
            
    return default_config

# --- FastAPI 应用定义 ---

# Load config at module level to define the route
CONFIG = load_config()

def get_app_config():
    # Dependency function now returns the pre-loaded config
    return CONFIG

app = FastAPI(title="传送带控制API转发服务", version="1.1.0")
client = None

class ConveyorControlEntity(BaseModel):
    conveyorId: Optional[str] = Field(None, description="传送带设备编号")
    orderId: str = Field(..., description="订单编号")
    weight: float = Field(..., description="重量")
    timestamp: Optional[str] = Field(None, description="时间")

@app.on_event("startup")
async def startup_event():
    global client
    # config is already loaded globally
    config = CONFIG
    client = httpx.AsyncClient(timeout=config['timeout'], verify=config['verify_ssl'])
    logger = logging.getLogger("uvicorn.error")
    logger.info("FastAPI服务启动，HTTP客户端已创建。")

@app.post(CONFIG.get("api_path", "/api/controlConveyor"))
async def control_conveyor(
    entity: ConveyorControlEntity,
    config: Dict = Depends(get_app_config)
):
    logger = logging.getLogger("uvicorn.error")
    try:
        data = entity.dict(exclude_none=True)
        
        # Add new fields
        data['city'] = config.get('city', '未知城市')
        data['warehouse_name'] = config.get('warehouse_name', '未知仓库')
        data['ip'] = get_local_ip()

        logger.info(f"转发请求到: {config['target_url']}，数据: {data}")
        response = await client.post(config['target_url'], json=data)
        logger.info(f"响应: {response.status_code} - {response.text}")
        response.raise_for_status()
        return response.json()
    except httpx.HTTPStatusError as e:
        logger.error(f"请求失败 (HTTP Status): {e.response.status_code} - {e.response.text}")
        raise HTTPException(status_code=e.response.status_code, detail=e.response.text)
    except Exception as e:
        logger.error(f"转发过程中出现未知错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.on_event("shutdown")
async def shutdown_event():
    if client:
        await client.aclose()
    logger = logging.getLogger("uvicorn.error")
    logger.info("FastAPI服务已关闭。")

def run_fastapi_app(config):
    log_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "()": "uvicorn.logging.DefaultFormatter",
                "fmt": "%(levelprefix)s %(asctime)s - %(message)s",
                "use_colors": False,
            },
            "access": {
                "()": "uvicorn.logging.AccessFormatter",
                "fmt": '%(levelprefix)s %(client_addr)s - "%(request_line)s" %(status_code)s',
                "use_colors": False,
            },
        },
        "handlers": {
            "default": {
                "formatter": "default",
                "class": "logging.handlers.TimedRotatingFileHandler",
                "filename": os.path.join(LOG_DIR, "fastapi_app.log"),
                "when": "midnight",
                "interval": 1,
                "backupCount": 7,
                "encoding": "utf-8",
            },
            "access": {
                "formatter": "access",
                "class": "logging.handlers.TimedRotatingFileHandler",
                "filename": os.path.join(LOG_DIR, "fastapi_access.log"),
                "when": "midnight",
                "interval": 1,
                "backupCount": 7,
                "encoding": "utf-8",
            },
        },
        "loggers": {
            "uvicorn": {"handlers": ["default"], "level": "INFO"},
            "uvicorn.error": {"level": "INFO"},
            "uvicorn.access": {"handlers": ["access"], "level": "INFO", "propagate": False},
        },
    }
    uvicorn.run(
        app,
        host=config["host"],
        port=config["port"],
        log_config=log_config
    )

# --- 系统托盘应用 ---

class TrayApp(QtWidgets.QApplication):
    def __init__(self, argv, config):
        super().__init__(argv)
        self.config = config
        self.setQuitOnLastWindowClosed(False)

        self.tray = QtWidgets.QSystemTrayIcon(self)
        self.tray.setIcon(self.style().standardIcon(QtWidgets.QStyle.SP_ComputerIcon))
        
        menu = QtWidgets.QMenu()
        menu.addAction("打开API文档").triggered.connect(self.open_docs)
        menu.addAction("检查服务状态").triggered.connect(self.check_status)
        menu.addSeparator()
        menu.addAction("退出").triggered.connect(self.quit_app)
        
        self.tray.setContextMenu(menu)
        self.tray.show()
        self.tray.setToolTip(f"转发服务运行在 {self.config['host']}:{self.config['port']}")
        
        QtCore.QTimer.singleShot(2000, self.check_status)

    def open_docs(self):
        url = f"http://{self.config['host']}:{self.config['port']}/docs"
        webbrowser.open(url)

    def check_status(self):
        try:
            with httpx.Client() as sync_client:
                response = sync_client.get(f"http://{self.config['host']}:{self.config['port']}/health")
            if response.status_code == 200:
                self.tray.showMessage("服务状态", "API服务运行正常。", QtWidgets.QSystemTrayIcon.Information, 3000)
            else:
                self.tray.showMessage("服务状态", f"API服务返回异常: {response.status_code}", QtWidgets.QSystemTrayIcon.Warning, 5000)
        except httpx.RequestError:
            self.tray.showMessage("服务状态", "无法连接到API服务。", QtWidgets.QSystemTrayIcon.Critical, 5000)

    def quit_app(self):
        global api_process
        if api_process and api_process.is_alive():
            main_logger.info("正在停止API服务...")
            api_process.terminate()
            api_process.join(timeout=5)
            main_logger.info("API服务已停止。")
        self.quit()

# --- 主程序入口 ---

def main():
    global api_process, main_logger
    
    # config is already loaded at module level as CONFIG
    config = CONFIG
    
    # 2. 设置主进程日志
    main_logger = setup_logging("main_app", config)
    main_logger.info("应用启动...")

    # 3. 启动FastAPI子进程
    api_process = multiprocessing.Process(target=run_fastapi_app, args=(config,), daemon=True)
    api_process.start()
    main_logger.info(f"API服务进程已启动 (PID: {api_process.pid})")
    
    # 4. 启动GUI
    qt_app = TrayApp(sys.argv, config)
    sys.exit(qt_app.exec())

if __name__ == "__main__":
    multiprocessing.freeze_support()
    # 注意: Windows下隐藏控制台的功能在打包时由pyinstaller的 --noconsole 参数实现
    main()