# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['转发.py'],
    pathex=[],
    binaries=[],
    datas=[('D:\\a_project\\aliyun\\xiondon\\aikefu\\APILLM\\src\\转发\\config.json', '.')],
    hiddenimports=['pkg_resources.py2_warn'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='熊洞科技-转发服务',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['D:\\a_project\\aliyun\\xiondon\\aikefu\\APILLM\\src\\转发\\icon.ico'],
)
