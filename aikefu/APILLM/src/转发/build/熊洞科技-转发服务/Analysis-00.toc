(['D:\\a_project\\aliyun\\xiondon\\aikefu\\APILLM\\src\\转发\\转发.py'],
 ['D:\\a_project\\aliyun\\xiondon\\aikefu\\APILLM\\src\\转发'],
 ['pkg_resources.py2_warn'],
 [('D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('D:\\app\\python_3.11\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\app\\python_3.11\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('config.json',
   'D:\\a_project\\aliyun\\xiondon\\aikefu\\APILLM\\src\\转发\\config.json',
   'DATA')],
 '3.11.0 (main, Oct 24 2022, 18:26:48) [MSC v.1933 64 bit (AMD64)]',
 [('pyi_rth_inspect',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyside6',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyside6.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('转发',
   'D:\\a_project\\aliyun\\xiondon\\aikefu\\APILLM\\src\\转发\\转发.py',
   'PYSOURCE')],
 [('subprocess', 'D:\\app\\python_3.11\\Lib\\subprocess.py', 'PYMODULE'),
  ('selectors', 'D:\\app\\python_3.11\\Lib\\selectors.py', 'PYMODULE'),
  ('contextlib', 'D:\\app\\python_3.11\\Lib\\contextlib.py', 'PYMODULE'),
  ('threading', 'D:\\app\\python_3.11\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\app\\python_3.11\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('signal', 'D:\\app\\python_3.11\\Lib\\signal.py', 'PYMODULE'),
  ('_strptime', 'D:\\app\\python_3.11\\Lib\\_strptime.py', 'PYMODULE'),
  ('calendar', 'D:\\app\\python_3.11\\Lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'D:\\app\\python_3.11\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\app\\python_3.11\\Lib\\textwrap.py', 'PYMODULE'),
  ('shutil', 'D:\\app\\python_3.11\\Lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'D:\\app\\python_3.11\\Lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'D:\\app\\python_3.11\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\app\\python_3.11\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'D:\\app\\python_3.11\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\app\\python_3.11\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\app\\python_3.11\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\app\\python_3.11\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\app\\python_3.11\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\app\\python_3.11\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\app\\python_3.11\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\app\\python_3.11\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\app\\python_3.11\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\app\\python_3.11\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'D:\\app\\python_3.11\\Lib\\tempfile.py', 'PYMODULE'),
  ('random', 'D:\\app\\python_3.11\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'D:\\app\\python_3.11\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'D:\\app\\python_3.11\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\app\\python_3.11\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\app\\python_3.11\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'D:\\app\\python_3.11\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\app\\python_3.11\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\app\\python_3.11\\Lib\\hashlib.py', 'PYMODULE'),
  ('bisect', 'D:\\app\\python_3.11\\Lib\\bisect.py', 'PYMODULE'),
  ('importlib._abc',
   'D:\\app\\python_3.11\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\app\\python_3.11\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\app\\python_3.11\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\app\\python_3.11\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\app\\python_3.11\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\app\\python_3.11\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\app\\python_3.11\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'D:\\app\\python_3.11\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'D:\\app\\python_3.11\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\app\\python_3.11\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\app\\python_3.11\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string', 'D:\\app\\python_3.11\\Lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\app\\python_3.11\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\app\\python_3.11\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'D:\\app\\python_3.11\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators',
   'D:\\app\\python_3.11\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\app\\python_3.11\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\app\\python_3.11\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\app\\python_3.11\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\app\\python_3.11\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset', 'D:\\app\\python_3.11\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders',
   'D:\\app\\python_3.11\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\app\\python_3.11\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\app\\python_3.11\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'D:\\app\\python_3.11\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'D:\\app\\python_3.11\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'D:\\app\\python_3.11\\Lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr',
   'D:\\app\\python_3.11\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('urllib.parse', 'D:\\app\\python_3.11\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('quopri', 'D:\\app\\python_3.11\\Lib\\quopri.py', 'PYMODULE'),
  ('email', 'D:\\app\\python_3.11\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'D:\\app\\python_3.11\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser',
   'D:\\app\\python_3.11\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv', 'D:\\app\\python_3.11\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'D:\\app\\python_3.11\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\app\\python_3.11\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\app\\python_3.11\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize', 'D:\\app\\python_3.11\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\app\\python_3.11\\Lib\\token.py', 'PYMODULE'),
  ('pathlib', 'D:\\app\\python_3.11\\Lib\\pathlib.py', 'PYMODULE'),
  ('struct', 'D:\\app\\python_3.11\\Lib\\struct.py', 'PYMODULE'),
  ('importlib.util',
   'D:\\app\\python_3.11\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('tarfile', 'D:\\app\\python_3.11\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\app\\python_3.11\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'D:\\app\\python_3.11\\Lib\\_compression.py', 'PYMODULE'),
  ('lzma', 'D:\\app\\python_3.11\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\app\\python_3.11\\Lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'D:\\app\\python_3.11\\Lib\\fnmatch.py', 'PYMODULE'),
  ('copy', 'D:\\app\\python_3.11\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\app\\python_3.11\\Lib\\gettext.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client', 'D:\\app\\python_3.11\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'D:\\app\\python_3.11\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\app\\python_3.11\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\app\\python_3.11\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'D:\\app\\python_3.11\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\app\\python_3.11\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\app\\python_3.11\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\app\\python_3.11\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass', 'D:\\app\\python_3.11\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'D:\\app\\python_3.11\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\app\\python_3.11\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\app\\python_3.11\\Lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'D:\\app\\python_3.11\\Lib\\shlex.py', 'PYMODULE'),
  ('mimetypes', 'D:\\app\\python_3.11\\Lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\app\\python_3.11\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http', 'D:\\app\\python_3.11\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.cookies', 'D:\\app\\python_3.11\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('ssl', 'D:\\app\\python_3.11\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'D:\\app\\python_3.11\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error', 'D:\\app\\python_3.11\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('xml.sax', 'D:\\app\\python_3.11\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler',
   'D:\\app\\python_3.11\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\app\\python_3.11\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\app\\python_3.11\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client', 'D:\\app\\python_3.11\\Lib\\http\\client.py', 'PYMODULE'),
  ('hmac', 'D:\\app\\python_3.11\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.context',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes', 'D:\\app\\python_3.11\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian',
   'D:\\app\\python_3.11\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'D:\\app\\python_3.11\\Lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\app\\python_3.11\\Lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('pickle', 'D:\\app\\python_3.11\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\app\\python_3.11\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'D:\\app\\python_3.11\\Lib\\dataclasses.py', 'PYMODULE'),
  ('inspect', 'D:\\app\\python_3.11\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\app\\python_3.11\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\app\\python_3.11\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\app\\python_3.11\\Lib\\ast.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\app\\python_3.11\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'D:\\app\\python_3.11\\Lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'D:\\app\\python_3.11\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\app\\python_3.11\\Lib\\zipimport.py', 'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('tracemalloc', 'D:\\app\\python_3.11\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('_py_abc', 'D:\\app\\python_3.11\\Lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'D:\\app\\python_3.11\\Lib\\stringprep.py', 'PYMODULE'),
  ('shiboken6',
   'D:\\app\\python_3.11\\Lib\\site-packages\\shiboken6\\__init__.py',
   'PYMODULE'),
  ('__future__', 'D:\\app\\python_3.11\\Lib\\__future__.py', 'PYMODULE'),
  ('PySide6',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\__init__.py',
   'PYMODULE'),
  ('PySide6.support.deprecated',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\support\\deprecated.py',
   'PYMODULE'),
  ('PySide6.support',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\support\\__init__.py',
   'PYMODULE'),
  ('pydantic',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\__init__.py',
   'PYMODULE'),
  ('pydantic.validators',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\validators.py',
   'PYMODULE'),
  ('pydantic.v1.version',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\version.py',
   'PYMODULE'),
  ('platform', 'D:\\app\\python_3.11\\Lib\\platform.py', 'PYMODULE'),
  ('pydantic.v1.validators',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\validators.py',
   'PYMODULE'),
  ('typing_extensions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('uuid', 'D:\\app\\python_3.11\\Lib\\uuid.py', 'PYMODULE'),
  ('ipaddress', 'D:\\app\\python_3.11\\Lib\\ipaddress.py', 'PYMODULE'),
  ('pydantic.v1.utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\utils.py',
   'PYMODULE'),
  ('pydantic.v1.typing',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\typing.py',
   'PYMODULE'),
  ('pydantic.v1.types',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\types.py',
   'PYMODULE'),
  ('pydantic.v1.tools',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\tools.py',
   'PYMODULE'),
  ('pydantic.v1.schema',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\schema.py',
   'PYMODULE'),
  ('pydantic.v1.parse',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\parse.py',
   'PYMODULE'),
  ('pydantic.v1.networks',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\networks.py',
   'PYMODULE'),
  ('pydantic.v1.mypy',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\mypy.py',
   'PYMODULE'),
  ('tomllib', 'D:\\app\\python_3.11\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser',
   'D:\\app\\python_3.11\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._types',
   'D:\\app\\python_3.11\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tomllib._re', 'D:\\app\\python_3.11\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('configparser', 'D:\\app\\python_3.11\\Lib\\configparser.py', 'PYMODULE'),
  ('pydantic.v1.main',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\main.py',
   'PYMODULE'),
  ('pydantic.v1.json',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\json.py',
   'PYMODULE'),
  ('pydantic.v1.generics',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\generics.py',
   'PYMODULE'),
  ('pydantic.v1.fields',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\fields.py',
   'PYMODULE'),
  ('pydantic.v1.errors',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\errors.py',
   'PYMODULE'),
  ('pydantic.v1.error_wrappers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.v1.env_settings',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\env_settings.py',
   'PYMODULE'),
  ('dotenv',
   'D:\\app\\python_3.11\\Lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'D:\\app\\python_3.11\\Lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'D:\\app\\python_3.11\\Lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.variables',
   'D:\\app\\python_3.11\\Lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('dotenv.parser',
   'D:\\app\\python_3.11\\Lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('pydantic.v1.decorator',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\decorator.py',
   'PYMODULE'),
  ('pydantic.v1.datetime_parse',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.v1.dataclasses',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.v1.config',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\config.py',
   'PYMODULE'),
  ('pydantic.v1.color',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\color.py',
   'PYMODULE'),
  ('colorsys', 'D:\\app\\python_3.11\\Lib\\colorsys.py', 'PYMODULE'),
  ('pydantic.v1.class_validators',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\class_validators.py',
   'PYMODULE'),
  ('pydantic.v1.annotated_types',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\annotated_types.py',
   'PYMODULE'),
  ('pydantic.v1._hypothesis_plugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\_hypothesis_plugin.py',
   'PYMODULE'),
  ('pydantic.v1',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\v1\\__init__.py',
   'PYMODULE'),
  ('pydantic.utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\utils.py',
   'PYMODULE'),
  ('pydantic.typing',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\typing.py',
   'PYMODULE'),
  ('pydantic.tools',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\tools.py',
   'PYMODULE'),
  ('pydantic.schema',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\schema.py',
   'PYMODULE'),
  ('pydantic.plugin._schema_validator',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\plugin\\_schema_validator.py',
   'PYMODULE'),
  ('pydantic.plugin._loader',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\plugin\\_loader.py',
   'PYMODULE'),
  ('pydantic.plugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\plugin\\__init__.py',
   'PYMODULE'),
  ('pydantic.parse',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\parse.py',
   'PYMODULE'),
  ('pydantic.mypy',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\mypy.py',
   'PYMODULE'),
  ('pydantic.json',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\json.py',
   'PYMODULE'),
  ('pydantic.generics',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\generics.py',
   'PYMODULE'),
  ('pydantic.experimental.pipeline',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\experimental\\pipeline.py',
   'PYMODULE'),
  ('annotated_types',
   'D:\\app\\python_3.11\\Lib\\site-packages\\annotated_types\\__init__.py',
   'PYMODULE'),
  ('pydantic.experimental',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\experimental\\__init__.py',
   'PYMODULE'),
  ('pydantic.error_wrappers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.env_settings',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\env_settings.py',
   'PYMODULE'),
  ('pydantic.deprecated.parse',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\deprecated\\parse.py',
   'PYMODULE'),
  ('pydantic.deprecated.json',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\deprecated\\json.py',
   'PYMODULE'),
  ('pydantic.deprecated.decorator',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\deprecated\\decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated.copy_internals',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\deprecated\\copy_internals.py',
   'PYMODULE'),
  ('pydantic.deprecated',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\deprecated\\__init__.py',
   'PYMODULE'),
  ('pydantic.decorator',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\decorator.py',
   'PYMODULE'),
  ('pydantic.datetime_parse',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.color',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\color.py',
   'PYMODULE'),
  ('pydantic.class_validators',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\class_validators.py',
   'PYMODULE'),
  ('pydantic.alias_generators',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\alias_generators.py',
   'PYMODULE'),
  ('pydantic._internal._validators',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_validators.py',
   'PYMODULE'),
  ('pydantic._internal._validate_call',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_validate_call.py',
   'PYMODULE'),
  ('pydantic._internal._utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_utils.py',
   'PYMODULE'),
  ('pydantic._internal._typing_extra',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_typing_extra.py',
   'PYMODULE'),
  ('zoneinfo', 'D:\\app\\python_3.11\\Lib\\zoneinfo\\__init__.py', 'PYMODULE'),
  ('tzdata',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.US',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Pacific',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Mexico',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Indian',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Europe',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Etc',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Chile',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Chile\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Canada',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Brazil',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Australia',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Atlantic',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Asia',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Arctic',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Arctic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Antarctica',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.North_Dakota',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Kentucky',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Indiana',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Argentina',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Africa',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'D:\\app\\python_3.11\\Lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE'),
  ('zoneinfo._common',
   'D:\\app\\python_3.11\\Lib\\zoneinfo\\_common.py',
   'PYMODULE'),
  ('zoneinfo._tzpath',
   'D:\\app\\python_3.11\\Lib\\zoneinfo\\_tzpath.py',
   'PYMODULE'),
  ('sysconfig', 'D:\\app\\python_3.11\\Lib\\sysconfig.py', 'PYMODULE'),
  ('_aix_support', 'D:\\app\\python_3.11\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess',
   'D:\\app\\python_3.11\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('pydantic._internal._std_types_schema',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_std_types_schema.py',
   'PYMODULE'),
  ('pydantic._internal._signature',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_signature.py',
   'PYMODULE'),
  ('pydantic._internal._serializers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_serializers.py',
   'PYMODULE'),
  ('pydantic._internal._schema_generation_shared',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_schema_generation_shared.py',
   'PYMODULE'),
  ('pydantic._internal._repr',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_repr.py',
   'PYMODULE'),
  ('pydantic._internal._namespace_utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_namespace_utils.py',
   'PYMODULE'),
  ('pydantic._internal._model_construction',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_model_construction.py',
   'PYMODULE'),
  ('pydantic._internal._mock_val_ser',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_mock_val_ser.py',
   'PYMODULE'),
  ('pydantic._internal._known_annotated_metadata',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_known_annotated_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._internal_dataclass',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_internal_dataclass.py',
   'PYMODULE'),
  ('pydantic._internal._import_utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_import_utils.py',
   'PYMODULE'),
  ('pydantic._internal._git',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_git.py',
   'PYMODULE'),
  ('pydantic._internal._generics',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_generics.py',
   'PYMODULE'),
  ('pydantic._internal._generate_schema',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_generate_schema.py',
   'PYMODULE'),
  ('pydantic._internal._forward_ref',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_forward_ref.py',
   'PYMODULE'),
  ('pydantic._internal._fields',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_fields.py',
   'PYMODULE'),
  ('pydantic._internal._docs_extraction',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_docs_extraction.py',
   'PYMODULE'),
  ('pydantic._internal._discriminated_union',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_discriminated_union.py',
   'PYMODULE'),
  ('pydantic._internal._decorators_v1',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_decorators_v1.py',
   'PYMODULE'),
  ('pydantic._internal._decorators',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_decorators.py',
   'PYMODULE'),
  ('pydantic._internal._dataclasses',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_dataclasses.py',
   'PYMODULE'),
  ('pydantic._internal._core_utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_core_utils.py',
   'PYMODULE'),
  ('rich',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\__init__.py',
   'PYMODULE'),
  ('rich.filesize',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\filesize.py',
   'PYMODULE'),
  ('rich._inspect',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\_inspect.py',
   'PYMODULE'),
  ('rich.text',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\text.py',
   'PYMODULE'),
  ('rich.ansi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\ansi.py',
   'PYMODULE'),
  ('pty', 'D:\\app\\python_3.11\\Lib\\pty.py', 'PYMODULE'),
  ('tty', 'D:\\app\\python_3.11\\Lib\\tty.py', 'PYMODULE'),
  ('rich.color',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\color.py',
   'PYMODULE'),
  ('rich.terminal_theme',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\terminal_theme.py',
   'PYMODULE'),
  ('rich.palette',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\palette.py',
   'PYMODULE'),
  ('rich.repr',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\repr.py',
   'PYMODULE'),
  ('rich.color_triplet',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\color_triplet.py',
   'PYMODULE'),
  ('rich._palettes',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\_palettes.py',
   'PYMODULE'),
  ('rich.markup',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\markup.py',
   'PYMODULE'),
  ('rich._emoji_replace',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\_emoji_replace.py',
   'PYMODULE'),
  ('rich._emoji_codes',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\_emoji_codes.py',
   'PYMODULE'),
  ('rich.style',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\style.py',
   'PYMODULE'),
  ('rich.segment',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\segment.py',
   'PYMODULE'),
  ('rich.syntax',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\syntax.py',
   'PYMODULE'),
  ('rich.padding',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\padding.py',
   'PYMODULE'),
  ('pygments.util',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\util.py',
   'PYMODULE'),
  ('pygments',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\__init__.py',
   'PYMODULE'),
  ('pygments.styles.zenburn',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\zenburn.py',
   'PYMODULE'),
  ('pygments.styles.xcode',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\xcode.py',
   'PYMODULE'),
  ('pygments.styles.vs',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\vs.py',
   'PYMODULE'),
  ('pygments.styles.vim',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\vim.py',
   'PYMODULE'),
  ('pygments.styles.trac',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\trac.py',
   'PYMODULE'),
  ('pygments.styles.tango',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\tango.py',
   'PYMODULE'),
  ('pygments.styles.stata_light',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\stata_light.py',
   'PYMODULE'),
  ('pygments.styles.stata_dark',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\stata_dark.py',
   'PYMODULE'),
  ('pygments.styles.staroffice',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\staroffice.py',
   'PYMODULE'),
  ('pygments.styles.solarized',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\solarized.py',
   'PYMODULE'),
  ('pygments.styles.sas',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\sas.py',
   'PYMODULE'),
  ('pygments.styles.rrt',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\rrt.py',
   'PYMODULE'),
  ('pygments.styles.rainbow_dash',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\rainbow_dash.py',
   'PYMODULE'),
  ('pygments.styles.perldoc',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\perldoc.py',
   'PYMODULE'),
  ('pygments.styles.pastie',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\pastie.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_light',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\paraiso_light.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_dark',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\paraiso_dark.py',
   'PYMODULE'),
  ('pygments.styles.onedark',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\onedark.py',
   'PYMODULE'),
  ('pygments.styles.nord',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\nord.py',
   'PYMODULE'),
  ('pygments.styles.native',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\native.py',
   'PYMODULE'),
  ('pygments.styles.murphy',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\murphy.py',
   'PYMODULE'),
  ('pygments.styles.monokai',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\monokai.py',
   'PYMODULE'),
  ('pygments.styles.material',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\material.py',
   'PYMODULE'),
  ('pygments.styles.manni',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\manni.py',
   'PYMODULE'),
  ('pygments.styles.lovelace',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\lovelace.py',
   'PYMODULE'),
  ('pygments.styles.lilypond',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\lilypond.py',
   'PYMODULE'),
  ('pygments.styles.lightbulb',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\lightbulb.py',
   'PYMODULE'),
  ('pygments.styles.inkpot',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\inkpot.py',
   'PYMODULE'),
  ('pygments.styles.igor',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\igor.py',
   'PYMODULE'),
  ('pygments.styles.gruvbox',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\gruvbox.py',
   'PYMODULE'),
  ('pygments.styles.gh_dark',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\gh_dark.py',
   'PYMODULE'),
  ('pygments.styles.fruity',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\fruity.py',
   'PYMODULE'),
  ('pygments.styles.friendly_grayscale',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\friendly_grayscale.py',
   'PYMODULE'),
  ('pygments.styles.friendly',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\friendly.py',
   'PYMODULE'),
  ('pygments.styles.emacs',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\emacs.py',
   'PYMODULE'),
  ('pygments.styles.dracula',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\dracula.py',
   'PYMODULE'),
  ('pygments.styles.default',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\default.py',
   'PYMODULE'),
  ('pygments.styles.colorful',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\colorful.py',
   'PYMODULE'),
  ('pygments.styles.coffee',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\coffee.py',
   'PYMODULE'),
  ('pygments.styles.bw',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\bw.py',
   'PYMODULE'),
  ('pygments.styles.borland',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\borland.py',
   'PYMODULE'),
  ('pygments.styles.autumn',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\autumn.py',
   'PYMODULE'),
  ('pygments.styles.arduino',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\arduino.py',
   'PYMODULE'),
  ('pygments.styles.algol_nu',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\algol_nu.py',
   'PYMODULE'),
  ('pygments.styles.algol',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\algol.py',
   'PYMODULE'),
  ('pygments.styles.abap',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\abap.py',
   'PYMODULE'),
  ('pygments.styles._mapping',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\_mapping.py',
   'PYMODULE'),
  ('pygments.lexers.zig',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\zig.py',
   'PYMODULE'),
  ('pygments.lexers.yara',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\yara.py',
   'PYMODULE'),
  ('pygments.lexers.yang',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\yang.py',
   'PYMODULE'),
  ('pygments.lexers.xorg',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\xorg.py',
   'PYMODULE'),
  ('pygments.lexers.x10',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\x10.py',
   'PYMODULE'),
  ('pygments.lexers.wren',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\wren.py',
   'PYMODULE'),
  ('pygments.lexers.wowtoc',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\wowtoc.py',
   'PYMODULE'),
  ('pygments.lexers.whiley',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\whiley.py',
   'PYMODULE'),
  ('pygments.lexers.wgsl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\wgsl.py',
   'PYMODULE'),
  ('pygments.lexers.webmisc',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\webmisc.py',
   'PYMODULE'),
  ('pygments.lexers.webidl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\webidl.py',
   'PYMODULE'),
  ('pygments.lexers.webassembly',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\webassembly.py',
   'PYMODULE'),
  ('pygments.lexers.web',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\web.py',
   'PYMODULE'),
  ('pygments.lexers.vyper',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\vyper.py',
   'PYMODULE'),
  ('pygments.lexers.vip',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\vip.py',
   'PYMODULE'),
  ('pygments.lexers.verifpal',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\verifpal.py',
   'PYMODULE'),
  ('pygments.lexers.verification',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\verification.py',
   'PYMODULE'),
  ('pygments.lexers.varnish',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\varnish.py',
   'PYMODULE'),
  ('pygments.lexers.usd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\usd.py',
   'PYMODULE'),
  ('pygments.lexers.urbi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\urbi.py',
   'PYMODULE'),
  ('pygments.lexers.unicon',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\unicon.py',
   'PYMODULE'),
  ('pygments.lexers.ul4',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\ul4.py',
   'PYMODULE'),
  ('pygments.lexers.typst',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\typst.py',
   'PYMODULE'),
  ('pygments.lexers.typoscript',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\typoscript.py',
   'PYMODULE'),
  ('pygments.lexers.trafficscript',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\trafficscript.py',
   'PYMODULE'),
  ('pygments.lexers.tnt',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\tnt.py',
   'PYMODULE'),
  ('pygments.lexers.tls',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\tls.py',
   'PYMODULE'),
  ('pygments.lexers.tlb',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\tlb.py',
   'PYMODULE'),
  ('pygments.lexers.thingsdb',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\thingsdb.py',
   'PYMODULE'),
  ('pygments.lexers.theorem',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\theorem.py',
   'PYMODULE'),
  ('pygments.lexers.textfmts',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\textfmts.py',
   'PYMODULE'),
  ('pygments.lexers.textedit',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\textedit.py',
   'PYMODULE'),
  ('pygments.lexers.text',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\text.py',
   'PYMODULE'),
  ('pygments.lexers.testing',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\testing.py',
   'PYMODULE'),
  ('pygments.lexers.teraterm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\teraterm.py',
   'PYMODULE'),
  ('pygments.lexers.templates',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\templates.py',
   'PYMODULE'),
  ('pygments.lexers.teal',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\teal.py',
   'PYMODULE'),
  ('pygments.lexers.tcl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\tcl.py',
   'PYMODULE'),
  ('pygments.lexers.tal',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\tal.py',
   'PYMODULE'),
  ('pygments.lexers.tact',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\tact.py',
   'PYMODULE'),
  ('pygments.lexers.tablegen',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\tablegen.py',
   'PYMODULE'),
  ('pygments.lexers.supercollider',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\supercollider.py',
   'PYMODULE'),
  ('pygments.lexers.stata',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\stata.py',
   'PYMODULE'),
  ('pygments.lexers.srcinfo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\srcinfo.py',
   'PYMODULE'),
  ('pygments.lexers.sql',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\sql.py',
   'PYMODULE'),
  ('pygments.lexers.spice',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\spice.py',
   'PYMODULE'),
  ('pygments.lexers.special',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\special.py',
   'PYMODULE'),
  ('pygments.lexers.sophia',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\sophia.py',
   'PYMODULE'),
  ('pygments.lexers.soong',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\soong.py',
   'PYMODULE'),
  ('pygments.lexers.solidity',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\solidity.py',
   'PYMODULE'),
  ('pygments.lexers.snobol',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\snobol.py',
   'PYMODULE'),
  ('pygments.lexers.smv',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\smv.py',
   'PYMODULE'),
  ('pygments.lexers.smithy',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\smithy.py',
   'PYMODULE'),
  ('pygments.lexers.smalltalk',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\smalltalk.py',
   'PYMODULE'),
  ('pygments.lexers.slash',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\slash.py',
   'PYMODULE'),
  ('pygments.lexers.sieve',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\sieve.py',
   'PYMODULE'),
  ('pygments.lexers.shell',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\shell.py',
   'PYMODULE'),
  ('pygments.lexers.sgf',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\sgf.py',
   'PYMODULE'),
  ('pygments.lexers.scripting',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\scripting.py',
   'PYMODULE'),
  ('pygments.lexers.scdoc',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\scdoc.py',
   'PYMODULE'),
  ('pygments.lexers.savi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\savi.py',
   'PYMODULE'),
  ('pygments.lexers.sas',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\sas.py',
   'PYMODULE'),
  ('pygments.lexers.rust',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\rust.py',
   'PYMODULE'),
  ('pygments.lexers.ruby',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\ruby.py',
   'PYMODULE'),
  ('pygments.lexers.robotframework',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\robotframework.py',
   'PYMODULE'),
  ('pygments.lexers.roboconf',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\roboconf.py',
   'PYMODULE'),
  ('pygments.lexers.rnc',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\rnc.py',
   'PYMODULE'),
  ('pygments.lexers.rita',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\rita.py',
   'PYMODULE'),
  ('pygments.lexers.ride',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\ride.py',
   'PYMODULE'),
  ('pygments.lexers.resource',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\resource.py',
   'PYMODULE'),
  ('pygments.lexers.rego',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\rego.py',
   'PYMODULE'),
  ('pygments.lexers.rebol',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\rebol.py',
   'PYMODULE'),
  ('pygments.lexers.rdf',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\rdf.py',
   'PYMODULE'),
  ('pygments.lexers.r',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\r.py',
   'PYMODULE'),
  ('pygments.lexers.qvt',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\qvt.py',
   'PYMODULE'),
  ('pygments.lexers.qlik',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\qlik.py',
   'PYMODULE'),
  ('pygments.lexers.q',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\q.py',
   'PYMODULE'),
  ('pygments.lexers.python',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\python.py',
   'PYMODULE'),
  ('pygments.lexers.ptx',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\ptx.py',
   'PYMODULE'),
  ('pygments.lexers.prql',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\prql.py',
   'PYMODULE'),
  ('pygments.lexers.promql',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\promql.py',
   'PYMODULE'),
  ('pygments.lexers.prolog',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\prolog.py',
   'PYMODULE'),
  ('pygments.lexers.procfile',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\procfile.py',
   'PYMODULE'),
  ('pygments.lexers.praat',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\praat.py',
   'PYMODULE'),
  ('pygments.lexers.pony',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\pony.py',
   'PYMODULE'),
  ('pygments.lexers.pointless',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\pointless.py',
   'PYMODULE'),
  ('pygments.lexers.php',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\php.py',
   'PYMODULE'),
  ('pygments.lexers.phix',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\phix.py',
   'PYMODULE'),
  ('pygments.lexers.perl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\perl.py',
   'PYMODULE'),
  ('pygments.lexers.pddl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\pddl.py',
   'PYMODULE'),
  ('pygments.lexers.pawn',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\pawn.py',
   'PYMODULE'),
  ('pygments.lexers.pascal',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\pascal.py',
   'PYMODULE'),
  ('pygments.scanner',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\scanner.py',
   'PYMODULE'),
  ('pygments.lexers.parsers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\parsers.py',
   'PYMODULE'),
  ('pygments.lexers.parasail',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\parasail.py',
   'PYMODULE'),
  ('pygments.lexers.other',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\other.py',
   'PYMODULE'),
  ('pygments.lexers.openscad',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\openscad.py',
   'PYMODULE'),
  ('pygments.lexers.ooc',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\ooc.py',
   'PYMODULE'),
  ('pygments.lexers.objective',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\objective.py',
   'PYMODULE'),
  ('pygments.lexers.oberon',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\oberon.py',
   'PYMODULE'),
  ('pygments.lexers.numbair',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\numbair.py',
   'PYMODULE'),
  ('pygments.lexers.nix',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\nix.py',
   'PYMODULE'),
  ('pygments.lexers.nit',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\nit.py',
   'PYMODULE'),
  ('pygments.lexers.nimrod',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\nimrod.py',
   'PYMODULE'),
  ('pygments.lexers.ncl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\ncl.py',
   'PYMODULE'),
  ('pygments.lexers.mosel',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\mosel.py',
   'PYMODULE'),
  ('pygments.lexers.monte',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\monte.py',
   'PYMODULE'),
  ('pygments.lexers.mojo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\mojo.py',
   'PYMODULE'),
  ('pygments.lexers.modula2',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\modula2.py',
   'PYMODULE'),
  ('pygments.lexers.modeling',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\modeling.py',
   'PYMODULE'),
  ('pygments.lexers.ml',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\ml.py',
   'PYMODULE'),
  ('pygments.lexers.mips',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\mips.py',
   'PYMODULE'),
  ('pygments.lexers.minecraft',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\minecraft.py',
   'PYMODULE'),
  ('pygments.lexers.mime',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\mime.py',
   'PYMODULE'),
  ('pygments.lexers.meson',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\meson.py',
   'PYMODULE'),
  ('pygments.lexers.maxima',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\maxima.py',
   'PYMODULE'),
  ('pygments.lexers.matlab',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\matlab.py',
   'PYMODULE'),
  ('pygments.lexers.math',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\math.py',
   'PYMODULE'),
  ('pygments.lexers.markup',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\markup.py',
   'PYMODULE'),
  ('pygments.lexers.maple',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\maple.py',
   'PYMODULE'),
  ('pygments.lexers.make',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\make.py',
   'PYMODULE'),
  ('pygments.lexers.macaulay2',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\macaulay2.py',
   'PYMODULE'),
  ('pygments.lexers.lisp',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\lisp.py',
   'PYMODULE'),
  ('pygments.lexers.lilypond',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\lilypond.py',
   'PYMODULE'),
  ('pygments.lexers.lean',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\lean.py',
   'PYMODULE'),
  ('pygments.lexers.ldap',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\ldap.py',
   'PYMODULE'),
  ('pygments.lexers.kusto',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\kusto.py',
   'PYMODULE'),
  ('pygments.lexers.kuin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\kuin.py',
   'PYMODULE'),
  ('pygments.lexers.jvm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\jvm.py',
   'PYMODULE'),
  ('pygments.lexers.julia',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\julia.py',
   'PYMODULE'),
  ('pygments.lexers.jsx',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\jsx.py',
   'PYMODULE'),
  ('pygments.lexers.jsonnet',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\jsonnet.py',
   'PYMODULE'),
  ('pygments.lexers.json5',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\json5.py',
   'PYMODULE'),
  ('pygments.lexers.jslt',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\jslt.py',
   'PYMODULE'),
  ('pygments.lexers.jmespath',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\jmespath.py',
   'PYMODULE'),
  ('pygments.lexers.javascript',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\javascript.py',
   'PYMODULE'),
  ('pygments.lexers.j',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\j.py',
   'PYMODULE'),
  ('pygments.lexers.iolang',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\iolang.py',
   'PYMODULE'),
  ('pygments.lexers.int_fiction',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\int_fiction.py',
   'PYMODULE'),
  ('pygments.lexers.installers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\installers.py',
   'PYMODULE'),
  ('pygments.lexers.inferno',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\inferno.py',
   'PYMODULE'),
  ('pygments.lexers.igor',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\igor.py',
   'PYMODULE'),
  ('pygments.lexers.idl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\idl.py',
   'PYMODULE'),
  ('pygments.lexers.html',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\html.py',
   'PYMODULE'),
  ('pygments.lexers.hexdump',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\hexdump.py',
   'PYMODULE'),
  ('pygments.lexers.hdl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\hdl.py',
   'PYMODULE'),
  ('pygments.lexers.haxe',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\haxe.py',
   'PYMODULE'),
  ('pygments.lexers.haskell',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\haskell.py',
   'PYMODULE'),
  ('pygments.lexers.hare',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\hare.py',
   'PYMODULE'),
  ('pygments.lexers.gsql',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\gsql.py',
   'PYMODULE'),
  ('pygments.lexers.graphviz',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\graphviz.py',
   'PYMODULE'),
  ('pygments.lexers.graphql',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\graphql.py',
   'PYMODULE'),
  ('pygments.lexers.graphics',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\graphics.py',
   'PYMODULE'),
  ('pygments.lexers.graph',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\graph.py',
   'PYMODULE'),
  ('pygments.lexers.grammar_notation',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\grammar_notation.py',
   'PYMODULE'),
  ('pygments.lexers.go',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\go.py',
   'PYMODULE'),
  ('pygments.lexers.gleam',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\gleam.py',
   'PYMODULE'),
  ('pygments.lexers.gdscript',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\gdscript.py',
   'PYMODULE'),
  ('pygments.lexers.gcodelexer',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\gcodelexer.py',
   'PYMODULE'),
  ('pygments.lexers.futhark',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\futhark.py',
   'PYMODULE'),
  ('pygments.lexers.functional',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\functional.py',
   'PYMODULE'),
  ('pygments.lexers.func',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\func.py',
   'PYMODULE'),
  ('pygments.lexers.freefem',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\freefem.py',
   'PYMODULE'),
  ('pygments.lexers.foxpro',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\foxpro.py',
   'PYMODULE'),
  ('pygments.lexers.fortran',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\fortran.py',
   'PYMODULE'),
  ('pygments.lexers.forth',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\forth.py',
   'PYMODULE'),
  ('pygments.lexers.floscript',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\floscript.py',
   'PYMODULE'),
  ('pygments.lexers.fift',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\fift.py',
   'PYMODULE'),
  ('pygments.lexers.felix',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\felix.py',
   'PYMODULE'),
  ('pygments.lexers.fantom',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\fantom.py',
   'PYMODULE'),
  ('pygments.lexers.factor',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\factor.py',
   'PYMODULE'),
  ('pygments.lexers.ezhil',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\ezhil.py',
   'PYMODULE'),
  ('pygments.lexers.esoteric',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\esoteric.py',
   'PYMODULE'),
  ('pygments.lexers.erlang',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\erlang.py',
   'PYMODULE'),
  ('pygments.lexers.email',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\email.py',
   'PYMODULE'),
  ('pygments.lexers.elpi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\elpi.py',
   'PYMODULE'),
  ('pygments.lexers.elm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\elm.py',
   'PYMODULE'),
  ('pygments.lexers.eiffel',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\eiffel.py',
   'PYMODULE'),
  ('pygments.lexers.ecl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\ecl.py',
   'PYMODULE'),
  ('pygments.lexers.dylan',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\dylan.py',
   'PYMODULE'),
  ('pygments.lexers.dsls',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\dsls.py',
   'PYMODULE'),
  ('pygments.lexers.dotnet',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\dotnet.py',
   'PYMODULE'),
  ('pygments.lexers.dns',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\dns.py',
   'PYMODULE'),
  ('pygments.lexers.diff',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\diff.py',
   'PYMODULE'),
  ('pygments.lexers.devicetree',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\devicetree.py',
   'PYMODULE'),
  ('pygments.lexers.dax',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\dax.py',
   'PYMODULE'),
  ('pygments.lexers.data',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\data.py',
   'PYMODULE'),
  ('pygments.lexers.dalvik',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\dalvik.py',
   'PYMODULE'),
  ('pygments.lexers.d',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\d.py',
   'PYMODULE'),
  ('pygments.lexers.css',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\css.py',
   'PYMODULE'),
  ('pygments.lexers.csound',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\csound.py',
   'PYMODULE'),
  ('pygments.lexers.crystal',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\crystal.py',
   'PYMODULE'),
  ('pygments.lexers.cplint',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\cplint.py',
   'PYMODULE'),
  ('pygments.lexers.console',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\console.py',
   'PYMODULE'),
  ('pygments.lexers.configs',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\configs.py',
   'PYMODULE'),
  ('pygments.lexers.compiled',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\compiled.py',
   'PYMODULE'),
  ('pygments.lexers.comal',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\comal.py',
   'PYMODULE'),
  ('pygments.lexers.codeql',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\codeql.py',
   'PYMODULE'),
  ('pygments.lexers.clean',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\clean.py',
   'PYMODULE'),
  ('pygments.lexers.chapel',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\chapel.py',
   'PYMODULE'),
  ('pygments.lexers.cddl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\cddl.py',
   'PYMODULE'),
  ('pygments.lexers.carbon',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\carbon.py',
   'PYMODULE'),
  ('pygments.lexers.capnproto',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\capnproto.py',
   'PYMODULE'),
  ('pygments.lexers.c_like',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\c_like.py',
   'PYMODULE'),
  ('pygments.lexers.c_cpp',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\c_cpp.py',
   'PYMODULE'),
  ('pygments.lexers.business',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\business.py',
   'PYMODULE'),
  ('pygments.lexers.bqn',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\bqn.py',
   'PYMODULE'),
  ('pygments.lexers.boa',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\boa.py',
   'PYMODULE'),
  ('pygments.lexers.blueprint',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\blueprint.py',
   'PYMODULE'),
  ('pygments.lexers.bibtex',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\bibtex.py',
   'PYMODULE'),
  ('pygments.lexers.berry',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\berry.py',
   'PYMODULE'),
  ('pygments.lexers.bdd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\bdd.py',
   'PYMODULE'),
  ('pygments.lexers.basic',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\basic.py',
   'PYMODULE'),
  ('pygments.lexers.bare',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\bare.py',
   'PYMODULE'),
  ('pygments.lexers.automation',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\automation.py',
   'PYMODULE'),
  ('pygments.lexers.asn1',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\asn1.py',
   'PYMODULE'),
  ('pygments.lexers.asm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\asm.py',
   'PYMODULE'),
  ('pygments.lexers.asc',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\asc.py',
   'PYMODULE'),
  ('pygments.lexers.arturo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\arturo.py',
   'PYMODULE'),
  ('pygments.lexers.arrow',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\arrow.py',
   'PYMODULE'),
  ('pygments.lexers.archetype',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\archetype.py',
   'PYMODULE'),
  ('pygments.lexers.apl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\apl.py',
   'PYMODULE'),
  ('pygments.lexers.apdlexer',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\apdlexer.py',
   'PYMODULE'),
  ('pygments.lexers.ampl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\ampl.py',
   'PYMODULE'),
  ('pygments.lexers.amdgpu',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\amdgpu.py',
   'PYMODULE'),
  ('pygments.lexers.ambient',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\ambient.py',
   'PYMODULE'),
  ('pygments.lexers.algebra',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\algebra.py',
   'PYMODULE'),
  ('pygments.lexers.agile',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\agile.py',
   'PYMODULE'),
  ('pygments.unistring',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\unistring.py',
   'PYMODULE'),
  ('pygments.lexers.ada',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\ada.py',
   'PYMODULE'),
  ('pygments.lexers.actionscript',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\actionscript.py',
   'PYMODULE'),
  ('pygments.lexers._vim_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_vim_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vbscript_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_vbscript_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._usd_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_usd_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._tsql_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_tsql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stata_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_stata_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stan_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_stan_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sourcemod_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_sourcemod_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scilab_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_scilab_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scheme_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_scheme_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._qlik_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_qlik_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._postgres_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_postgres_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._php_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_php_builtins.py',
   'PYMODULE'),
  ('glob', 'D:\\app\\python_3.11\\Lib\\glob.py', 'PYMODULE'),
  ('pygments.lexers._openedge_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_openedge_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mysql_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_mysql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mql_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_mql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mapping',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_mapping.py',
   'PYMODULE'),
  ('pygments.lexers._luau_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_luau_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lua_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_lua_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lilypond_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_lilypond_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lasso_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_lasso_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._julia_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_julia_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._googlesql_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_googlesql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._css_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_css_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._csound_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_csound_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cocoa_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_cocoa_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cl_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_cl_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._asy_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_asy_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._ada_builtins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\_ada_builtins.py',
   'PYMODULE'),
  ('pygments.formatters.terminal256',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\formatters\\terminal256.py',
   'PYMODULE'),
  ('pygments.console',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\console.py',
   'PYMODULE'),
  ('pygments.formatters.terminal',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\formatters\\terminal.py',
   'PYMODULE'),
  ('pygments.formatters.svg',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\formatters\\svg.py',
   'PYMODULE'),
  ('pygments.formatters.rtf',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\formatters\\rtf.py',
   'PYMODULE'),
  ('pygments.formatters.pangomarkup',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\formatters\\pangomarkup.py',
   'PYMODULE'),
  ('pygments.formatters.other',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\formatters\\other.py',
   'PYMODULE'),
  ('pygments.formatters.latex',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\formatters\\latex.py',
   'PYMODULE'),
  ('pygments.formatters.irc',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\formatters\\irc.py',
   'PYMODULE'),
  ('pygments.formatters.img',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\formatters\\img.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\app\\python_3.11\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging',
   'D:\\app\\python_3.11\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\app\\python_3.11\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\app\\python_3.11\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\app\\python_3.11\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\app\\python_3.11\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\app\\python_3.11\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\app\\python_3.11\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\app\\python_3.11\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\app\\python_3.11\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._util',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._typing',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('psutil',
   'D:\\app\\python_3.11\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\app\\python_3.11\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\app\\python_3.11\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('doctest', 'D:\\app\\python_3.11\\Lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'D:\\app\\python_3.11\\Lib\\pdb.py', 'PYMODULE'),
  ('pydoc', 'D:\\app\\python_3.11\\Lib\\pydoc.py', 'PYMODULE'),
  ('http.server', 'D:\\app\\python_3.11\\Lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'D:\\app\\python_3.11\\Lib\\socketserver.py', 'PYMODULE'),
  ('html', 'D:\\app\\python_3.11\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\app\\python_3.11\\Lib\\html\\entities.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\app\\python_3.11\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\app\\python_3.11\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('readline',
   'D:\\app\\python_3.11\\Lib\\site-packages\\readline.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\app\\python_3.11\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes.util', 'D:\\app\\python_3.11\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\app\\python_3.11\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\app\\python_3.11\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\app\\python_3.11\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\app\\python_3.11\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\app\\python_3.11\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('pyreadline3.keysyms',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('code', 'D:\\app\\python_3.11\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\app\\python_3.11\\Lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'D:\\app\\python_3.11\\Lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\app\\python_3.11\\Lib\\cmd.py', 'PYMODULE'),
  ('difflib', 'D:\\app\\python_3.11\\Lib\\difflib.py', 'PYMODULE'),
  ('unittest.case', 'D:\\app\\python_3.11\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest._log', 'D:\\app\\python_3.11\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.util', 'D:\\app\\python_3.11\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('unittest.result',
   'D:\\app\\python_3.11\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('unittest', 'D:\\app\\python_3.11\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   'D:\\app\\python_3.11\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('asyncio', 'D:\\app\\python_3.11\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\app\\python_3.11\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'D:\\app\\python_3.11\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\app\\python_3.11\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\app\\python_3.11\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\app\\python_3.11\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\app\\python_3.11\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\app\\python_3.11\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\app\\python_3.11\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\app\\python_3.11\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\app\\python_3.11\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\app\\python_3.11\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\app\\python_3.11\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\app\\python_3.11\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\app\\python_3.11\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\app\\python_3.11\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\app\\python_3.11\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\app\\python_3.11\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\app\\python_3.11\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\app\\python_3.11\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\app\\python_3.11\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\app\\python_3.11\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\app\\python_3.11\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks', 'D:\\app\\python_3.11\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.locks', 'D:\\app\\python_3.11\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\app\\python_3.11\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\app\\python_3.11\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\app\\python_3.11\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\app\\python_3.11\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\app\\python_3.11\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\app\\python_3.11\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\app\\python_3.11\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\app\\python_3.11\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\app\\python_3.11\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\app\\python_3.11\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\app\\python_3.11\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\app\\python_3.11\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\app\\python_3.11\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main', 'D:\\app\\python_3.11\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner',
   'D:\\app\\python_3.11\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\app\\python_3.11\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\app\\python_3.11\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('threadpoolctl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\threadpoolctl.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.compat',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('yaml',
   'D:\\app\\python_3.11\\Lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'D:\\app\\python_3.11\\Lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.resolver',
   'D:\\app\\python_3.11\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.representer',
   'D:\\app\\python_3.11\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.serializer',
   'D:\\app\\python_3.11\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'D:\\app\\python_3.11\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.dumper',
   'D:\\app\\python_3.11\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'D:\\app\\python_3.11\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.loader',
   'D:\\app\\python_3.11\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.composer',
   'D:\\app\\python_3.11\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.parser',
   'D:\\app\\python_3.11\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.scanner',
   'D:\\app\\python_3.11\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.reader',
   'D:\\app\\python_3.11\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'D:\\app\\python_3.11\\Lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.events',
   'D:\\app\\python_3.11\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.tokens',
   'D:\\app\\python_3.11\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('yaml.error',
   'D:\\app\\python_3.11\\Lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('numpy.array_api',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\array_api\\_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.Image',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\app\\python_3.11\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\app\\python_3.11\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\app\\python_3.11\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\app\\python_3.11\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\app\\python_3.11\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('pygments.formatters.html',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\formatters\\html.py',
   'PYMODULE'),
  ('pygments.formatters.groff',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\formatters\\groff.py',
   'PYMODULE'),
  ('pygments.formatters.bbcode',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\formatters\\bbcode.py',
   'PYMODULE'),
  ('pygments.formatters._mapping',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\formatters\\_mapping.py',
   'PYMODULE'),
  ('pygments.formatters',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\formatters\\__init__.py',
   'PYMODULE'),
  ('pygments.plugin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\plugin.py',
   'PYMODULE'),
  ('pygments.formatter',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\formatter.py',
   'PYMODULE'),
  ('pygments.token',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\token.py',
   'PYMODULE'),
  ('pygments.styles',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\styles\\__init__.py',
   'PYMODULE'),
  ('pygments.style',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\style.py',
   'PYMODULE'),
  ('pygments.lexers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexers\\__init__.py',
   'PYMODULE'),
  ('pygments.modeline',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\modeline.py',
   'PYMODULE'),
  ('pygments.lexer',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\lexer.py',
   'PYMODULE'),
  ('pygments.regexopt',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\regexopt.py',
   'PYMODULE'),
  ('pygments.filters',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\filters\\__init__.py',
   'PYMODULE'),
  ('pygments.filter',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pygments\\filter.py',
   'PYMODULE'),
  ('rich.measure',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\measure.py',
   'PYMODULE'),
  ('rich.protocol',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\protocol.py',
   'PYMODULE'),
  ('rich.emoji',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\emoji.py',
   'PYMODULE'),
  ('rich.columns',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\columns.py',
   'PYMODULE'),
  ('rich.constrain',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\constrain.py',
   'PYMODULE'),
  ('rich.containers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\containers.py',
   'PYMODULE'),
  ('rich.cells',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\cells.py',
   'PYMODULE'),
  ('rich._cell_widths',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\_cell_widths.py',
   'PYMODULE'),
  ('rich.align',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\align.py',
   'PYMODULE'),
  ('rich._wrap',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\_wrap.py',
   'PYMODULE'),
  ('rich._pick',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\_pick.py',
   'PYMODULE'),
  ('rich._loop',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\_loop.py',
   'PYMODULE'),
  ('rich.table',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\table.py',
   'PYMODULE'),
  ('rich._timer',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\_timer.py',
   'PYMODULE'),
  ('rich._ratio',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\_ratio.py',
   'PYMODULE'),
  ('rich.pretty',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\pretty.py',
   'PYMODULE'),
  ('rich.abc',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\abc.py',
   'PYMODULE'),
  ('attr',
   'D:\\app\\python_3.11\\Lib\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._version_info',
   'D:\\app\\python_3.11\\Lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr._next_gen',
   'D:\\app\\python_3.11\\Lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._make',
   'D:\\app\\python_3.11\\Lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._compat',
   'D:\\app\\python_3.11\\Lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._funcs',
   'D:\\app\\python_3.11\\Lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._cmp',
   'D:\\app\\python_3.11\\Lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr.validators',
   'D:\\app\\python_3.11\\Lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attr.filters',
   'D:\\app\\python_3.11\\Lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.converters',
   'D:\\app\\python_3.11\\Lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.setters',
   'D:\\app\\python_3.11\\Lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr._config',
   'D:\\app\\python_3.11\\Lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('rich.panel',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\panel.py',
   'PYMODULE'),
  ('rich.jupyter',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\jupyter.py',
   'PYMODULE'),
  ('rich.highlighter',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\highlighter.py',
   'PYMODULE'),
  ('rich.control',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\control.py',
   'PYMODULE'),
  ('rich.console',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\console.py',
   'PYMODULE'),
  ('rich._windows_renderer',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\_windows_renderer.py',
   'PYMODULE'),
  ('rich._win32_console',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\_win32_console.py',
   'PYMODULE'),
  ('rich.traceback',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\traceback.py',
   'PYMODULE'),
  ('rich.json',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\json.py',
   'PYMODULE'),
  ('rich.rule',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\rule.py',
   'PYMODULE'),
  ('rich.status',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\status.py',
   'PYMODULE'),
  ('rich.spinner',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\spinner.py',
   'PYMODULE'),
  ('rich._spinners',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\_spinners.py',
   'PYMODULE'),
  ('rich.live',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\live.py',
   'PYMODULE'),
  ('rich.live_render',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\live_render.py',
   'PYMODULE'),
  ('rich.file_proxy',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\file_proxy.py',
   'PYMODULE'),
  ('rich._windows',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\_windows.py',
   'PYMODULE'),
  ('rich.theme',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\theme.py',
   'PYMODULE'),
  ('rich.default_styles',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\default_styles.py',
   'PYMODULE'),
  ('rich.styled',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\styled.py',
   'PYMODULE'),
  ('rich.screen',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\screen.py',
   'PYMODULE'),
  ('rich.scope',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\scope.py',
   'PYMODULE'),
  ('rich.region',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\region.py',
   'PYMODULE'),
  ('rich.pager',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\pager.py',
   'PYMODULE'),
  ('rich.__main__',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\__main__.py',
   'PYMODULE'),
  ('rich.markdown',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\markdown.py',
   'PYMODULE'),
  ('rich._stack',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\_stack.py',
   'PYMODULE'),
  ('markdown_it.token',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\token.py',
   'PYMODULE'),
  ('markdown_it._compat',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\_compat.py',
   'PYMODULE'),
  ('markdown_it',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\__init__.py',
   'PYMODULE'),
  ('markdown_it.main',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\main.py',
   'PYMODULE'),
  ('markdown_it.utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\utils.py',
   'PYMODULE'),
  ('markdown_it.rules_core.state_core',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_core\\state_core.py',
   'PYMODULE'),
  ('markdown_it.rules_core',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_core\\__init__.py',
   'PYMODULE'),
  ('markdown_it.rules_core.text_join',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_core\\text_join.py',
   'PYMODULE'),
  ('markdown_it.rules_core.smartquotes',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_core\\smartquotes.py',
   'PYMODULE'),
  ('markdown_it.rules_core.replacements',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_core\\replacements.py',
   'PYMODULE'),
  ('markdown_it.rules_core.normalize',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_core\\normalize.py',
   'PYMODULE'),
  ('markdown_it.rules_core.linkify',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_core\\linkify.py',
   'PYMODULE'),
  ('markdown_it.rules_core.inline',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_core\\inline.py',
   'PYMODULE'),
  ('markdown_it.rules_core.block',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_core\\block.py',
   'PYMODULE'),
  ('markdown_it.ruler',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\ruler.py',
   'PYMODULE'),
  ('markdown_it.renderer',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\renderer.py',
   'PYMODULE'),
  ('markdown_it.parser_inline',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\parser_inline.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.state_inline',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_inline\\state_inline.py',
   'PYMODULE'),
  ('markdown_it.rules_inline',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_inline\\__init__.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.text',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_inline\\text.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.newline',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_inline\\newline.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.linkify',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_inline\\linkify.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.link',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_inline\\link.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.image',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_inline\\image.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.html_inline',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_inline\\html_inline.py',
   'PYMODULE'),
  ('markdown_it.common.html_re',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\common\\html_re.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.fragments_join',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_inline\\fragments_join.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.escape',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_inline\\escape.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.entity',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_inline\\entity.py',
   'PYMODULE'),
  ('markdown_it.common.entities',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\common\\entities.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.balance_pairs',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_inline\\balance_pairs.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.backticks',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_inline\\backticks.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.autolink',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_inline\\autolink.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.strikethrough',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_inline\\strikethrough.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.emphasis',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_inline\\emphasis.py',
   'PYMODULE'),
  ('markdown_it.parser_core',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\parser_core.py',
   'PYMODULE'),
  ('markdown_it.parser_block',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\parser_block.py',
   'PYMODULE'),
  ('markdown_it.rules_block.state_block',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_block\\state_block.py',
   'PYMODULE'),
  ('markdown_it.common.utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\common\\utils.py',
   'PYMODULE'),
  ('markdown_it.common.normalize_url',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\common\\normalize_url.py',
   'PYMODULE'),
  ('mdurl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\mdurl\\__init__.py',
   'PYMODULE'),
  ('mdurl._url',
   'D:\\app\\python_3.11\\Lib\\site-packages\\mdurl\\_url.py',
   'PYMODULE'),
  ('mdurl._parse',
   'D:\\app\\python_3.11\\Lib\\site-packages\\mdurl\\_parse.py',
   'PYMODULE'),
  ('mdurl._format',
   'D:\\app\\python_3.11\\Lib\\site-packages\\mdurl\\_format.py',
   'PYMODULE'),
  ('mdurl._encode',
   'D:\\app\\python_3.11\\Lib\\site-packages\\mdurl\\_encode.py',
   'PYMODULE'),
  ('mdurl._decode',
   'D:\\app\\python_3.11\\Lib\\site-packages\\mdurl\\_decode.py',
   'PYMODULE'),
  ('markdown_it.common',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\common\\__init__.py',
   'PYMODULE'),
  ('markdown_it.rules_block',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_block\\__init__.py',
   'PYMODULE'),
  ('markdown_it.rules_block.table',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_block\\table.py',
   'PYMODULE'),
  ('markdown_it.rules_block.reference',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_block\\reference.py',
   'PYMODULE'),
  ('markdown_it.rules_block.paragraph',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_block\\paragraph.py',
   'PYMODULE'),
  ('markdown_it.rules_block.list',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_block\\list.py',
   'PYMODULE'),
  ('markdown_it.rules_block.lheading',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_block\\lheading.py',
   'PYMODULE'),
  ('markdown_it.rules_block.html_block',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_block\\html_block.py',
   'PYMODULE'),
  ('markdown_it.common.html_blocks',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\common\\html_blocks.py',
   'PYMODULE'),
  ('markdown_it.rules_block.hr',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_block\\hr.py',
   'PYMODULE'),
  ('markdown_it.rules_block.heading',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_block\\heading.py',
   'PYMODULE'),
  ('markdown_it.rules_block.fence',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_block\\fence.py',
   'PYMODULE'),
  ('markdown_it.rules_block.code',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_block\\code.py',
   'PYMODULE'),
  ('markdown_it.rules_block.blockquote',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\rules_block\\blockquote.py',
   'PYMODULE'),
  ('markdown_it._punycode',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\_punycode.py',
   'PYMODULE'),
  ('markdown_it.presets',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\presets\\__init__.py',
   'PYMODULE'),
  ('markdown_it.presets.zero',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\presets\\zero.py',
   'PYMODULE'),
  ('markdown_it.presets.default',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\presets\\default.py',
   'PYMODULE'),
  ('markdown_it.presets.commonmark',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\presets\\commonmark.py',
   'PYMODULE'),
  ('markdown_it.helpers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\helpers\\__init__.py',
   'PYMODULE'),
  ('markdown_it.helpers.parse_link_title',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\helpers\\parse_link_title.py',
   'PYMODULE'),
  ('markdown_it.helpers.parse_link_label',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\helpers\\parse_link_label.py',
   'PYMODULE'),
  ('markdown_it.helpers.parse_link_destination',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markdown_it\\helpers\\parse_link_destination.py',
   'PYMODULE'),
  ('rich._log_render',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\_log_render.py',
   'PYMODULE'),
  ('rich._fileno',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\_fileno.py',
   'PYMODULE'),
  ('rich._export_format',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\_export_format.py',
   'PYMODULE'),
  ('rich._null_file',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\_null_file.py',
   'PYMODULE'),
  ('rich._extension',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\_extension.py',
   'PYMODULE'),
  ('rich.themes',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\themes.py',
   'PYMODULE'),
  ('rich.box',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\box.py',
   'PYMODULE'),
  ('rich.errors',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\errors.py',
   'PYMODULE'),
  ('pydantic._internal._core_metadata',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_core_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._config',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\_config.py',
   'PYMODULE'),
  ('pydantic._internal',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_internal\\__init__.py',
   'PYMODULE'),
  ('pydantic.root_model',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\root_model.py',
   'PYMODULE'),
  ('pydantic.deprecated.tools',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\deprecated\\tools.py',
   'PYMODULE'),
  ('pydantic.deprecated.config',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\deprecated\\config.py',
   'PYMODULE'),
  ('pydantic.deprecated.class_validators',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\deprecated\\class_validators.py',
   'PYMODULE'),
  ('pydantic.warnings',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\warnings.py',
   'PYMODULE'),
  ('pydantic.validate_call_decorator',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\validate_call_decorator.py',
   'PYMODULE'),
  ('pydantic.type_adapter',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\type_adapter.py',
   'PYMODULE'),
  ('pydantic.networks',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\networks.py',
   'PYMODULE'),
  ('pydantic.main',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\main.py',
   'PYMODULE'),
  ('pydantic.json_schema',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\json_schema.py',
   'PYMODULE'),
  ('pydantic.functional_validators',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\functional_validators.py',
   'PYMODULE'),
  ('pydantic.functional_serializers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\functional_serializers.py',
   'PYMODULE'),
  ('pydantic.fields',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\fields.py',
   'PYMODULE'),
  ('pydantic.errors',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\errors.py',
   'PYMODULE'),
  ('pydantic.config',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\config.py',
   'PYMODULE'),
  ('pydantic.annotated_handlers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\annotated_handlers.py',
   'PYMODULE'),
  ('pydantic.aliases',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\aliases.py',
   'PYMODULE'),
  ('pydantic.dataclasses',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.types',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\types.py',
   'PYMODULE'),
  ('pydantic_core.core_schema',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic_core\\core_schema.py',
   'PYMODULE'),
  ('pydantic_core',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic_core\\__init__.py',
   'PYMODULE'),
  ('pydantic.version',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\version.py',
   'PYMODULE'),
  ('pydantic._migration',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic\\_migration.py',
   'PYMODULE'),
  ('fastapi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\__init__.py',
   'PYMODULE'),
  ('fastapi.websockets',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\websockets.py',
   'PYMODULE'),
  ('starlette.websockets',
   'D:\\app\\python_3.11\\Lib\\site-packages\\starlette\\websockets.py',
   'PYMODULE'),
  ('starlette.types',
   'D:\\app\\python_3.11\\Lib\\site-packages\\starlette\\types.py',
   'PYMODULE'),
  ('starlette.responses',
   'D:\\app\\python_3.11\\Lib\\site-packages\\starlette\\responses.py',
   'PYMODULE'),
  ('starlette.datastructures',
   'D:\\app\\python_3.11\\Lib\\site-packages\\starlette\\datastructures.py',
   'PYMODULE'),
  ('starlette.concurrency',
   'D:\\app\\python_3.11\\Lib\\site-packages\\starlette\\concurrency.py',
   'PYMODULE'),
  ('starlette.background',
   'D:\\app\\python_3.11\\Lib\\site-packages\\starlette\\background.py',
   'PYMODULE'),
  ('starlette._utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\starlette\\_utils.py',
   'PYMODULE'),
  ('anyio.to_thread',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\to_thread.py',
   'PYMODULE'),
  ('anyio.abc',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\abc\\__init__.py',
   'PYMODULE'),
  ('anyio.from_thread',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\from_thread.py',
   'PYMODULE'),
  ('anyio._core',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\_core\\__init__.py',
   'PYMODULE'),
  ('anyio._core._tasks',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\_core\\_tasks.py',
   'PYMODULE'),
  ('anyio._core._synchronization',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\_core\\_synchronization.py',
   'PYMODULE'),
  ('anyio._core._testing',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\_core\\_testing.py',
   'PYMODULE'),
  ('anyio._core._exceptions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('anyio.lowlevel',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\lowlevel.py',
   'PYMODULE'),
  ('sniffio',
   'D:\\app\\python_3.11\\Lib\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'D:\\app\\python_3.11\\Lib\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('anyio.abc._testing',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\abc\\_testing.py',
   'PYMODULE'),
  ('anyio.abc._tasks',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\abc\\_tasks.py',
   'PYMODULE'),
  ('anyio.abc._subprocesses',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\abc\\_subprocesses.py',
   'PYMODULE'),
  ('anyio.abc._streams',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\abc\\_streams.py',
   'PYMODULE'),
  ('anyio._core._typedattr',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\_core\\_typedattr.py',
   'PYMODULE'),
  ('anyio.abc._sockets',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\abc\\_sockets.py',
   'PYMODULE'),
  ('anyio._core._sockets',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\_core\\_sockets.py',
   'PYMODULE'),
  ('idna',
   'D:\\app\\python_3.11\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\app\\python_3.11\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\app\\python_3.11\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\app\\python_3.11\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\app\\python_3.11\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\app\\python_3.11\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('anyio._core._resources',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\_core\\_resources.py',
   'PYMODULE'),
  ('anyio.streams.tls',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\streams\\tls.py',
   'PYMODULE'),
  ('anyio.streams',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\streams\\__init__.py',
   'PYMODULE'),
  ('anyio.streams.stapled',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\streams\\stapled.py',
   'PYMODULE'),
  ('anyio.abc._resources',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\abc\\_resources.py',
   'PYMODULE'),
  ('anyio.abc._eventloop',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\abc\\_eventloop.py',
   'PYMODULE'),
  ('anyio._core._eventloop',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\_core\\_eventloop.py',
   'PYMODULE'),
  ('anyio',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\__init__.py',
   'PYMODULE'),
  ('anyio._backends._trio',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\_backends\\_trio.py',
   'PYMODULE'),
  ('anyio.streams.memory',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\streams\\memory.py',
   'PYMODULE'),
  ('anyio._backends._asyncio',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\_backends\\_asyncio.py',
   'PYMODULE'),
  ('anyio._core._asyncio_selector_thread',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\_core\\_asyncio_selector_thread.py',
   'PYMODULE'),
  ('anyio._backends',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\_backends\\__init__.py',
   'PYMODULE'),
  ('anyio._core._subprocesses',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\_core\\_subprocesses.py',
   'PYMODULE'),
  ('anyio._core._streams',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\_core\\_streams.py',
   'PYMODULE'),
  ('anyio._core._signals',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\_core\\_signals.py',
   'PYMODULE'),
  ('anyio._core._fileio',
   'D:\\app\\python_3.11\\Lib\\site-packages\\anyio\\_core\\_fileio.py',
   'PYMODULE'),
  ('starlette.requests',
   'D:\\app\\python_3.11\\Lib\\site-packages\\starlette\\requests.py',
   'PYMODULE'),
  ('multipart.multipart',
   'D:\\app\\python_3.11\\Lib\\site-packages\\multipart\\multipart.py',
   'PYMODULE'),
  ('multipart',
   'D:\\app\\python_3.11\\Lib\\site-packages\\multipart\\__init__.py',
   'PYMODULE'),
  ('python_multipart',
   'D:\\app\\python_3.11\\Lib\\site-packages\\python_multipart\\__init__.py',
   'PYMODULE'),
  ('starlette.routing',
   'D:\\app\\python_3.11\\Lib\\site-packages\\starlette\\routing.py',
   'PYMODULE'),
  ('starlette.middleware',
   'D:\\app\\python_3.11\\Lib\\site-packages\\starlette\\middleware\\__init__.py',
   'PYMODULE'),
  ('starlette.convertors',
   'D:\\app\\python_3.11\\Lib\\site-packages\\starlette\\convertors.py',
   'PYMODULE'),
  ('starlette._exception_handler',
   'D:\\app\\python_3.11\\Lib\\site-packages\\starlette\\_exception_handler.py',
   'PYMODULE'),
  ('starlette.applications',
   'D:\\app\\python_3.11\\Lib\\site-packages\\starlette\\applications.py',
   'PYMODULE'),
  ('starlette.middleware.exceptions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\starlette\\middleware\\exceptions.py',
   'PYMODULE'),
  ('starlette.middleware.errors',
   'D:\\app\\python_3.11\\Lib\\site-packages\\starlette\\middleware\\errors.py',
   'PYMODULE'),
  ('starlette.middleware.base',
   'D:\\app\\python_3.11\\Lib\\site-packages\\starlette\\middleware\\base.py',
   'PYMODULE'),
  ('python_multipart.multipart',
   'D:\\app\\python_3.11\\Lib\\site-packages\\python_multipart\\multipart.py',
   'PYMODULE'),
  ('python_multipart.exceptions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\python_multipart\\exceptions.py',
   'PYMODULE'),
  ('python_multipart.decoders',
   'D:\\app\\python_3.11\\Lib\\site-packages\\python_multipart\\decoders.py',
   'PYMODULE'),
  ('starlette.formparsers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\starlette\\formparsers.py',
   'PYMODULE'),
  ('starlette.exceptions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\starlette\\exceptions.py',
   'PYMODULE'),
  ('fastapi.responses',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\responses.py',
   'PYMODULE'),
  ('orjson',
   'D:\\app\\python_3.11\\Lib\\site-packages\\orjson\\__init__.py',
   'PYMODULE'),
  ('fastapi.requests',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\requests.py',
   'PYMODULE'),
  ('fastapi.param_functions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\param_functions.py',
   'PYMODULE'),
  ('fastapi.openapi.models',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\openapi\\models.py',
   'PYMODULE'),
  ('fastapi.openapi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\openapi\\__init__.py',
   'PYMODULE'),
  ('fastapi.logger',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\logger.py',
   'PYMODULE'),
  ('fastapi._compat',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\_compat.py',
   'PYMODULE'),
  ('fastapi.openapi.constants',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\openapi\\constants.py',
   'PYMODULE'),
  ('fastapi.types',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\types.py',
   'PYMODULE'),
  ('fastapi.exceptions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\exceptions.py',
   'PYMODULE'),
  ('fastapi.datastructures',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\datastructures.py',
   'PYMODULE'),
  ('fastapi.background',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\background.py',
   'PYMODULE'),
  ('fastapi.applications',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\applications.py',
   'PYMODULE'),
  ('fastapi.utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\utils.py',
   'PYMODULE'),
  ('fastapi.openapi.utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\openapi\\utils.py',
   'PYMODULE'),
  ('fastapi.encoders',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\encoders.py',
   'PYMODULE'),
  ('fastapi.dependencies.utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\dependencies\\utils.py',
   'PYMODULE'),
  ('fastapi.dependencies',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\dependencies\\__init__.py',
   'PYMODULE'),
  ('fastapi.security.open_id_connect_url',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\security\\open_id_connect_url.py',
   'PYMODULE'),
  ('fastapi.security',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\security\\__init__.py',
   'PYMODULE'),
  ('fastapi.security.http',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\security\\http.py',
   'PYMODULE'),
  ('fastapi.security.utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\security\\utils.py',
   'PYMODULE'),
  ('fastapi.security.api_key',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\security\\api_key.py',
   'PYMODULE'),
  ('fastapi.security.oauth2',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\security\\oauth2.py',
   'PYMODULE'),
  ('fastapi.security.base',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\security\\base.py',
   'PYMODULE'),
  ('fastapi.concurrency',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\concurrency.py',
   'PYMODULE'),
  ('fastapi.dependencies.models',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\dependencies\\models.py',
   'PYMODULE'),
  ('fastapi.openapi.docs',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\openapi\\docs.py',
   'PYMODULE'),
  ('fastapi.exception_handlers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\exception_handlers.py',
   'PYMODULE'),
  ('fastapi.routing',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\routing.py',
   'PYMODULE'),
  ('fastapi.params',
   'D:\\app\\python_3.11\\Lib\\site-packages\\fastapi\\params.py',
   'PYMODULE'),
  ('starlette.status',
   'D:\\app\\python_3.11\\Lib\\site-packages\\starlette\\status.py',
   'PYMODULE'),
  ('starlette',
   'D:\\app\\python_3.11\\Lib\\site-packages\\starlette\\__init__.py',
   'PYMODULE'),
  ('uvicorn',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\__init__.py',
   'PYMODULE'),
  ('uvicorn.workers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\workers.py',
   'PYMODULE'),
  ('uvicorn.supervisors.watchfilesreload',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\supervisors\\watchfilesreload.py',
   'PYMODULE'),
  ('watchfiles',
   'D:\\app\\python_3.11\\Lib\\site-packages\\watchfiles\\__init__.py',
   'PYMODULE'),
  ('watchfiles.version',
   'D:\\app\\python_3.11\\Lib\\site-packages\\watchfiles\\version.py',
   'PYMODULE'),
  ('watchfiles.run',
   'D:\\app\\python_3.11\\Lib\\site-packages\\watchfiles\\run.py',
   'PYMODULE'),
  ('watchfiles.main',
   'D:\\app\\python_3.11\\Lib\\site-packages\\watchfiles\\main.py',
   'PYMODULE'),
  ('watchfiles.filters',
   'D:\\app\\python_3.11\\Lib\\site-packages\\watchfiles\\filters.py',
   'PYMODULE'),
  ('uvicorn.supervisors.statreload',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\supervisors\\statreload.py',
   'PYMODULE'),
  ('uvicorn.supervisors.multiprocess',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\supervisors\\multiprocess.py',
   'PYMODULE'),
  ('click',
   'D:\\app\\python_3.11\\Lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click.utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('click._compat',
   'D:\\app\\python_3.11\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('colorama',
   'D:\\app\\python_3.11\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\app\\python_3.11\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\app\\python_3.11\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\app\\python_3.11\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('click._winconsole',
   'D:\\app\\python_3.11\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.termui',
   'D:\\app\\python_3.11\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click._termui_impl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click.parser',
   'D:\\app\\python_3.11\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.globals',
   'D:\\app\\python_3.11\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.formatting',
   'D:\\app\\python_3.11\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click._textwrap',
   'D:\\app\\python_3.11\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click.exceptions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.decorators',
   'D:\\app\\python_3.11\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('importlib_metadata',
   'D:\\app\\python_3.11\\Lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'D:\\app\\python_3.11\\Lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat',
   'D:\\app\\python_3.11\\Lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp',
   'D:\\app\\python_3.11\\Lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp._functools',
   'D:\\app\\python_3.11\\Lib\\site-packages\\zipp\\_functools.py',
   'PYMODULE'),
  ('zipp.glob',
   'D:\\app\\python_3.11\\Lib\\site-packages\\zipp\\glob.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'D:\\app\\python_3.11\\Lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'D:\\app\\python_3.11\\Lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'D:\\app\\python_3.11\\Lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'D:\\app\\python_3.11\\Lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'D:\\app\\python_3.11\\Lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'D:\\app\\python_3.11\\Lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'D:\\app\\python_3.11\\Lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'D:\\app\\python_3.11\\Lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'D:\\app\\python_3.11\\Lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'D:\\app\\python_3.11\\Lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'D:\\app\\python_3.11\\Lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('click.core',
   'D:\\app\\python_3.11\\Lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.shell_completion',
   'D:\\app\\python_3.11\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.types',
   'D:\\app\\python_3.11\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('uvicorn.supervisors.basereload',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\supervisors\\basereload.py',
   'PYMODULE'),
  ('uvicorn.supervisors',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\supervisors\\__init__.py',
   'PYMODULE'),
  ('uvicorn.server',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\server.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.wsproto_impl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\protocols\\websockets\\wsproto_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.websockets_impl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\protocols\\websockets\\websockets_impl.py',
   'PYMODULE'),
  ('websockets.typing',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\typing.py',
   'PYMODULE'),
  ('websockets.server',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\server.py',
   'PYMODULE'),
  ('websockets.utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\utils.py',
   'PYMODULE'),
  ('websockets.protocol',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\protocol.py',
   'PYMODULE'),
  ('websockets.streams',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\streams.py',
   'PYMODULE'),
  ('websockets.frames',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\frames.py',
   'PYMODULE'),
  ('websockets.imports',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\imports.py',
   'PYMODULE'),
  ('websockets.http11',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\http11.py',
   'PYMODULE'),
  ('websockets.version',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\version.py',
   'PYMODULE'),
  ('websockets.headers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\headers.py',
   'PYMODULE'),
  ('websockets.extensions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\extensions\\__init__.py',
   'PYMODULE'),
  ('websockets.legacy.server',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\legacy\\server.py',
   'PYMODULE'),
  ('websockets.legacy',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\legacy\\__init__.py',
   'PYMODULE'),
  ('websockets.legacy.protocol',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\legacy\\protocol.py',
   'PYMODULE'),
  ('websockets.legacy.framing',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\legacy\\framing.py',
   'PYMODULE'),
  ('websockets.legacy.http',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\legacy\\http.py',
   'PYMODULE'),
  ('websockets.legacy.exceptions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\legacy\\exceptions.py',
   'PYMODULE'),
  ('websockets.asyncio.compatibility',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\asyncio\\compatibility.py',
   'PYMODULE'),
  ('websockets.asyncio',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\asyncio\\__init__.py',
   'PYMODULE'),
  ('websockets.asyncio.async_timeout',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\asyncio\\async_timeout.py',
   'PYMODULE'),
  ('websockets.extensions.permessage_deflate',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\extensions\\permessage_deflate.py',
   'PYMODULE'),
  ('websockets.extensions.base',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\extensions\\base.py',
   'PYMODULE'),
  ('websockets.exceptions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\exceptions.py',
   'PYMODULE'),
  ('websockets.datastructures',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\datastructures.py',
   'PYMODULE'),
  ('websockets.legacy.handshake',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\legacy\\handshake.py',
   'PYMODULE'),
  ('websockets',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\__init__.py',
   'PYMODULE'),
  ('websockets.uri',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\uri.py',
   'PYMODULE'),
  ('websockets.sync.utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\sync\\utils.py',
   'PYMODULE'),
  ('websockets.sync.server',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\sync\\server.py',
   'PYMODULE'),
  ('websockets.sync.router',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\sync\\router.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('markupsafe',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.http',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.test',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('bcrypt',
   'D:\\app\\python_3.11\\Lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('websockets.sync.messages',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\sync\\messages.py',
   'PYMODULE'),
  ('websockets.sync.connection',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\sync\\connection.py',
   'PYMODULE'),
  ('websockets.sync.client',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\sync\\client.py',
   'PYMODULE'),
  ('websockets.sync',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\sync\\__init__.py',
   'PYMODULE'),
  ('websockets.legacy.client',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\legacy\\client.py',
   'PYMODULE'),
  ('websockets.legacy.auth',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\legacy\\auth.py',
   'PYMODULE'),
  ('websockets.http',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\http.py',
   'PYMODULE'),
  ('websockets.connection',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\connection.py',
   'PYMODULE'),
  ('websockets.cli',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\cli.py',
   'PYMODULE'),
  ('websockets.auth',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\auth.py',
   'PYMODULE'),
  ('websockets.asyncio.messages',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\asyncio\\messages.py',
   'PYMODULE'),
  ('websockets.asyncio.connection',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\asyncio\\connection.py',
   'PYMODULE'),
  ('websockets.__main__',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\__main__.py',
   'PYMODULE'),
  ('websockets.client',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\client.py',
   'PYMODULE'),
  ('websockets.asyncio.server',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\asyncio\\server.py',
   'PYMODULE'),
  ('websockets.asyncio.router',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\asyncio\\router.py',
   'PYMODULE'),
  ('websockets.asyncio.client',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\asyncio\\client.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.auto',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\protocols\\websockets\\auto.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\protocols\\websockets\\__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols.utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\protocols\\utils.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.httptools_impl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\protocols\\http\\httptools_impl.py',
   'PYMODULE'),
  ('httptools',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httptools\\__init__.py',
   'PYMODULE'),
  ('httptools._version',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httptools\\_version.py',
   'PYMODULE'),
  ('httptools.parser',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httptools\\parser\\__init__.py',
   'PYMODULE'),
  ('httptools.parser.errors',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httptools\\parser\\errors.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.h11_impl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\protocols\\http\\h11_impl.py',
   'PYMODULE'),
  ('h11._connection',
   'D:\\app\\python_3.11\\Lib\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._writers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('h11._util',
   'D:\\app\\python_3.11\\Lib\\site-packages\\h11\\_util.py',
   'PYMODULE'),
  ('h11._state',
   'D:\\app\\python_3.11\\Lib\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'D:\\app\\python_3.11\\Lib\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._readers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('h11._abnf',
   'D:\\app\\python_3.11\\Lib\\site-packages\\h11\\_abnf.py',
   'PYMODULE'),
  ('h11._headers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._events',
   'D:\\app\\python_3.11\\Lib\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11',
   'D:\\app\\python_3.11\\Lib\\site-packages\\h11\\__init__.py',
   'PYMODULE'),
  ('h11._version',
   'D:\\app\\python_3.11\\Lib\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.flow_control',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\protocols\\http\\flow_control.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.auto',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\protocols\\http\\auto.py',
   'PYMODULE'),
  ('uvicorn.protocols.http',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\protocols\\http\\__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\protocols\\__init__.py',
   'PYMODULE'),
  ('uvicorn.middleware.wsgi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\middleware\\wsgi.py',
   'PYMODULE'),
  ('uvicorn.middleware.proxy_headers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\middleware\\proxy_headers.py',
   'PYMODULE'),
  ('uvicorn.middleware.message_logger',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\middleware\\message_logger.py',
   'PYMODULE'),
  ('uvicorn.middleware.asgi2',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\middleware\\asgi2.py',
   'PYMODULE'),
  ('uvicorn.middleware',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\middleware\\__init__.py',
   'PYMODULE'),
  ('uvicorn.loops.uvloop',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\loops\\uvloop.py',
   'PYMODULE'),
  ('uvicorn.loops.auto',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\loops\\auto.py',
   'PYMODULE'),
  ('uvicorn.loops.asyncio',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\loops\\asyncio.py',
   'PYMODULE'),
  ('uvicorn.loops',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\loops\\__init__.py',
   'PYMODULE'),
  ('uvicorn.logging',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\logging.py',
   'PYMODULE'),
  ('uvicorn.lifespan.on',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\lifespan\\on.py',
   'PYMODULE'),
  ('uvicorn.lifespan.off',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\lifespan\\off.py',
   'PYMODULE'),
  ('uvicorn.lifespan',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\lifespan\\__init__.py',
   'PYMODULE'),
  ('uvicorn.importer',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\importer.py',
   'PYMODULE'),
  ('uvicorn._types',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\_types.py',
   'PYMODULE'),
  ('uvicorn._subprocess',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\_subprocess.py',
   'PYMODULE'),
  ('uvicorn.__main__',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\__main__.py',
   'PYMODULE'),
  ('uvicorn.main',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\main.py',
   'PYMODULE'),
  ('uvicorn.config',
   'D:\\app\\python_3.11\\Lib\\site-packages\\uvicorn\\config.py',
   'PYMODULE'),
  ('logging.config',
   'D:\\app\\python_3.11\\Lib\\logging\\config.py',
   'PYMODULE'),
  ('httpx',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\__init__.py',
   'PYMODULE'),
  ('httpx._main',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_main.py',
   'PYMODULE'),
  ('httpcore',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\__init__.py',
   'PYMODULE'),
  ('httpcore._backends.trio',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_backends\\trio.py',
   'PYMODULE'),
  ('httpcore._backends',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_backends\\__init__.py',
   'PYMODULE'),
  ('httpcore._backends.anyio',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_backends\\anyio.py',
   'PYMODULE'),
  ('httpcore._utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_utils.py',
   'PYMODULE'),
  ('httpcore._sync',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_sync\\__init__.py',
   'PYMODULE'),
  ('httpcore._sync.socks_proxy',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_sync\\socks_proxy.py',
   'PYMODULE'),
  ('httpcore._trace',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_trace.py',
   'PYMODULE'),
  ('httpcore._synchronization',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_synchronization.py',
   'PYMODULE'),
  ('httpcore._sync.http2',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_sync\\http2.py',
   'PYMODULE'),
  ('httpcore._sync.interfaces',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_sync\\interfaces.py',
   'PYMODULE'),
  ('httpcore._sync.http_proxy',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_sync\\http_proxy.py',
   'PYMODULE'),
  ('httpcore._sync.http11',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_sync\\http11.py',
   'PYMODULE'),
  ('httpcore._sync.connection_pool',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_sync\\connection_pool.py',
   'PYMODULE'),
  ('httpcore._sync.connection',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_sync\\connection.py',
   'PYMODULE'),
  ('httpcore._ssl',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_ssl.py',
   'PYMODULE'),
  ('certifi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\app\\python_3.11\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('httpcore._models',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_models.py',
   'PYMODULE'),
  ('httpcore._exceptions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_exceptions.py',
   'PYMODULE'),
  ('httpcore._backends.sync',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_backends\\sync.py',
   'PYMODULE'),
  ('httpcore._backends.mock',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_backends\\mock.py',
   'PYMODULE'),
  ('httpcore._backends.base',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_backends\\base.py',
   'PYMODULE'),
  ('httpcore._async',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_async\\__init__.py',
   'PYMODULE'),
  ('httpcore._async.socks_proxy',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_async\\socks_proxy.py',
   'PYMODULE'),
  ('httpcore._backends.auto',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_backends\\auto.py',
   'PYMODULE'),
  ('httpcore._async.http2',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_async\\http2.py',
   'PYMODULE'),
  ('httpcore._async.interfaces',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_async\\interfaces.py',
   'PYMODULE'),
  ('httpcore._async.http_proxy',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_async\\http_proxy.py',
   'PYMODULE'),
  ('httpcore._async.http11',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_async\\http11.py',
   'PYMODULE'),
  ('httpcore._async.connection_pool',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_async\\connection_pool.py',
   'PYMODULE'),
  ('httpcore._async.connection',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_async\\connection.py',
   'PYMODULE'),
  ('httpcore._api',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpcore\\_api.py',
   'PYMODULE'),
  ('rich.progress',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\progress.py',
   'PYMODULE'),
  ('rich.progress_bar',
   'D:\\app\\python_3.11\\Lib\\site-packages\\rich\\progress_bar.py',
   'PYMODULE'),
  ('httpx._urls',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_urls.py',
   'PYMODULE'),
  ('httpx._utils',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_utils.py',
   'PYMODULE'),
  ('httpx._urlparse',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_urlparse.py',
   'PYMODULE'),
  ('httpx._types',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_types.py',
   'PYMODULE'),
  ('httpx._transports',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_transports\\__init__.py',
   'PYMODULE'),
  ('httpx._transports.wsgi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_transports\\wsgi.py',
   'PYMODULE'),
  ('httpx._transports.mock',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_transports\\mock.py',
   'PYMODULE'),
  ('httpx._transports.default',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_transports\\default.py',
   'PYMODULE'),
  ('httpx._transports.base',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_transports\\base.py',
   'PYMODULE'),
  ('httpx._transports.asgi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_transports\\asgi.py',
   'PYMODULE'),
  ('httpx._status_codes',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_status_codes.py',
   'PYMODULE'),
  ('httpx._models',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_models.py',
   'PYMODULE'),
  ('httpx._multipart',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_multipart.py',
   'PYMODULE'),
  ('httpx._decoders',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_decoders.py',
   'PYMODULE'),
  ('zstandard',
   'D:\\app\\python_3.11\\Lib\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE'),
  ('brotli', 'D:\\app\\python_3.11\\Lib\\site-packages\\brotli.py', 'PYMODULE'),
  ('httpx._exceptions',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_exceptions.py',
   'PYMODULE'),
  ('httpx._content',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_content.py',
   'PYMODULE'),
  ('httpx._config',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_config.py',
   'PYMODULE'),
  ('httpx._client',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_client.py',
   'PYMODULE'),
  ('httpx._auth',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_auth.py',
   'PYMODULE'),
  ('httpx._api',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\_api.py',
   'PYMODULE'),
  ('httpx.__version__',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httpx\\__version__.py',
   'PYMODULE'),
  ('typing', 'D:\\app\\python_3.11\\Lib\\typing.py', 'PYMODULE'),
  ('datetime', 'D:\\app\\python_3.11\\Lib\\datetime.py', 'PYMODULE'),
  ('socket', 'D:\\app\\python_3.11\\Lib\\socket.py', 'PYMODULE'),
  ('webbrowser', 'D:\\app\\python_3.11\\Lib\\webbrowser.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\app\\python_3.11\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('json', 'D:\\app\\python_3.11\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'D:\\app\\python_3.11\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'D:\\app\\python_3.11\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\app\\python_3.11\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging.handlers',
   'D:\\app\\python_3.11\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'D:\\app\\python_3.11\\Lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'D:\\app\\python_3.11\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('win32con',
   'D:\\app\\python_3.11\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('smtplib', 'D:\\app\\python_3.11\\Lib\\smtplib.py', 'PYMODULE'),
  ('logging', 'D:\\app\\python_3.11\\Lib\\logging\\__init__.py', 'PYMODULE')],
 [('python311.dll', 'D:\\app\\python_3.11\\python311.dll', 'BINARY'),
  ('PySide6\\plugins\\imageformats\\qgif.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qsvg.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtiff.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwebp.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qoffscreen.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qminimal.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qdirect2d.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qdirect2d.dll',
   'BINARY'),
  ('PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwbmp.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qwindows.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qico.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qpdf.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtga.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qjpeg.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qicns.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PySide6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('PySide6\\opengl32sw.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\opengl32sw.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qschannelbackend.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\tls\\qschannelbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qopensslbackend.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\tls\\qopensslbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'BINARY'),
  ('numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'BINARY'),
  ('select.pyd', 'D:\\app\\python_3.11\\DLLs\\select.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\app\\python_3.11\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\app\\python_3.11\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\app\\python_3.11\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'D:\\app\\python_3.11\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\app\\python_3.11\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\app\\python_3.11\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\app\\python_3.11\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\app\\python_3.11\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\app\\python_3.11\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\app\\python_3.11\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('PySide6\\QtCore.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\QtCore.pyd',
   'EXTENSION'),
  ('shiboken6\\Shiboken.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\shiboken6\\Shiboken.pyd',
   'EXTENSION'),
  ('PySide6\\QtGui.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\QtGui.pyd',
   'EXTENSION'),
  ('PySide6\\QtWidgets.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\QtWidgets.pyd',
   'EXTENSION'),
  ('PySide6\\QtNetwork.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\QtNetwork.pyd',
   'EXTENSION'),
  ('_socket.pyd', 'D:\\app\\python_3.11\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_uuid.pyd', 'D:\\app\\python_3.11\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('pydantic_core\\_pydantic_core.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pydantic_core\\_pydantic_core.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_zoneinfo.pyd', 'D:\\app\\python_3.11\\DLLs\\_zoneinfo.pyd', 'EXTENSION'),
  ('PIL\\_imagingft.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\_imagingft.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\_imaging.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\app\\python_3.11\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd', 'D:\\app\\python_3.11\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\random\\_philox.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\random\\_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\random\\_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\yaml\\_yaml.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\_webp.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\_imagingtk.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\_imagingcms.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PIL\\_imagingmath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\app\\python_3.11\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('orjson\\orjson.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\orjson\\orjson.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('watchfiles\\_rust_notify.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\watchfiles\\_rust_notify.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('websockets\\speedups.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets\\speedups.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\markupsafe\\_speedups.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('bcrypt\\_bcrypt.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\bcrypt\\_bcrypt.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('httptools\\parser\\url_parser.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httptools\\parser\\url_parser.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('httptools\\parser\\parser.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\httptools\\parser\\parser.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\_cffi.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\zstandard\\_cffi.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\backend_c.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\zstandard\\backend_c.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_brotli.cp311-win_amd64.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\_brotli.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32evtlog.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'D:\\app\\python_3.11\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('PySide6\\Qt6Core.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\Qt6Core.dll',
   'BINARY'),
  ('PySide6\\Qt6Gui.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\Qt6Gui.dll',
   'BINARY'),
  ('PySide6\\Qt6Svg.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\Qt6Svg.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('shiboken6\\MSVCP140.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\shiboken6\\MSVCP140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\app\\python_3.11\\VCRUNTIME140_1.dll', 'BINARY'),
  ('PySide6\\Qt6Network.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\Qt6Network.dll',
   'BINARY'),
  ('PySide6\\Qt6VirtualKeyboard.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\Qt6VirtualKeyboard.dll',
   'BINARY'),
  ('PySide6\\Qt6Pdf.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\Qt6Pdf.dll',
   'BINARY'),
  ('PySide6\\Qt6Widgets.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\Qt6Widgets.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'D:\\app\\python_3.11\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll', 'D:\\app\\python_3.11\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('libffi-8.dll', 'D:\\app\\python_3.11\\DLLs\\libffi-8.dll', 'BINARY'),
  ('PySide6\\VCRUNTIME140_1.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PySide6\\MSVCP140.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\MSVCP140.dll',
   'BINARY'),
  ('PySide6\\VCRUNTIME140.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\VCRUNTIME140.dll',
   'BINARY'),
  ('python3.dll', 'D:\\app\\python_3.11\\python3.dll', 'BINARY'),
  ('shiboken6\\shiboken6.abi3.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\shiboken6\\shiboken6.abi3.dll',
   'BINARY'),
  ('PySide6\\pyside6.abi3.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\pyside6.abi3.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140_1.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\shiboken6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\shiboken6\\VCRUNTIME140.dll',
   'BINARY'),
  ('PySide6\\MSVCP140_2.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\MSVCP140_2.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes311.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pywin32_system32\\pywintypes311.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('ucrtbase.dll', 'D:\\app\\Java\\jdk-17\\bin\\ucrtbase.dll', 'BINARY'),
  ('PySide6\\MSVCP140_1.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\MSVCP140_1.dll',
   'BINARY'),
  ('PySide6\\Qt6Quick.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\Qt6Quick.dll',
   'BINARY'),
  ('PySide6\\Qt6Qml.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\Qt6Qml.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\app\\Java\\jdk-17\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlModels.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\Qt6QmlModels.dll',
   'BINARY'),
  ('PySide6\\Qt6OpenGL.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\Qt6OpenGL.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlMeta.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\Qt6QmlMeta.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlWorkerScript.dll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\Qt6QmlWorkerScript.dll',
   'BINARY')],
 [],
 [],
 [('config.json',
   'D:\\a_project\\aliyun\\xiondon\\aikefu\\APILLM\\src\\转发\\config.json',
   'DATA'),
  ('PySide6\\translations\\qt_cs.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ar.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hr.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_de.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hr.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ru.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_tr.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_gl.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_bg.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fr.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nl.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_CN.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lt.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_lt.qm',
   'DATA'),
  ('PySide6\\translations\\qt_he.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_he.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_uk.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hu.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hu.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ar.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ru.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_tr.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_es.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ko.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nn.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_fr.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gl.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ka.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ja.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_PT.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sl.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ca.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_es.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_en.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pl.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_CN.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_da.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_CN.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_da.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ka.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nl.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ja.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_it.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_de.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_BR.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ar.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hr.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gd.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_cs.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ca.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_uk.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pt_BR.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_lv.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nn.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_it.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sk.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pt_BR.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_it.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pl.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_TW.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qt_es.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_es.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ka.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hu.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_cs.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fr.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_gd.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fi.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qt_en.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_da.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_TW.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_de.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sl.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_bg.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_tr.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ca.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sk.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ru.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pl.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_uk.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lv.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ko.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_he.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_bg.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nl.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_sk.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fa.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sv.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_sv.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ja.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fi.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ko.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_en.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nn.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fa.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_TW.qm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\PySide6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kigali',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Luanda',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lusaka',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vatican',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yangon',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montreal',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-3',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ojinaga',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tel_Aviv',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Grand_Turk',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\UCT',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tomsk',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cordoba',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tegucigalpa',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Phnom_Penh',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kamchatka',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('tzdata\\zoneinfo\\Greenwich',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Greenwich',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Budapest',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Lisbon',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-12',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Chatham',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Reunion',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Barbados',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bahrain',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bogota',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Adak',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Adak',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Douala',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Singapore',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Merida',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Merida',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aqtobe',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bamako',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Michigan',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Copenhagen',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Kerguelen',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Prague',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Chuuk',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Newfoundland',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Marengo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bahia_Banderas',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Lord_Howe',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Creston',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Creston',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\Center',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('tzdata\\zoneinfo\\PRC',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\PRC',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Oral',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Pacific',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Maputo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Chicago',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('tzdata\\zoneinfo\\ROK',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\ROK',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Los_Angeles',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Cape_Verde',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Omsk',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Factory',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Factory',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Belize',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Belize',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Bermuda',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-5',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guatemala',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Riga',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuching',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Thomas',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Gambier',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kyiv',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Martinique',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Punta_Arenas',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\London',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\London',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+9',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cuiaba',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('tzdata\\zones',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zones',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Busingen',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pago_Pago',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Virgin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Broken_Hill',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Cordoba',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\San_Juan',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Vancouver',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Metlakatla',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Vincennes',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Isle_of_Man',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-1',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Podgorica',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Ponape',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Belgrade',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dubai',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dushanbe',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fort_Nelson',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Eucla',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Abidjan',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('tzdata\\zoneinfo\\CST6CDT',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\CST6CDT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Whitehorse',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Hobart',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Alaska',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Juba',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\San_Marino',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Sitka',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Sao_Tome',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Rarotonga',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Saigon',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dacca',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tortola',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Chihuahua',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+7',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Apia',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tirane',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT0',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\GMT0',
   'DATA'),
  ('tzdata\\zoneinfo\\Zulu',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Zulu',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Cairo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Calcutta',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Cocos',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Andorra',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Puerto_Rico',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Maceio',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kolkata',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuwait',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('tzdata\\zoneinfo\\Egypt',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Egypt',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Jujuy',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\North',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\North',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Niue',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cayman',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Buenos_Aires',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('tzdata\\zoneinfo\\UTC',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\UTC',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Atyrau',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Midway',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Astrakhan',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Jamaica',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Tasmania',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('tzdata\\zoneinfo\\EET',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\EET',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Helsinki',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\West',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\West',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Warsaw',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Samoa',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Scoresbysund',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Enderbury',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vilnius',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Riyadh',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Havana',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Havana',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Bratislava',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Canberra',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Istanbul',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Makassar',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Brunei',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santo_Domingo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Samoa',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dhaka',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Porto_Velho',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Guam',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dawson',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Boa_Vista',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bangkok',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-9',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zaporozhye',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Funafuti',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kiev',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Ulyanovsk',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guayaquil',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Simferopol',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Jersey',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Choibalsan',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Istanbul',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Manaus',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('tzdata\\zoneinfo\\Navajo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Navajo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nome',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Nome',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Niamey',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guadeloupe',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Atka',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Atka',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Araguaina',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Victoria',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Galapagos',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Ushuaia',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Asmera',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Thimbu',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Catamarca',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Yancowinna',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bujumbura',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dominica',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('tzdata\\zoneinfo\\Portugal',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Portugal',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Greenwich',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Mountain',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('tzdata\\zoneinfo\\Eire',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Eire',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Saskatchewan',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Windhoek',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kampala',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Harbin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Recife',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Recife',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Darwin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Faeroe',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Norfolk',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Vientiane',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Brazzaville',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Managua',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Managua',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Tunis',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Shanghai',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Port_Moresby',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Mendoza',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pohnpei',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('tzdata\\zoneinfo\\MST',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\MST',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Gibraltar',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Indianapolis',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mendoza',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Sakhalin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Maseru',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Glace_Bay',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\MET',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\MET',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Comoro',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Malabo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Inuvik',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT+0',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\GMT+0',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Costa_Rica',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Denver',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Denver',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Reykjavik',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Lucia',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Perth',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('tzdata\\zoneinfo\\Cuba',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Cuba',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Aleutian',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+12',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Winamac',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-2',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kentucky\\Monticello',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\GMT',
   'DATA'),
  ('tzdata\\zoneinfo\\Arctic\\Longyearbyen',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Louisville',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tokyo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Asuncion',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Damascus',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Sofia',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\South_Georgia',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Pangnirtung',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('tzdata\\zoneinfo\\HST',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\HST',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Adelaide',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Salta',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kiritimati',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Palmer',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ndjamena',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Easter',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('tzdata\\zoneinfo\\Singapore',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Singapore',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Pyongyang',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('tzdata\\zoneinfo\\zone1970.tab',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Thimphu',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Baku',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Seoul',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Panama',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Panama',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Bucharest',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Currie',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Algiers',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Krasnoyarsk',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Blanc-Sablon',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Madeira',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Knox',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ciudad_Juarez',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Detroit',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ulan_Bator',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Syowa',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Belem',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Belem',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Moscow',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Magadan',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montserrat',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qatar',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bangui',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Coyhaique',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Honolulu',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bahia',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Rangoon',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Porto_Acre',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Stanley',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('tzdata\\zoneinfo\\W-SU',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\W-SU',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Barnaul',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\La_Paz',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\La_Rioja',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kabul',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lome',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\Acre',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Nauru',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\South_Pole',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Khartoum',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Nairobi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Saipan',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\DumontDUrville',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+11',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Banjul',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Eirunepe',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-7',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Toronto',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rio_Branco',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('tzdata\\zoneinfo\\Chile\\EasterIsland',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Yakutat',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Mountain',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Famagusta',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Marigot',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Fiji',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-0',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\UTC',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tarawa',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Johannesburg',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Beirut',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Eastern',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Minsk',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Antigua',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Winnipeg',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jakarta',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Katmandu',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chongqing',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Addis_Ababa',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Djibouti',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\St_Helena',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+8',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Maldives',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Hermosillo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tehran',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-8',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Davis',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Sao_Paulo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\General',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('tzdata\\zoneinfo\\Iran',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Iran',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Tripoli',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\McMurdo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\South',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\South',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Uzhgorod',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Auckland',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Juneau',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Volgograd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dili',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tashkent',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Iqaluit',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-13',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Guernsey',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\New_York',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\New_York',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+2',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Monterrey',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Skopje',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hebron',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Almaty',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\ACT',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Irkutsk',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Sarajevo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Atlantic',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ashkhabad',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Resolute',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kanton',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Pacific',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Conakry',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Fakaofo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tbilisi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mauritius',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('tzdata\\zoneinfo\\GB-Eire',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\GB-Eire',
   'DATA'),
  ('tzdata\\zoneinfo\\GB',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\GB',
   'DATA'),
  ('tzdata\\zoneinfo\\Jamaica',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Jamaica',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fort_Wayne',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Yukon',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Porto-Novo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Nicosia',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vienna',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hong_Kong',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Colombo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Novokuznetsk',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Campo_Grande',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Mogadishu',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ujung_Pandang',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+4',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Brisbane',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lagos',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Belfast',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Faroe',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ensenada',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Amsterdam',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Catamarca',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bishkek',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+1',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('tzdata\\zoneinfo\\MST7MDT',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\MST7MDT',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Karachi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zurich',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Danmarkshavn',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Libreville',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Godthab',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Gaza',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Paramaribo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Petersburg',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Guadalcanal',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bissau',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zagreb',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qostanay',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Coral_Harbour',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('tzdata\\zoneinfo\\leapseconds',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\leapseconds',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kirov',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Azores',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Thunder_Bay',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Manila',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Efate',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Thule',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Thule',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-10',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Phoenix',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Miquelon',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Casablanca',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('tzdata\\zoneinfo\\EST',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\EST',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Lima',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Lima',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Curacao',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Taipei',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Kitts',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rankin_Inlet',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Central',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Central',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Eastern',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Madrid',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\LHI',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Vincent',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Wake',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Lindeman',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Melbourne',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Stockholm',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Urumqi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pitcairn',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Macquarie',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Rothera',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Central',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Palau',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Oslo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('tzdata\\zoneinfo\\Iceland',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Iceland',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+3',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+10',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indianapolis',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('tzdata\\zoneinfo\\Libya',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Libya',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Bougainville',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Noumea',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Noronha',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Halifax',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Macao',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Antananarivo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('tzdata\\zoneinfo\\UCT',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\UCT',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Casey',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Johns',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Jan_Mayen',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mayotte',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Barthelemy',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('tzdata\\zoneinfo\\Chile\\Continental',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Universal',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('tzdata\\zoneinfo\\WET',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\WET',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fortaleza',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-11',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kosrae',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Troll',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Amman',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('tzdata\\zoneinfo\\Japan',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Japan',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+5',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Tell_City',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Mariehamn',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Yap',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kwajalein',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT0',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Lower_Princes',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('tzdata\\zoneinfo\\ROC',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\ROC',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Asmara',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Jujuy',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('tzdata\\zoneinfo\\Hongkong',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Hongkong',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Moncton',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Boise',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Boise',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Timbuktu',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Dublin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Matamoros',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('tzdata\\zoneinfo\\Kwajalein',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Kwajalein',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Aruba',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kentucky\\Louisville',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kralendijk',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-6',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('tzdata\\zoneinfo\\NZ-CHAT',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vaduz',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mahe',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aden',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Yellowknife',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ceuta',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qyzylorda',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Vevay',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kinshasa',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\El_Salvador',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kashgar',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Anadyr',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Baghdad',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Sydney',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Caracas',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kathmandu',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jerusalem',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Zulu',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Atikokan',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Athens',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lubumbashi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-4',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Dakar',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chungking',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('tzdata\\zoneinfo\\NZ',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\NZ',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Port-au-Prince',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Christmas',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yakutsk',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Berlin',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Monrovia',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Gaborone',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nassau',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Dar_es_Salaam',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Tucuman',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\BajaNorte',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Monaco',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('tzdata\\zoneinfo\\Universal',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Universal',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Anchorage',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kaliningrad',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Malta',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Paris',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-14',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yekaterinburg',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Pontianak',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Nicosia',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Mawson',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('tzdata\\zoneinfo\\CET',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\CET',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Shiprock',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santarem',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jayapura',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Truk',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuala_Lumpur',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Luxembourg',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('tzdata\\zoneinfo\\zonenow.tab',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cancun',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('tzdata\\zoneinfo\\iso3166.tab',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Wallis',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('tzdata\\zoneinfo\\PST8PDT',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\PST8PDT',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Nouakchott',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Johnston',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+0',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tahiti',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aqtau',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Srednekolymsk',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Arizona',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montevideo',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Indiana-Starke',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Novosibirsk',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tongatapu',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rosario',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dawson_Creek',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('tzdata\\zoneinfo\\Israel',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Israel',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Macau',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Marquesas',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('tzdata\\zoneinfo\\EST5EDT',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\EST5EDT',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Accra',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tallinn',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\Beulah',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ulaanbaatar',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Rome',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\NSW',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hovd',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santa_Isabel',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('tzdata\\zoneinfo\\Poland',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Poland',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tiraspol',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nuuk',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Hawaii',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Grenada',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Chagos',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Swift_Current',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT-0',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\GMT-0',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Ljubljana',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+6',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\BajaSur',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Chisinau',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tijuana',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Edmonton',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Queensland',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Majuro',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Saratov',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Knox_IN',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\El_Aaiun',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\East-Indiana',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rainy_River',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Regina',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Regina',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ashgabat',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ust-Nera',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Khandyga',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\West',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santiago',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ouagadougou',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\East',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Muscat',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Vostok',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('tzdata\\zoneinfo\\Turkey',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Turkey',
   'DATA'),
  ('tzdata\\zoneinfo\\zone.tab',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\zone.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\tzdata.zi',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Port_of_Spain',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Brussels',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Canary',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mazatlan',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Blantyre',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Mbabane',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chita',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cambridge_Bay',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Samara',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\San_Luis',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Vladivostok',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nipigon',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Harare',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cayenne',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guyana',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Freetown',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Goose_Bay',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Menominee',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\DeNoronha',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Samarkand',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Anguilla',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yerevan',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mexico_City',
   'D:\\app\\python_3.11\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\LICENSE',
   'D:\\app\\python_3.11\\Lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\REQUESTED',
   'D:\\app\\python_3.11\\Lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\REQUESTED',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\METADATA',
   'D:\\app\\python_3.11\\Lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\RECORD',
   'D:\\app\\python_3.11\\Lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\top_level.txt',
   'D:\\app\\python_3.11\\Lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\top_level.txt',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\WHEEL',
   'D:\\app\\python_3.11\\Lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\INSTALLER',
   'D:\\app\\python_3.11\\Lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\licenses\\LICENSE.BSD',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography-45.0.2.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\WHEEL',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography-45.0.2.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\RECORD',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography-45.0.2.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\INSTALLER',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography-45.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\REQUESTED',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography-45.0.2.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\licenses\\LICENSE.APACHE',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography-45.0.2.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\licenses\\LICENSE',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography-45.0.2.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\METADATA',
   'D:\\app\\python_3.11\\Lib\\site-packages\\cryptography-45.0.2.dist-info\\METADATA',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\app\\python_3.11\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\app\\python_3.11\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'D:\\app\\python_3.11\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('websockets-15.0.1.dist-info\\METADATA',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets-15.0.1.dist-info\\METADATA',
   'DATA'),
  ('websockets-15.0.1.dist-info\\WHEEL',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets-15.0.1.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'D:\\app\\python_3.11\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\REQUESTED',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\REQUESTED',
   'DATA'),
  ('websockets-15.0.1.dist-info\\LICENSE',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets-15.0.1.dist-info\\LICENSE',
   'DATA'),
  ('attrs-25.3.0.dist-info\\INSTALLER',
   'D:\\app\\python_3.11\\Lib\\site-packages\\attrs-25.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('attrs-25.3.0.dist-info\\WHEEL',
   'D:\\app\\python_3.11\\Lib\\site-packages\\attrs-25.3.0.dist-info\\WHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\REQUESTED',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets-15.0.1.dist-info\\REQUESTED',
   'DATA'),
  ('attrs-25.3.0.dist-info\\METADATA',
   'D:\\app\\python_3.11\\Lib\\site-packages\\attrs-25.3.0.dist-info\\METADATA',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\RECORD',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\RECORD',
   'DATA'),
  ('websockets-15.0.1.dist-info\\RECORD',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets-15.0.1.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'D:\\app\\python_3.11\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\INSTALLER',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\INSTALLER',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\REQUESTED',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\REQUESTED',
   'D:\\app\\python_3.11\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\REQUESTED',
   'DATA'),
  ('websockets-15.0.1.dist-info\\INSTALLER',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets-15.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\METADATA',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\METADATA',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\top_level.txt',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\top_level.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'D:\\app\\python_3.11\\Lib\\site-packages\\attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\WHEEL',
   'D:\\app\\python_3.11\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\WHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\entry_points.txt',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets-15.0.1.dist-info\\entry_points.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'D:\\app\\python_3.11\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'D:\\app\\python_3.11\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'D:\\app\\python_3.11\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('attrs-25.3.0.dist-info\\RECORD',
   'D:\\app\\python_3.11\\Lib\\site-packages\\attrs-25.3.0.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'D:\\app\\python_3.11\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('websockets-15.0.1.dist-info\\top_level.txt',
   'D:\\app\\python_3.11\\Lib\\site-packages\\websockets-15.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('base_library.zip',
   'D:\\a_project\\aliyun\\xiondon\\aikefu\\APILLM\\src\\转发\\build\\熊洞科技-转发服务\\base_library.zip',
   'DATA')],
 [('stat', 'D:\\app\\python_3.11\\Lib\\stat.py', 'PYMODULE'),
  ('_weakrefset', 'D:\\app\\python_3.11\\Lib\\_weakrefset.py', 'PYMODULE'),
  ('linecache', 'D:\\app\\python_3.11\\Lib\\linecache.py', 'PYMODULE'),
  ('_collections_abc',
   'D:\\app\\python_3.11\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('reprlib', 'D:\\app\\python_3.11\\Lib\\reprlib.py', 'PYMODULE'),
  ('sre_constants', 'D:\\app\\python_3.11\\Lib\\sre_constants.py', 'PYMODULE'),
  ('warnings', 'D:\\app\\python_3.11\\Lib\\warnings.py', 'PYMODULE'),
  ('sre_compile', 'D:\\app\\python_3.11\\Lib\\sre_compile.py', 'PYMODULE'),
  ('enum', 'D:\\app\\python_3.11\\Lib\\enum.py', 'PYMODULE'),
  ('re._parser', 'D:\\app\\python_3.11\\Lib\\re\\_parser.py', 'PYMODULE'),
  ('re._constants', 'D:\\app\\python_3.11\\Lib\\re\\_constants.py', 'PYMODULE'),
  ('re._compiler', 'D:\\app\\python_3.11\\Lib\\re\\_compiler.py', 'PYMODULE'),
  ('re._casefix', 'D:\\app\\python_3.11\\Lib\\re\\_casefix.py', 'PYMODULE'),
  ('re', 'D:\\app\\python_3.11\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('functools', 'D:\\app\\python_3.11\\Lib\\functools.py', 'PYMODULE'),
  ('genericpath', 'D:\\app\\python_3.11\\Lib\\genericpath.py', 'PYMODULE'),
  ('io', 'D:\\app\\python_3.11\\Lib\\io.py', 'PYMODULE'),
  ('abc', 'D:\\app\\python_3.11\\Lib\\abc.py', 'PYMODULE'),
  ('keyword', 'D:\\app\\python_3.11\\Lib\\keyword.py', 'PYMODULE'),
  ('collections.abc',
   'D:\\app\\python_3.11\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'D:\\app\\python_3.11\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('types', 'D:\\app\\python_3.11\\Lib\\types.py', 'PYMODULE'),
  ('operator', 'D:\\app\\python_3.11\\Lib\\operator.py', 'PYMODULE'),
  ('weakref', 'D:\\app\\python_3.11\\Lib\\weakref.py', 'PYMODULE'),
  ('heapq', 'D:\\app\\python_3.11\\Lib\\heapq.py', 'PYMODULE'),
  ('sre_parse', 'D:\\app\\python_3.11\\Lib\\sre_parse.py', 'PYMODULE'),
  ('ntpath', 'D:\\app\\python_3.11\\Lib\\ntpath.py', 'PYMODULE'),
  ('traceback', 'D:\\app\\python_3.11\\Lib\\traceback.py', 'PYMODULE'),
  ('posixpath', 'D:\\app\\python_3.11\\Lib\\posixpath.py', 'PYMODULE'),
  ('codecs', 'D:\\app\\python_3.11\\Lib\\codecs.py', 'PYMODULE'),
  ('locale', 'D:\\app\\python_3.11\\Lib\\locale.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\app\\python_3.11\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\app\\python_3.11\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\app\\python_3.11\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\app\\python_3.11\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\app\\python_3.11\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\app\\python_3.11\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\app\\python_3.11\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\app\\python_3.11\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\app\\python_3.11\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\app\\python_3.11\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\app\\python_3.11\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\app\\python_3.11\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\app\\python_3.11\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\app\\python_3.11\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\app\\python_3.11\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\app\\python_3.11\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\app\\python_3.11\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\app\\python_3.11\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\app\\python_3.11\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\app\\python_3.11\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\app\\python_3.11\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\app\\python_3.11\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\app\\python_3.11\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem', 'D:\\app\\python_3.11\\Lib\\encodings\\oem.py', 'PYMODULE'),
  ('encodings.mbcs',
   'D:\\app\\python_3.11\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\app\\python_3.11\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\app\\python_3.11\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\app\\python_3.11\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\app\\python_3.11\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\app\\python_3.11\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\app\\python_3.11\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\app\\python_3.11\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\app\\python_3.11\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\app\\python_3.11\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\app\\python_3.11\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\app\\python_3.11\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\app\\python_3.11\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\app\\python_3.11\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\app\\python_3.11\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\app\\python_3.11\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\app\\python_3.11\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\app\\python_3.11\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\app\\python_3.11\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz', 'D:\\app\\python_3.11\\Lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\app\\python_3.11\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\app\\python_3.11\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', 'D:\\app\\python_3.11\\Lib\\encodings\\gbk.py', 'PYMODULE'),
  ('encodings.gb2312',
   'D:\\app\\python_3.11\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\app\\python_3.11\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\app\\python_3.11\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\app\\python_3.11\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\app\\python_3.11\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\app\\python_3.11\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\app\\python_3.11\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\app\\python_3.11\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\app\\python_3.11\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\app\\python_3.11\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\app\\python_3.11\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\app\\python_3.11\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\app\\python_3.11\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\app\\python_3.11\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\app\\python_3.11\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('copyreg', 'D:\\app\\python_3.11\\Lib\\copyreg.py', 'PYMODULE'),
  ('os', 'D:\\app\\python_3.11\\Lib\\os.py', 'PYMODULE')])
