# 熊洞科技IoT设备远程控制系统介绍

## 系统概述

熊洞科技IoT设备远程控制系统是一套用于远程管理和控制易飞IoT声光标签设备的解决方案。系统采用三层架构设计，实现了设备通信、业务逻辑处理和用户界面交互的分离，提供了高效、稳定的声光标签远程管理平台。该系统支持包括标签扫描上报、设备状态监控、远程控制和声光标签触发等功能。

## 系统架构

### 三层架构


系统采用经典的三层架构设计：

#### 1. 设备通信层

设备通信层负责与易飞声光标签硬件设备进行直接数据交换，是整个系统的基础。

**核心特性：**
- **通信协议**：UDP（用户数据报协议）
- **监听端口**：5899
- **通信内容**：设备心跳、状态上报、控制指令
- **实现方式**：Socket直接通信

**关键功能：**
- 接收设备心跳包，维护设备在线状态
- 发送控制指令（如启动扫描、禁止上报等）
- 实现NAT穿透，保持与设备的通信通道
- 处理标签扫描数据上报

**NAT穿透解决方案：**
系统实现了自动保活机制，每20秒向所有已连接设备发送无害的保活数据包（"PING\r\n"），确保NAT通道保持开放状态，解决了内网设备通信问题。

#### 2. 业务逻辑层

业务逻辑层是系统的核心，负责处理各类业务逻辑，连接设备通信层和应用接口层。

**核心特性：**
- 设备管理和状态追踪
- 命令解析和响应处理
- 数据转发和缓存
- 标签触发逻辑处理

**关键功能：**
- 设备连接状态管理
- 命令映射和执行
- 标签数据解析与处理
- 过期设备清理

**多线程机制：**
- UDP服务线程：处理设备通信
- 设备清理线程：定期清理过期设备
- 保活包发送线程：维持NAT通道开放

#### 3. 应用接口层

应用接口层负责对外提供服务接口，支持用户界面和第三方系统集成。

**核心特性：**
- **通信协议**：HTTP/WebSocket
- **监听端口**：5012
- **接口类型**：RESTful API和WebSocket实时数据流
- **实现方式**：FastAPI框架

**关键功能：**
- 设备列表查询接口
- 命令发送接口
- 标签触发接口
- 通道测试接口
- 实时数据推送（WebSocket）

## 系统数据流

### 1. 设备注册与心跳流程

```
设备 → UDP端口5899 → 服务器 → 设备注册 → 心跳维护
```

**详细流程：**
1. 设备从随机源端口发送UDP数据包到服务器5899端口
2. 服务器解析数据包，提取设备IP和源端口
3. 服务器将设备信息（IP:源端口）注册到connected_devices字典
4. 服务器记录设备最后通信时间
5. 设备定期发送心跳维持连接

### 2. 服务器到设备的通信流程

```
用户界面 → HTTP请求 → 业务逻辑 → UDP通信 → 设备
```

**详细流程：**
1. 用户通过Web界面发起指令请求
2. FastAPI处理HTTP请求并调用业务逻辑
3. 业务逻辑将中文指令转换为设备可识别的十六进制命令
4. 服务器通过UDP发送命令到目标设备的源端口
5. 服务器每20秒发送保活包维持NAT通道开放

### 3. 扫描数据上报流程

```
设备扫描 → UDP数据 → 服务器处理 → WebSocket推送 → 实时显示
```

**详细流程：**
1. 设备扫描到标签信息后通过UDP发送数据包到服务器
2. 服务器接收数据并解析
3. 服务器通过WebSocket将数据推送给已连接的客户端
4. Web界面接收数据并实时显示

### 4. 声光标签触发流程

```
用户界面 → 触发请求 → 服务器命令生成 → UDP发送 → 设备执行 → 标签响应
```

**详细流程：**
1. 用户在Web界面输入标签信息并点击触发
2. 服务器接收请求，验证标签格式
3. 服务器生成触发命令（包含标签数量和标签内容）
4. 服务器将命令通过UDP发送到目标设备
5. 设备接收命令并触发相应的声光标签
6. 标签被触发后，设备返回确认信息
7. 服务器接收确认信息，并通过WebSocket通知客户端触发成功

## 关键技术点

1. **NAT穿透机制**：通过保活包定期发送，确保内网设备可持续接收控制指令
2. **设备身份识别**：使用`IP:源端口`作为设备唯一标识符
3. **实时数据推送**：通过WebSocket向前端推送设备数据和状态变化
4. **命令映射转换**：将人类可读的中文命令映射为设备可识别的十六进制指令
5. **多线程协作**：通过多线程实现UDP通信、设备管理和接口服务的并行处理

## 系统优势

1. **灵活部署**：支持公网部署，解决了内网设备通信问题
2. **实时响应**：WebSocket提供实时数据推送，无需轮询
3. **易于集成**：标准RESTful API接口，便于第三方系统集成
4. **可靠连接**：保活机制确保NAT通道持续开放
5. **友好界面**：简洁直观的Web操作界面，支持中文指令

## API接口说明

### RESTful API

| 接口路径 | 方法 | 描述 |
|---------|------|------|
| `/yifei/devices` | GET | 获取所有已连接设备的列表 |
| `/yifei/command` | POST | 发送控制命令到设备 |
| `/yifei/trigger-tags` | POST | 触发声光标签 |
| `/yifei/test-channel/{device_id}` | POST | 测试与指定设备的UDP通道 |
| `/yifei/find` | POST | 发送自定义UDP数据包 |

### WebSocket

| 接口路径 | 描述 |
|---------|------|
| `/yifei/ws` | 实时数据推送，包括设备扫描数据和状态变更 |

## 配置说明

- **UDP端口**：5899，用于与设备通信
- **HTTP/WebSocket端口**：5012，提供Web服务和API接口
- **UDP超时时间**：600秒，超时后清理设备连接
- **保活间隔**：20秒，向已连接设备发送保活数据包

## 启动方式

```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务
python yifei.py
```

服务启动后，访问 `http://服务器IP:5012` 打开控制界面。

## 系统扩展性

系统支持进一步扩展：
- 增加更多设备类型支持
- 扩展控制指令集
- 增强安全认证机制
- 添加数据分析和可视化功能 