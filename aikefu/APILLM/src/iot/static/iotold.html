<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>熊洞物联网</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
    <style>
        :root {
            --primary-color: #007AFF; /* 苹果蓝：清爽科技感，适配深/浅色模式 */
            --primary-dark: #0A68D6;
            --secondary-color: #8E8E93; /* 标准系统灰，完美适配辅助信息 */
            --success-color: #34C759; /* 苹果生态绿，高辨识度清新配色 */
            --danger-color: #FF3B30; /* 系统级红色警示，保持视觉重量 */
            --warning-color: #e8e367; /* 优化橙色，避免廉价感 */
            --warningadd-color: #e8e367; /* 添加 e8e367*/
            --info-color: #5AC8FA; /* 浅天蓝，增强信息层级区分 */
            --light-bg: #F8F8FA; /* 带蓝调的浅灰，提升屏幕舒适度 */
            --bq-color: #3974b5; /* 浅天蓝，增强信息层级区分 */
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            --hover-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
            --border-radius: 5px;
        }

        body {
            background-color: var(--light-bg);
            padding: 15px;
            font-family: 'Microsoft YaHei', sans-serif;
            color: #333;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: var(--border-radius); /* 设置元素的圆角半径 --border-radius */
            box-shadow: var(--card-shadow); /* 为元素添加阴影效果 --card-shadow */
            padding: 20px; /* 设置元素的内边距为20像素 */
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 15px;
        }

        h1 {
            color: var(--primary-color);
            font-weight: bold;
            font-size: 1.8rem;
            margin-bottom: 5px;
        }

        .header p {
            color: var(--secondary-color);
            opacity: 0.8;
        }

        @media (max-width: 576px) {
            h1 {
                font-size: 1.5rem;
            }

            .container {
                padding: 15px;
            }
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            margin-bottom: 20px;
            transition: all 0.2s ease-in-out;
        }

        .card:hover {
            box-shadow: var(--hover-shadow);
            transform: translateY(-3px);
        }

        /* 卡片头部样式 */
        .card-header {
            /* 设置背景颜色为全局主要颜色 */
            background-color: var(--primary-color);
            /* 设置文字颜色为白色 */
            color: white;
            /* 设置字体粗细 */
            font-weight: 500;
            /* 设置圆角样式，底部为直角 */
            border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
            /* 使用flex布局以均匀排列子元素 */
            display: flex;
            /* 子元素在主轴上空间均匀分布 */
            justify-content: space-between;
            /* 子元素在交叉轴上对齐方式为居中 */
            align-items: center;
            /* 设置内边距 */
            padding: 6px 8px;
            /* 设置字体大小 */
            font-size: 1.1rem;
        }

        .card-header i {
            margin-left: 5px;
        }

        .card-body {
            padding: 10px;
        }

        .btn {
            border-radius: var(--border-radius);
            font-weight: 500;
            padding: 6px 12px;
            transition: all 0.2s;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover, .btn-primary:focus {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
        }

        .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }
        .btn-add {
            background-color: var(--warningadd-color);
            border-color: var(--warningadd-color);
        }

        .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
        }

        .btn-info {
            background-color: var(--info-color);
            border-color: var(--info-color);
        }

        .btn-control {
            margin: 5px;
            min-width: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-control i {
            margin-right: 8px;
            font-size: 1rem;
        }

        .device-item {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: var(--border-radius);
            background-color: #f5f5f5;
            cursor: pointer;
            transition: all 0.2s;
            border-left: 3px solid transparent;
        }

        .device-item:hover {
            background-color: #e3f2fd;
        }

        .device-selected {
            background-color: #e3f2fd;
            border-left: 4px solid var(--primary-color);
        }

        .status-indicator {
            height: 10px;
            width: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-online {
            background-color: var(--success-color);
        }

        .status-offline {
            background-color: var(--danger-color);
        }

        #responseArea {
            max-height: 200px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border-radius: var(--border-radius);
            padding: 15px;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            color: var(--secondary-color);
            font-size: 14px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        .no-devices {
            text-align: center;
            padding: 25px;
            color: #757575;
        }

        .no-devices i {
            font-size: 2.5rem;
            color: #bbbbbb;
            margin-bottom: 10px;
        }

        #scanDataContainer {
            display: block;
            margin-top: 20px;
        }

        #scanDataArea {
            max-height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: var(--border-radius);
            padding: 15px;
            font-family: monospace;
            font-size: 13px;
        }

        .scan-data-item {
            margin-bottom: 10px;
            padding: 12px;
            border-radius: var(--border-radius);
            background-color: #ffffff;
            border-left: 3px solid var(--success-color);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s;
        }

        .scan-data-item:hover {
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .scan-data-time {
            color: #666;
            font-size: 12px;
        }

        .scan-data-device {
            font-weight: bold;
            color: var(--primary-color);
            margin: 5px 0;
        }

        .scan-data-hex {
            color: var(--warning-color);
            word-break: break-all;
        }

        .scan-data-text {
            color: #666;
            word-break: break-all;
            margin-top: 3px;
        }

        .data-controls {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            align-items: center;
        }


        .badge-scan-active {
            /* 设置背景颜色为成功颜色 */
            background-color: var(--success-color);
            /* 设置文本颜色为白色 */
            color: white;
            /* 设置内边距 */
            padding: 6px 12px;
            /* 设置圆角边框 */
            border-radius: 12px;
            /* 默认隐藏元素 */
            display: none;
            /* 设置对齐方式为居中对齐 */
            align-items: center;
            /* 设置字体大小 */
            font-size: 0.9rem;
        }


        .badge-scan-active .dot {
            /* 将dot元素显示为行内块元素 */
            display: inline-block;
            /* 设置元素宽度 */
            width: 8px;
            /* 设置元素高度 */
            height: 8px;
            /* 设置背景颜色为白色 */
            background-color: white;
            /* 设置圆角为50%，形成圆形 */
            border-radius: 50%;
            /* 设置右边距 */
            margin-right: 5px;
            /* 添加脉冲动画效果，持续时间2秒，无限循环 */
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }
            50% {
                opacity: 0.3;
            }
            100% {
                opacity: 1;
            }
        }

        /* 标签输入相关样式 */
        .tag-input {
            font-family: monospace;
            letter-spacing: 1px;
            font-size: 1rem;
        }

        .tag-input.is-invalid {
            border-color: var(--danger-color);
            background-image: none;
        }

        #tagInputsContainer {
            max-height: 300px;
            overflow-y: auto;
        }

        .tag-input-group {
            margin-bottom: 10px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .tag-input-group .input-group-text {
            background-color: var(--bq-color);
            color: white;
            border: none;
            min-width: 80px;
            text-align: center;
        }

        .tag-input-group .form-control {
            border: 1px solid #e9ecef;
            padding: 10px 15px;
        }

        .tag-input-group .btn {
            border-top-right-radius: var(--border-radius);
            border-bottom-right-radius: var(--border-radius);
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .alert {
            border-radius: var(--border-radius);
            border: none;
            padding: 15px;
        }

        .alert-success {
            background-color: rgba(46, 204, 113, 0.15);
            color: #1e8449;
        }

        .alert-danger {
            background-color: rgba(231, 76, 60, 0.15);
            color: #b93a2f;
        }

        /* 设置适当的间距 */
        .mb-3 {
            margin-bottom: 1.25rem !important;
        }

        .mb-4 {
            margin-bottom: 1.5rem !important;
        }

        /* 操作区域标题样式 */
        h5 {
            color: var(--secondary-color);
            font-weight: 600;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        h5 i {
            margin-right: 6px;
            color: var(--primary-color);
        }

        /* 下拉菜单样式 */
        .dropdown-menu {
            border: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-radius: var(--border-radius);
        }



        #triggerTagsBtn {
            padding: 12px;
            font-weight: 600;
            font-size: 1.05rem;
        }

        #triggerTagsBtn i {
            margin-right: 8px;
        }

        /* 响应式布局优化 */
        @media (max-width: 768px) {
            .btn-control {
                min-width: auto;
                width: 100%;
            }

            .tag-input-group .input-group-text {
                min-width: 70px;
            }
        }
    </style>
</head>
<body>
<!-- <div class="container"> -->
<!--
<div class="header">
    <h1><i class="bi bi-hdd-network"></i> 设备控制面板</h1>
    <p class="text-muted">远程监控与控制设备</p>
</div>
-->


<!-- 控制面板（在移动设备上显示在上方） -->
<div class="col-lg-7 order-1 order-lg-2">
    <div class="card mb-4">
        <div class="card-header">
            <span><i class="bi bi-dpad"></i> 远程控制</span>
            <span class="badge-scan-active" id="scanActiveBadge">
                            <span class="dot"></span> 正在扫描
                        </span>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-6 d-grid">
                    <button class="btn btn-success btn-control" data-command="启动扫描上报">
                        <i class="bi bi-play-circle"></i> 启动上报
                    </button>
                </div>
                <div class="col-6 d-grid">
                    <button class="btn btn-secondary btn-control" data-command="禁止扫描上报">
                        <i class="bi bi-stop-circle"></i> 停止上报
                    </button>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-6 d-grid">
                    <button class="btn btn-info btn-control" data-command="OTA升级">
                        <i class="bi bi-cloud-arrow-up"></i> OTA升级
                    </button>
                </div>
                <div class="col-6 d-grid">
                    <button class="btn btn-danger btn-control" data-command="重启设备">
                        <i class="bi bi-arrow-repeat"></i> 重启设备
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 标签触发卡片 -->
    <div class="card card-tag-trigger mb-3">
        <div class="card-header">
            <span><i class="bi bi-bell"></i> 触发声光</span>
            <button type="button" id="addTagBtn" class="btn btn-sm btn-add">
                <i class="bi bi-plus-circle"></i> 添加
            </button>
        </div>


        <div class="card-body">
            <div class="mb-3">
                <div id="tagInputsContainer">
                    <div class="input-group mb-2 tag-input-group">
                        <span class="input-group-text">标签 1</span>
                        <input type="text" class="form-control tag-input" placeholder="例:DB8CD787D2CB"
                               maxlength="20">
                        <button class="btn btn-outline-danger remove-tag-btn" type="button">
                            <i class="bi bi-trash"></i>
                        </button>

                    </div>
                </div>

                <div id="tagErrorMsg" class="text-danger mt-2"></div>
            </div>
            <div class="d-grid">
                <button type="button" class="btn btn-warning" id="triggerTagsBtn">
                    <i class="bi bi-lightning-charge"></i> 发送触发声光指令
                </button>
            </div>
        </div>
    </div>

    <!-- 设备列表  -->
    <div class="col-lg-5 order-2 order-lg-1 mb-4 mb-lg-0">
        <div class="card">
            <div class="card-header">
                <span><i class="bi bi-list-ul"></i> 设备列表</span>
                <button id="refreshDevices" class="btn btn-sm btn-success">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
            </div>
            <div class="card-body">
                <div id="deviceList" class="mb-3">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载设备列表...</p>
                    </div>
                </div>
                <div class="text-center" id="deviceSelectionControl" style="display: none;">
                    <button class="btn btn-sm btn-outline-secondary" id="clearDeviceSelection">
                        <i class="bi bi-x-circle"></i> 取消选择
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- 扫描数据容器 -->
    <div id="scanDataContainer" class="card">
        <div class="card-header">
            <span><i class="bi bi-list-ul"></i> 上报数据</span>
            <div>
                <button id="clearScanData" class="btn btn-sm btn-danger">
                    <i class="bi bi-trash"></i> 清空
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="data-controls">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="autoScrollSwitch" checked>
                    <label class="form-check-label" for="autoScrollSwitch">自动滚动</label>
                </div>
                <span id="dataCount" class="badge bg-secondary">0 条数据</span>
            </div>
            <div id="scanDataArea">
                <p class="text-muted text-center">等待数据...</p>
            </div>
        </div>
    </div>
    <div class="mt-4">
        <h5><i class="bi bi-terminal"></i> 操作结果</h5>
        <div id="responseArea" class="border rounded p-3 bg-light">
            <p class="text-muted text-center"><i class="bi bi-info-circle"></i> 操作结果将显示在这里...</p>
        </div>
    </div>

</div>


<div class="footer">
    <p> © 2025 熊洞科技 - 远程物联控制系统</p>
</div>
<!-- </div> -->


<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // 当前选中的设备ID
    let selectedDeviceId = null;
    // WebSocket连接
    let ws = null;
    // 扫描数据计数
    let dataCount = 0;
    // 扫描状态 - 默认为true
    let scanningActive = true;
    // 数据缓存
    let dataCache = [];
    // 最大缓存数据条数
    const MAX_CACHE_SIZE = 100;

    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function () {
        // 获取设备列表
        fetchDevices();

        // 显示扫描数据区域
        showScanDataContainer();

        // 建立WebSocket连接
        connectWebSocket();

        // 设置刷新按钮
        document.getElementById('refreshDevices').addEventListener('click', fetchDevices);

        // 设置取消设备选择按钮
        document.getElementById('clearDeviceSelection').addEventListener('click', clearDeviceSelection);

        // 设置所有控制按钮的点击事件
        document.querySelectorAll('.btn-control').forEach(button => {
            button.addEventListener('click', function () {
                const command = this.getAttribute('data-command');
                sendCommand(command);
            });
        });

        // 清空扫描数据按钮
        document.getElementById('clearScanData').addEventListener('click', function () {
            document.getElementById('scanDataArea').innerHTML = '<p class="text-muted text-center">等待数据...</p>';
            dataCount = 0;
            dataCache = [];
            updateDataCount();
        });

        // 添加标签按钮
        document.getElementById('addTagBtn').addEventListener('click', function () {
            addTagInput();
        });

        // 触发标签按钮
        document.getElementById('triggerTagsBtn').addEventListener('click', function () {
            triggerTags();
        });

        // 初始化移除标签按钮事件
        initRemoveTagButtons();
    });

    // 建立WebSocket连接
    function connectWebSocket() {
        // 确定WebSocket URL
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/yifei/ws`;

        console.log("protocol:", protocol)
        console.log("WebSocket URL:", wsUrl)

        // 创建WebSocket
        ws = new WebSocket(wsUrl);

        // 连接打开时的处理
        ws.onopen = function (event) {
            console.log("WebSocket连接已建立");
        };

        // 接收消息的处理
        ws.onmessage = function (event) {
            try {
                const data = JSON.parse(event.data);

                // 处理扫描数据
                if (data.type === "scan_data") {
                    // 将数据添加到缓存
                    addToDataCache(data);

                    // 显示数据 - 无论show标志如何，都显示数据
                    // 确保扫描数据容器显示
                    showScanDataContainer();
                    // 添加到显示区域
                    addScanData(data);
                }
                // 处理UI控制命令
                else if (data.type === "ui_command") {
                    handleUICommand(data.command);
                }
            } catch (e) {
                console.error("解析WebSocket消息失败:", e);
            }
        };

        // 连接关闭时的处理
        ws.onclose = function (event) {
            console.log("WebSocket连接已关闭");
            // 3秒后尝试重新连接
            setTimeout(connectWebSocket, 3000);
        };

        // 连接出错时的处理
        ws.onerror = function (event) {
            console.error("WebSocket连接出错:", event);
        };
    }

    // 处理UI控制命令
    function handleUICommand(command) {
        console.log("收到UI控制命令:", command);

        if (command === "show_scan_data") {
            // 显示扫描数据和扫描状态
            showScanDataContainer();
            document.getElementById('scanActiveBadge').style.display = 'flex';
            scanningActive = true;

            // 将缓存的数据添加到显示区域
            renderCachedData();
        } else if (command === "hide_scan_data") {
            // 隐藏扫描状态
            // document.getElementById('scanActiveBadge').style.display = 'none';
            // scanningActive = false;
        }
    }

    // 显示扫描数据容器
    function showScanDataContainer() {
        document.getElementById('scanDataContainer').style.display = 'block';
    }

    // 将数据添加到缓存
    function addToDataCache(data) {
        // 添加到缓存
        dataCache.push(data);

        // 如果缓存超过最大大小，移除最旧的数据
        if (dataCache.length > MAX_CACHE_SIZE) {
            dataCache.shift();
        }
    }

    // 渲染缓存的数据
    function renderCachedData() {
        const scanDataArea = document.getElementById('scanDataArea');
        scanDataArea.innerHTML = '';
        dataCount = 0;

        // 添加所有缓存的数据
        dataCache.forEach(data => {
            addScanDataToDisplay(data);
        });
    }

    // 添加扫描数据到显示区域
    function addScanData(data) {
        const scanDataArea = document.getElementById('scanDataArea');

        // 如果是第一条数据，清空"等待数据"提示
        if (dataCount === 0) {
            scanDataArea.innerHTML = '';
        }

        addScanDataToDisplay(data);
    }

    // 添加扫描数据到显示元素
    function addScanDataToDisplay(data) {
        const scanDataArea = document.getElementById('scanDataArea');

        // 创建数据项
        const dataItem = document.createElement('div');
        dataItem.className = 'scan-data-item';

        // 格式化显示内容
        let hexData = data.data;
        let readableData = data.readable || "无法解析";

        dataItem.innerHTML = `
                <div class="scan-data-time">${data.timestamp}</div>
                <div class="scan-data-device">设备: ${data.device}</div>
                <div class="scan-data-text">内容: ${readableData}</div>
            `;

        // 添加到显示区域
        scanDataArea.appendChild(dataItem);

        // 更新数据计数
        dataCount++;
        updateDataCount();

        // 如果启用了自动滚动，滚动到底部
        if (document.getElementById('autoScrollSwitch').checked) {
            scanDataArea.scrollTop = scanDataArea.scrollHeight;
        }
    }

    // 更新数据计数显示
    function updateDataCount() {
        document.getElementById('dataCount').textContent = `${dataCount} 条数据`;
    }

    // 获取设备列表
    function fetchDevices() {
        const deviceListElem = document.getElementById('deviceList');

        // 显示加载状态
        deviceListElem.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载设备列表...</p>
                </div>
            `;

        // 发送请求获取设备列表
        fetch('/yifei/devices')
            .then(response => response.json())
            .then(data => {
                // 清空设备列表
                deviceListElem.innerHTML = '';

                if (data.total === 0) {
                    deviceListElem.innerHTML = `
                            <div class="no-devices">
                                <i class="bi bi-exclamation-circle mb-2"></i>
                                <p>暂无连接设备</p>
                            </div>
                        `;
                    return;
                }

                // 遍历设备列表
                data.devices.forEach(device => {
                    const deviceElem = document.createElement('div');
                    deviceElem.className = 'device-item';
                    deviceElem.setAttribute('data-device-id', device.device_id);

                    // 如果当前设备是选中的设备，添加选中样式
                    if (device.device_id === selectedDeviceId) {
                        deviceElem.classList.add('device-selected');
                    }

                    deviceElem.innerHTML = `
                            <div>
                                <span class="status-indicator status-online" title="在线"></span>
                                <strong>${device.ip}:${device.source_port}</strong>
                            </div>
                            <div class="text-muted small">
                                <i class="bi bi-clock"></i> 上次连接: ${device.last_seen}
                            </div>

                        `;

                    // 添加点击事件，选中设备
                    deviceElem.addEventListener('click', function () {
                        // 移除其他设备的选中样式
                        document.querySelectorAll('.device-item').forEach(item => {
                            item.classList.remove('device-selected');
                        });
                        // 添加当前设备的选中样式
                        this.classList.add('device-selected');
                        // 设置当前选中的设备ID
                        selectedDeviceId = this.getAttribute('data-device-id');
                        // 更新触发按钮文本
                        document.getElementById('triggerTagsBtn').innerHTML = '<i class="bi bi-lightning-charge"></i> 发送触发声光指令(选中设备)';
                        // 显示取消选择按钮
                        document.getElementById('deviceSelectionControl').style.display = 'block';
                    });

                    deviceListElem.appendChild(deviceElem);
                });

                // 当没有选中任何设备时，更新触发按钮文本为"所有设备"
                if (!selectedDeviceId) {
                    document.getElementById('triggerTagsBtn').innerHTML = '<i class="bi bi-lightning-charge"></i> 发送触发声光指令';
                }
            })
            .catch(error => {
                console.error('获取设备列表失败:', error);
                deviceListElem.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i> 获取设备列表失败: ${error.message}
                        </div>
                    `;
            });
    }

    // 发送命令
    function sendCommand(command) {
        const responseArea = document.getElementById('responseArea');

        // 显示加载状态
        responseArea.innerHTML = `
                <div class="text-center py-2">
                    <div class="spinner-border text-primary spinner-border-sm" role="status">
                        <span class="visually-hidden">发送中...</span>
                    </div>
                    <span class="ms-2">正在发送命令: ${command}...</span>
                </div>
            `;

        // 准备请求数据
        const requestData = {
            command: command
        };

        // 如果有选中的设备，添加到请求数据中
        if (selectedDeviceId) {
            requestData.device_id = selectedDeviceId;
        }

        // 发送请求
        fetch('/yifei/command', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => {
                        throw new Error(err.detail || '请求失败: ' + response.status);
                    });
                }
                return response.json();
            })
            .then(data => {
                // 显示响应结果
                responseArea.innerHTML = `
                    <div class="alert alert-success">
                        <div><strong>命令:</strong> ${data.command}</div>
                        <div><strong>设备:</strong> ${data.device}</div>
                        <div><strong>十六进制:</strong> ${data.hex_data}</div>
                        <div><strong>状态:</strong> ${data.result.status}</div>
                    </div>
                `;
            })
            .catch(error => {
                console.error('发送命令失败:', error);
                responseArea.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i> 发送命令失败: ${error.message}
                    </div>
                `;
            });
    }

    // 定时刷新设备列表（每180秒）
    setInterval(fetchDevices, 180000);

    // 在页面关闭前关闭WebSocket连接
    window.addEventListener('beforeunload', function () {
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.close();
        }
    });

    // 初始化移除标签按钮事件
    function initRemoveTagButtons() {
        document.querySelectorAll('.remove-tag-btn').forEach(button => {
            button.addEventListener('click', function () {
                const container = this.closest('.tag-input-group');
                // 如果只有一个标签，不允许删除
                if (document.querySelectorAll('.tag-input-group').length > 1) {
                    container.remove();
                    // 重新编号
                    updateTagNumbers();
                }
            });
        });
    }

    // 更新标签编号
    function updateTagNumbers() {
        document.querySelectorAll('.tag-input-group').forEach((group, index) => {
            group.querySelector('.input-group-text').textContent = `标签 ${index + 1}`;
        });
    }

    // 添加标签输入框
    function addTagInput() {
        // 检查当前标签数量
        const currentTagCount = document.querySelectorAll('.tag-input-group').length;
        if (currentTagCount >= 8) {
            showTagError('最多支持8个标签');
            return;
        }

        const container = document.getElementById('tagInputsContainer');
        const newGroup = document.createElement('div');
        newGroup.className = 'input-group mb-2 tag-input-group';
        newGroup.innerHTML = `
                <span class="input-group-text">标签 ${currentTagCount + 1}</span>
                <input type="text" class="form-control tag-input" placeholder="例:DB8CD787D2CB" maxlength="20">
                <button class="btn btn-outline-danger remove-tag-btn" type="button">
                    <i class="bi bi-trash"></i>
                </button>
            `;
        container.appendChild(newGroup);

        // 初始化新添加的移除按钮
        initRemoveTagButtons();
    }

    // 重置标签输入
    function resetTagInputs() {
        const container = document.getElementById('tagInputsContainer');
        // 清空现有标签
        container.innerHTML = '';

        // 添加一个初始标签
        const newGroup = document.createElement('div');
        newGroup.className = 'input-group mb-2 tag-input-group';
        newGroup.innerHTML = `
                <span class="input-group-text">标签 1</span>
                <input type="text" class="form-control tag-input" placeholder="例:DB8CD787D2CB" maxlength="20">
                <button class="btn btn-outline-danger remove-tag-btn" type="button">
                    <i class="bi bi-trash"></i>
                </button>
            `;
        container.appendChild(newGroup);

        // 初始化移除按钮
        initRemoveTagButtons();

        // 清除错误信息
        clearTagError();
    }

    // 显示标签错误信息
    function showTagError(message) {
        const errorElement = document.getElementById('tagErrorMsg');
        errorElement.textContent = message;
    }

    // 清除标签错误信息
    function clearTagError() {
        const errorElement = document.getElementById('tagErrorMsg');
        errorElement.textContent = '';
    }

    // 验证标签格式
    function validateTag(tagValue) {
        // 移除所有空格
        const cleanTag = tagValue.replace(/\s+/g, '');

        // 检查是否为有效的十六进制
        if (!/^[0-9A-Fa-f]+$/.test(cleanTag)) {
            return {valid: false, message: '标签必须只包含十六进制字符 (0-9, A-F)'};
        }

        // 检查长度
        if (cleanTag.length !== 12) {
            return {
                valid: false,
                message: `标签长度必须为6字节 (12个十六进制字符)，当前长度为 ${cleanTag.length / 2} 字节`
            };
        }

        return {valid: true};
    }

    // 格式化标签为两个字符一组，以空格分隔
    function formatTag(tagValue) {
        // 移除所有空格
        const cleanTag = tagValue.replace(/\s+/g, '');
        // 转换为大写并按每两个字符添加空格
        return cleanTag.toUpperCase().match(/.{1,2}/g).join(' ');
    }

    // 触发标签
    function triggerTags() {
        // 清除错误信息
        clearTagError();
        document.querySelectorAll('.tag-input').forEach(input => {
            input.classList.remove('is-invalid');
        });

        // 获取所有标签值
        const tagInputs = document.querySelectorAll('.tag-input');
        const tags = [];
        let hasError = false;

        tagInputs.forEach((input, index) => {
            const value = input.value.trim();

            // 检查是否为空
            if (!value) {
                input.classList.add('is-invalid');
                showTagError(`标签 ${index + 1} 不能为空`);
                hasError = true;
                return;
            }

            // 验证标签格式
            const validation = validateTag(value);
            if (!validation.valid) {
                input.classList.add('is-invalid');
                showTagError(`标签 ${index + 1}: ${validation.message}`);
                hasError = true;
                return;
            }

            // 格式化标签为正确的格式（两个字符一组，以空格分隔，全部大写）
            const formattedTag = formatTag(value);
            tags.push(formattedTag);

            // 在输入框中显示格式化后的标签，提供用户反馈
            input.value = formattedTag;
        });

        if (hasError) {
            return;
        }

        // 构建请求数据
        const requestData = {
            tags: tags
        };

        // 如果有选中的设备，添加到请求数据中
        if (selectedDeviceId) {
            requestData.device_id = selectedDeviceId;
        }

        // 显示加载状态
        const responseArea = document.getElementById('responseArea');
        responseArea.innerHTML = `
                <div class="text-center py-2">
                    <div class="spinner-border text-primary spinner-border-sm" role="status">
                        <span class="visually-hidden">发送中...</span>
                    </div>
                    <span class="ms-2">正在发送触发声光标签命令，共 ${tags.length} 个标签${selectedDeviceId ? '到选中设备' : '到所有已连接设备'}...</span>
                </div>
            `;

        // 发送请求
        fetch('/yifei/trigger-tags', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => {
                        throw new Error(err.detail || '请求失败: ' + response.status);
                    });
                }
                return response.json();
            })
            .then(data => {
                // 显示响应结果
                const responseArea = document.getElementById('responseArea');

                // 构建设备结果显示
                let devicesHtml = '';
                if (data.results && data.results.length > 0) {
                    data.results.forEach((result, index) => {
                        const statusClass = result.result.status === 'success' ? 'text-success' : 'text-danger';
                        devicesHtml += `
                            <div class="mb-1">
                                <strong>设备 ${index + 1}:</strong> ${result.device}
                                <span class="${statusClass}">[${result.result.status}]</span>
                            </div>
                        `;
                    });
                }

                responseArea.innerHTML = `
                    <div class="alert alert-success">
                        <div><strong>命令:</strong> ${data.command}</div>
                        <div><strong>标签数量:</strong> ${data.tags_count}</div>
                        <div><strong>设备总数:</strong> ${data.devices_total} (成功: ${data.devices_success})</div>
                        ${devicesHtml ? '<div class="mt-2"><strong>设备结果:</strong></div>' + devicesHtml : ''}
                    </div>
                `;
            })
            .catch(error => {
                console.error('发送触发标签命令失败:', error);
                const responseArea = document.getElementById('responseArea');
                responseArea.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i> 发送触发标签命令失败: ${error.message}
                    </div>
                `;
            });
    }

    // 清除设备选择
    function clearDeviceSelection() {
        // 移除所有设备的选中样式
        document.querySelectorAll('.device-item').forEach(item => {
            item.classList.remove('device-selected');
        });

        // 清除选中设备ID
        selectedDeviceId = null;

        // 隐藏取消选择按钮
        document.getElementById('deviceSelectionControl').style.display = 'none';

        // 更新触发按钮文本
        document.getElementById('triggerTagsBtn').innerHTML = '<i class="bi bi-lightning-charge"></i> 发送触发声光指令';
    }
</script>
</body>
</html>