import socket
import threading
from datetime import datetime
import logging
import os
from pathlib import Path
import json
from typing import List, Optional
import time  # 添加time模块导入

from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("yifei.log"),  # 日志写入文件
        logging.StreamHandler()  # 日志输出到控制台
    ]
)

app = FastAPI()

# 设置静态文件目录
current_dir = Path(__file__).parent
static_dir = current_dir / "static"

# 创建静态目录（如果不存在）
if not static_dir.exists():
    os.makedirs(static_dir)

# 挂载静态文件目录
app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

# 全局字典用于存储已连接设备的IP和端口
connected_devices = {}

# 全局变量，存储WebSocket连接
active_websockets = []
# 是否正在进行扫描上报 (仍然保留这个变量，但不再用于控制是否发送数据)
scanning_active = False
# 是否显示扫描上报数据 - 默认为True，以便始终显示
show_scan_data = True

# 全局变量，存储UDP套接字
udp_socket = None

# UDP连接超时时间(秒)
UDP_TIMEOUT = 600  # 默认600秒，可根据实际网络环境调整

# 指令映射
COMMAND_MAP = {
    "启动扫描上报": "53 43 41 4E 3A 31 0D 0A",
    "禁止扫描上报": "53 43 41 4E 3A 30 0D 0A",
    "重启设备": "57 4C 49 53 54 3A 00 CF FE DF 20 58 4C D8 B6 28 15 C7 7D 0D 0A",
    "OTA升级": "44 46 55 42 4F 4F 54 0D 0A"
}

# 全局变量，存储最近发送的标签信息
last_sent_tags = {}  # 格式: {device_id: [标签列表]}


class CommandRequest(BaseModel):
    command: str  # 中文指令
    device_id: str = None  # 可选参数：设备ID，如果不提供则使用最后连接的设备


# HTTP 路由：首页
@app.get("/")
def read_root():
    logging.info("访问了根路径")
    return FileResponse(static_dir / "iot.html")


# 添加favicon路由
@app.get("/favicon.ico")
def get_favicon():
    return FileResponse(static_dir / "favicon.ico")


class UDPPacket(BaseModel):
    ip: str  # 目标设备的 IP 地址
    port: int  # 目标设备的端口号
    hex_data: str  # 要发送的 HEX 数据，例如 "57 4C 49 53 54 3A 01 CE A9 B7 5B 17 42 0D 0A"


def send_udp_command(ip: str, port: int, hex_data: str, specific_device_id=None):
    """
    发送 UDP 命令到指定的 IP 和端口。

    :param ip: 目标设备的 IP 地址
    :param port: 目标设备的端口号
    :param hex_data: 要发送的 HEX 数据（字符串形式，例如 "57 4C 49 53 54 3A 01 CE A9 B7 5B 17 42 0D 0A"）
    :param specific_device_id: 可选，指定特定设备ID（格式：IP:端口）
    """
    global udp_socket
    
    logging.info(f"尝试发送数据到 {ip}:{port} -> HEX: {hex_data}")
    
    # 如果传入了完整的设备ID，直接使用该ID
    if specific_device_id and specific_device_id in connected_devices:
        device_info = connected_devices[specific_device_id]
        source_port = device_info.get('source_port', port)
        logging.info(f"使用指定设备ID: {specific_device_id}, 源端口: {source_port}")
    else:
        # 尝试查找设备
        # 检查是否有匹配的设备（可能有多个源端口）
        matching_devices = {}
        for device_id, info in connected_devices.items():
            if info['ip'] == ip:
                matching_devices[device_id] = info
        
        if not matching_devices:
            error_msg = f"设备 {ip} 未连接或连接已过期"
            logging.error(error_msg)
            return {"status": "error", "message": error_msg}
        
        # 如果只提供了IP但没有指定源端口，我们使用最近连接的设备
        if len(matching_devices) > 1:
            # 按最后连接时间排序
            device_id = max(matching_devices, key=lambda k: matching_devices[k]['last_seen'])
            device_info = matching_devices[device_id]
            logging.info(f"找到多个匹配设备，使用最近连接的: {device_id}")
        else:
            # 只有一个匹配设备
            device_id = list(matching_devices.keys())[0]
            device_info = matching_devices[device_id]
        
        source_port = device_info.get('source_port', port)
    
    try:
        # 将 HEX 字符串转换为字节数据
        byte_data = bytes.fromhex(hex_data.replace(" ", ""))
        
        # 使用保存的地址信息发送数据
        udp_socket.sendto(byte_data, (ip, source_port))
        logging.info(f"已通过UDP通道发送数据到 {ip}:{source_port} -> HEX: {hex_data}")
        return {"status": "success", "message": f"数据已发送到 {ip}:{source_port}"}
    except Exception as e:
        logging.error(f"发送数据时出错: {e}")
        return {"status": "error", "message": str(e)}


@app.post("/yifei/find")
async def test(packet: UDPPacket):
    """
    FastAPI 路由方法，用于通过 POST 请求发送 UDP 数据。
    请求体应为 JSON 格式，包含 ip、port 和 hex_data 字段。
    """
    logging.info(f"收到 POST 请求: {packet.dict()}")
    result = send_udp_command(packet.ip, packet.port, packet.hex_data)
    if result["status"] == "error":
        logging.error(f"POST 请求处理失败: {result['message']}")
        return {
            "code": 400,
            "data": result,
            "msg": result["message"]
        }
    return {
        "code": 200,
        "data": result,
        "msg": "发送UDP数据成功"
    }


@app.post("/yifei/command")
async def send_command(request: CommandRequest):
    """
    通过中文指令发送对应的十六进制命令到设备

    :param request: 包含中文指令和可选设备ID的请求
    :return: 发送结果
    """
    global scanning_active, show_scan_data

    logging.info(f"收到中文指令请求: {request.command}")

    # 验证指令是否存在
    if request.command not in COMMAND_MAP:
        valid_commands = ", ".join(COMMAND_MAP.keys())
        error_msg = f"未知指令: {request.command}. 有效指令包括: {valid_commands}"
        logging.error(error_msg)
        return {
            "code": 400,
            "data": None,
            "msg": error_msg
        }

    # 获取对应的十六进制命令
    hex_command = COMMAND_MAP[request.command]

    # 确定目标设备
    if request.device_id:
        # 使用指定的设备ID
        if request.device_id not in connected_devices:
            error_msg = f"找不到设备ID: {request.device_id}"
            logging.error(error_msg)
            return {
                "code": 404,
                "data": None,
                "msg": error_msg
            }
        device = connected_devices[request.device_id]
    else:
        # 使用最后连接的设备
        if not connected_devices:
            error_msg = "没有可用的已连接设备"
            logging.error(error_msg)
            return {
                "code": 404,
                "data": None,
                "msg": error_msg
            }

        # 按照最后连接时间排序，获取最新的设备
        latest_device_id = max(connected_devices, key=lambda k: connected_devices[k]['last_seen'])
        device = connected_devices[latest_device_id]

    # 通过UDP通道发送命令
    result = send_udp_command(device['ip'], device['port'], hex_command, request.device_id)
    logging.info(f"发送指令结果: {result}")

    # 设置扫描状态和前端显示状态
    if request.command == "启动扫描上报":
        scanning_active = True
        show_scan_data = True
        # 发送UI控制命令到前端
        await broadcast_ui_command("show_scan_data")
    elif request.command == "禁止扫描上报":
        scanning_active = False
        show_scan_data = False
        # 发送UI控制命令到前端
        await broadcast_ui_command("hide_scan_data")

    original_response = {
        "command": request.command,
        "hex_data": hex_command,
        "device": f"{device['ip']}:{device.get('source_port', device['port'])}",
        "result": result
    }

    return {
        "code": 200 if result["status"] == "success" else 500,
        "data": original_response,
        "msg": result["message"]
    }


@app.get("/yifei/devices")
async def get_devices():
    """
    获取所有已连接设备的列表

    :return: 已连接设备信息的列表
    """
    # 转换设备信息为可序列化的格式
    device_list = []
    for device_id, info in connected_devices.items():
        device_list.append({
            "device_id": device_id,
            "ip": info["ip"],
            "port": info["port"],
            "source_port": info.get("source_port", "未知"),  # 添加源端口信息
            "last_seen": info["last_seen"].strftime("%Y-%m-%d %H:%M:%S"),  # 转换为年月日时分秒格式
            "last_seen_ago": (datetime.now() - info["last_seen"]).total_seconds()  # 多久之前收到心跳
        })

    # 按最后连接时间降序排序
    device_list.sort(key=lambda x: x["last_seen"], reverse=True)

    original_response = {
        "total": len(device_list),
        "devices": device_list
    }

    return {
        "code": 200,
        "data": original_response,
        "msg": f"成功获取设备列表，共{len(device_list)}个设备"
    }


# WebSocket连接
@app.websocket("/yifei/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    active_websockets.append(websocket)

    # 连接成功后立即发送UI控制命令，显示扫描数据
    await websocket.send_text(json.dumps({
        "type": "ui_command",
        "command": "show_scan_data",
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    }))

    try:
        while True:
            # 保持连接活跃
            await websocket.receive_text()
    except WebSocketDisconnect:
        active_websockets.remove(websocket)


# 广播消息到所有WebSocket连接
async def broadcast_message(message: str):
    for websocket in active_websockets:
        try:
            await websocket.send_text(message)
        except Exception as e:
            logging.error(f"发送WebSocket消息失败: {e}")


# 广播UI控制命令到所有WebSocket连接
async def broadcast_ui_command(command: str):
    message = json.dumps({
        "type": "ui_command",
        "command": command,
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    })
    await broadcast_message(message)


# UDP 服务：接收心跳
def udp_server():
    global udp_socket, last_sent_tags
    # 创建 UDP 套接字
    udp_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    udp_socket.bind(("0.0.0.0", 5899))  # 监听所有地址和端口 5899

    logging.info("UDP 服务已启动，正在监听端口 5899...")
    while True:
        data, addr = udp_socket.recvfrom(1024)  # 缓冲区大小为 1024 字节

        device_ip = addr[0]
        device_port = addr[1]  # 源端口，用于回复数据
        
        # 将设备信息保存到全局字典中
        # 使用 IP地址:源端口 作为设备标识符，以区分同一IP不同端口的设备
        device_id = f"{device_ip}:{device_port}"
        connected_devices[device_id] = {
            "ip": device_ip,
            "port": 5899,  # 目标端口保持不变，因为基站监听这个端口
            "source_port": device_port,  # 保存源端口，用于回复数据
            "last_seen": datetime.now()
        }
        
        logging.info(f"收到来自 {addr} 的 UDP 数据: {data}")
        logging.debug(f"已保存设备信息: {device_id} -> 源端口: {device_port}, 目标端口: 5899")
        
        # 检查该设备是否有最近发送的标签
        if device_id in last_sent_tags and last_sent_tags[device_id]:
            data_str = data.decode('utf-8', errors='replace').lower()
            data_hex = data.hex().lower()
            
            # 检查返回的数据是否包含某个发送的标签
            for tag in last_sent_tags[device_id]:
                clean_tag = tag.replace(" ", "").lower()
                if clean_tag in data_hex:
                    logging.info(f"标签触发成功! 标签: {tag}, 设备: {device_id}")
                    
                    # 准备发送到前端的消息
                    trigger_success_message = json.dumps({
                        "type": "trigger_success",
                        "device": device_id,
                        "tag": tag,
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                    })
                    
                    # 使用异步循环广播成功消息
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(broadcast_message(trigger_success_message))
                    loop.close()
                    
                    # 清理已触发的标签
                    last_sent_tags[device_id].remove(tag)
                    break
        
        # 如果存在活跃的WebSocket连接，则始终广播消息
        if active_websockets:
            # 尝试将字节数据转换为可序列化的格式
            try:
                hex_data = data.hex()
                # 尝试解码为可读字符串
                try:
                    readable_data = data.decode('utf-8', errors='replace')
                except:
                    readable_data = "无法解码为文本"
            except:
                hex_data = str(data)
                readable_data = str(data)
                
            # 构建消息
            message = json.dumps({
                "type": "scan_data",
                "device": device_id,
                "data": hex_data,
                "readable": readable_data,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3],
                "show": show_scan_data  # 添加显示控制标志
            })
            
            # 使用异步循环启动广播任务
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(broadcast_message(message))
            loop.close()


# 增加一个定期清理过期设备的函数
def clean_expired_devices():
    """
    清理超过UDP_TIMEOUT时间未收到心跳的设备
    """
    global connected_devices
    now = datetime.now()
    expired_devices = []

    for device_id, info in connected_devices.items():
        elapsed = (now - info['last_seen']).total_seconds()
        if elapsed > UDP_TIMEOUT:
            expired_devices.append(device_id)

    for device_id in expired_devices:
        logging.info(f"清理过期设备: {device_id}, 最后心跳: {connected_devices[device_id]['last_seen']}")
        del connected_devices[device_id]

    return len(expired_devices)


# 定期清理过期设备的线程
def cleanup_thread():
    import time
    while True:
        # 每10分钟清理一次
        time.sleep(600)
        try:
            cleaned = clean_expired_devices()
            if cleaned > 0:
                logging.info(f"清理了 {cleaned} 个过期设备")
        except Exception as e:
            logging.error(f"清理过期设备时出错: {e}")


# 添加定期发送保活数据包的函数
def send_keepalive_packets():
    """
    定期向所有已连接的设备发送保活数据包，以保持NAT通道开放
    """
    global connected_devices, udp_socket
    
    # 保活数据包 - 无害的PING命令
    keepalive_command = "50 49 4E 47 0D 0A"  # "PING\r\n" 的十六进制表示
    
    while True:
        # 每20秒执行一次
        time.sleep(20)
        
        if not connected_devices:
            logging.debug("没有已连接的设备，跳过发送保活数据包")
            continue
        
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        success_count = 0
        for device_id, device in connected_devices.items():
            try:
                device_ip = device['ip']
                source_port = device.get('source_port')
                
                if not source_port:
                    logging.warning(f"设备 {device_id} 没有源端口信息，跳过发送保活数据包")
                    continue
                
                # 将HEX字符串转换为字节数据
                byte_data = bytes.fromhex(keepalive_command.replace(" ", ""))
                
                # 发送保活数据包
                udp_socket.sendto(byte_data, (device_ip, source_port))
                success_count += 1
                
                # 详细日志级别
                logging.info(f"已发送保活数据包到 {device_ip}:{source_port}")
            except Exception as e:
                logging.error(f"向设备 {device_id} 发送保活数据包时出错: {e}")
        
        logging.debug(f"[{now}] 成功向 {success_count}/{len(connected_devices)} 个设备发送了保活数据包")


# 添加测试UDP通道的API
@app.post("/yifei/test-channel/{device_id}")
async def test_udp_channel(device_id: str):
    """
    测试与指定设备的UDP通道是否正常

    :param device_id: 设备ID(IP地址)
    :return: 测试结果
    """
    # 验证设备是否存在
    if device_id not in connected_devices:
        error_msg = f"找不到设备ID: {device_id}"
        return {
            "code": 404,
            "data": None,
            "msg": error_msg
        }

    device = connected_devices[device_id]

    # 发送一个简单的测试指令
    test_command = "54 45 53 54 0D 0A"  # "TEST\r\n" 的十六进制表示
    result = send_udp_command(device['ip'], device['port'], test_command, device_id)

    # 计算上次收到心跳的时间
    last_seen_ago = (datetime.now() - device['last_seen']).total_seconds()

    original_response = {
        "device_id": device_id,
        "ip": device['ip'],
        "port": device['port'],
        "source_port": device.get('source_port', '未知'),
        "last_seen": device['last_seen'].strftime("%Y-%m-%d %H:%M:%S"),
        "last_seen_ago": last_seen_ago,
        "channel_status": "正常" if result["status"] == "success" else "异常",
        "test_result": result
    }

    channel_status = "正常" if result["status"] == "success" else "异常"
    
    return {
        "code": 200 if result["status"] == "success" else 500,
        "data": original_response,
        "msg": f"UDP通道测试{channel_status}: {result['message']}"
    }


# 启动 UDP 服务
udp_thread = threading.Thread(target=udp_server, daemon=True)
udp_thread.start()

# 启动清理过期设备的线程
cleanup_thread = threading.Thread(target=cleanup_thread, daemon=True)
cleanup_thread.start()

# 启动保活数据包发送线程
keepalive_thread = threading.Thread(target=send_keepalive_packets, daemon=True)
keepalive_thread.start()


# 添加触发多个标签请求的模型
class TagTriggerRequest(BaseModel):
    tags: List[str]  # 标签列表
    device_id: Optional[str] = None  # 可选的设备ID


# 触发多个标签的API端点
@app.post("/yifei/trigger-tags")
async def trigger_tags(request: TagTriggerRequest):
    """
    触发多个声光标签，可以发送到所有已连接设备或指定设备
    
    Args:
        request (TagTriggerRequest): 包含标签列表和可选设备ID的请求
        
    Returns:
        dict: 包含命令和执行结果的响应
    """
    # 验证标签数量
    if not request.tags:
        error_msg = "必须提供至少一个标签"
        return {
            "code": 400,
            "data": None,
            "msg": error_msg
        }
    
    if len(request.tags) > 8:
        error_msg = "最多支持8个标签"
        return {
            "code": 400,
            "data": None,
            "msg": error_msg
        }
    
    # 验证并格式化每个标签
    formatted_tags = []
    for i, tag in enumerate(request.tags):
        # 移除所有空格
        clean_tag = tag.replace(" ", "")
        
        # 检查是否为有效的十六进制
        if not all(c in "0123456789ABCDEFabcdef" for c in clean_tag):
            error_msg = f"标签 {i + 1} 必须只包含十六进制字符"
            return {
                "code": 400,
                "data": None,
                "msg": error_msg
            }
        
        # 检查长度
        if len(clean_tag) != 12:
            error_msg = f"标签 {i + 1} 长度必须为6字节 (12个十六进制字符)，当前长度为 {len(clean_tag) // 2} 字节"
            return {
                "code": 400,
                "data": None,
                "msg": error_msg
            }
        
        # 格式化为大写，每两个字符加空格
        formatted_tag = " ".join([clean_tag[j:j + 2].upper() for j in range(0, len(clean_tag), 2)])
        formatted_tags.append(formatted_tag)
    
    # 构建命令
    # 前缀: 57 4C 49 53 54 3A
    prefix = "57 4C 49 53 54 3A"
    
    # 标签数量，转换为十六进制
    tags_count = len(request.tags)
    count_hex = format(tags_count, '02X')
    
    # 标签内容
    tags_content = " ".join(formatted_tags)
    
    # 后缀: 0D 0A
    suffix = "0D 0A"
    
    # 完整命令
    command = f"{prefix} {count_hex} {tags_content} {suffix}"
    
    # 执行命令
    results = []
    sent_count = 0
    
    # 如果指定了设备ID，则只发送到特定设备
    if request.device_id:
        if request.device_id not in connected_devices:
            error_msg = f"找不到设备ID: {request.device_id}"
            return {
                "code": 404,
                "data": None,
                "msg": error_msg
            }
        
        device = connected_devices[request.device_id]
        device_ip = device['ip']
        source_port = device.get('source_port')
        device_id = request.device_id
        
        try:
            # 将HEX字符串转换为字节数据
            byte_data = bytes.fromhex(command.replace(" ", ""))
            
            # 直接发送到指定设备的源端口
            udp_socket.sendto(byte_data, (device_ip, source_port))
            logging.info(f"已直接发送数据到 {device_ip}:{source_port} -> HEX: {command}")
            result = {"status": "success", "message": f"数据已发送到 {device_ip}:{source_port}"}
            
            # 保存发送的标签信息
            last_sent_tags[device_id] = formatted_tags.copy()
            logging.info(f"已保存标签信息: {device_id} -> {formatted_tags}")
        except Exception as e:
            logging.error(f"发送数据时出错: {e}")
            result = {"status": "error", "message": str(e)}
        
        results.append({
            "device_id": device_id,
            "device": f"{device_ip}:{source_port}",
            "result": result
        })
        
        if result["status"] == "success":
            sent_count += 1
    
    # 如果没有指定设备ID，则发送到所有已连接设备
    else:
        if not connected_devices:
            error_msg = "没有可用的已连接设备"
            return {
                "code": 404,
                "data": None,
                "msg": error_msg
            }
        
        # 遍历所有已连接设备
        for device_id, device in connected_devices.items():
            device_ip = device['ip']
            source_port = device.get('source_port')
            
            try:
                # 将HEX字符串转换为字节数据
                byte_data = bytes.fromhex(command.replace(" ", ""))
                
                # 直接发送到指定设备的源端口
                udp_socket.sendto(byte_data, (device_ip, source_port))
                logging.info(f"已直接发送数据到 {device_ip}:{source_port} -> HEX: {command}")
                result = {"status": "success", "message": f"数据已发送到 {device_ip}:{source_port}"}
                
                # 保存发送的标签信息
                last_sent_tags[device_id] = formatted_tags.copy()
                logging.info(f"已保存标签信息: {device_id} -> {formatted_tags}")
            except Exception as e:
                logging.error(f"发送数据时出错: {e}")
                result = {"status": "error", "message": str(e)}
            
            results.append({
                "device_id": device_id,
                "device": f"{device_ip}:{source_port}",
                "result": result
            })
            
            if result["status"] == "success":
                sent_count += 1
    
    original_response = {
        "command": command,
        "tags_count": tags_count,
        "devices_total": len(results),
        "devices_success": sent_count,
        "results": results
    }
    
    return {
        "code": 200 if sent_count > 0 else 500,
        "data": original_response,
        "msg": f"成功向{sent_count}/{len(results)}个设备发送了{tags_count}个标签"
    }


if __name__ == "__main__":
    import uvicorn

    # 启动 FastAPI 服务
    uvicorn.run(app, host="0.0.0.0", port=5012)