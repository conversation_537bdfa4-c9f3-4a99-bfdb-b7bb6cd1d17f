from machine import Pin
import time
import network
import socket
import urequests

# 配置 GPIO 引脚
pin_2 = Pin(2, Pin.OUT)

# WiFi 配置
SSID = "shui"
PASSWORD = "84618088"

# 阿里云服务器配置
SERVER_DOMAIN = "uat.juranguanjia.com"  # 替换为你的阿里云服务器域名
SERVER_PORT = 5899  # 替换为你的服务器端口号
LOCAL_PORT = 50001  # 本地监听端口

# 使用ping来获取域名对应的IP地址
def get_ip_address(domain):
    try:
        # 使用一个公共API服务来获取域名解析
        response = urequests.get(f"http://ip-api.com/json/{domain}")
        if response.status_code == 200:
            result = response.json()
            if 'query' in result:
                ip = result['query']
                print(f"域名 {domain} 解析成功，IP 地址为: {ip}")
                return ip
        # 如果上述方法失败，可以使用备用硬编码IP地址
        # 这里可以通过电脑先ping一下域名，获取其IP地址作为备用
        print("使用备用IP地址")
        return "**************"  # 替换为实际的IP地址
    except Exception as e:
        print(f"域名解析失败: {e}")
        # 返回备用IP地址
        return "**************"  # 替换为实际的IP地址

# 连接到 WiFi
def connect_wifi():
    wlan = network.WLAN(network.STA_IF)
    wlan.active(True)
    if not wlan.isconnected():
        print("正在连接 WiFi...")
        wlan.connect(SSID, PASSWORD)
        while not wlan.isconnected():
            pass
    print("WiFi 连接成功")
    print("IP 地址:", wlan.ifconfig()[0])

    # 测试互联网连接
    try:
        response = urequests.get("http://www.baidu.com")
        print("互联网连接正常")
    except Exception as e:
        print("互联网连接失败:", e)

# 初始化 UDP 套接字
def init_udp_socket():
    udp_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    udp_socket.bind(("0.0.0.0", LOCAL_PORT))  # 绑定本地端口
    return udp_socket

# 发送心跳消息
def send_heartbeat(udp_socket, server_ip, server_port):
    message = "Device Heartbeat: ESP32 is alive!"
    try:
        udp_socket.sendto(message.encode(), (server_ip, server_port))
        print(f"已发送心跳消息到 {server_ip}:{server_port}: {message}")
    except Exception as e:
        print(f"发送心跳消息失败: {e}")

# 接收 UDP 消息
def receive_udp_message(udp_socket):
    udp_socket.settimeout(1)  # 设置超时时间，避免阻塞
    try:
        data, addr = udp_socket.recvfrom(1024)  # 接收数据
        print("收到消息:", data.decode(), "来自:", addr)
    except OSError:
        # 超时异常处理
        pass

# 主函数
def main():
    connect_wifi()  # 连接 WiFi
    
    # 获取服务器IP地址
    server_ip = get_ip_address(SERVER_DOMAIN)
    print(f"使用服务器IP: {server_ip}")
    
    udp_socket = init_udp_socket()  # 初始化 UDP 套接字
    last_send_time = time.time()  # 记录上次发送心跳的时间

    while True:
        current_time = time.time()

        # 每隔 10 秒发送一次心跳消息（测试频率加快）
        if current_time - last_send_time >= 10:
            send_heartbeat(udp_socket, server_ip, SERVER_PORT)
            last_send_time = current_time

        # 尝试接收 UDP 消息
        receive_udp_message(udp_socket)

        # 避免 CPU 占用过高
        time.sleep(0.1)

# 启动程序
if __name__ == "__main__":
    main()