<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>智能客服系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        #chat-box {
            height: 400px;
            border: 1px solid #ccc;
            padding: 10px;
            overflow-y: auto;
        }
        .user-msg { color: #007bff; margin: 5px 0; }
        .ai-msg { color: #3c3; margin: 5px 0; white-space: pre-wrap; }
        #input-area { margin-top: 10px; }
        #user-input { width: calc(100% - 60px); padding: 8px; }
        #send-btn { padding: 8px 15px; background: #007bff; color: white; border: none; cursor: pointer; }
    </style>
</head>
<body>
<h2>智能客服系统</h2>
<div id="chat-box"></div>
<div id="input-area">
    <input type="text" id="user-input" placeholder="输入您的问题...">
    <button id="send-btn">发送</button>
</div>

<script>
    const chatBox = document.getElementById('chat-box');
    const userInput = document.getElementById('user-input');
    const sendBtn = document.getElementById('send-btn');

    // WebSocket连接管理
    let ws = null;
    let reconnectTimer = null;
    let currentAiMsg = null;

    function connectWebSocket() {
        if (ws && ws.readyState === WebSocket.OPEN) return;

        ws = new WebSocket('ws://127.0.0.1:8000/chat/ws');

        ws.onopen = () => {
            console.log("WebSocket连接成功");
            clearTimeout(reconnectTimer);
        };

        ws.onmessage = (event) => {
            const chunk = event.data;

            if (!currentAiMsg) {
                currentAiMsg = document.createElement('div');
                currentAiMsg.className = 'ai-msg';
                currentAiMsg.textContent = 'AI：';
                chatBox.appendChild(currentAiMsg);
            }

            // 智能空格处理
            const lastChar = currentAiMsg.textContent.slice(-1) || '';
            const firstChar = chunk[0] || '';
            const cnPunc = /[！？。，、；：“”‘’]/;

            if (
                !cnPunc.test(lastChar) &&
                !cnPunc.test(firstChar) &&
                lastChar !== ' ' &&
                firstChar !== ' ' &&
                currentAiMsg.textContent.length > 3
            ) {
                currentAiMsg.textContent += ' ';
            }

            currentAiMsg.textContent += chunk;
            chatBox.scrollTop = chatBox.scrollHeight;
        };

        ws.onerror = (error) => {
            console.error('WebSocket错误:', error);
        };

        ws.onclose = () => {
            console.log("WebSocket连接关闭，尝试重连...");
            currentAiMsg = null;
            reconnectTimer = setTimeout(connectWebSocket, 5000);
        };
    }

    function sendMessage() {
        if (!ws || ws.readyState !== WebSocket.OPEN) {
            alert("连接未就绪，请稍后再试");
            return;
        }

        const message = userInput.value.trim();
        if (!message) return;

        // 添加用户消息
        chatBox.innerHTML += `<div class="user-msg">用户：${message}</div>`;
        userInput.value = '';
        chatBox.scrollTop = chatBox.scrollHeight;
        currentAiMsg = null;  // 重置AI消息容器

        // 发送消息
        try {
            ws.send(JSON.stringify({ message }));
        } catch (e) {
            console.error("消息发送失败:", e);
        }
    }

    // 初始化连接
    connectWebSocket();

    // 事件绑定
    sendBtn.addEventListener('click', sendMessage);
    userInput.addEventListener('keypress', (e) => e.key === 'Enter' && sendMessage());
</script>
</body>
</html>