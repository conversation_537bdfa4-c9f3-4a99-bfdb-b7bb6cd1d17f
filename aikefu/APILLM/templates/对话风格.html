<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>小熊客服</title>
<style>
:root {
    --primary-bg: #ffffff;
    --secondary-bg: #f5f5f7;
    --text-color: #333333;
    --user-msg-bg: #007bff;
    --ai-msg-bg: #e9ecef;
    --input-bg: #ffffff;
    --border-color: #e0e0e0;
    --toggle-bg: #007bff;
    --toggle-color: white;
    --icon-size: 24px;
}

[data-theme="dark"] {
    --primary-bg: #1a1a1a;
    --secondary-bg: #2d2d2d;
    --text-color: #e0e0e0;
    --user-msg-bg: #0056b3;
    --ai-msg-bg: #3a3a3a;
    --input-bg: #2d2d2d;
    --border-color: #444444;
    --toggle-bg: #444444;
    --toggle-color: #e0e0e0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: var(--secondary-bg);
    color: var(--text-color);
    transition: all 0.3s ease;
    line-height: 1.6;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

h2 {
    font-size: 1.5rem;
    margin: 0;
}

#theme-toggle {
    background: var(--toggle-bg);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#theme-toggle:hover {
    transform: scale(1.05);
}

.sun-icon, .moon-icon {
    width: var(--icon-size);
    height: var(--icon-size);
    fill: var(--toggle-color);
    transition: opacity 0.3s ease;
}

.sun-icon { opacity: 1; }
.moon-icon { opacity: 0; }

[data-theme="dark"] .sun-icon { opacity: 0; }
[data-theme="dark"] .moon-icon { opacity: 1; }

#chat-box {
    height: 500px;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    background-color: var(--primary-bg);
    padding: 15px;
    overflow-y: auto;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.message {
    margin: 10px 0;
    display: flex;
    align-items: flex-start;
}

.user-msg {
    justify-content: flex-end;
}

.ai-msg {
    justify-content: flex-start;
}

.message-content {
    max-width: 75%;
    padding: 10px 15px;
    border-radius: 15px;
    font-size: 0.875rem;
    word-wrap: break-word;
    margin: 5px 10px;
}

.user-msg .message-content {
    background-color: var(--user-msg-bg);
    color: white;
    margin-right: 0;
}

.ai-msg .message-content {
    background-color: var(--ai-msg-bg);
    color: var(--text-color);
    margin-left: 0;
}

.ai-avatar {
    width: 36px;
    height: 36px;
    background-image: url('bot.jpg'); /* 替换为你的机器人头像路径 */
    background-size: cover;
    background-position: center;
    border-radius: 50%;
    margin-right: 10px;
}

#input-area {
    display: flex;
    align-items: center;
    background: var(--input-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 5px;
    transition: all 0.3s ease;
}

#user-input {
    flex: 1;
    padding: 10px;
    background: transparent;
    border: none;
    outline: none;
    color: var(--text-color);
    font-size: 0.875rem;
    margin-right: 10px;
}

#send-btn {
    padding: 8px 15px;
    background-color: var(--user-msg-bg);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    transition: background-color 0.3s ease;
    margin-left: 5px;
}

#send-btn:hover {
    background-color: #0056b3;
}

/* 滚动条样式 */
#chat-box::-webkit-scrollbar {
    width: 8px;
}

#chat-box::-webkit-scrollbar-track {
    background: var(--border-color);
    border-radius: 10px;
}

#chat-box::-webkit-scrollbar-thumb {
    background: var(--toggle-bg);
    border-radius: 10px;
}

/* 响应式调整 */
@media (max-width: 600px) {
    body {
        padding: 10px;
    }

    #chat-box {
        height: 400px;
    }

    header {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    #theme-toggle {
        margin-top: 10px;
    }
}
</style>
</head>
<body>
<header>
    <h2>小熊客服</h2>
    <button id="theme-toggle">
        <svg class="sun-icon" viewBox="0 0 24 24">
            <path d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
        </svg>
        <svg class="moon-icon" viewBox="0 0 24 24">
            <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"/>
        </svg>
    </button>
</header>
<div id="chat-box"></div>
<div id="input-area">
    <input type="text" id="user-input" placeholder="输入您的问题...">
    <button id="send-btn">发送</button>
</div>

<script>
const chatBox = document.getElementById('chat-box');
const userInput = document.getElementById('user-input');
const sendBtn = document.getElementById('send-btn');
const themeToggle = document.getElementById('theme-toggle');

// 主题初始化
let isDarkMode = localStorage.getItem('theme') === 'dark';
document.documentElement.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');

// 切换主题事件
themeToggle.addEventListener('click', () => {
    isDarkMode = !isDarkMode;
    document.documentElement.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');
    localStorage.setItem('theme', isDarkMode ? 'dark' : 'light');
});

// 发送消息
sendBtn.addEventListener('click', sendMessage);
userInput.addEventListener('keypress', (e) => e.key === 'Enter' && sendMessage());

function sendMessage() {
    const message = userInput.value.trim();
    if (!message) return;

    addMessage('user', message);
    userInput.value = '';
    chatBox.scrollTop = chatBox.scrollHeight;

    fetch('http://127.0.0.1:8000/chat/stream', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ messages: message })
    })
    .then(response => {
        const reader = response.body.getReader();
        const decoder = new TextDecoder('utf-8');
        const aiMsgContent = addMessage('ai', '');

        function read() {
            reader.read().then(({ done, value }) => {
                if (done) return;
                const chunk = decoder.decode(value, { stream: true });
                chunk.split('\n').forEach(line => {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(5).trim().replace(/\s+/g, ' ');
                        if (data) aiMsgContent.textContent += data;
                    }
                });
                chatBox.scrollTop = chatBox.scrollHeight;
                read();
            });
        }
        read();
    })
    .catch(error => {
        console.error('错误:', error);
        addMessage('ai', '连接异常，请稍后再试');
    });
}

function addMessage(sender, content) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-msg`;

    if (sender === 'ai') {
        const avatar = document.createElement('div');
        avatar.className = 'ai-avatar';
        messageDiv.appendChild(avatar);
    }

    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';
    messageContent.textContent = content;
    messageDiv.appendChild(messageContent);

    chatBox.appendChild(messageDiv);
    return messageContent;
}
</script>
</body>
</html>