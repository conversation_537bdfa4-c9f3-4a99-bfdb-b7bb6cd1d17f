<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>智能客服系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .chat-container {
            background: #f5f5f5;
            border-radius: 12px;
            padding: 20px;
            min-height: 600px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .message {
            margin: 15px 0;
        }

        .message.user {
            text-align: right;
        }

        .message.ai {
            text-align: left;
        }

        .message-content {
            display: inline-block;
            padding: 12px 18px;
            border-radius: 20px;
            max-width: 75%;
            line-height: 1.5;
        }

        .user .message-content {
            background: #007bff;
            color: white;
        }

        .ai .message-content {
            background: white;
            color: #333;
            border: 1px solid #eee;
        }

        .input-group {
            margin-top: 20px;
            display: flex;
            gap: 10px;
        }

        #user-input {
            flex: 1;
            padding: 12px;
            border: 2px solid #007bff;
            border-radius: 8px;
            font-size: 16px;
            outline: none;
        }

        #send-btn {
            padding: 12px 24px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }

        #send-btn:hover {
            background: #0056b3;
        }

        .loading {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
<div class="chat-container">
    <div id="chat-box"></div>
    <div class="input-group">
        <input type="text" id="user-input" placeholder="输入您的问题..." autocomplete="off">
        <button id="send-btn">发送</button>
    </div>
    <div id="loading" class="loading" style="display: none;">正在等待回复...</div>
</div>

<script>
    const chatBox = document.getElementById('chat-box');
    const userInput = document.getElementById('user-input');
    const sendBtn = document.getElementById('send-btn');
    const loading = document.getElementById('loading');

    // 发送消息
    async function sendMessage() {
        const message = userInput.value.trim();
        if (!message) return;

        // 添加用户消息到界面
        appendMessage('user', message);
        userInput.value = '';
        userInput.focus();

        // 显示加载状态
        sendBtn.disabled = true;
        loading.style.display = 'block';

        try {
            // 发送请求到后端
            const response = await fetch('http://127.0.0.1:5000/chat', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({user_input: message}),
                credentials: 'same-origin'
            });

            if (!response.ok) throw new Error('网络响应异常');

            const data = await response.json();
            if (data.error) throw new Error(data.error);

            // 添加AI回复到界面
            appendMessage('ai', data.response);

        } catch (error) {
            appendMessage('ai', `系统错误：${error.message}`);
        } finally {
            sendBtn.disabled = false;
            loading.style.display = 'none';
        }
    }

    // 添加消息到聊天框
    function appendMessage(sender, content) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.innerHTML = content.replace(/\n/g, '<br>');

        messageDiv.appendChild(contentDiv);
        chatBox.appendChild(messageDiv);
        chatBox.scrollTop = chatBox.scrollHeight;
    }

    // 事件监听
    sendBtn.addEventListener('click', sendMessage);
    userInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') sendMessage();
    });

    // 页面加载完成后聚焦输入框
    document.addEventListener('DOMContentLoaded', () => {
        userInput.focus();
    });
</script>
</body>
</html>