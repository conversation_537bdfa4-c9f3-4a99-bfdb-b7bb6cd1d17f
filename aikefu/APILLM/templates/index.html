<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>智能客服系统</title>
<style>
body {
font-family: Arial, sans-serif;
max-width: 800px;
margin: 0 auto;
padding: 20px;
}

#chat-box {
height: 400px;
border: 1px solid #ccc;
padding: 10px;
overflow-y: auto;
}

.user-msg {
color: #007bff;
margin: 5px 0;
}

.ai-msg {
color: #3c3;
margin: 5px 0;
white-space: pre-wrap; /* 保留自然换行 */
}

#input-area {
margin-top: 10px;
}

#user-input {
width: calc(100% - 60px);
padding: 8px;
}

#send-btn {
padding: 8px 15px;
background: #007bff;
color: white;
border: none;
cursor: pointer;
}
</style>
</head>
<body>
<h2>智能客服系统</h2>
<div id="chat-box"></div>
<div id="input-area">
<input type="text" id="user-input" placeholder="输入您的问题...">
<button id="send-btn">发送</button>
</div>

<script>
const chatBox = document.getElementById('chat-box');
const userInput = document.getElementById('user-input');
const sendBtn = document.getElementById('send-btn');

sendBtn.addEventListener('click', sendMessage);
userInput.addEventListener('keypress', (e) => {
if (e.key === 'Enter') sendMessage();
});

function sendMessage() {
const message = userInput.value.trim();
if (!message) return;

// 显示用户消息
chatBox.innerHTML += `<div class="user-msg">用户：${message}</div>`;
userInput.value = '';
chatBox.scrollTop = chatBox.scrollHeight;

// 创建POST请求
fetch('http://127.0.0.1:8000/chat/stream', {
method: 'POST',
headers: {
'Content-Type': 'application/json',
},
body: JSON.stringify({messages: message})
})
.then(response => {
const reader = response.body.getReader();
const decoder = new TextDecoder('utf-8');

// 创建AI消息容器
const aiMsgElement = document.createElement('div');
aiMsgElement.className = 'ai-msg';
aiMsgElement.textContent = 'AI：';
chatBox.appendChild(aiMsgElement);

// 流式处理函数
function read() {
reader.read().then(({done, value}) => {
if (done) {
aiMsgElement.textContent += ' '; // 添加结束空格
return;
}

const chunk = decoder.decode(value, {stream: true});
const lines = chunk.split('\n');

lines.forEach(line => {
if (line.startsWith('data: ')) {
let data = line.slice(5).trim();
data = data.replace(/\s+/g, ' '); // 合并连续空格

if (data) {
// 智能空格处理逻辑
const currentContent = aiMsgElement.textContent.slice(3).trim();
const prevChar = currentContent.slice(-1) || '';
const nextChar = data[0] || '';

const punctuation = ['！', '。', '，', '？', '；', '：', '、', '.', ',', '?', ';', ':'];
const needsSpace = !(prevChar === ' ' || punctuation.includes(nextChar));

if (needsSpace && currentContent) {
aiMsgElement.textContent += ' ';
}

aiMsgElement.textContent += data;
}
}
});

// 保持滚动条在底部
chatBox.scrollTop = chatBox.scrollHeight;
read();
});
}

read();
})
.catch(error => {
console.error('错误:', error);
chatBox.innerHTML += `<div class="ai-msg">连接异常，请稍后再试</div>`;
});
}
</script>
</body>
</html>