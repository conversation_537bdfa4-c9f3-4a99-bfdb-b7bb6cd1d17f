# -*- coding: utf-8 -*-
import os
import glob
import re
import time
import logging
import datetime
import schedule
from pathlib import Path
from logging.handlers import TimedRotatingFileHandler

import oss2

# OSS 配置信息（来自你的 info）
oss_info = {
    "endpoint": "http://oss-cn-beijing.aliyuncs.com",   # 改为外网 endpoint
    "keyid": "LTAIR4PwcCd6ZFAQ",
    "keysecret": "zk4KPaeVNW9M252PSzIfneKhy29ox9",
    "bucketname": "juranfile",
    "filehost": "https://file.juranguanjia.com"
}

# 本地配置
local_config = {
    "root_path": "D:/Image/Ori/",  # 配置本地根目录，可根据需要修改
    "scan_interval": 15,        # 扫描间隔，单位分钟
}

# 创建logs目录（如果不存在）
logs_dir = Path("logs")
if not logs_dir.exists():
    try:
        logs_dir.mkdir(exist_ok=True)
    except Exception as e:
        print(f"创建日志目录失败: {str(e)}")

# 配置日志
# 使用TimedRotatingFileHandler按天轮转日志，保留最近7天
log_file_path = logs_dir / "oss_upload.log"
file_handler = TimedRotatingFileHandler(
    filename=str(log_file_path),
    when='midnight',  # 每天午夜切换新日志文件
    interval=1,       # 间隔为1天
    backupCount=7,    # 保留7个备份文件
    encoding='utf-8'
)
file_handler.suffix = "%Y-%m-%d.log"  # 日志文件后缀名

# 标准输出处理器
stream_handler = logging.StreamHandler()

# 配置日志格式和处理器
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        file_handler,
        stream_handler
    ]
)
logger = logging.getLogger('oss_uploader')

# 初始化 Auth
auth = oss2.AuthV4(oss_info['keyid'], oss_info['keysecret'])

# 设置 endpoint 和 region
endpoint = oss_info['endpoint']
region = "cn-beijing"  # 手动设置更安全

# 初始化 Bucket
bucket = oss2.Bucket(auth, endpoint, oss_info['bucketname'], region=region)

def parse_filename(filename):
    """
    解析文件名，提取物流单号和时间信息
    文件名格式例如: JD1234568876-1-1_2025-06-17 151234.jpg 或 JDX040566571860-1-1-_2025-06-18 075136816.jpg
    返回: 物流单号(如JD1234568876), 日期(如20250617), 小时(如2025061715), 纯物流单号(如JD1234568876)
    """
    try:
        # 移除文件扩展名
        base_name = os.path.splitext(filename)[0]
        
        # 使用正则表达式匹配物流单号和日期时间
        # 修改后的正则可兼容末尾带有短横线"-"的物流单号格式
        pattern = r'([A-Z0-9][A-Z0-9\-]*)_(\d{4}-\d{2}-\d{2})\s*(\d{6})'
        match = re.search(pattern, base_name)
        
        if match:
            tracking_number = match.group(1)  # 完整物流单号 (可能包含 -1-1 或 -1-1-)
            date_str = match.group(2)         # 日期，格式如 2025-06-17
            time_str = match.group(3)         # 时间，格式如 151234
            
            # 提取日期数字 (20250617)
            date_digits = date_str.replace('-', '')
            
            # 提取小时 (2025061715)
            hour_digits = date_digits + time_str[:2]
            
            # 提取纯物流单号 (去除 -1-1 等后缀)
            pure_tracking_number = tracking_number.split('-')[0]
            
            return tracking_number, date_digits, hour_digits, pure_tracking_number
        else:
            logger.error(f"文件名格式不匹配: {filename}")
            return None, None, None, None
            
    except Exception as e:
        logger.error(f"解析文件名异常: {filename}, 错误: {str(e)}")
        return None, None, None, None

def upload_file(file_path):
    """
    上传单个文件到OSS
    file_path: 文件完整路径
    """
    try:
        # 使用Path对象处理路径
        file_path = Path(file_path)
        
        # 确保文件存在
        if not file_path.exists():
            logger.error(f"文件不存在: {file_path}")
            return False
            
        # 获取文件名
        filename = file_path.name
        logger.info(f"处理文件: {filename}")
        
        # 解析文件名
        tracking_number, date_digits, hour_digits, pure_tracking_number = parse_filename(filename)
        if not all([tracking_number, date_digits, hour_digits, pure_tracking_number]):
            logger.warning(f"跳过文件 {filename}: 文件名格式不符合要求")
            return False
        
        # 获取文件扩展名
        ext = file_path.suffix
        
        # 构造OSS对象路径 (ftpFiles/20250617/2025061715/JD1234568876.jpg)
        oss_object_key = f"ftpFiles/{date_digits}/{hour_digits}/{pure_tracking_number}{ext}"
        
        # 上传文件
        with open(str(file_path), 'rb') as fileobj:
            result = bucket.put_object(oss_object_key, fileobj)
        
        # 检查上传状态
        if result.status == 200:
            logger.info(f"上传成功: {filename} -> {oss_object_key}")
            
            # 文件访问URL
            file_url = f"{oss_info['filehost']}/{oss_object_key}"
            logger.info(f"文件访问地址: {file_url}")
            
            # 重命名本地文件为"已上传_xxxx"
            dir_path = file_path.parent
            new_filename = f"已上传_{filename}"
            new_file_path = dir_path / new_filename
            
            try:
                file_path.rename(new_file_path)
                logger.info(f"文件已重命名: {filename} -> {new_filename}")
                return True
            except Exception as e:
                logger.error(f"重命名文件失败: {filename}, 错误: {str(e)}")
                return True  # 上传成功但重命名失败，仍然返回True
        else:
            logger.error(f"上传失败: {filename}, 状态码: {result.status}")
            return False
            
    except Exception as e:
        logger.error(f"上传文件异常: {file_path}, 错误: {str(e)}")
        return False

def scan_directory():
    """
    扫描本地目录，查找需要上传的图片
    只处理当天和前两天的文件夹
    """
    # 使用Path对象处理路径，确保路径分隔符的一致性
    root_path = Path(local_config['root_path'])
    logger.info(f"开始扫描目录: {root_path}")
    
    # 获取当前日期和前两天日期
    today = datetime.datetime.now()
    target_dates = []
    
    # 生成当天和前两天的日期字符串，格式为YYYY-MM-DD
    for days_delta in range(3):  # 0=今天, 1=昨天, 2=前天
        date = today - datetime.timedelta(days=days_delta)
        date_str = date.strftime("%Y-%m-%d")
        target_dates.append(date_str)
    
    logger.info(f"将处理以下日期文件夹: {', '.join(target_dates)}")
    
    # 查找格式为 YYYY-MM-DD 的日期文件夹
    date_folders = []
    try:
        # 使用Path的glob方法查找所有日期文件夹
        all_date_pattern = root_path.glob("[0-9][0-9][0-9][0-9]-[0-9][0-9]-[0-9][0-9]")
        all_date_folders = list(all_date_pattern)
        
        # 筛选当天和前两天的文件夹
        for folder in all_date_folders:
            if folder.name in target_dates:
                date_folders.append(folder)
                
    except Exception as e:
        logger.error(f"扫描目录异常: {str(e)}")
    
    if not date_folders:
        logger.info(f"未找到目标日期文件夹 {target_dates}，请确认根目录路径: {root_path}")
        return
    
    logger.info(f"发现 {len(date_folders)} 个目标日期文件夹: {[folder.name for folder in date_folders]}")
    
    # 遍历每个日期文件夹
    uploaded_count = 0
    skipped_count = 0
    
    for date_folder in date_folders:
        folder_name = date_folder.name
        logger.info(f"处理文件夹: {folder_name}")
        
        # 查找所有图片文件 (jpg, jpeg, png)
        image_files = []
        
        try:
            # Windows系统下文件扩展名不区分大小写，使用一个模式可以匹配所有情况
            # 使用set避免重复
            image_paths = set()
            
            # 使用Path的glob方法查找图片文件，使用小写扩展名即可
            for ext in ['jpg', 'jpeg', 'png']:
                # 将Path对象转换为字符串，以便于比较和去重
                for img_path in date_folder.glob(f"*.{ext}"):
                    image_paths.add(str(img_path))
                    
            # 转回Path对象
            image_files = [Path(path) for path in image_paths]
                
        except Exception as e:
            logger.error(f"查找图片文件异常: {str(e)}")
        
        logger.info(f"在 {folder_name} 中发现 {len(image_files)} 个图片文件")
        
        # 筛选掉已经上传过的图片 (文件名以"已上传_"开头)
        new_images = [f for f in image_files if not f.name.startswith("已上传_")]
        
        logger.info(f"在 {folder_name} 中发现 {len(new_images)} 个新图片文件需要上传")
        
        # 上传每个新图片
        for image_path in new_images:
            # 将Path对象转换为字符串，确保路径格式正确
            image_path_str = str(image_path)
            if upload_file(image_path_str):
                uploaded_count += 1
            else:
                skipped_count += 1
    
    logger.info(f"扫描完成. 成功上传: {uploaded_count}, 跳过: {skipped_count}")
    return uploaded_count, skipped_count

def setup_scheduler():
    """
    设置定时任务调度器
    """
    logger.info(f"设置定时任务，每 {local_config['scan_interval']} 分钟扫描一次")
    schedule.every(local_config['scan_interval']).minutes.do(scan_directory)
    
    # 立即执行一次
    logger.info("立即执行首次扫描")
    scan_directory()
    
    # 持续运行定时任务
    logger.info("定时任务已开始，按 Ctrl+C 停止程序")
    try:
        while True:
            schedule.run_pending()
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("程序已停止")
    except Exception as e:
        logger.error(f"定时任务异常: {str(e)}")
        raise

def test_parse_filename():
    """
    测试函数，用于验证解析文件名功能
    """
    test_filenames = [
        "JDX040566571860-1-1-_2025-06-18 075136816.jpg",
        "JDX040566571860666-8-1-90-_2025-06-18 075136816.jpg",# 新格式
        "JD1234568876-1-1_2025-06-17 151234.jpg",        # 旧格式
        "JD9876543210_2025-06-18 103045.jpg"             # 简单格式
    ]
    
    print("===== 测试文件名解析功能 =====")
    for test_file in test_filenames:
        result = parse_filename(test_file)
        if result[0]:
            print(f"文件: {test_file}")
            print(f"  - 物流单号: {result[0]}")
            print(f"  - 日期: {result[1]}")
            print(f"  - 小时: {result[2]}")
            print(f"  - 纯物流单号: {result[3]}")
            print(f"  - 生成的OSS路径: ftpFiles/{result[1]}/{result[2]}/{result[3]}.jpg")
            print()
        else:
            print(f"无法解析文件名: {test_file}")
            print()

def main():
    """
    主函数
    """
    # 运行测试函数 (仅在直接运行脚本时执行，可以根据需要注释掉)
    
    logger.info("=== 阿里云OSS图片上传程序启动 ===")
    
    # 标准化本地根目录路径
    local_config['root_path'] = str(Path(local_config['root_path']).resolve())
    logger.info(f"本地根目录: {local_config['root_path']}")
    logger.info(f"OSS存储桶: {oss_info['bucketname']}")

    # 检查目录是否存在
    root_path = Path(local_config['root_path'])
    if not root_path.exists():
        logger.error(f"本地根目录不存在: {root_path}")
        logger.info("正在尝试创建目录...")
        try:
            root_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"已创建目录: {root_path}")
        except Exception as e:
            logger.error(f"创建目录失败: {str(e)}")
            return

    # 启动定时任务
    setup_scheduler()


if __name__ == "__main__":
    main()