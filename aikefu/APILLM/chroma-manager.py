import os
import uuid
import numpy as np
import torch
from transformers import AutoTokenizer, AutoModel
import chromadb
from fastapi import FastAPI, HTTPException, Query, Depends
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import uvicorn
from contextlib import asynccontextmanager


# 全局常量定义
DEFAULT_COLLECTION_NAME = "information"  # 默认集合名称
DEFAULT_DB_PATH = "xiondon_information"  # 默认数据库路径
DEFAULT_MODEL_NAME = "BAAI/bge-m3"  # 模型名称
DEFAULT_LOCAL_MODEL_PATH = "./models/BAAI-bge-m3"  # 本地模型路径
DEFAULT_BATCH_SIZE = 20  # 默认批处理大小
DEFAULT_PAGE = 1  # 默认页码
DEFAULT_PAGE_SIZE = 10  # 默认每页数量
MAX_PAGE_SIZE = 100  # 最大每页数量


class Document(BaseModel):
    text: str
    id: Optional[str] = None  # 此字段将被忽略，服务器将自动生成唯一ID
    collection_name: str = DEFAULT_COLLECTION_NAME  # 集合名称


class QueryRequest(BaseModel):
    query_text: str
    n_results: int = 3
    collection_name: str = DEFAULT_COLLECTION_NAME  # 集合名称


class UpdateDocument(BaseModel):
    id: str
    text: str


class UpdateRequest(BaseModel):
    documents: List[UpdateDocument]
    collection_name: str = DEFAULT_COLLECTION_NAME  # 集合名称


class DeleteRequest(BaseModel):
    ids: List[str]
    collection_name: str = DEFAULT_COLLECTION_NAME  # 集合名称


class ListRequest(BaseModel):
    page: int = DEFAULT_PAGE
    page_size: int = DEFAULT_PAGE_SIZE
    collection_name: str = DEFAULT_COLLECTION_NAME  # 集合名称


class InformationManager:
    def __init__(self, db_path=DEFAULT_DB_PATH, default_collection_name=DEFAULT_COLLECTION_NAME, 
                 model_name=DEFAULT_MODEL_NAME, local_model_path=DEFAULT_LOCAL_MODEL_PATH, batch_size=DEFAULT_BATCH_SIZE):
        self.CHROMA_DB_PATH = db_path
        self.DEFAULT_COLLECTION_NAME = default_collection_name
        self.BATCH_SIZE = batch_size
        self.MODEL_NAME = model_name
        self.LOCAL_MODEL_PATH = local_model_path
        self.collections = {}  # 缓存集合实例

        # 初始化Chroma客户端
        self.client = chromadb.PersistentClient(path=self.CHROMA_DB_PATH)
        # 初始化默认集合
        self.get_collection(self.DEFAULT_COLLECTION_NAME)

        # 加载模型和分词器（优先从本地路径加载）
        self._load_model()

    def _load_model(self):
        """加载模型和分词器，优先从本地路径加载"""
        import os
        local_path_exists = os.path.exists(self.LOCAL_MODEL_PATH)
        
        if local_path_exists:
            print(f"从本地路径加载模型: {self.LOCAL_MODEL_PATH}")
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(self.LOCAL_MODEL_PATH)
                self.model = AutoModel.from_pretrained(self.LOCAL_MODEL_PATH)
                
                # 初始化设备并迁移模型
                self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
                print(f"使用的设备为: {self.device} device")
                self.model.to(self.device)
                
                print("本地模型加载成功")
                return
            except Exception as e:
                print(f"从本地路径加载模型失败，错误信息: {e}")
                print(f"尝试从Hugging Face下载模型: {self.MODEL_NAME}")
        else:
            print(f"本地模型路径不存在: {self.LOCAL_MODEL_PATH}")
            print(f"尝试从Hugging Face下载模型: {self.MODEL_NAME}")
            
        # 从Hugging Face下载模型
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(self.MODEL_NAME)
            self.model = AutoModel.from_pretrained(self.MODEL_NAME)
            
            # 初始化设备并迁移模型
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            print(f"使用的设备为: {self.device} device")
            self.model.to(self.device)
            
            print("从Hugging Face下载模型成功")
        except Exception as e:
            raise RuntimeError(f"模型加载失败，错误信息: {e}")
    
    def get_collection(self, collection_name=None):
        """获取或创建指定名称的集合"""
        if collection_name is None:
            collection_name = self.DEFAULT_COLLECTION_NAME
            
        if collection_name not in self.collections:
            self.collections[collection_name] = self.client.get_or_create_collection(name=collection_name)
            print(f"获取或创建集合: {collection_name}")
        return self.collections[collection_name]

    def read_txt_file(self, file_path):
        """读取整个文件内容作为单个文本"""
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read().strip()

    def generate_embeddings(self, texts):
        """生成嵌入向量"""
        inputs = self.tokenizer(
            texts,
            padding=True,
            truncation=True,
            return_tensors="pt",
            max_length=1024
        )
        # 将输入数据迁移到设备
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        with torch.no_grad():
            outputs = self.model(**inputs)
        return outputs.last_hidden_state[:, 0, :].cpu().numpy()

    def add_documents(self, texts, collection_name=None, ids=None):
        """添加文档到指定集合"""
        if collection_name is None:
            collection_name = self.DEFAULT_COLLECTION_NAME
            
        collection = self.get_collection(collection_name)
        
        if ids is None:
            ids = [str(uuid.uuid4()) for _ in texts]

        embeddings = self.generate_embeddings(texts)

        # 过滤无效数据
        valid_indices = [
            j for j, (emb, txt) in enumerate(zip(embeddings, texts))
            if isinstance(emb, np.ndarray) and txt.strip()
        ]

        if not valid_indices:
            return []

        valid_embeddings = [embeddings[j].tolist() for j in valid_indices]
        valid_texts = [texts[j] for j in valid_indices]
        valid_ids = [ids[j] for j in valid_indices]

        # 插入到指定集合
        collection.add(
            ids=valid_ids,
            embeddings=valid_embeddings,
            documents=valid_texts
        )
        print(f"插入 {len(valid_texts)} 个文档到集合 {collection_name}")
        return valid_ids

    def delete_documents(self, ids, collection_name=None):
        """从指定集合删除文档"""
        if collection_name is None:
            collection_name = self.DEFAULT_COLLECTION_NAME
            
        collection = self.get_collection(collection_name)
        
        # 先检查这些ID是否存在
        existing_ids = []
        non_existing_ids = []
        
        # 获取所有ID
        all_docs = collection.get()
        all_ids = set(all_docs["ids"]) if "ids" in all_docs and all_docs["ids"] else set()
        
        # 分类ID是否存在
        for doc_id in ids:
            if doc_id in all_ids:
                existing_ids.append(doc_id)
            else:
                non_existing_ids.append(doc_id)
        
        # 只删除存在的ID
        if existing_ids:
            collection.delete(ids=existing_ids)
            print(f"从集合 {collection_name} 删除 {len(existing_ids)} 个文档")
        
        if non_existing_ids:
            print(f"警告：集合 {collection_name} 中有 {len(non_existing_ids)} 个ID不存在")
        
        return {"deleted": existing_ids, "not_found": non_existing_ids}

    def update_documents(self, ids, texts, collection_name=None):
        """更新指定集合中的文档"""
        if collection_name is None:
            collection_name = self.DEFAULT_COLLECTION_NAME
            
        self.delete_documents(ids, collection_name)
        return self.add_documents(texts, collection_name, ids)

    def query_documents(self, query_text, n_results=3, collection_name=None):
        """查询指定集合中的文档"""
        if collection_name is None:
            collection_name = self.DEFAULT_COLLECTION_NAME
            
        collection = self.get_collection(collection_name)
        
        query_embedding = self.generate_embeddings([query_text])[0].tolist()
        results = collection.query(
            query_embeddings=[query_embedding],
            n_results=n_results
        )
        return results

    def get_top_documents(self, n=DEFAULT_PAGE_SIZE, page=DEFAULT_PAGE, collection_name=None):
        """获取指定集合中的文档，支持分页"""
        if collection_name is None:
            collection_name = self.DEFAULT_COLLECTION_NAME
            
        collection = self.get_collection(collection_name)
        
        # 获取所有ID以便于分页
        all_docs = collection.get()
        all_ids = all_docs.get("ids", [])
        
        total_documents = len(all_ids)
        
        # 计算分页索引
        start_idx = (page - 1) * n
        end_idx = min(start_idx + n, total_documents)
        
        # 如果请求的页码超出范围，返回空结果
        if start_idx >= total_documents:
            return {
                "ids": [],
                "documents": [],
                "metadatas": [],
                "embeddings": [],
                "pagination": {
                    "total": total_documents,
                    "page": page,
                    "page_size": n,
                    "total_pages": max(1, (total_documents + n - 1) // n)
                }
            }
        
        # 获取当前页的ID
        page_ids = all_ids[start_idx:end_idx]
        
        # 使用这些ID查询详细信息
        if page_ids:
            results = collection.get(ids=page_ids)
        else:
            results = {"ids": [], "documents": [], "metadatas": [], "embeddings": []}
        
        # 添加分页信息
        results["pagination"] = {
            "total": total_documents,
            "page": page,
            "page_size": n,
            "total_pages": max(1, (total_documents + n - 1) // n)
        }
        
        return results

    def list_collections(self):
        """列出所有可用的集合"""
        return self.client.list_collections()


# 创建一个应用启动和关闭时的上下文管理器
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 应用启动时初始化 InformationManager
    app.state.manager = InformationManager(
        db_path=DEFAULT_DB_PATH,
        default_collection_name=DEFAULT_COLLECTION_NAME,
        model_name=DEFAULT_MODEL_NAME,
        local_model_path=DEFAULT_LOCAL_MODEL_PATH
    )
    yield
    # 应用关闭时可以进行清理工作
    # 例如释放模型资源等
    print("应用关闭，正在释放资源...")


# 创建 FastAPI 应用
app = FastAPI(
    title="文档向量检索 API",
    description="基于 Chroma 的文档向量检索 API，提供增删改查功能",
    version="1.0.0",
    lifespan=lifespan,
)


# 依赖函数，用于获取 InformationManager 实例
def get_manager():
    return app.state.manager


@app.post("/")
async def read_root():
    return {"message": "欢迎使用文档向量检索 API"}


@app.post("/collections/list")
async def list_collections(manager: InformationManager = Depends(get_manager)):
    """列出所有可用的集合"""
    collections = manager.list_collections()
    # ChromaDB v0.6.0中，list_collections直接返回集合名称列表而不是集合对象
    return {"collections": collections}


@app.post("/documents/add", response_model=Dict[str, Any])
async def add_documents(documents: List[Document], manager: InformationManager = Depends(get_manager)):
    """
    添加文档到数据库
    
    注意：
    - 即使提供了id字段，系统也会自动为每个文档生成新的唯一ID
    - 可以通过collection_name字段指定要添加到的集合
    """
    if not documents:
        return {"ids": []}
    
    # 按集合名称分组
    docs_by_collection = {}
    for doc in documents:
        if doc.collection_name not in docs_by_collection:
            docs_by_collection[doc.collection_name] = []
        docs_by_collection[doc.collection_name].append(doc.text)
    
    # 分别处理每个集合的文档
    all_ids = {}
    for collection_name, texts in docs_by_collection.items():
        result_ids = manager.add_documents(texts, collection_name=collection_name, ids=None)
        all_ids[collection_name] = result_ids
    
    return {"results": all_ids}


@app.post("/documents/delete", response_model=Dict[str, Any])
async def delete_documents(request: DeleteRequest, manager: InformationManager = Depends(get_manager)):
    """
    从数据库中删除文档
    
    可以通过collection_name字段指定要操作的集合
    """
    result = manager.delete_documents(request.ids, collection_name=request.collection_name)
    
    # 如果没有成功删除任何文档，返回404状态码
    if not result["deleted"] and result["not_found"]:
        raise HTTPException(
            status_code=404, 
            detail=f"在集合 {request.collection_name} 中未找到要删除的文档: {result['not_found']}"
        )
    
    return result


@app.post("/documents/update", response_model=Dict[str, Any])
async def update_documents(request: UpdateRequest, manager: InformationManager = Depends(get_manager)):
    """
    更新数据库中的文档
    
    可以通过collection_name字段指定要操作的集合
    """
    ids = [doc.id for doc in request.documents]
    texts = [doc.text for doc in request.documents]
    updated_ids = manager.update_documents(ids, texts, collection_name=request.collection_name)
    return {"collection": request.collection_name, "updated": updated_ids}


@app.post("/query", response_model=Dict[str, Any])
async def query_documents(query: QueryRequest, manager: InformationManager = Depends(get_manager)):
    """
    查询数据库中的文档
    
    可以通过collection_name字段指定要查询的集合
    """
    results = manager.query_documents(query.query_text, query.n_results, collection_name=query.collection_name)
    return {"collection": query.collection_name, "results": results}


@app.post("/documents/list", response_model=Dict[str, Any])
async def get_documents(request: ListRequest = None, manager: InformationManager = Depends(get_manager)):
    """
    获取数据库中的文档，支持分页
    
    - page: 页码，从1开始
    - page_size: 每页记录数，范围1-100
    - collection_name: 集合名称，默认为"information"
    """
    page = DEFAULT_PAGE
    page_size = DEFAULT_PAGE_SIZE
    collection_name = DEFAULT_COLLECTION_NAME
    
    if request:
        page = request.page
        page_size = request.page_size
        collection_name = request.collection_name
    
    if page < 1:
        raise HTTPException(status_code=400, detail="页码必须大于或等于1")
    
    if page_size < 1 or page_size > MAX_PAGE_SIZE:
        raise HTTPException(status_code=400, detail=f"每页记录数必须在1到{MAX_PAGE_SIZE}之间")
    
    results = manager.get_top_documents(n=page_size, page=page, collection_name=collection_name)
    return {"collection": collection_name, "results": results}


if __name__ == "__main__":
    # 直接运行此文件时启动 API 服务
    uvicorn.run("chroma-manager:app", host="0.0.0.0", port=8001, reload=True)