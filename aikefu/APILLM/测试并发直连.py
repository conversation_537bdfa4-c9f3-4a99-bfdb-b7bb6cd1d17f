import random
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# 定义问题列表
questions = [
    "扫地机器人内部错误，如何恢复出厂设置？",
    "介绍一下熊洞智家",
    "熊洞智家在哪？",
    "扫地机器人内部错误，如何恢复出厂设置？",
    "天乔智能家居包头店"
]

# 随机选择一个问题
selected_question = random.choice(questions)

# 打印选择的问题
print(f"随机选择的问题: {selected_question}\n")

# 定义请求的 URL 和请求数据
url = "http://127.0.0.1:8000/chat"
data = {
    "messages": "你是谁"
}

# 定义一个函数来发送请求
def send_request():
    try:
        response = requests.post(url, json=data)
        response.raise_for_status()  # 检查请求是否成功
        return response.json()
    except requests.exceptions.RequestException as e:
        return f"请求失败: {e}"

# 测试不同数量的并发请求
def test_concurrency(concurrency_level):
    start_time = time.time()
    with ThreadPoolExecutor(max_workers=concurrency_level) as executor:
        futures = [executor.submit(send_request) for _ in range(concurrency_level)]
        results = []
        for future in as_completed(futures):
            result = future.result()
            results.append(result)
    end_time = time.time()
    duration = end_time - start_time
    success_count = sum(1 for result in results if isinstance(result, dict))
    failure_count = concurrency_level - success_count
    print(f"并发级别: {concurrency_level}")
    print(f"总请求数: {concurrency_level}")
    print(f"成功请求数: {success_count}")
    print(f"失败请求数: {failure_count}")
    print(f"耗时: {duration:.2f} 秒")
    print(f"每秒请求数: {concurrency_level / duration:.2f}\n")

# 设置要测试的并发级别
concurrency_levels = [1, 5, 10, 20, 30, 50, 100]  # 你可以根据需要调整并发级别

# 逐个测试不同的并发级别
for level in concurrency_levels:
    test_concurrency(level)
    time.sleep(2)  # 之间等待一段时间，避免对服务器造成过大压力



import random
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# 定义问题列表
questions = [
    "扫地机器人内部错误，如何恢复出厂设置？",
    "介绍一下熊洞智家",
    "熊洞智家在哪？",
    "扫地机器人内部错误，如何恢复出厂设置？",
    "天乔智能家居包头店"
]

# 随机选择一个问题
selected_question = random.choice(questions)

# 打印选择的问题
print(f"随机选择的问题: {selected_question}\n")

# 定义请求的 URL 和请求数据
url = "http://127.0.0.1:8000/chat"
data = {
    "messages": "你是谁"
}

# 定义一个函数来发送请求
def send_request():
    try:
        response = requests.post(url, json=data)
        response.raise_for_status()  # 检查请求是否成功
        return response.json()
    except requests.exceptions.RequestException as e:
        return f"请求失败: {e}"

# 测试不同数量的并发请求
def test_concurrency(concurrency_level):
    start_time = time.time()
    with ThreadPoolExecutor(max_workers=concurrency_level) as executor:
        futures = [executor.submit(send_request) for _ in range(concurrency_level)]
        results = []
        for future in as_completed(futures):
            result = future.result()
            results.append(result)
    end_time = time.time()
    duration = end_time - start_time
    success_count = sum(1 for result in results if isinstance(result, dict))
    failure_count = concurrency_level - success_count
    print(f"并发级别: {concurrency_level}")
    print(f"总请求数: {concurrency_level}")
    print(f"成功请求数: {success_count}")
    print(f"失败请求数: {failure_count}")
    print(f"耗时: {duration:.2f} 秒")
    print(f"每秒请求数: {concurrency_level / duration:.2f}\n")

# 设置要测试的并发级别
concurrency_levels = [1, 5, 10, 20, 30, 50, 100]  # 你可以根据需要调整并发级别

# 逐个测试不同的并发级别
for level in concurrency_levels:
    test_concurrency(level)
    time.sleep(2)  # 之间等待一段时间，避免对服务器造成过大压力



import random
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# 定义问题列表
questions = [
    "扫地机器人内部错误，如何恢复出厂设置？",
    "介绍一下熊洞智家",
    "熊洞智家在哪？",
    "扫地机器人内部错误，如何恢复出厂设置？",
    "天乔智能家居包头店"
]

# 随机选择一个问题
selected_question = random.choice(questions)

# 打印选择的问题
print(f"随机选择的问题: {selected_question}\n")

# 定义请求的 URL 和请求数据
url = "http://127.0.0.1:8000/chat"
data = {
    "messages": "你是谁"
}

# 定义一个函数来发送请求
def send_request():
    try:
        response = requests.post(url, json=data)
        response.raise_for_status()  # 检查请求是否成功
        return response.json()
    except requests.exceptions.RequestException as e:
        return f"请求失败: {e}"

# 测试不同数量的并发请求
def test_concurrency(concurrency_level):
    start_time = time.time()
    with ThreadPoolExecutor(max_workers=concurrency_level) as executor:
        futures = [executor.submit(send_request) for _ in range(concurrency_level)]
        results = []
        for future in as_completed(futures):
            result = future.result()
            results.append(result)
    end_time = time.time()
    duration = end_time - start_time
    success_count = sum(1 for result in results if isinstance(result, dict))
    failure_count = concurrency_level - success_count
    print(f"并发级别: {concurrency_level}")
    print(f"总请求数: {concurrency_level}")
    print(f"成功请求数: {success_count}")
    print(f"失败请求数: {failure_count}")
    print(f"耗时: {duration:.2f} 秒")
    print(f"每秒请求数: {concurrency_level / duration:.2f}\n")

# 设置要测试的并发级别
concurrency_levels = [1, 5, 10, 20, 30, 50, 100]  # 你可以根据需要调整并发级别

# 逐个测试不同的并发级别
for level in concurrency_levels:
    test_concurrency(level)
    time.sleep(1)  # 之间等待一段时间，避免对服务器造成过大压力


