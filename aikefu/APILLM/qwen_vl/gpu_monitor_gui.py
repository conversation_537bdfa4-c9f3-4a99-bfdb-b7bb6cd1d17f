import os
import subprocess
import tkinter as tk
from tkinter import ttk, Scrollbar
from ctypes import *
import psutil

import pynvml
from pynvml import *

# 手动加载 nvml.dll
nvml_path = r"C:\Windows\System32\nvml.dll"
nvml_lib = CDLL(nvml_path)
pynvml.nvmlLib = nvml_lib


def get_process_detailed_info(pid):
    info = {
        "name": "Unknown",
        "path": "N/A",
        "memory_mb": 0,
        "gpu_memory": "N/A",
        "creation_time": "N/A",
        "cmdline": "N/A",
        "type": "未知"
    }

    try:
        p = psutil.Process(pid)
        info["name"] = p.name()
        info["path"] = p.exe()
        info["memory_mb"] = round(p.memory_info().rss / 1024 / 1024, 2)
        info["creation_time"] = p.create_time()
        info["cmdline"] = " ".join(p.cmdline())
    except:
        return info

    return info


def get_gpu_process_info():
    pynvml.nvmlInit()

    data = []
    device_count = pynvml.nvmlDeviceGetCount()

    for i in range(device_count):
        handle = pynvml.nvmlDeviceGetHandleByIndex(i)
        try:
            compute_procs = pynvml.nvmlDeviceGetComputeRunningProcesses(handle)
        except:
            compute_procs = []

        try:
            graphics_procs = pynvml.nvmlDeviceGetGraphicsRunningProcesses(handle)
        except:
            graphics_procs = []

        pid_map = {}

        for p in compute_procs:
            if p.pid not in pid_map:
                pid_map[p.pid] = {"gpu_memory": p.usedGpuMemory, "type": "计算"}
            else:
                if pid_map[p.pid]["type"] != "图形":
                    pid_map[p.pid]["type"] = "计算+图形"

        for p in graphics_procs:
            if p.pid not in pid_map:
                pid_map[p.pid] = {"gpu_memory": p.usedGpuMemory, "type": "图形"}
            else:
                if pid_map[p.pid]["type"] != "计算":
                    pid_map[p.pid]["type"] = "计算+图形"

        for pid, meta in pid_map.items():
            detail = get_process_detailed_info(pid)
            detail["gpu_memory"] = (
                f"{meta['gpu_memory'] // 1024 // 1024} MiB" if meta["gpu_memory"] else "N/A"
            )
            detail["type"] = meta["type"]
            data.append(detail)

    pynvml.nvmlShutdown()

    # 按系统占用内存排序（从大到小）
    return sorted(data, key=lambda x: x.get("memory_mb", 0), reverse=True)


def refresh_table():
    for row in tree.get_children():
        tree.delete(row)

    process_list = get_gpu_process_info()
    for proc in process_list:
        tree.insert(
            "", "end", values=(
                proc["name"],
                proc["type"],
                proc["gpu_memory"],
                f"{proc['memory_mb']} MB",
                proc["path"],
                proc["cmdline"][:80] + ("..." if len(proc["cmdline"]) > 80 else "")
            )
        )


# GUI 创建
root = tk.Tk()
root.title("GPU 占用进程查看器 - by ChatGPT")
root.geometry("1400x600")
root.resizable(True, True)

columns = ("进程名", "类型", "GPU显存", "系统内存", "启动路径", "命令行")

tree = ttk.Treeview(root, columns=columns, show="headings", height=25)

for col in columns:
    tree.heading(col, text=col)
    tree.column(col, anchor="w")

tree.column("进程名", width=150)
tree.column("类型", width=100)
tree.column("GPU显存", width=80)
tree.column("系统内存", width=100)
tree.column("启动路径", width=400)
tree.column("命令行", width=500)

tree.pack(side=tk.TOP, fill=tk.BOTH, expand=True)

scrollbar_y = Scrollbar(root, orient=tk.VERTICAL, command=tree.yview)
tree.configure(yscroll=scrollbar_y.set)
scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)

refresh_button = tk.Button(root, text="🔄 刷新数据", command=refresh_table, font=("微软雅黑", 12))
refresh_button.pack(pady=10)

refresh_table()
root.mainloop()