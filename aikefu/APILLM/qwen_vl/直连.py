import base64
import requests
import json

# 读取图片并转成 base64
with open("电饭煲.png", "rb") as image_file:
    image_base64 = base64.b64encode(image_file.read()).decode("utf-8")

# 请求 payload
payload = {
    "model": "qwen2.5vl:3b",
    "messages": [
        {
            "role": "user",
            "content": [
                {"type": "image", "image": image_base64},
                {"type": "text", "text": "请描述这张图片的内容。"}
            ]
        }
    ],
    "stream": False
}

# 发送请求
response = requests.post("http://localhost:11434/api/chat", json=payload)

# 调试输出整个响应内容
try:
    data = response.json()
    print("完整响应：", json.dumps(data, indent=2, ensure_ascii=False))
    # 检查 message 字段
    if "message" in data and "content" in data["message"]:
        print("模型回复：", data["message"]["content"])
    else:
        print("❌ 未找到 message -> content 字段！响应结构异常！")
except Exception as e:
    print("❌ 无法解析 JSON，返回内容为：\n", response.text)
    print("错误详情：", str(e))