('D:\\a_project\\aliyun\\xiondon\\aikefu\\APILLM\\qwen_vl\\build\\GPU查看器\\PYZ-00.pyz',
 [('_compat_pickle',
   'D:\\app\\python_3.11\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression', 'D:\\app\\python_3.11\\Lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'D:\\app\\python_3.11\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\app\\python_3.11\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'D:\\app\\python_3.11\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\app\\python_3.11\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\app\\python_3.11\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\app\\python_3.11\\Lib\\ast.py', 'PYMODULE'),
  ('base64', 'D:\\app\\python_3.11\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\app\\python_3.11\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\app\\python_3.11\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\app\\python_3.11\\Lib\\calendar.py', 'PYMODULE'),
  ('contextlib', 'D:\\app\\python_3.11\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\app\\python_3.11\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\app\\python_3.11\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\app\\python_3.11\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\app\\python_3.11\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\app\\python_3.11\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian',
   'D:\\app\\python_3.11\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\app\\python_3.11\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\app\\python_3.11\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\app\\python_3.11\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\app\\python_3.11\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'D:\\app\\python_3.11\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('dataclasses', 'D:\\app\\python_3.11\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\app\\python_3.11\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\app\\python_3.11\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'D:\\app\\python_3.11\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'D:\\app\\python_3.11\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\app\\python_3.11\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\app\\python_3.11\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\app\\python_3.11\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\app\\python_3.11\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\app\\python_3.11\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset', 'D:\\app\\python_3.11\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\app\\python_3.11\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\app\\python_3.11\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors', 'D:\\app\\python_3.11\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser',
   'D:\\app\\python_3.11\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\app\\python_3.11\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header', 'D:\\app\\python_3.11\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\app\\python_3.11\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\app\\python_3.11\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message', 'D:\\app\\python_3.11\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\app\\python_3.11\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\app\\python_3.11\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime',
   'D:\\app\\python_3.11\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils', 'D:\\app\\python_3.11\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'D:\\app\\python_3.11\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\app\\python_3.11\\Lib\\fractions.py', 'PYMODULE'),
  ('getopt', 'D:\\app\\python_3.11\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\app\\python_3.11\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'D:\\app\\python_3.11\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\app\\python_3.11\\Lib\\hashlib.py', 'PYMODULE'),
  ('importlib',
   'D:\\app\\python_3.11\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\app\\python_3.11\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\app\\python_3.11\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\app\\python_3.11\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\app\\python_3.11\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\app\\python_3.11\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\app\\python_3.11\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\app\\python_3.11\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\app\\python_3.11\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\app\\python_3.11\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\app\\python_3.11\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\app\\python_3.11\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\app\\python_3.11\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\app\\python_3.11\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\app\\python_3.11\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\app\\python_3.11\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\app\\python_3.11\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\app\\python_3.11\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\app\\python_3.11\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\app\\python_3.11\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\app\\python_3.11\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\app\\python_3.11\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\app\\python_3.11\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\app\\python_3.11\\Lib\\ipaddress.py', 'PYMODULE'),
  ('logging', 'D:\\app\\python_3.11\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\app\\python_3.11\\Lib\\lzma.py', 'PYMODULE'),
  ('numbers', 'D:\\app\\python_3.11\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\app\\python_3.11\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'D:\\app\\python_3.11\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'D:\\app\\python_3.11\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\app\\python_3.11\\Lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'D:\\app\\python_3.11\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\app\\python_3.11\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\app\\python_3.11\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile', 'D:\\app\\python_3.11\\Lib\\py_compile.py', 'PYMODULE'),
  ('pynvml', 'D:\\app\\python_3.11\\Lib\\site-packages\\pynvml.py', 'PYMODULE'),
  ('quopri', 'D:\\app\\python_3.11\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\app\\python_3.11\\Lib\\random.py', 'PYMODULE'),
  ('selectors', 'D:\\app\\python_3.11\\Lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'D:\\app\\python_3.11\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\app\\python_3.11\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\app\\python_3.11\\Lib\\socket.py', 'PYMODULE'),
  ('statistics', 'D:\\app\\python_3.11\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\app\\python_3.11\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\app\\python_3.11\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\app\\python_3.11\\Lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'D:\\app\\python_3.11\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\app\\python_3.11\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\app\\python_3.11\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\app\\python_3.11\\Lib\\threading.py', 'PYMODULE'),
  ('tkinter', 'D:\\app\\python_3.11\\Lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.constants',
   'D:\\app\\python_3.11\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.ttk', 'D:\\app\\python_3.11\\Lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('token', 'D:\\app\\python_3.11\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\app\\python_3.11\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\app\\python_3.11\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'D:\\app\\python_3.11\\Lib\\typing.py', 'PYMODULE'),
  ('urllib', 'D:\\app\\python_3.11\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\app\\python_3.11\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('zipfile', 'D:\\app\\python_3.11\\Lib\\zipfile.py', 'PYMODULE')])
