<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>熊洞科技-视觉大模型</title>
    <style>
        body {
            font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #1890ff;
            margin-bottom: 20px;
        }

        .header-description {
            text-align: right;
            margin-bottom: 20px;
            color: #666;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #eee;
        }

        .tab {
            padding: 10px 20px;
            cursor: pointer;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
        }

        .tab.active {
            background: #1890ff;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }

        input[type="text"], input[type="number"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
        }

        textarea {
            min-height: 100px;
            resize: vertical;
        }

        .file-input {
            margin-bottom: 15px;
        }

        .image-preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 10px;
            margin-bottom: 20px;
        }

        .image-preview {
            max-width: 450px;
            max-height: 450px;
            width: 100%;
            border: 1px dashed #ddd;
            padding: 5px;
            position: relative;
        }

        .image-preview img {
            max-width: 100%;
            max-height: 440px;
            display: block;
            margin: 0 auto;
            object-fit: contain;
        }

        .clear-image {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 50%;
            width: 25px;
            height: 25px;
            text-align: center;
            line-height: 25px;
            cursor: pointer;
        }

        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s;
        }

        button:hover {
            background-color: #40a9ff;
        }

        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }

        .button-container {
            text-align: center;
            margin-top: 20px;
        }

        .result {
            margin-top: 30px;
            padding: 20px;
            background-color: #f0f8ff;
            border-radius: 8px;
            border-left: 5px solid #1890ff;
            white-space: pre-wrap;
            display: none;
        }

        .result-header {
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
        }

        .loader {
            text-align: center;
            margin: 20px 0;
            display: none;
        }

        .loader-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #1890ff;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        .error {
            background-color: #fff1f0;
            border-left: 5px solid #f5222d;
            padding: 10px;
            margin-top: 10px;
            border-radius: 4px;
            display: none;
        }

        .api-settings {
            margin-bottom: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 5px;
        }

        .expand-collapse {
            cursor: pointer;
            color: #1890ff;
            text-decoration: underline;
        }

        .toggle-content {
            margin-top: 10px;
            display: none;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>熊洞科技-视觉大模型</h1>
    <div class="header-description">基于Qwen2.5-VL-3B模型</div>

    <div class="api-settings">
        <div class="expand-collapse" onclick="toggleSettings()">API设置 ▼</div>
        <div class="toggle-content" id="settings-content">
            <div class="form-group">
                <label for="api-url">API基础URL:</label>
                <input type="text" id="api-url" value="http://localhost:8025" placeholder="例如: http://localhost:8025">
            </div>
        </div>
    </div>

    <div class="tabs">
        <div class="tab active" onclick="showTab('upload-tab')">图片上传</div>
        <div class="tab" onclick="showTab('url-tab')">图片URL</div>
    </div>

    <div id="upload-tab" class="tab-content active">
        <div class="form-group">
            <label>图片上传:</label>
            <div style="display: flex; gap: 20px; flex-wrap: wrap;">
                <div style="flex: 1; min-width: 450px;">
                    <label for="file1">上传图片 (必需):</label>
                    <input type="file" id="file1" class="file-input" accept="image/*"
                           onchange="previewImage(event, 'preview1')">
                    <div id="preview-container1" class="image-preview-container">
                        <div id="preview1" class="image-preview" style="display:none">
                            <img id="preview-img1" src="#" alt="预览图片1">
                            <div class="clear-image" onclick="clearImage('file1', 'preview1')">×</div>
                        </div>
                    </div>
                </div>
                
                <div style="flex: 1; min-width: 450px;">
                    <label for="file2">上传第二张图片:</label>
                    <input type="file" id="file2" class="file-input" accept="image/*"
                           onchange="previewImage(event, 'preview2')">
                    <div id="preview-container2" class="image-preview-container">
                        <div id="preview2" class="image-preview" style="display:none">
                            <img id="preview-img2" src="#" alt="预览图片2">
                            <div class="clear-image" onclick="clearImage('file2', 'preview2')">×</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label for="prompt-upload">提示文本:</label>
            <textarea id="prompt-upload" rows="6">
请严格基于图像内容，对比两张图片中的电器是否为**完全相同的一台设备**（即同一台实物）。
    请重点关注以下细节：
        - 是否存在物理差异（如污渍、贴纸、二维码、划痕、破损、电线、螺丝、灰尘等）
        - 比较颜色、外壳轮廓、模具边框是否一致
        - 判断品牌Logo、贴纸、铭牌是否一致，位置是否一致
        - 对比分控面板、按钮布局、显示区域是否一致
如果确认是同一台设备，请回复："是"；
如果发现任何差异或无法确认，请回复："否"，并在下一行说明具体差异

            </textarea>

            <div class="form-group">
                <label for="max-tokens-upload">最大生成Token数:</label>
                <input type="number" id="max-tokens-upload" value="256" min="1" max="4096">
            </div>

            <div class="button-container">
                <button id="submit-upload" onclick="uploadImages()">提交请求</button>
            </div>
        </div>

        <div id="url-tab" class="tab-content">
            <div class="form-group">
                <label>图片URL:</label>
                <div style="display: flex; gap: 20px; flex-wrap: wrap;">
                    <div style="flex: 1; min-width: 450px;">
                        <label for="image-url">图片URL (必需):</label>
                        <input type="text" id="image-url" placeholder="请输入图片URL">
                    </div>
                    
                    <div style="flex: 1; min-width: 450px;">
                        <label for="image-url2">第二张图片URL (可选):</label>
                        <input type="text" id="image-url2" placeholder="请输入第二张图片URL（可选）">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="prompt-url">提示文本:</label>
                <textarea id="prompt-url" placeholder="请输入提示文本，例如：请描述这张图片中的内容"></textarea>
            </div>

            <div class="form-group">
                <label for="max-tokens-url">最大生成Token数:</label>
                <input type="number" id="max-tokens-url" value="256" min="1" max="4096">
            </div>

            <div class="button-container">
                <button id="submit-url" onclick="generateFromURL()">提交请求</button>
            </div>
        </div>

        <div class="loader">
            <div class="loader-spinner"></div>
            <p>处理中，请稍候...</p>
        </div>

        <div class="error" id="error-message"></div>

        <div class="result" id="result-container">
            <div class="result-header">
                <span>模型回答:</span>
                <span id="processing-time"></span>
            </div>
            <div id="result-content"></div>
        </div>
    </div>

    <script>
        // 显示选中的标签页
        function showTab(tabId) {
            document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));

            document.getElementById(tabId).classList.add('active');

            if (tabId === 'upload-tab') {
                document.querySelector('.tab:nth-child(1)').classList.add('active');
            } else {
                document.querySelector('.tab:nth-child(2)').classList.add('active');
            }
        }

        // 切换API设置区域的显示/隐藏
        function toggleSettings() {
            const content = document.getElementById('settings-content');
            const toggle = document.querySelector('.expand-collapse');

            if (content.style.display === 'block') {
                content.style.display = 'none';
                toggle.innerHTML = 'API设置 ▼';
            } else {
                content.style.display = 'block';
                toggle.innerHTML = 'API设置 ▲';
            }
        }

        // 图片预览功能
        function previewImage(event, previewId) {
            const preview = document.getElementById(previewId);
            const previewImg = document.getElementById(`preview-img${previewId.slice(-1)}`);
            const file = event.target.files[0];

            if (file) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    previewImg.src = e.target.result;
                    preview.style.display = 'block';
                }
                reader.readAsDataURL(file);
            } else {
                preview.style.display = 'none';
            }
        }

        // 清除已选图片
        function clearImage(fileId, previewId) {
            document.getElementById(fileId).value = '';
            document.getElementById(previewId).style.display = 'none';
        }

        // 显示错误信息
        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            document.querySelector('.loader').style.display = 'none';
        }

        // 显示结果
        function showResult(data) {
            document.querySelector('.loader').style.display = 'none';

            const resultContainer = document.getElementById('result-container');
            const resultContent = document.getElementById('result-content');
            const processingTime = document.getElementById('processing-time');

            if (data.error) {
                showError(`错误: ${data.error}`);
                resultContainer.style.display = 'none';
                return;
            }

            resultContent.textContent = data.result;
            processingTime.textContent = `处理时间: ${data.processing_time}`;
            resultContainer.style.display = 'block';
            document.getElementById('error-message').style.display = 'none';

            // 滚动到结果区域
            resultContainer.scrollIntoView({behavior: 'smooth'});
        }

        // 获取API基础URL
        function getApiBaseUrl() {
            return document.getElementById('api-url').value.trim() || 'http://localhost:8025';
        }

        // 使用图片URL生成回答
        async function generateFromURL() {
            const imageUrl = document.getElementById('image-url').value.trim();
            const imageUrl2 = document.getElementById('image-url2').value.trim();
            const prompt = document.getElementById('prompt-url').value.trim();
            const maxTokens = parseInt(document.getElementById('max-tokens-url').value);

            if (!imageUrl) {
                showError('请输入至少一个图片URL');
                return;
            }

            if (!prompt) {
                showError('请输入提示文本');
                return;
            }

            // 准备请求数据
            const requestData = {
                prompt: prompt,
                max_tokens: maxTokens,
                image_url: imageUrl
            };

            // 如果提供了第二个URL，添加到请求中
            if (imageUrl2) {
                requestData.image_url2 = imageUrl2;
            }

            document.getElementById('error-message').style.display = 'none';
            document.querySelector('.loader').style.display = 'block';
            document.getElementById('result-container').style.display = 'none';
            document.getElementById('submit-url').disabled = true;

            try {
                const apiBaseUrl = getApiBaseUrl();
                const response = await fetch(`${apiBaseUrl}/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();
                showResult(data);
            } catch (error) {
                showError(`请求失败: ${error.message}`);
            } finally {
                document.getElementById('submit-url').disabled = false;
            }
        }

        // 上传图片并生成回答
        async function uploadImages() {
            const file1 = document.getElementById('file1').files[0];
            const file2 = document.getElementById('file2').files[0];
            const prompt = document.getElementById('prompt-upload').value.trim();
            const maxTokens = parseInt(document.getElementById('max-tokens-upload').value);

            if (!file1) {
                showError('请至少上传一张图片');
                return;
            }

            if (!prompt) {
                showError('请输入提示文本');
                return;
            }

            // 准备表单数据
            const formData = new FormData();
            formData.append('file1', file1);
            if (file2) {
                formData.append('file2', file2);
            }
            formData.append('prompt', prompt);
            formData.append('max_tokens', maxTokens);

            document.getElementById('error-message').style.display = 'none';
            document.querySelector('.loader').style.display = 'block';
            document.getElementById('result-container').style.display = 'none';
            document.getElementById('submit-upload').disabled = true;

            try {
                const apiBaseUrl = getApiBaseUrl();
                const response = await fetch(`${apiBaseUrl}/upload-image`, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                showResult(data);
            } catch (error) {
                showError(`请求失败: ${error.message}`);
            } finally {
                document.getElementById('submit-upload').disabled = false;
            }
        }
    </script>
</body>
</html> 