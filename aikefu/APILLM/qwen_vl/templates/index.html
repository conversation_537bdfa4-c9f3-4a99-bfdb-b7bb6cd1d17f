<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qwen2.5-VL 交互界面</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
            font-size: 16px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .image-preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 10px 0;
        }
        .image-preview {
            width: 150px;
            height: 150px;
            border: 1px solid #ddd;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        .image-preview img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        .remove-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background-color: rgba(255, 0, 0, 0.7);
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        button[type="submit"] {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            width: 100%;
            transition: background-color 0.3s;
        }
        button[type="submit"]:hover {
            background-color: #45a049;
        }
        .response-container {
            margin-top: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }
        .loading-spinner {
            border: 6px solid #f3f3f3;
            border-top: 6px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Qwen2.5-VL 模型交互</h1>
        <form id="chat-form" enctype="multipart/form-data">
            <div class="form-group">
                <label for="image-upload">上传图片（最多3张）：</label>
                <input type="file" id="image-upload" accept="image/*" multiple onchange="previewImages()">
                <div class="image-preview-container" id="image-preview-container"></div>
            </div>
            <div class="form-group">
                <label for="prompt">输入文本：</label>
                <textarea id="prompt" name="prompt" placeholder="请输入文本..." required></textarea>
            </div>
            <button type="submit">提交</button>
        </form>
        <div class="loading" id="loading">
            <div class="loading-spinner"></div>
            <p>正在处理，请稍候...</p>
        </div>
        <div class="response-container" id="response-container" style="display: none;">
            <h3>模型回复：</h3>
            <div id="response-text"></div>
        </div>
    </div>

    <script>
        // 已选择的图片文件数组
        let selectedFiles = [];

        // 预览上传的图片
        function previewImages() {
            const input = document.getElementById('image-upload');
            const container = document.getElementById('image-preview-container');
            const files = input.files;

            // 检查选择的文件数量是否超过上限
            if (selectedFiles.length + files.length > 3) {
                alert('最多只能上传3张图片');
                input.value = '';
                return;
            }

            // 将新选择的文件添加到已选择文件列表
            for (let i = 0; i < files.length; i++) {
                selectedFiles.push(files[i]);
            }

            // 清除预览容器
            container.innerHTML = '';

            // 为每个已选择的文件创建预览
            selectedFiles.forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.createElement('div');
                    preview.className = 'image-preview';
                    preview.innerHTML = `
                        <img src="${e.target.result}" alt="预览图片">
                        <button type="button" class="remove-btn" onclick="removeImage(${index})">×</button>
                    `;
                    container.appendChild(preview);
                };
                reader.readAsDataURL(file);
            });

            // 清空文件输入以便于再次选择相同的文件
            input.value = '';
        }

        // 移除已选择的图片
        function removeImage(index) {
            selectedFiles.splice(index, 1);
            // 重新显示预览
            document.getElementById('image-preview-container').innerHTML = '';
            selectedFiles.forEach((file, idx) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.createElement('div');
                    preview.className = 'image-preview';
                    preview.innerHTML = `
                        <img src="${e.target.result}" alt="预览图片">
                        <button type="button" class="remove-btn" onclick="removeImage(${idx})">×</button>
                    `;
                    document.getElementById('image-preview-container').appendChild(preview);
                };
                reader.readAsDataURL(file);
            });
        }

        // 处理表单提交
        document.getElementById('chat-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const prompt = document.getElementById('prompt').value.trim();
            if (!prompt) {
                alert('请输入文本内容');
                return;
            }

            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('response-container').style.display = 'none';

            // 创建FormData对象
            const formData = new FormData();
            formData.append('prompt', prompt);
            
            // 添加已选择的图片
            selectedFiles.forEach(file => {
                formData.append('images', file);
            });

            try {
                // 发送请求
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP 错误! 状态码: ${response.status}`);
                }

                const data = await response.json();
                
                // 显示响应
                document.getElementById('response-text').textContent = data.response;
                document.getElementById('response-container').style.display = 'block';
            } catch (error) {
                console.error('发生错误:', error);
                document.getElementById('response-text').textContent = `错误: ${error.message}`;
                document.getElementById('response-container').style.display = 'block';
            } finally {
                // 隐藏加载状态
                document.getElementById('loading').style.display = 'none';
            }
        });
    </script>
</body>
</html> 