import os
import io
import base64
import requests
import sys
import time
import json
from typing import List, Optional
from fastapi import FastAPI, File, Form, UploadFile, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
from PIL import Image

# 创建目录（如果不存在）
os.makedirs("templates", exist_ok=True)

# 创建FastAPI应用
app = FastAPI(title="Qwen2.5-VL API", description="使用Ollama与Qwen2.5-VL模型交互的API")

# 添加CORS中间件，允许跨域请求
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头信息
)

# 设置模板
templates = Jinja2Templates(directory="templates")

# Ollama API的基础URL
OLLAMA_API_BASE_URL = "http://localhost:11434/api"
MODEL_NAME = "qwen2.5vl:3b"  # 使用的模型名称

# 全局变量，表示Ollama服务状态
ollama_status = {
    "running": False,
    "model_loaded": False,
    "available_models": [],
    "last_check_time": None,
    "error_message": None
}

# 检查Ollama服务函数
def check_ollama_service():
    """检查Ollama服务是否在运行，以及需要的模型是否可用"""
    global ollama_status
    
    try:
        response = requests.get(f"{OLLAMA_API_BASE_URL}/tags", timeout=5)
        response.raise_for_status()
        
        models = response.json().get("models", [])
        available_models = [model["name"] for model in models]
        
        ollama_status["running"] = True
        ollama_status["available_models"] = available_models
        ollama_status["model_loaded"] = MODEL_NAME in available_models
        ollama_status["last_check_time"] = time.time()
        ollama_status["error_message"] = None
        
        return True, available_models
    
    except Exception as e:
        ollama_status["running"] = False
        ollama_status["model_loaded"] = False
        ollama_status["last_check_time"] = time.time()
        ollama_status["error_message"] = str(e)
        
        return False, []

# 启动时检查Ollama服务
print("正在检查Ollama服务...")
running, available_models = check_ollama_service()

if not running:
    print(f"警告: 无法连接到Ollama服务 ({ollama_status['error_message']})")
    print("请确保Ollama服务已启动并运行在: http://localhost:11434")
else:
    if MODEL_NAME not in available_models:
        print(f"警告: 模型 {MODEL_NAME} 不在可用模型列表中")
        print(f"可用的模型: {available_models}")
        print(f"请运行 'ollama pull {MODEL_NAME}' 来下载必要的模型")
    else:
        print(f"Ollama服务正常运行，{MODEL_NAME} 模型已加载")

# 响应模型
class ModelResponse(BaseModel):
    response: str

# 中间件，在每个请求前检查Ollama服务状态
@app.middleware("http")
async def check_ollama_service_middleware(request: Request, call_next):
    """每个API请求之前检查Ollama服务状态"""
    # 跳过特定路径的检查
    skip_paths = ['/', '/status', '/health', '/check-ollama']
    if request.url.path in skip_paths:
        return await call_next(request)
    
    # 定期检查Ollama服务状态（每60秒一次）
    if ollama_status["last_check_time"] is None or time.time() - ollama_status["last_check_time"] > 60:
        check_ollama_service()
    
    # 如果Ollama服务未运行，返回错误
    if not ollama_status["running"]:
        return JSONResponse(
            status_code=503,
            content={"detail": f"Ollama服务未运行或无法访问: {ollama_status['error_message']}"}
        )
    
    # 如果需要的模型未加载，返回错误
    if not ollama_status["model_loaded"]:
        return JSONResponse(
            status_code=503,
            content={
                "detail": f"模型 {MODEL_NAME} 未加载。可用模型: {ollama_status['available_models']}",
                "available_models": ollama_status["available_models"]
            }
        )
    
    # 继续处理请求
    return await call_next(request)

@app.get("/", response_class=HTMLResponse)
async def get_html_page(request: Request):
    """提供HTML前端页面"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/api/chat", response_model=ModelResponse)
async def chat_with_model(
    prompt: str = Form(...),
    images: List[UploadFile] = File(None),
):
    """
    与Qwen2.5-VL模型交互的接口
    
    - **prompt**: 用户输入的文本内容
    - **images**: 用户上传的图片，最多3张
    """
    try:
        # 检查图片数量是否超过3张
        if images and len(images) > 3:
            raise HTTPException(status_code=400, detail="最多只能上传3张图片")
        
        # 准备消息
        if images:
            # 如果有图片，使用消息格式需要包含图片数组
            message_content = prompt
            image_list = []
            
            for image in images:
                # 读取图片内容
                image_content = await image.read()
                
                try:
                    # 使用PIL打开和处理图片，确保它是有效的图片
                    img = Image.open(io.BytesIO(image_content))
                    
                    # 如果图片有透明通道(RGBA)，转换为RGB模式
                    if img.mode == 'RGBA':
                        # 创建白色背景
                        background = Image.new('RGB', img.size, (255, 255, 255))
                        # 将原图片粘贴到白色背景上，考虑透明度
                        background.paste(img, mask=img.split()[3])  # 使用透明通道作为蒙版
                        img = background
                    elif img.mode != 'RGB':
                        # 处理其他非RGB模式（如灰度图等）
                        img = img.convert('RGB')
                    
                    # 将图片转换为JPEG格式
                    img_byte_arr = io.BytesIO()
                    img.save(img_byte_arr, format='JPEG')
                    img_byte_arr = img_byte_arr.getvalue()
                    
                    # 将图片转换为Base64编码 - 直接使用原始Base64字符串，不添加MIME前缀
                    image_base64 = base64.b64encode(img_byte_arr).decode("utf-8")
                    
                    # 添加图片到图片列表，不再添加MIME类型前缀
                    image_list.append(image_base64)
                    
                except Exception as e:
                    print(f"图片处理错误: {str(e)}")
                    raise HTTPException(status_code=400, detail=f"图片处理错误: {str(e)}")
            
            # 为多模态模型构建消息，包含图片
            messages = [
                {
                    "role": "user", 
                    "content": message_content,
                    "images": image_list
                }
            ]
        else:
            # 如果没有图片，使用简单的文本消息
            messages = [
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        
        print(f"发送到Ollama的请求: {json.dumps(messages, ensure_ascii=False)}")
        
        # 调用Ollama API
        try:
            request_data = {
                "model": MODEL_NAME,
                "messages": messages,
                "stream": False  # 不使用流式响应
            }
            print(f"完整API请求参数: {json.dumps(request_data, ensure_ascii=False)}")
            
            response = requests.post(
                f"{OLLAMA_API_BASE_URL}/chat",
                json=request_data,
                timeout=60  # 增加超时时间，因为图像处理可能需要更长时间
            )
            
            # 打印请求与响应信息
            print(f"Ollama API请求URL: {OLLAMA_API_BASE_URL}/chat")
            print(f"Ollama API响应状态码: {response.status_code}")
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            
            print(f"Ollama API响应: {json.dumps(result, ensure_ascii=False)}")
            
            # 返回模型的回答
            if "message" in result and "content" in result["message"]:
                return {"response": result["message"]["content"]}
            else:
                return {"response": str(result)}
            
        except requests.RequestException as e:
            print(f"Ollama API请求错误: {str(e)}")
            print(f"错误详情: {str(e.__class__.__name__)}")
            
            if hasattr(e, 'response') and e.response is not None:
                print(f"错误状态码: {e.response.status_code}")
                print(f"错误响应: {e.response.text}")
            
            # 尝试使用/api/generate端点作为备选
            try:
                print("尝试使用/api/generate端点作为备选方案")
                # 构建适合/api/generate端点的请求
                generate_prompt = prompt
                
                # 如果有图片，添加到提示中
                if images and len(images) > 0:
                    generate_prompt = f"以下是图片内容分析请求。请回答: {prompt}"
                
                # 构建适用于generate端点的请求
                generate_data = {
                    "model": MODEL_NAME,
                    "prompt": generate_prompt,
                    "stream": False
                }
                
                # 如果有图片，添加images参数 - 使用相同的图片处理方式
                if images and len(images) > 0:
                    # 这里使用同样的图片列表，确保格式一致
                    generate_data["images"] = image_list
                
                print(f"Generate API请求参数: {json.dumps(generate_data, ensure_ascii=False)}")
                
                generate_response = requests.post(
                    f"{OLLAMA_API_BASE_URL}/generate",
                    json=generate_data,
                    timeout=60
                )
                
                print(f"Generate API响应状态码: {generate_response.status_code}")
                
                generate_response.raise_for_status()
                generate_result = generate_response.json()
                
                print(f"Generate API响应: {json.dumps(generate_result, ensure_ascii=False)}")
                
                if "response" in generate_result:
                    return {"response": generate_result["response"]}
                else:
                    raise HTTPException(status_code=500, detail="模型未返回预期的响应格式")
                
            except Exception as gen_error:
                print(f"备选请求也失败: {str(gen_error)}")
                if hasattr(gen_error, 'response') and gen_error.response is not None:
                    print(f"备选错误状态码: {gen_error.response.status_code}")
                    print(f"备选错误响应: {gen_error.response.text}")
                raise HTTPException(status_code=500, detail=f"API请求错误: {str(e)}，备选请求也失败: {str(gen_error)}")
    
    except HTTPException:
        # 重新抛出HTTPException异常
        raise
    except Exception as e:
        print(f"服务器错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")

@app.get("/status")
async def status():
    """服务器状态检查"""
    # 更新Ollama状态
    check_ollama_service()
    return {
        "status": "running",
        "model": MODEL_NAME,
        "ollama_service": {
            "running": ollama_status["running"],
            "model_loaded": ollama_status["model_loaded"],
            "available_models": ollama_status["available_models"],
            "last_check_time": ollama_status["last_check_time"],
            "error": ollama_status["error_message"]
        }
    }

@app.get("/health")
def health_check():
    return {"status": "ok", "model": MODEL_NAME}

# 添加一个端点检查Ollama服务是否在运行
@app.get("/check-ollama")
async def check_ollama():
    """检查Ollama服务是否在运行"""
    running, available_models = check_ollama_service()
    
    if not running:
        return {"status": "error", "message": f"无法连接到Ollama服务: {ollama_status['error_message']}"}
    
    if not ollama_status["model_loaded"]:
        return {
            "status": "warning", 
            "message": f"Ollama服务正在运行，但{MODEL_NAME}模型未加载。可用模型: {available_models}"
        }
        
    return {"status": "ok", "message": f"Ollama服务正在运行，{MODEL_NAME}模型已加载"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8061, reload=False)