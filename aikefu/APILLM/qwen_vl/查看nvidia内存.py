import os
import ctypes
import subprocess
from ctypes import *

# 1️⃣ 手动加载 nvml.dll
nvml_path = r"C:\Windows\System32\nvml.dll"
nvml_lib = ctypes.CDLL(nvml_path)

# 2️⃣ 导入 pynvml 并替换其内部库引用
import pynvml

pynvml.nvmlLib = nvml_lib


def get_process_name_by_pid(pid):
    """通过 PID 获取进程名"""
    try:
        result = subprocess.run(['tasklist', '/FI', f'PID eq {pid}', '/FO', 'CSV'],
                                capture_output=True, text=True, encoding='gbk')
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:  # 跳过标题行
                # CSV 格式: "进程名","PID","会话名","会话#","内存使用"
                process_info = lines[1].replace('"', '').split(',')
                if len(process_info) > 0:
                    return process_info[0]
    except:
        pass
    return "Unknown"


def get_process_path_by_pid(pid):
    """通过 PID 获取进程的可执行文件路径"""
    try:
        # 方法1: 使用 wmic 命令获取进程路径
        result = subprocess.run([
            'wmic', 'process', 'where', f'ProcessId={pid}',
            'get', 'ExecutablePath', '/format:list'
        ], capture_output=True, text=True, encoding='gbk')

        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if line.startswith('ExecutablePath=') and '=' in line:
                    path = line.split('=', 1)[1].strip()
                    if path:
                        return path

        # 方法2: 如果 wmic 失败，尝试使用 PowerShell
        ps_result = subprocess.run([
            'powershell', '-Command',
            f'(Get-Process -Id {pid} -ErrorAction SilentlyContinue).Path'
        ], capture_output=True, text=True, encoding='utf-8')

        if ps_result.returncode == 0 and ps_result.stdout.strip():
            return ps_result.stdout.strip()

    except Exception as e:
        pass

    return "N/A"


def get_process_detailed_info(pid):
    """获取进程的完整详细信息"""
    process_info = {
        'name': 'Unknown',
        'path': 'N/A',
        'creation_date': 'N/A',
        'memory_mb': 'N/A',
        'command_line': 'N/A'
    }

    try:
        # 获取进程名
        process_info['name'] = get_process_name_by_pid(pid)

        # 获取进程路径
        process_info['path'] = get_process_path_by_pid(pid)

        # 使用 wmic 获取更多详细信息
        result = subprocess.run([
            'wmic', 'process', 'where', f'ProcessId={pid}',
            'get', 'CommandLine,CreationDate,WorkingSetSize', '/format:list'
        ], capture_output=True, text=True, encoding='gbk')

        if result.returncode == 0:
            info_dict = {}
            for line in result.stdout.split('\n'):
                if '=' in line and line.strip():
                    key, value = line.split('=', 1)
                    info_dict[key.strip()] = value.strip()

            if 'CreationDate' in info_dict and info_dict['CreationDate']:
                process_info['creation_date'] = info_dict['CreationDate']

            if 'WorkingSetSize' in info_dict and info_dict['WorkingSetSize']:
                try:
                    memory_mb = int(info_dict['WorkingSetSize']) / 1024 / 1024
                    process_info['memory_mb'] = f"{memory_mb:.2f}"
                except:
                    pass

            if 'CommandLine' in info_dict and info_dict['CommandLine']:
                process_info['command_line'] = info_dict['CommandLine']

    except Exception as e:
        pass

    return process_info


try:
    pynvml.nvmlInit()
    deviceCount = pynvml.nvmlDeviceGetCount()

    print(f"🎮 发现 {deviceCount} 个 GPU 设备")
    print("=" * 150)

    for i in range(deviceCount):
        handle = pynvml.nvmlDeviceGetHandleByIndex(i)
        gpu_name = pynvml.nvmlDeviceGetName(handle).decode()
        print(f"GPU {i}: {gpu_name}")

        # 获取显存信息
        try:
            mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
            total_mb = mem_info.total / 1024 / 1024
            used_mb = mem_info.used / 1024 / 1024
            free_mb = mem_info.free / 1024 / 1024
            usage_percent = (used_mb / total_mb) * 100

            print(f"  📊 显存状态:")
            print(f"    总容量: {total_mb:.0f} MiB")
            print(f"    已使用: {used_mb:.0f} MiB ({usage_percent:.1f}%)")
            print(f"    可用: {free_mb:.0f} MiB")

            # 显示进度条
            bar_length = 30
            filled_length = int(bar_length * usage_percent / 100)
            bar = '█' * filled_length + '░' * (bar_length - filled_length)
            print(f"    使用率: [{bar}] {usage_percent:.1f}%")

        except Exception as e:
            print(f"  ❌ 无法获取显存信息: {e}")

        # 获取所有进程（计算 + 图形）
        all_processes = []
        unique_pids = set()  # 避免重复显示同一个进程

        # 计算进程
        try:
            procs = pynvml.nvmlDeviceGetComputeRunningProcesses(handle)
            for p in procs:
                if p.pid not in unique_pids:
                    memory_usage = "N/A" if p.usedGpuMemory is None else f"{p.usedGpuMemory / 1024 / 1024:.2f} MiB"
                    all_processes.append((p.pid, "计算", memory_usage))
                    unique_pids.add(p.pid)
        except Exception as e:
            print(f"  ⚠️ 计算进程获取失败: {e}")

        # 图形进程
        try:
            graphics_procs = pynvml.nvmlDeviceGetGraphicsRunningProcesses(handle)
            for p in graphics_procs:
                if p.pid not in unique_pids:
                    memory_usage = "N/A" if p.usedGpuMemory is None else f"{p.usedGpuMemory / 1024 / 1024:.2f} MiB"
                    all_processes.append((p.pid, "图形", memory_usage))
                    unique_pids.add(p.pid)
                else:
                    # 如果进程既是计算又是图形，更新类型为"计算+图形"
                    for idx, (existing_pid, proc_type, mem_usage) in enumerate(all_processes):
                        if existing_pid == p.pid:
                            all_processes[idx] = (existing_pid, "计算+图形", mem_usage)
                            break
        except Exception as e:
            print(f"  ⚠️ 图形进程获取失败: {e}")

        # 显示所有进程的详细信息
        if all_processes:
            print(f"\n  🔄 正在使用 GPU 的进程 ({len(all_processes)} 个):")

            # 表格头部
            print("  " + "-" * 140)
            print(
                f"  {'进程名':<25} | {'类型':<8} | {'GPU显存':<12} | {'系统内存':<12} | {'启动时间':<20} | {'启动路径':<50}")
            print("  " + "-" * 140)

            # 收集所有进程的详细信息
            process_details = []

            for pid, proc_type, gpu_memory in all_processes:
                # 获取进程详细信息
                detail_info = get_process_detailed_info(pid)

                # 截断过长的路径用于表格显示
                path_short = detail_info['path']
                if len(path_short) > 47:
                    path_short = "..." + path_short[-44:]

                # 格式化启动时间（只显示日期时间部分）
                creation_time = detail_info['creation_date']
                if len(creation_time) > 17:
                    creation_time = creation_time[:17]

                # 表格行
                print(
                    f"  {detail_info['name']:<25} | {proc_type:<8} | {gpu_memory:<12} | {detail_info['memory_mb']} MB{'':<6} | {creation_time:<20} | {path_short:<50}")

                # 保存详细信息用于后续显示
                process_details.append((pid, detail_info, proc_type, gpu_memory))

            print("  " + "-" * 140)

            # 显示详细信息
            print(f"\n  📋 进程详细信息:")
            print("  " + "=" * 120)

            for pid, detail_info, proc_type, gpu_memory in process_details:
                print(f"  PID: {pid}")
                print(f"     进程名: {detail_info['name']}")
                print(f"     启动路径: {detail_info['path']}")
                print(f"     类型: {proc_type}")
                print(f"     GPU显存: {gpu_memory}")
                print(f"     系统内存: {detail_info['memory_mb']} MB")
                print(f"     启动时间: {detail_info['creation_date']}")

                # 显示命令行（如果太长则截断）
                if detail_info['command_line'] != 'N/A':
                    if len(detail_info['command_line']) > 100:
                        cmd_short = detail_info['command_line'][:97] + "..."
                        print(f"     命令行: {cmd_short}")
                    else:
                        print(f"     命令行: {detail_info['command_line']}")

                print("  " + "-" * 120)
        else:
            print("  ✅ 没有检测到使用 GPU 的进程")

        print("=" * 150)

    pynvml.nvmlShutdown()

except Exception as e:
    print(f"❌ 错误: {e}")