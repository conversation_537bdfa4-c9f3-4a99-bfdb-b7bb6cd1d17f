# Qwen2.5-VL 图文交互API

这是一个基于FastAPI的Python应用程序，实现了与Ollama部署的Qwen2.5-VL模型的交互。支持上传最多3张图片并提交文本，然后获取模型的回复。

## 功能特点

- 基于FastAPI开发，高性能且易于扩展
- 支持上传最多3张图片进行图文理解
- 简洁直观的用户界面
- 与本地部署的Ollama服务集成
- 内置错误处理和状态检查

## 安装要求

- Python 3.7+
- Ollama（已安装并启动）
- qwen2.5vl:3b 模型（通过Ollama加载）

## 安装步骤

1. 克隆此仓库或下载源代码：

```bash
git clone <仓库URL>
cd <项目目录>
```

2. 安装所需的Python包：

```bash
pip install -r requirements.txt
```

3. 确保已安装Ollama并加载qwen2.5vl:3b模型：

```bash
# 安装Ollama（如果尚未安装）
# 请访问 https://ollama.com/ 获取适合您系统的安装指南

# 启动Ollama服务
ollama serve

# 拉取并加载qwen2.5vl:3b模型（在另一个终端中执行）
ollama pull qwen2.5vl:3b
```

## 使用方法

1. 启动FastAPI应用：

```bash
uvicorn main:app --reload
```

2. 在浏览器中访问应用：

```
http://127.0.0.1:8000/
```

3. 使用Web界面：
   - 上传最多3张图片（可选）
   - 输入问题或描述文本
   - 点击"提交"按钮
   - 等待模型处理并查看结果

## API端点

- `GET /` - 返回HTML前端界面
- `POST /api/chat` - 接收图片和文本输入，调用模型并返回结果
- `GET /status` - 检查服务器状态
- `GET /health` - 健康检查端点
- `GET /check-ollama` - 检查Ollama服务的状态和可用模型

## 注意事项

- 确保Ollama服务在调用API之前已经启动
- qwen2.5vl:3b模型必须已通过Ollama加载
- 图像处理可能需要一些时间，特别是对于复杂或高分辨率的图像
- 默认超时时间为60秒，可以根据需要在代码中调整

## 故障排除

如果您遇到问题：

1. 确保Ollama服务正在运行：
   - 检查`http://127.0.0.1:11434/api/tags`是否可以访问
   
2. 检查qwen2.5vl:3b模型是否已加载：
   - 使用`ollama list`命令查看已加载的模型
   - 或访问`http://127.0.0.1:8000/check-ollama`检查状态

3. 查看日志信息：
   - 应用程序会在控制台输出详细的日志
   - 检查报错信息以确定问题所在

## 许可证

[在此处指定许可证]

## 联系方式

[在此处提供联系信息] 