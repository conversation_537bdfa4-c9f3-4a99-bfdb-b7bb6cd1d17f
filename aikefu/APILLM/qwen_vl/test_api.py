#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json
import os
import sys
import argparse
from pathlib import Path

def test_health_and_status(base_url):
    """测试健康检查和状态端点"""
    print("=== 测试健康检查和状态端点 ===")
    
    # 测试健康检查端点
    try:
        response = requests.get(f"{base_url}/health")
        print(f"健康检查响应: {response.status_code}")
        if response.status_code == 200:
            print(json.dumps(response.json(), ensure_ascii=False, indent=2))
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"健康检查请求失败: {str(e)}")
    
    # 测试状态端点
    try:
        response = requests.get(f"{base_url}/status")
        print(f"状态检查响应: {response.status_code}")
        if response.status_code == 200:
            print(json.dumps(response.json(), ensure_ascii=False, indent=2))
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"状态检查请求失败: {str(e)}")
    
    # 测试Ollama检查端点
    try:
        response = requests.get(f"{base_url}/check-ollama")
        print(f"Ollama检查响应: {response.status_code}")
        if response.status_code == 200:
            print(json.dumps(response.json(), ensure_ascii=False, indent=2))
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"Ollama检查请求失败: {str(e)}")

def test_chat_api_text_only(base_url, prompt):
    """测试仅文本的聊天API"""
    print("\n=== 测试仅文本的聊天API ===")
    
    try:
        response = requests.post(
            f"{base_url}/api/chat",
            data={"prompt": prompt}
        )
        
        print(f"API响应: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("模型回复:")
            print(result.get("response", "无回复"))
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"API请求失败: {str(e)}")

def test_chat_api_with_image(base_url, prompt, image_path):
    """测试带图片的聊天API"""
    print(f"\n=== 测试带图片的聊天API ===")
    print(f"图片路径: {image_path}")
    
    try:
        # 检查图片文件是否存在
        if not os.path.isfile(image_path):
            print(f"错误: 图片文件 '{image_path}' 不存在")
            return
        
        # 准备表单数据
        files = {"images": (os.path.basename(image_path), open(image_path, "rb"))}
        data = {"prompt": prompt}
        
        # 发送请求
        response = requests.post(
            f"{base_url}/api/chat",
            files=files,
            data=data
        )
        
        print(f"API响应: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("模型回复:")
            print(result.get("response", "无回复"))
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"API请求失败: {str(e)}")
    finally:
        # 确保文件被关闭
        if 'files' in locals() and isinstance(files, dict) and 'images' in files:
            files['images'][1].close()

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="测试Qwen2.5-VL API")
    parser.add_argument("--url", default="http://127.0.0.1:8061", help="API的基础URL")
    parser.add_argument("--prompt", default="你好，请介绍一下自己", help="要发送的提示文本")
    parser.add_argument("--image", help="要发送的图片文件路径")
    parser.add_argument("--test", choices=["health", "text", "image", "all"], default="all", 
                        help="要运行的测试类型 (health: 健康检查, text: 仅文本, image: 带图片, all: 所有测试)")
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    base_url = args.url
    prompt = args.prompt
    image_path = args.image
    test_type = args.test
    
    print(f"API基础URL: {base_url}")
    print(f"提示文本: {prompt}")
    if image_path:
        print(f"图片路径: {image_path}")
    print(f"测试类型: {test_type}")
    print("-" * 50)
    
    # 根据测试类型运行测试
    if test_type in ["health", "all"]:
        test_health_and_status(base_url)
    
    if test_type in ["text", "all"]:
        test_chat_api_text_only(base_url, prompt)
    
    if test_type in ["image", "all"] and image_path:
        test_chat_api_with_image(base_url, prompt, image_path)
    elif test_type in ["image", "all"] and not image_path:
        print("\n警告: 没有提供图片路径，跳过图片测试")

if __name__ == "__main__":
    main() 