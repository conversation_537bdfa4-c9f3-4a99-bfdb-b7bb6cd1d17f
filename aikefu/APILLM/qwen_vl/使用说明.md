# Qwen2.5-VL 图文交互应用使用说明

## 一、简介

这是一个基于FastAPI的Python应用，用于与ollama部署的qwen2.5vl:3b模型进行交互。您可以上传最多3张图片并输入文本，应用会将这些内容发送给模型，并返回模型的回复。

## 二、安装步骤

### 1. 安装依赖

首先，确保您已经安装了Python 3.7或更高版本，然后安装所需的依赖：

```bash
pip install -r requirements.txt
```

### 2. 安装和配置Ollama

1. 从[Ollama官网](https://ollama.com/)下载并安装Ollama
2. 启动Ollama服务：
   ```bash
   ollama serve
   ```
3. 下载qwen2.5vl:3b模型：
   ```bash
   ollama pull qwen2.5vl:3b
   ```

## 三、启动应用

在安装好所有依赖并确保Ollama服务正在运行后，执行以下命令启动应用：

```bash
uvicorn main:app --reload
```

应用将在`http://127.0.0.1:8000`启动，您可以在浏览器中访问此地址。

## 四、使用方法

### 1. 通过Web界面使用

1. 在浏览器中访问`http://127.0.0.1:8000`
2. 点击"选择图片"按钮，可以上传最多3张图片（可选）
3. 在文本框中输入您的问题或描述
4. 点击"提交"按钮
5. 等待模型处理并查看结果

### 2. 通过API使用

#### 发送仅文本请求：

```bash
curl -X POST "http://127.0.0.1:8000/api/chat" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "prompt=请介绍一下自己"
```

#### 发送带图片的请求：

```bash
curl -X POST "http://127.0.0.1:8000/api/chat" \
  -F "images=@/路径/到/您的/图片.jpg" \
  -F "prompt=这张图片是什么内容？"
```

## 五、测试工具使用

应用附带了一个测试脚本`test_api.py`，可以帮助您测试API的基本功能：

### 测试健康检查和状态端点：

```bash
python test_api.py --test health
```

### 测试仅文本API：

```bash
python test_api.py --test text --prompt "你好，请问你能做什么？"
```

### 测试带图片的API：

```bash
python test_api.py --test image --prompt "描述这张图片" --image "/路径/到/图片.jpg"
```

### 测试所有功能：

```bash
python test_api.py --image "/路径/到/图片.jpg"
```

## 六、错误排查

1. **无法连接到Ollama服务**：
   - 确保Ollama服务已启动（运行`ollama serve`）
   - 验证Ollama服务在`http://localhost:11434`可访问

2. **模型未找到**：
   - 确保已通过`ollama pull qwen2.5vl:3b`下载了模型
   - 使用`ollama list`检查可用模型

3. **图片处理错误**：
   - 确保上传的是有效图片文件（支持JPG、PNG等常见格式）
   - 图片不应过大，建议小于10MB

4. **响应超时**：
   - 模型处理可能需要时间，特别是对于复杂的图像
   - 可在main.py中增加超时设置（默认为60秒）

## 七、更多信息

可以访问以下端点获取更多系统信息：

- `/health` - 健康检查
- `/status` - 服务器状态和Ollama信息
- `/check-ollama` - Ollama服务和模型可用性检查 