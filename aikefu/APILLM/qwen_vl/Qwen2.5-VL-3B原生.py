from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
import transformers
from qwen_vl_utils import process_vision_info
import torch
import sys
import platform
import os
import time

# 打印依赖和版本信息
print("=" * 50)
print("依赖和版本信息:")
print(f"Python 版本: {sys.version}")
print(f"操作系统: {platform.system()} {platform.release()}")
print(f"PyTorch 版本: {torch.__version__}")
print(f"Transformers 版本: {transformers.__version__}")
print(f"CUDA 是否可用: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA 版本: {torch.version.cuda}")
    print(f"当前设备: {torch.cuda.get_device_name(0)}")
print("=" * 50)

# 设置模型路径
model_name = "Qwen/Qwen2.5-VL-3B-Instruct"
local_model_path = "./models/Qwen2.5-VL-3B-Instruct"  # 本地模型路径

# 检查本地模型是否存在
use_local_model = os.path.exists(local_model_path)
if use_local_model:
    print(f"使用本地模型: {local_model_path}")
    model_path = local_model_path
else:
    print(f"从网络下载模型: {model_name}")
    print("注意: 模型较大，下载可能需要一些时间...")
    model_path = model_name

# 启用  以节省显存并加速推理
print("正在加载模型...")
start_time = time.time()
model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
    model_path,
    torch_dtype=torch.float16,  # 使用 float16 数据类型以减少显存占用
    device_map="auto"  # 自动分配设备
)
print(f"模型加载完成，耗时: {time.time() - start_time:.2f} 秒")

# 默认处理器，并限制图片的最大像素数以节省显存
print("正在加载处理器...")
start_time = time.time()
min_pixels = 256 * 28 * 28  # 最小像素数
max_pixels = 512 * 28 * 28  # 最大像素数（降低分辨率以减少显存占用）
processor = AutoProcessor.from_pretrained(model_path, min_pixels=min_pixels, max_pixels=max_pixels)
print(f"处理器加载完成，耗时: {time.time() - start_time:.2f} 秒")

# 本地图片路径
local_image_path = r"C:\Users\<USER>\Desktop\ai-avatar.png"  # 替换为你的本地图片路径

# 构造输入消息，包含一张图片和一段文本
messages = [
    {
        "role": "user",  # 用户角色
        "content": [
            {
                "type": "image",  # 图片类型
                # "image": "https://qianwen-res.oss-cn-beijing.aliyuncs.com/Qwen-VL/assets/demo.jpeg",  # 图片 URL
                "image": local_image_path,  # 图片 URL

            },
            {"type": "text", "text": "请描述图片."},  # 文本类型及内容
        ],
    }
]

# 准备推理输入
print("正在处理输入...")
# 使用处理器生成对话模板，`tokenize=False` 表示不进行分词，`add_generation_prompt=True` 表示添加生成提示。
text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)

# 处理视觉信息（如图片和视频）
image_inputs, video_inputs = process_vision_info(messages)

# 将文本、图片和视频信息传递给处理器，生成模型输入张量
# `padding=True` 表示对输入进行填充，`return_tensors="pt"` 表示返回 PyTorch 张量。
inputs = processor(
    text=[text],
    images=image_inputs,
    videos=video_inputs,
    padding=True,
    return_tensors="pt",
)

# 清理显存
torch.cuda.empty_cache()

# 将输入张量移动到 GPU（"cuda" 设备）
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"使用设备: {device}")
inputs = inputs.to(device)

# 推理：生成输出
print("正在生成输出...")
start_time = time.time()
# `max_new_tokens=64` 表示生成的最大 token 数量（减少生成长度以节省显存）。
generated_ids = model.generate(**inputs, max_new_tokens=64)
print(f"生成完成，耗时: {time.time() - start_time:.2f} 秒")

# 去除输入部分的 token，只保留生成的部分
generated_ids_trimmed = [
    out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
]

# 解码生成的 token 为文本
output_text = processor.batch_decode(
    generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
)

# 打印生成的文本
print("\n生成的文本:")
print("=" * 50)
print(output_text)
print("=" * 50)