import base64
import os
import sys
import time
from io import BytesIO
from typing import Optional

import torch
import transformers
import uvicorn
from PIL import Image
from fastapi import FastAPI, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from qwen_vl_utils import process_vision_info
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor

# 打印依赖和版本信息
print("=" * 50)
print("欢迎使用图文理解API,基于Qwen2.5-VL-3B模型的图文理解服务！")
print("依赖和版本信息:")
print(f"Python 版本: {sys.version}")
print(f"PyTorch 版本: {torch.__version__}")
print(f"Transformers 版本: {transformers.__version__}")
print(f"CUDA 是否可用: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA 版本: {torch.version.cuda}")
    print(f"当前设备: {torch.cuda.get_device_name(0)}")
print("=" * 50)

# 创建FastAPI应用
app = FastAPI(title="千问VL图文理解API", description="基于Qwen2.5-VL-3B模型的图文理解服务")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 定义请求模型
class ImageRequest(BaseModel):
    prompt: str
    image_base64: Optional[str] = None
    image_url: Optional[str] = None
    max_tokens: int = 256

# 模型初始化函数
def load_model():
    # 设置模型路径
    model_name = "Qwen/Qwen2.5-VL-3B-Instruct"
    local_model_path = "../models/models--Qwen--Qwen2.5-VL-3B-Instruct/snapshots/66285546d2b821cf421d4f5eb2576359d3770cd3"  # 本地模型路径

    # 检查本地模型是否存在
    use_local_model = os.path.exists(local_model_path)
    if use_local_model:
        print(f"使用本地模型: {local_model_path}")
        model_path = local_model_path
    else:
        print(f"从网络下载模型: {model_name}")
        print("注意: 模型较大，下载可能需要一些时间...")
        model_path = model_name

    # 启用以节省显存并加速推理
    print("正在加载模型...")
    start_time = time.time()
    model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
        model_path,
        torch_dtype=torch.float16,  # 使用 float16 数据类型以减少显存占用
        device_map="auto"  # 自动分配设备
    )
    print(f"模型加载完成，耗时: {time.time() - start_time:.2f} 秒")

    # 默认处理器，并限制图片的最大像素数以节省显存
    print("正在加载处理器...")
    start_time = time.time()
    min_pixels = 256 * 28 * 28  # 最小像素数
    max_pixels = 512 * 28 * 28  # 最大像素数（降低分辨率以减少显存占用）
    processor = AutoProcessor.from_pretrained(model_path, min_pixels=min_pixels, max_pixels=max_pixels)
    print(f"处理器加载完成，耗时: {time.time() - start_time:.2f} 秒")
    
    return model, processor

# 全局变量存储模型和处理器
model, processor = load_model()

@app.post("/generate")
async def generate(request: ImageRequest):
    """
    图文理解接口
    - prompt: 提示文本
    - image_base64: Base64编码的图片（可选）
    - image_url: 图片URL（可选）
    - max_tokens: 最大生成token数
    """
    try:
        # 构造消息
        message_content = []
        
        # 添加图片
        if request.image_base64:
            # 处理Base64图片
            image_data = base64.b64decode(request.image_base64)
            image = Image.open(BytesIO(image_data))
            message_content.append({"type": "image", "image": image})
        elif request.image_url:
            # 使用图片URL
            message_content.append({"type": "image", "image": request.image_url})
        
        # 添加文本提示
        message_content.append({"type": "text", "text": request.prompt})
        
        # 构造最终消息
        messages = [{"role": "user", "content": message_content}]
        
        # 准备推理输入
        text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
        
        # 处理视觉信息
        image_inputs, video_inputs = process_vision_info(messages)
        
        # 生成模型输入
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt",
        )
        
        # 清理显存
        torch.cuda.empty_cache()
        
        # 将输入移至适当设备
        device = "cuda" if torch.cuda.is_available() else "cpu"
        inputs = inputs.to(device)
        
        # 生成输出
        start_time = time.time()
        generated_ids = model.generate(**inputs, max_new_tokens=request.max_tokens)
        
        # 去除输入部分的token
        generated_ids_trimmed = [
            out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
        ]
        
        # 解码生成的token为文本
        output_text = processor.batch_decode(
            generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
        )
        
        processing_time = time.time() - start_time
        
        return {
            "result": output_text[0],
            "processing_time": f"{processing_time:.2f}秒"
        }
    
    except Exception as e:
        return {"error": str(e)}

@app.post("/upload-image")
async def upload_image(file1: UploadFile = File(...), file2: UploadFile = None, prompt: str = Form(...), max_tokens: int = Form(256)):
    """
    上传图片接口
    - file1: 上传的第一张图片文件（必需）
    - file2: 上传的第二张图片文件（可选）
    - prompt: 提示文本
    - max_tokens: 最大生成token数
    """
    try:
        # 读取上传的图片
        image_data1 = await file1.read()
        image1 = Image.open(BytesIO(image_data1))
        
        # 读取上传的第二张图片
        if file2:
            image_data2 = await file2.read()
            image2 = Image.open(BytesIO(image_data2))
        else:
            image2 = None
        
        # 构造消息
        content = [{"type": "image", "image": image1}]
        
        # 如果第二张图片存在，也添加到content列表
        if file2 and image2:
            content.append({"type": "image", "image": image2})
        
        # 添加提示文本
        content.append({"type": "text", "text": prompt})
        
        messages = [{
            "role": "user",
            "content": content
        }]
        
        # 准备推理输入
        text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
        
        # 处理视觉信息
        image_inputs, video_inputs = process_vision_info(messages)
        
        # 生成模型输入
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt",
        )
        
        # 清理显存
        torch.cuda.empty_cache()
        
        # 将输入移至适当设备
        device = "cuda" if torch.cuda.is_available() else "cpu"
        inputs = inputs.to(device)
        
        # 生成输出
        start_time = time.time()
        generated_ids = model.generate(**inputs, max_new_tokens=max_tokens)
        
        # 去除输入部分的token
        generated_ids_trimmed = [
            out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
        ]
        
        # 解码生成的token为文本
        output_text = processor.batch_decode(
            generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
        )
        
        processing_time = time.time() - start_time
        
        return {
            "result": output_text[0],
            "processing_time": f"{processing_time:.2f}秒"
        }
    
    except Exception as e:
        return {"error": str(e)}

@app.get("/")
def read_root():
    return {"message": "欢迎使用千问VL图文理解API", "status": "服务正常运行"}

# 启动服务器
if __name__ == "__main__":
    # 启动FastAPI应用
    uvicorn.run(app, host="0.0.0.0", port=8025, reload=False)