package com.kefang.service;

import com.kefang.entity.ChatMessage;
import java.util.List;
import java.util.Date;
import java.util.Map;
import java.time.LocalDateTime;

public interface ChatMessageService {

    /**
     * 发送消息
     * @param chatMessage 消息内容
     * @return 消息ID，失败返回null
     */
    Long sendMessage(ChatMessage chatMessage);
    
    /**
     * 根据ID查询消息
     * @param id 消息ID
     * @return 消息内容
     */
    ChatMessage getMessageById(Long id);
    
    /**
     * 根据会话ID查询消息列表
     * @param sessionId 会话ID
     * @return 消息列表
     */
    List<ChatMessage> getMessagesBySessionId(Long sessionId);
    
    /**
     * 分页查询会话消息
     * @param sessionId 会话ID
     * @param page 页码（从1开始）
     * @param size 每页条数
     * @return 消息列表
     */
    List<ChatMessage> getMessagesByPage(Long sessionId, Integer page, Integer size);
    
    /**
     * 更新消息已读状态
     * @param sessionId 会话ID
     * @param senderType 发送者类型（更新对方发的消息）
     * @return 操作结果
     */
    boolean updateMessageReadStatus(Long sessionId, Integer senderType);
    
    /**
     * 统计未读消息数
     * @param sessionId 会话ID
     * @param senderType 发送者类型（统计对方发的消息）
     * @return 未读消息数
     */
    int countUnreadMessages(Long sessionId, Integer senderType);
    
    /**
     * 获取会话最近一条消息
     * @param sessionId 会话ID
     * @return 最近一条消息
     */
    ChatMessage getLatestMessage(Long sessionId);
    
    /**
     * 按类型统计消息数量
     * @param sessionId 会话ID
     * @param msgType 消息类型 
     * @return 消息数量
     */
    int countMessagesByType(Long sessionId, Integer msgType);
    
    /**
     * 按时间范围查询消息
     * @param sessionId 会话ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    List<ChatMessage> getMessagesByTimeRange(Long sessionId, Date startTime, Date endTime);
    
    /**
     * 按条件查询消息（支持分页查询）
     * @param params 查询参数，支持sessionId, keyword, msgType, senderType, startTime, endTime, page, size
     * @return 消息列表
     */
    Map<String, Object> getMessagesByCondition(Map<String, Object> params);
    
    /**
     * 获取最近的用户文本消息
     * 
     * @param limit 限制条数
     * @return 用户消息列表，包含消息内容和创建时间
     */
    List<Map<String, Object>> getRecentUserMessages(Integer limit);
    
    /**
     * 统计指定客服在特定时间范围内的消息回答数量
     * @param agentId 客服ID（字符串形式）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 回答消息数量
     */
    int countMessagesByAgentIDAndTimeRange(String agentId, LocalDateTime startTime, LocalDateTime endTime);
} 