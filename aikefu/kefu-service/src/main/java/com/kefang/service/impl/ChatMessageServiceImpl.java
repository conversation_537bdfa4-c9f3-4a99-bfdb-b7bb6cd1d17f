package com.kefang.service.impl;

import com.kefang.entity.Agent;
import com.kefang.entity.ChatMessage;
import com.kefang.mapper.ChatMessageMapper;
import com.kefang.service.AgentService;
import com.kefang.service.ChatMessageService;
import com.mysql.cj.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.time.LocalDateTime;
import java.time.ZoneId;

@Service
public class ChatMessageServiceImpl implements ChatMessageService {

    @Autowired
    private ChatMessageMapper chatMessageMapper;
    @Autowired
    private AgentService agentService;


    @Override
    public Long sendMessage(ChatMessage chatMessage) {
        // 设置默认值
        if (chatMessage.getMsgType() == null) {
            chatMessage.setMsgType(0); // 文本消息
        }
        chatMessage.setIsRead(0); // 未读
        if(StringUtils.isNullOrEmpty(chatMessage.getScene())){
            chatMessage.setScene("默认");
        }
        if(StringUtils.isNullOrEmpty(chatMessage.getDatasource())){
            chatMessage.setDatasource("默认");
        }
        // 移除 <think>...</think> 标签及其包裹的内容，只保留标签外的正常消息。
        String content = chatMessage.getContent();
        content = content.replaceAll("<think>.*?</think>", "");
        content = content.replaceAll("<think>", "").replaceAll("</think>", "");
        chatMessage.setContent(content);
        // 插入消息并获取ID
        int result = chatMessageMapper.insert(chatMessage);
        if (result > 0) {
            return chatMessage.getId(); // 返回消息ID
        }
        return null; // 插入失败返回null
    }

    @Override
    public ChatMessage getMessageById(Long id) {
        return chatMessageMapper.selectById(id);
    }

    @Override
    public List<ChatMessage> getMessagesBySessionId(Long sessionId) {
        List<ChatMessage> chatMessages = chatMessageMapper.selectBySessionId(sessionId);
        List<Agent> agents = agentService.getAllAgents();

        // 构建 Map<agentId, name>
        Map<Long, String> agentMap = new HashMap<>();
        for (Agent agent : agents) {
            agentMap.put(agent.getId(), agent.getName());
        }


        // 设置发送人名称
        for (ChatMessage message : chatMessages) {
            // 如果senderType是2，则设置为"熊小智"
            if (message.getSenderType() != null && message.getSenderType() == 2) {
                message.setSenderName("熊小智");
                continue;
            }
            // 如果senderType是3，则设置为"系统"
            if (message.getSenderType() != null && message.getSenderType() == 3) {
                message.setSenderName("系统");
                continue;
            }
            
            // 如果senderType是0，则不设置senderName
            if (message.getSenderType() != null && message.getSenderType() == 0) {
                message.setSenderName("用户");
                continue;
            }
            
            // 处理其他情况
            // 检查senderId是否为null
            if (message.getSenderId() != null) {
                // 确保senderId为Long类型
                Long senderIdLong;
                Object senderIdObj = message.getSenderId();
                
                if (senderIdObj instanceof String) {
                    try {
                        senderIdLong = Long.parseLong((String) senderIdObj);
                    } catch (NumberFormatException e) {
                        senderIdLong = null;
                    }
                } else if (senderIdObj instanceof Long) {
                    senderIdLong = (Long) senderIdObj;
                } else if (senderIdObj instanceof Number) {
                    senderIdLong = ((Number) senderIdObj).longValue();
                } else {
                    senderIdLong = null;
                }
                
                message.setSenderName(agentMap.getOrDefault(senderIdLong, "未知用户"));
            } else {
                message.setSenderName("未知用户");
            }
        }

        return chatMessages;
    }

    @Override
    public List<ChatMessage> getMessagesByPage(Long sessionId, Integer page, Integer size) {
        int offset = (page - 1) * size;
        return chatMessageMapper.selectPageBySessionId(sessionId, offset, size);
    }

    @Override
    public boolean updateMessageReadStatus(Long sessionId, Integer senderType) {
        return chatMessageMapper.updateReadStatus(sessionId, senderType) > 0;
    }

    @Override
    public int countUnreadMessages(Long sessionId, Integer senderType) {
        return chatMessageMapper.countUnreadMessages(sessionId, senderType);
    }
    
    @Override
    public ChatMessage getLatestMessage(Long sessionId) {
        return chatMessageMapper.selectLatestBySessionId(sessionId);
    }
    
    @Override
    public int countMessagesByType(Long sessionId, Integer msgType) {
        return chatMessageMapper.countMessagesByType(sessionId, msgType);
    }
    
    @Override
    public List<ChatMessage> getMessagesByTimeRange(Long sessionId, Date startTime, Date endTime) {
        return chatMessageMapper.selectByTimeRange(sessionId, startTime, endTime);
    }
    
    @Override
    public Map<String, Object> getMessagesByCondition(Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取参数
        Long sessionId = params.get("sessionId") != null ? Long.valueOf(params.get("sessionId").toString()) : null;
        String keyword = (String) params.get("keyword");
        Integer msgType = params.get("msgType") != null ? Integer.valueOf(params.get("msgType").toString()) : null;
        Integer senderType = params.get("senderType") != null ? Integer.valueOf(params.get("senderType").toString()) : null;
        Date startTime = (Date) params.get("startTime");
        Date endTime = (Date) params.get("endTime");
        
        // 分页参数
        Integer page = params.get("page") != null ? Integer.valueOf(params.get("page").toString()) : 1;
        Integer size = params.get("size") != null ? Integer.valueOf(params.get("size").toString()) : 20;
        Integer offset = (page - 1) * size;
        
        // 查询总数
        int total = chatMessageMapper.countMessagesByCondition(sessionId, keyword, msgType, senderType, startTime, endTime);
        
        // 查询数据
        List<ChatMessage> messages = chatMessageMapper.selectMessagesByCondition(
            sessionId, keyword, msgType, senderType, startTime, endTime, offset, size
        );
        
        // 封装结果
        result.put("total", total);
        result.put("messages", messages);
        result.put("page", page);
        result.put("size", size);
        
        return result;
    }
    
    @Override
    public List<Map<String, Object>> getRecentUserMessages(Integer limit) {
        return chatMessageMapper.getRecentUserMessages(limit);
    }

    @Override
    public int countMessagesByAgentIDAndTimeRange(String agentId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 将LocalDateTime转换为Date (如果需要的话)
            Date startDate = Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant());
            Date endDate = Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant());
            
            // 统计特定客服在指定时间范围内的消息数量
            // 这里假设消息表中有sender_id和sender_type字段，sender_type=1表示客服发送的消息
            return chatMessageMapper.countAgentMessages(agentId, startDate, endDate);
        } catch (Exception e) {
            // 记录错误日志
            e.printStackTrace();
            return 0;
        }
    }
} 