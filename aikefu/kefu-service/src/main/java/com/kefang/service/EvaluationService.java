package com.kefang.service;

import com.kefang.entity.Evaluation;
import java.util.List;

public interface EvaluationService {

    /**
     * 添加评价
     * @param evaluation 评价信息
     * @return 操作结果
     */
    boolean addEvaluation(Evaluation evaluation);
    
    /**
     * 根据ID查询评价
     * @param id 评价ID
     * @return 评价信息
     */
    Evaluation getEvaluationById(Long id);
    
    /**
     * 根据会话ID查询评价
     * @param sessionId 会话ID
     * @return 评价信息
     */
    Evaluation getEvaluationBySessionId(Long sessionId);
    
    /**
     * 根据用户ID查询评价列表
     * @param userId 用户ID
     * @return 评价列表
     */
    List<Evaluation> getEvaluationsByUserId(Long userId);
    
    /**
     * 查询所有评价
     * @return 评价列表
     */
    List<Evaluation> getAllEvaluations();
} 