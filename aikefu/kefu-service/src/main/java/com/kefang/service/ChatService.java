package com.kefang.service;

import com.kefang.entity.ChatSession;
import com.kefang.vo.WaitingSessionVO;

import java.util.List;

/**
 * 聊天服务接口
 */
public interface ChatService {
    
    /**
     * 获取待处理会话列表
     * @return 待处理会话列表
     */
    List<WaitingSessionVO> getWaitingSessions();
    
    /**
     * 接受会话
     * @param sessionId 会话ID
     * @param agentId 客服ID
     * @return 会话对象
     */
    ChatSession acceptSession(Long sessionId, Long agentId);
} 