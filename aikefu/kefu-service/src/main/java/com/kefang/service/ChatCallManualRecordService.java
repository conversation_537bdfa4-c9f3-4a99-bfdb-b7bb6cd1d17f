package com.kefang.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kefang.entity.ChatCallManualRecord;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 人工客服通话记录服务接口
 */
public interface ChatCallManualRecordService extends IService<ChatCallManualRecord> {

    /**
     * 统计指定客服的所有接入会话数
     * @param agentId 客服ID
     * @return 会话数量
     */
    int countAcceptedSessionsByAgent(Long agentId);
    
    /**
     * 统计指定客服在特定时间范围内的接入会话数
     * @param agentId 客服ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 会话数量
     */
    int countAcceptedSessionsByAgentAndTimeRange(Long agentId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取指定客服的平均响应时间
     * @param agentId 客服ID
     * @return 平均响应时间（秒）
     */
    Double getAvgResponseTimeByAgent(Long agentId);
    
    /**
     * 获取指定客服在特定时间范围内的平均响应时间
     * @param agentId 客服ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 平均响应时间（秒）
     */
    Double getAvgResponseTimeByAgentAndTimeRange(Long agentId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取指定客服的平均对话时间
     * @param agentId 客服ID
     * @return 平均对话时间（秒）
     */
    Double getAvgChatDurationByAgent(Long agentId);
    
    /**
     * 获取指定客服在特定时间范围内的平均对话时间
     * @param agentId 客服ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 平均对话时间（秒）
     */
    Double getAvgChatDurationByAgentAndTimeRange(Long agentId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取指定客服在特定时间范围内接待的会话ID列表，按照最近接入时间降序排序
     * @param agentId 客服ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 会话ID列表
     */
    List<Long> getSessionIdsByAgentAndTimeRangeOrderByAcceptTimeDesc(Long agentId, LocalDateTime startTime, LocalDateTime endTime);
} 