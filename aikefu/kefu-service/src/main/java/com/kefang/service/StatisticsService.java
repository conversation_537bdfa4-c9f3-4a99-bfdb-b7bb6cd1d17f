package com.kefang.service;

import com.kefang.vo.SessionStatVO;

import java.util.List;
import java.util.Map;

/**
 * 统计服务接口
 */
public interface StatisticsService {
    
    /**
     * 获取工作台统计数据
     * @return 统计数据集合
     */
    Map<String, Object> getDashboardStats();
    
    /**
     * 获取会话统计数据
     * @param period 时间周期：day, week, month
     * @return 会话统计数据列表
     */
    List<SessionStatVO> getSessionStats(String period);
    
    /**
     * 获取对话统计数据
     * @param period 时间周期：day, week, month
     * @return 对话统计数据列表
     */
    List<SessionStatVO> getMessageStats(String period);
    
    /**
     * 获取满意度统计数据
     * @param period 时间周期：day, week, month
     * @return 满意度统计数据列表
     */
    List<SessionStatVO> getSatisfactionStats(String period);
} 