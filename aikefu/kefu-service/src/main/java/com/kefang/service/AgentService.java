package com.kefang.service;

import com.kefang.entity.Agent;
import java.util.List;

public interface AgentService {

    /**
     * 客服登录
     * @param agentNo 客服工号
     * @param password 密码
     * @return 客服信息
     */
    Agent login(String agentNo, String password);
    
    /**
     * 根据ID查询客服
     * @param id 客服ID
     * @return 客服信息
     */
    Agent getAgentById(Long id);
    
    /**
     * 根据工号查询客服
     * @param agentNo 工号
     * @return 客服信息
     */
    Agent getAgentByAgentNo(String agentNo);
    
    /**
     * 新增客服
     * @param agent 客服信息
     * @return 操作结果
     */
    boolean addAgent(Agent agent);
    
    /**
     * 更新客服信息
     * @param agent 客服信息
     * @return 操作结果
     */
    boolean updateAgent(Agent agent);
    
    /**
     * 更新客服在线状态
     * @param id 客服ID
     * @param status 状态（0离线 1在线）
     * @return 操作结果
     */
    boolean updateAgentStatus(Long id, Integer status);
    
    /**
     * 更新客服登录时间
     * @param id 客服ID
     * @return 操作结果
     */
    boolean updateLoginTime(Long id);
    
    /**
     * 获取所有客服
     * @return 客服列表
     */
    List<Agent> getAllAgents();
    
    /**
     * 获取所有在线客服
     * @return 在线客服列表
     */
    List<Agent> getOnlineAgents();
} 