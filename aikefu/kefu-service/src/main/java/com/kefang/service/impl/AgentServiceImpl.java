package com.kefang.service.impl;

import com.kefang.entity.Agent;
import com.kefang.mapper.AgentMapper;
import com.kefang.service.AgentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AgentServiceImpl implements AgentService {

    @Autowired
    private AgentMapper agentMapper;

    @Override
    public Agent login(String agentNo, String password) {
        Agent agent = agentMapper.selectByAgentNo(agentNo);
        if (agent != null && password.equals(agent.getPassword())) {
            // 更新登录时间
            updateLoginTime(agent.getId());
            // 重新查询获取更新后的信息
            return agentMapper.selectById(agent.getId());
        }
        return null;
    }

    @Override
    public Agent getAgentById(Long id) {
        return agentMapper.selectById(id);
    }

    @Override
    public Agent getAgentByAgentNo(String agentNo) {
        return agentMapper.selectByAgentNo(agentNo);
    }

    @Override
    public boolean addAgent(Agent agent) {
        // 默认状态为离线
        if (agent.getStatus() == null) {
            agent.setStatus(0);
        }
        return agentMapper.insert(agent) > 0;
    }

    @Override
    public boolean updateAgent(Agent agent) {
        return agentMapper.update(agent) > 0;
    }

    @Override
    public boolean updateAgentStatus(Long id, Integer status) {
        return agentMapper.updateStatus(id, status) > 0;
    }
    
    @Override
    public boolean updateLoginTime(Long id) {
        return agentMapper.updateLoginTime(id) > 0;
    }

    @Override
    public List<Agent> getAllAgents() {
        return agentMapper.selectAll();
    }

    @Override
    public List<Agent> getOnlineAgents() {
        return agentMapper.selectOnlineAgents();
    }
} 