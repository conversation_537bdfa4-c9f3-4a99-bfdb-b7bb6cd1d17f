package com.kefang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kefang.entity.ChatCallManualRecord;
import com.kefang.mapper.ChatCallManualRecordMapper;
import com.kefang.service.ChatCallManualRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 人工呼叫记录服务实现类
 */
@Slf4j
@Service
public class ChatCallManualRecordServiceImpl extends ServiceImpl<ChatCallManualRecordMapper, ChatCallManualRecord> implements ChatCallManualRecordService {

    @Override
    public Double getAvgResponseTimeByAgent(Long agentId) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<ChatCallManualRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ChatCallManualRecord::getAgentId, agentId)
                    .eq(ChatCallManualRecord::getIsAccepted, true)
                    .isNotNull(ChatCallManualRecord::getResponseDuration);
            
            List<ChatCallManualRecord> records = this.list(queryWrapper);
            
            if (records.isEmpty()) {
                return 0.0;
            }
            
            // 计算平均响应时间
            double totalDuration = records.stream()
                    .mapToLong(ChatCallManualRecord::getResponseDuration)
                    .sum();
            
            return totalDuration / records.size();
        } catch (Exception e) {
            log.error("计算客服平均响应时间出错", e);
            return 0.0;
        }
    }

    @Override
    public Double getAvgResponseTimeByAgentAndTimeRange(Long agentId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<ChatCallManualRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ChatCallManualRecord::getAgentId, agentId)
                    .eq(ChatCallManualRecord::getIsAccepted, true)
                    .isNotNull(ChatCallManualRecord::getResponseDuration)
                    .between(ChatCallManualRecord::getRequestTime, startTime, endTime);
            
            List<ChatCallManualRecord> records = this.list(queryWrapper);
            
            if (records.isEmpty()) {
                return 0.0;
            }
            
            // 计算平均响应时间
            double totalDuration = records.stream()
                    .mapToLong(ChatCallManualRecord::getResponseDuration)
                    .sum();
            
            return totalDuration / records.size();
        } catch (Exception e) {
            log.error("计算客服在指定时间范围内的平均响应时间出错", e);
            return 0.0;
        }
    }
    
    @Override
    public Double getAvgChatDurationByAgent(Long agentId) {
        try {
            // 构建查询条件
            QueryWrapper<ChatCallManualRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("agent_id", agentId.toString())
                    .eq("is_accepted", 1)
                    .isNotNull("end_time")
                    .isNotNull("service_duration");
            
            List<ChatCallManualRecord> records = this.list(queryWrapper);
            
            if (records.isEmpty()) {
                return 0.0;
            }
            
            // 计算平均对话时间
            double totalDuration = records.stream()
                    .mapToLong(ChatCallManualRecord::getServiceDuration)
                    .sum();
            
            return totalDuration / records.size();
        } catch (Exception e) {
            log.error("计算客服平均对话时间出错", e);
            return 0.0;
        }
    }

    @Override
    public Double getAvgChatDurationByAgentAndTimeRange(Long agentId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 由于requestTime是Date类型，需要转换LocalDateTime为Date
            Date startDate = java.util.Date.from(startTime.atZone(java.time.ZoneId.systemDefault()).toInstant());
            Date endDate = java.util.Date.from(endTime.atZone(java.time.ZoneId.systemDefault()).toInstant());
            
            // 构建查询条件
            QueryWrapper<ChatCallManualRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("agent_id", agentId.toString())
                    .eq("is_accepted", 1)
                    .isNotNull("end_time")
                    .isNotNull("service_duration")
                    .between("request_time", startDate, endDate);
            
            List<ChatCallManualRecord> records = this.list(queryWrapper);
            
            if (records.isEmpty()) {
                return 0.0;
            }
            
            // 计算平均对话时间
            double totalDuration = records.stream()
                    .mapToLong(ChatCallManualRecord::getServiceDuration)
                    .sum();
            
            return totalDuration / records.size();
        } catch (Exception e) {
            log.error("计算时间范围内客服平均对话时间出错", e);
            return 0.0;
        }
    }

    /**
     * 历史接入会话总数
     * @param agentId 客服ID
     * @return
     */
    @Override
    public int countAcceptedSessionsByAgent(Long agentId) {
        try {
            // 构建查询条件：已接入的会话
            LambdaQueryWrapper<ChatCallManualRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ChatCallManualRecord::getAgentId, agentId)
                    .eq(ChatCallManualRecord::getIsAccepted, true);
            
            // 获取所有记录
            List<ChatCallManualRecord> records = this.list(queryWrapper);
            
            // 根据会话ID去重计数
            return records.size();
        } catch (Exception e) {
            log.error("统计客服接入会话总数出错", e);
            return 0;
        }
    }

    /***
     * 统计客服在指定时间范围内的接入会话总数
     * @param agentId 客服ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    @Override
    public int countAcceptedSessionsByAgentAndTimeRange(Long agentId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 获取指定时间范围内的记录
            LambdaQueryWrapper<ChatCallManualRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ChatCallManualRecord::getAgentId, agentId)
                    .eq(ChatCallManualRecord::getIsAccepted, true)
                    .between(ChatCallManualRecord::getRequestTime, startTime, endTime);
            
            // 获取所有记录
            List<ChatCallManualRecord> records = this.list(queryWrapper);
            
            // 根据会话ID去重计数
            return records.size();
        } catch (Exception e) {
            log.error("统计指定时间范围内客服接入会话数出错", e);
            return 0;
        }
    }
    
    /**
     * 获取指定客服在特定时间范围内接待的会话ID列表，按照最近接入时间降序排序
     * @param agentId 客服ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 会话ID列表
     */
    @Override
    public List<Long> getSessionIdsByAgentAndTimeRangeOrderByAcceptTimeDesc(Long agentId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 由于  request_time 是Date类型，需要转换LocalDateTime为Date
            Date startDate = java.util.Date.from(startTime.atZone(java.time.ZoneId.systemDefault()).toInstant());
            Date endDate = java.util.Date.from(endTime.atZone(java.time.ZoneId.systemDefault()).toInstant());
            
            // 构建查询条件
            QueryWrapper<ChatCallManualRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("agent_id", agentId.toString())
                    .eq("is_accepted", 1)
                    .between("request_time", startDate, endDate)
                    .orderByDesc("request_time");
            
            // 获取所有符合条件的记录
            List<ChatCallManualRecord> records = this.list(queryWrapper);
            
            // 按照会话ID分组，并保留每个会话的最新记录（即最大 request_time）
            Map<Long, ChatCallManualRecord> sessionMap = records.stream()
                    .collect(Collectors.toMap(
                            ChatCallManualRecord::getSessionId,
                            record -> record,
                            (existing, replacement) -> existing.getAcceptTime().after(replacement.getAcceptTime()) ? existing : replacement
                    ));
            
            // 将结果转换为按照接入时间降序排序的会话ID列表
            return sessionMap.values().stream()
                    .sorted((r1, r2) -> r2.getAcceptTime().compareTo(r1.getAcceptTime()))
                    .map(ChatCallManualRecord::getSessionId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取客服接待的会话ID列表出错", e);
            return List.of();
        }
    }
} 