package com.kefang.service.impl;

import com.kefang.entity.User;
import com.kefang.mapper.UserMapper;
import com.kefang.service.UserService;
import com.kefang.vo.PageResult;
import com.mysql.cj.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.time.LocalDate;
import java.time.ZoneId;
import java.text.SimpleDateFormat;
import java.util.Date;

@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Override
    public boolean register(User user) {
        // 判断手机号是否已注册
        User existUser = userMapper.selectByPhone(user.getPhone());
        if (existUser != null) {
            return false;
        }
        if (StringUtils.isNullOrEmpty(user.getDatasource())){
            user.setDatasource("未知");
        }
        // TODO: 密码加密处理
        return userMapper.insert(user) > 0;
    }

    @Override
    public User login(String phone, String password) {
        User user = userMapper.selectByPhone(phone);
        if (user != null && user.getPassword().equals(password)) {
            // TODO: 密码验证应该使用加密比对
            return user;
        }
        return null;
    }

    @Override
    public User getUserById(Long id) {
        return userMapper.selectById(id);
    }

    @Override
    public User getUserByPhone(String phone) {
        return userMapper.selectByPhone(phone);
    }

    @Override
    public boolean updateUser(User user) {
        return userMapper.update(user) > 0;
    }

    @Override
    public List<User> getAllUsers() {
        return userMapper.selectAll();
    }
    
    @Override
    public PageResult<User> getUsersByPage(Map<String, Object> params) {
        // 获取分页参数
        int pageNum = Integer.parseInt(params.getOrDefault("pageNum", "1").toString());
        int pageSize = Integer.parseInt(params.getOrDefault("pageSize", "10").toString());
        
        // 获取所有用户
        List<User> allUsers = userMapper.selectAll();
        
        // 过滤条件
        String userType = (String) params.get("userType");
        String keyword = (String) params.get("keyword");
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");
        
        // 应用过滤
        List<User> filteredUsers = allUsers.stream().filter(user -> {
            // 用户类型过滤 - 检查VIP级别
            if (userType != null && !userType.isEmpty()) {
                if (user.getVipLevel() == null) {
                    return userType.equals("1"); // 没有VIP级别的视为普通用户
                }
                
                // 假设1=普通用户,2=会员,3=VIP
                String userVipLevel = user.getVipLevel().toString();
                if (!userType.equals(userVipLevel)) {
                    return false;
                }
            }
            
            // 关键词过滤(用户名、昵称、手机号、ID)
            if (keyword != null && !keyword.isEmpty()) {
                String keywordLower = keyword.toLowerCase();
                boolean matchesKeyword = (user.getNickname() != null && user.getNickname().toLowerCase().contains(keywordLower)) ||
                                       (user.getPhone() != null && user.getPhone().contains(keyword)) ||
                                       (user.getId() != null && user.getId().toString().contains(keyword));
                if (!matchesKeyword) {
                    return false;
                }
            }
            
            // 日期范围过滤
            if (startDate != null && !startDate.isEmpty() && endDate != null && !endDate.isEmpty()) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    Date start = sdf.parse(startDate);
                    // 结束日期需要加1天，因为我们需要包含结束日期当天
                    Date end = sdf.parse(endDate);
                    
                    Date registerTime = user.getCreatedAt();
                    if (registerTime == null) {
                        return false;
                    }
                    
                    // 只比较日期部分
                    Date registerDate = sdf.parse(sdf.format(registerTime));
                    return !registerDate.before(start) && !registerDate.after(end);
                } catch (Exception e) {
                    return true; // 日期格式错误时不过滤
                }
            }
            
            return true;
        }).collect(Collectors.toList());
        
        // 计算总数
        long total = filteredUsers.size();
        
        // 分页
        int fromIndex = (pageNum - 1) * pageSize;
        if (fromIndex >= filteredUsers.size()) {
            return new PageResult<>(new ArrayList<>(), total, pageNum, pageSize);
        }
        
        int toIndex = Math.min(fromIndex + pageSize, filteredUsers.size());
        List<User> pageData = filteredUsers.subList(fromIndex, toIndex);
        
        // 构建分页结果
        return new PageResult<>(pageData, total, pageNum, pageSize);
    }
} 