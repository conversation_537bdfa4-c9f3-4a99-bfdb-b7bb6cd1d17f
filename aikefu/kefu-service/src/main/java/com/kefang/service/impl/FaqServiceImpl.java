package com.kefang.service.impl;

import com.kefang.entity.Faq;
import com.kefang.mapper.ChatMessageMapper;
import com.kefang.mapper.FaqMapper;
import com.kefang.service.FaqService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

@Service
public class FaqServiceImpl implements FaqService {

    @Autowired
    private FaqMapper faqMapper;
    
    @Autowired
    private ChatMessageMapper chatMessageMapper;

    @Override
    public boolean addFaq(Faq faq) {
        return faqMapper.insert(faq) > 0;
    }

    @Override
    public boolean updateFaq(Faq faq) {
        return faqMapper.update(faq) > 0;
    }

    @Override
    public boolean deleteFaq(Long id) {
        return faqMapper.deleteById(id) > 0;
    }

    @Override
    public Faq getFaqById(Long id) {
        return faqMapper.selectById(id);
    }

    @Override
    public List<Faq> getFaqsByCategory(String category) {
        return faqMapper.selectByCategory(category);
    }

    @Override
    public List<Faq> getAllFaqs() {
        return faqMapper.selectAll();
    }
    
    @Override
    public List<Map<String, Object>> getFaqBySceneAndDatasource(String scene, String datasource) {
        // 如果数据库中有数据，则从数据库获取
        // =List<Map<String, Object>> faqList = chatMessageMapper.selectFaqBySceneAndDatasource(scene, datasource);
        List<Map<String, Object>> faqList = new ArrayList<>();

        // 小智回收
        if ("小智回收".equals(datasource)) {
            if ("首页在线".equals(scene)) {
                addMockFaq(faqList, "可以上门回收吗?", scene, datasource);
                addMockFaq(faqList, "回收价格怎么计算的?", scene, datasource);
                addMockFaq(faqList, "回收哪些品类的设备?", scene, datasource);
                addMockFaq(faqList, "怎么预约上门回收吗?", scene, datasource);
                addMockFaq(faqList, "回收流程需要多久?", scene, datasource);
                addMockFaq(faqList, "如何注册小智回收账号?", scene, datasource);
            } else if ("常见问题页面".equals(scene)) {
                addMockFaq(faqList, "为什么我的设备估价这么低?", scene, datasource);
                addMockFaq(faqList, "如何确保我的数据安全?", scene, datasource);
                addMockFaq(faqList, "设备有磕碰影响回收吗?", scene, datasource);
                addMockFaq(faqList, "可以同时回收多台设备吗?", scene, datasource);
                addMockFaq(faqList, "回收款多久到账?", scene, datasource);
                addMockFaq(faqList, "有哪些支付方式?", scene, datasource);
            } else if ("估价成功页面".equals(scene)) {
                addMockFaq(faqList, "如何查看我的估价详情?", scene, datasource);
                addMockFaq(faqList, "预约上门回收需要准备什么?", scene, datasource);
                addMockFaq(faqList, "回收价格是否会变动?", scene, datasource);
                addMockFaq(faqList, "回收员什么时候能到?", scene, datasource);
                addMockFaq(faqList, "支付方式有哪些?", scene, datasource);
                addMockFaq(faqList, "我的设备信息填错了怎么办?", scene, datasource);
            } else if ("订单详情页面".equals(scene)) {
                addMockFaq(faqList, "如何取消订单?", scene, datasource);
                addMockFaq(faqList, "如何修改预约时间?", scene, datasource);
                addMockFaq(faqList, "上门费用是多少?", scene, datasource);
                addMockFaq(faqList, "如何联系回收员?", scene, datasource);
                addMockFaq(faqList, "订单状态什么时候更新?", scene, datasource);
                addMockFaq(faqList, "运费怎么计算?", scene, datasource);
            }
        } 
        // 小智二手商城
        else if ("小智二手商城".equals(datasource)) {
            if ("商城首页区域".equals(scene)) {
                addMockFaq(faqList, "如何搜索商品?", scene, datasource);
                addMockFaq(faqList, "商品质量如何保障?", scene, datasource);
                addMockFaq(faqList, "有什么优惠活动?", scene, datasource);
                addMockFaq(faqList, "如何筛选品牌型号?", scene, datasource);
                addMockFaq(faqList, "商品售后怎么处理?", scene, datasource);
                addMockFaq(faqList, "如何查看历史浏览记录?", scene, datasource);
            } else if ("个人中心".equals(scene)) {
                addMockFaq(faqList, "如何修改个人信息?", scene, datasource);
                addMockFaq(faqList, "积分如何使用?", scene, datasource);
                addMockFaq(faqList, "如何查看我的订单?", scene, datasource);
                addMockFaq(faqList, "如何管理我的收货地址?", scene, datasource);
                addMockFaq(faqList, "如何查看我的优惠券?", scene, datasource);
                addMockFaq(faqList, "账号安全问题怎么设置?", scene, datasource);
            } else if ("商品详情页面".equals(scene)) {
                addMockFaq(faqList, "如何查看商品的具体参数?", scene, datasource);
                addMockFaq(faqList, "商品质检报告在哪里?", scene, datasource);
                addMockFaq(faqList, "可以分期付款吗?", scene, datasource);
                addMockFaq(faqList, "如何查看用户评价?", scene, datasource);
                addMockFaq(faqList, "运费是多少?", scene, datasource);
                addMockFaq(faqList, "如何加入购物车?", scene, datasource);
            } else if ("订单详情".equals(scene)) {
                addMockFaq(faqList, "如何申请退款?", scene, datasource);
                addMockFaq(faqList, "发货时间是什么时候?", scene, datasource);
                addMockFaq(faqList, "如何修改收货地址?", scene, datasource);
                addMockFaq(faqList, "快递信息在哪里查看?", scene, datasource);
                addMockFaq(faqList, "如何申请售后?", scene, datasource);
                addMockFaq(faqList, "如何评价已收到的商品?", scene, datasource);
            }
        } 
        // 小智集市
        else if ("小智集市".equals(datasource)) {
            if ("专属客服".equals(scene)) {
                addMockFaq(faqList, "如何在集市发布商品?", scene, datasource);
                addMockFaq(faqList, "商品如何定价更合理?", scene, datasource);
                addMockFaq(faqList, "发布商品需要哪些材料?", scene, datasource);
                addMockFaq(faqList, "如何提高商品曝光?", scene, datasource);
                addMockFaq(faqList, "佣金比例是多少?", scene, datasource);
                addMockFaq(faqList, "平台审核需要多久?", scene, datasource);
            } else if ("订单详情（我购买的）".equals(scene)) {
                addMockFaq(faqList, "如何联系卖家?", scene, datasource);
                addMockFaq(faqList, "商品质量有问题怎么办?", scene, datasource);
                addMockFaq(faqList, "收到的商品与描述不符怎么办?", scene, datasource);
                addMockFaq(faqList, "如何确认收货?", scene, datasource);
                addMockFaq(faqList, "退款流程是怎样的?", scene, datasource);
                addMockFaq(faqList, "如何查看物流信息?", scene, datasource);
            } else if ("订单详情（我卖出的）".equals(scene)) {
                addMockFaq(faqList, "如何发货?", scene, datasource);
                addMockFaq(faqList, "何时能收到货款?", scene, datasource);
                addMockFaq(faqList, "买家申请退款怎么处理?", scene, datasource);
                addMockFaq(faqList, "如何添加物流信息?", scene, datasource);
                addMockFaq(faqList, "如何与买家沟通?", scene, datasource);
                addMockFaq(faqList, "订单超时未发货会怎样?", scene, datasource);
            }
        } 
        // 工程师
        else if ("工程师".equals(datasource)) {
            if ("我的客服".equals(scene)) {
                addMockFaq(faqList, "如何申请成为平台工程师?", scene, datasource);
                addMockFaq(faqList, "工程师服务范围有哪些?", scene, datasource);
                addMockFaq(faqList, "维修报价怎么计算?", scene, datasource);
                addMockFaq(faqList, "如何接单?", scene, datasource);
                addMockFaq(faqList, "工程师认证需要什么材料?", scene, datasource);
                addMockFaq(faqList, "提现周期是多久?", scene, datasource);
            }
        } 
        // 默认情况
        else {
            // 默认通用问题
            addMockFaq(faqList, "你好", scene, datasource);
            addMockFaq(faqList, "维修报价怎么计算?", scene, datasource);
            addMockFaq(faqList, "怎么申请退款怎么处理?", scene, datasource);
            addMockFaq(faqList, "有哪些支付方式?", scene, datasource);
            addMockFaq(faqList, "如何确认收货?", scene, datasource);
            addMockFaq(faqList, "提现周期是多久?", scene, datasource);
        }
        
        return faqList;
    }
    
    /**
     * 添加模拟FAQ数据到列表中
     * 
     * @param faqList FAQ列表
     * @param content 问题内容
     * @param scene 场景
     * @param datasource 数据源
     */
    private void addMockFaq(List<Map<String, Object>> faqList, String content, String scene, String datasource) {
        Map<String, Object> faq = new HashMap<>();
        faq.put("content", content);
        faq.put("scene", scene);
        faq.put("datasource", datasource);
        faq.put("num", new Random().nextInt(50) + 1); // 模拟问题出现次数
        faqList.add(faq);
    }
} 