package com.kefang.service.impl;

import com.kefang.entity.Evaluation;
import com.kefang.mapper.EvaluationMapper;
import com.kefang.service.EvaluationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EvaluationServiceImpl implements EvaluationService {

    @Autowired
    private EvaluationMapper evaluationMapper;

    @Override
    public boolean addEvaluation(Evaluation evaluation) {
        return evaluationMapper.insert(evaluation) > 0;
    }

    @Override
    public Evaluation getEvaluationById(Long id) {
        return evaluationMapper.selectById(id);
    }

    @Override
    public Evaluation getEvaluationBySessionId(Long sessionId) {
        return evaluationMapper.selectBySessionId(sessionId);
    }

    @Override
    public List<Evaluation> getEvaluationsByUserId(Long userId) {
        return evaluationMapper.selectByUserId(userId);
    }

    @Override
    public List<Evaluation> getAllEvaluations() {
        return evaluationMapper.selectAll();
    }
} 