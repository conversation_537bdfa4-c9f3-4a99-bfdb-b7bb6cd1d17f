package com.kefang.service.impl;

import com.kefang.entity.ChatSession;
import com.kefang.entity.User;
import com.kefang.mapper.ChatMessageMapper;
import com.kefang.mapper.ChatSessionMapper;
import com.kefang.mapper.UserMapper;
import com.kefang.service.ChatService;
import com.kefang.vo.WaitingSessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 聊天服务实现类
 */
@Service
public class ChatServiceImpl implements ChatService {
    
    @Autowired
    private ChatSessionMapper chatSessionMapper;
    
    @Autowired
    private ChatMessageMapper chatMessageMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    /**
     * 获取待处理会话列表
     * @return 待处理会话列表
     */
    @Override
    public List<WaitingSessionVO> getWaitingSessions() {
        // 获取所有状态为0（排队等待）的会话
        List<ChatSession> waitingSessions = chatSessionMapper.getWaitingSessions();
        List<WaitingSessionVO> result = new ArrayList<>();
        
        for (ChatSession session : waitingSessions) {
            WaitingSessionVO vo = new WaitingSessionVO();
            vo.setId(session.getId());
            vo.setUserId(session.getUserId());
            
            // 获取用户信息
            User user = userMapper.selectById(session.getUserId());
            if (user != null) {
                vo.setUserNickname(user.getNickname());
                vo.setUserAvatar(user.getAvatar());
                vo.setVipLevel(user.getVipLevel());
            }
            
            vo.setStartTime(session.getStartTime());
            
            // 获取最近一条消息
            String lastMessage = chatMessageMapper.getLastMessageContent(session.getId());
            vo.setLastMessage(lastMessage);
            
            result.add(vo);
        }
        
        return result;
    }
    
    /**
     * 接受会话
     * @param sessionId 会话ID
     * @param agentId 客服ID
     * @return 会话对象
     */
    @Override
    @Transactional
    public ChatSession acceptSession(Long sessionId, Long agentId) {
        // 获取会话
        ChatSession session = chatSessionMapper.selectById(sessionId);
        
        if (session != null && session.getStatus() == 0) { // 状态为排队中
            // 更新会话状态
            session.setAgentId(agentId);
            session.setStatus(1); // 1: 人工会话中
            
            chatSessionMapper.updateById(session);
            return session;
        }
        
        return null;
    }
} 