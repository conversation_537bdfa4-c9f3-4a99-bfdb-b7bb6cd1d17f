package com.kefang.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kefang.entity.ChatSession;
import com.kefang.entity.ChatMessage;
import com.kefang.dto.WebSocketMessage;
import com.kefang.vo.PageResult;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 聊天会话服务接口
 */
public interface ChatSessionService extends IService<ChatSession> {

    /**
     * 创建会话
     * @param userId 用户ID
     * @param channel 数据源渠道
     * @return 会话信息
     */
    ChatSession createSession(Long userId, String channel, String datasource,String collection_name, String scene);
    
    /**
     * 分配客服
     * @param sessionId 会话ID
     * @return 分配结果
     */
    boolean assignAgent(Long sessionId);
    
    /**
     * 手动分配客服
     * @param sessionId 会话ID
     * @param agentId 客服ID
     * @return 分配结果
     */
    boolean assignAgentManually(Long sessionId, Long agentId);
    
    /**
     * 客服接入会话
     * @param sessionId 会话ID
     * @param agentId 客服ID
     * @return 接入结果
     */
    boolean acceptSession(Long sessionId, Long agentId);
    
    /**
     * 关闭会话
     * @param id 会话ID
     * @param closedBy 关闭方（0用户 1客服）
     * @return 操作结果
     */
    boolean closeSession(Long id, Integer closedBy);
    
    /**
     * 根据ID查询会话
     * @param id 会话ID
     * @return 会话信息
     */
    ChatSession getSessionById(Long id);
    
    /**
     * 根据用户ID查询会话列表
     * @param userId 用户ID
     * @return 会话列表
     */
    List<ChatSession> getSessionsByUserId(Long userId);
    
    /**
     * 根据客服ID查询会话列表
     * @param agentId 客服ID
     * @return 会话列表
     */
    List<ChatSession> getSessionsByAgentId(Long agentId);
    
    /**
     * 查询客服正在处理的会话
     * @param agentId 客服ID
     * @return 会话列表
     */
    List<ChatSession> getActiveSessionsByAgentId(Long agentId);
    
    /**
     * 查询等待分配客服的会话
     * @return 会话列表
     */
    List<ChatSession> getQueueSessions();
    
    /**
     * 创建与AI客服的新会话
     * @param userId 用户ID
     * @param channel 数据源渠道
     * @return 会话信息
     */
    ChatSession createSessionWithAI(Long userId, String channel, String datasource, String collectionName, String scene);
    
    /**
     * 发送消息
     * @param message 消息内容
     * @return 消息对象
     */
    ChatMessage sendMessage(WebSocketMessage message);
    
    /**
     * 转接到人工客服
     * @param sessionId 会话ID
     * @return 转接结果
     */
    boolean transferToHumanAgent(Long sessionId);
    
    /**
     * 用户添加会话评价
     * @param sessionId 会话ID
     * @param satisfactionLevel 满意度评分(1-5星)
     * @param feedbackContent 评价内容
     * @param isSolved 问题是否解决(0-未解决，1-已解决)
     * @param userSuggestion 用户建议内容
     * @return 操作结果
     */
    boolean addEvaluation(Long sessionId, Integer satisfactionLevel, String feedbackContent, Integer isSolved, String userSuggestion);
    
    /**
     * 客服添加解决方案描述
     * @param sessionId 会话ID
     * @param solutionDescription 解决方案描述
     * @param isSolved 问题是否解决(0-未解决，1-已解决)
     * @return 操作结果
     */
    boolean addSolutionDescription(Long sessionId, String solutionDescription, Integer isSolved);
    
    /**
     * 查询已解决的会话列表
     * @return 会话列表
     */
    List<ChatSession> getSolvedSessions();
    
    /**
     * 根据满意度等级获取会话列表
     */
    List<ChatSession> getSessionsBySatisfactionLevel(Integer level);
    
    /**
     * 根据过滤条件获取会话列表
     */
    List<ChatSession> getSessionsByFilters(Map<String, Object> filters);
    
    /**
     * 获取消息类型和数据来源统计数据
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 统计数据，包含senderTypeStats和datasourceStats
     */
    Map<String, Object> getMessageTypeAndSourceStats(String startDate, String endDate);
    
    /**
     * 根据客服ID查询会话列表，支持多条件过滤
     * @param agentId 客服ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param agentType 客服类型
     * @param status 会话状态
     * @param isSolved 问题是否解决
     * @param satisfactionLevel 满意度评分
     * @param datasource 数据源
     * @param collectionName 知识库集合名称
     * @param scene 对话应用场景
     * @param page 页码
     * @param size 每页大小
     * @return 分页结果
     */
    PageResult<ChatSession> getSessionsByAgentIdWithFilters(
            String type,
            Long agentId, 
            String startDate, 
            String endDate, 
            Integer agentType, 
            Integer status, 
            Integer isSolved, 
            Integer satisfactionLevel,
            String datasource,
            String collectionName,
            String scene,
            Integer page,
            Integer size);
            
    /**
     * 判断会话是否为AI客服模式
     * @param sessionId 会话ID
     * @return 布尔值，true表示是AI客服会话，false表示是人工客服会话
     */
    boolean isAISession(Long sessionId);

    /**
     * 根据客服ID列表查询会话列表，支持多条件过滤
     * @param agentId
     * @param startDate
     * @param endDate
     * @param agentType
     * @param status
     * @param isSolved
     * @param satisfactionLevel
     * @param page
     * @param size
     * @return
     */
    PageResult<ChatSession> getSessionsByAgentIdList(Long agentId, String startDate, String endDate, Integer agentType, Integer status, Integer isSolved, Integer satisfactionLevel, Integer page, Integer size);
    
    /**
     * 获取数据源下拉选项
     * @return 包含数据源名称和数量的列表
     */
    List<Map<String, Object>> getDatasourceOptions();
    
    /**
     * 获取知识库集合下拉选项
     * @return 包含知识库集合名称和数量的列表
     */
    List<Map<String, Object>> getCollectionOptions();
    
    /**
     * 获取对话场景下拉选项
     * @return 包含对话场景名称和数量的列表
     */
    List<Map<String, Object>> getSceneOptions();
    
    /**
     * 获取渠道来源下拉选项
     * @return 包含渠道来源名称和数量的列表
     */
    List<Map<String, Object>> getChannelOptions();

    /**
     * 统计客服的会话总数
     *
     * @param agentId 客服ID
     * @return 会话总数
     */
    int countSessionsByAgent(Long agentId);

    /**
     * 统计指定时间范围内客服的会话数
     *
     * @param agentId 客服ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 会话数
     */
    int countSessionsByAgentAndTimeRange(Long agentId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取客服的平均对话时间
     * 
     * @deprecated 请使用 {@link com.kefang.service.ChatCallManualRecordService#getAvgChatDurationByAgent} 替代，
     *             它使用chat_call_manual_records表的serviceDuration字段提供更准确的计算
     * @param agentId 客服ID
     * @return 平均对话时间（秒）
     */
    @Deprecated
    Double getAvgChatDurationByAgent(Long agentId);

    /**
     * 获取指定时间范围内客服的平均对话时间
     * 
     * @deprecated 请使用 {@link com.kefang.service.ChatCallManualRecordService#getAvgChatDurationByAgentAndTimeRange} 替代，
     *             它使用chat_call_manual_records表的serviceDuration字段提供更准确的计算
     * @param agentId 客服ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 平均对话时间（秒）
     */
    @Deprecated
    Double getAvgChatDurationByAgentAndTimeRange(Long agentId, LocalDateTime startTime, LocalDateTime endTime);
}