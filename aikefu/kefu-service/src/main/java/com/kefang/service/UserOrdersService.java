package com.kefang.service;

import com.kefang.entity.UserOrders;

public interface UserOrdersService {

    /**
     * 根据手机号查询用户订单信息
     * @param phone 手机号
     * @return 用户订单信息
     */
    UserOrders getUserOrdersByPhone(String phone);
    
    /**
     * 保存或更新用户订单信息
     * 如果用户订单信息已存在，则更新；否则，插入新记录
     * @param userOrders 用户订单信息
     * @return 操作结果
     */
    boolean saveOrUpdateUserOrders(UserOrders userOrders);

    UserOrders getUserOrdersByID(Long senderId);
} 