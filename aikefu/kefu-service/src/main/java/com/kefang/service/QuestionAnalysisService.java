package com.kefang.service;

import java.util.List;
import java.util.Map;

/**
 * 热门问题分析服务接口
 */
public interface QuestionAnalysisService {
    
    /**
     * 获取问题分类统计
     * 
     * @param scene 场景
     * @param datasource 数据源
     * @param startDate 开始日期
     * @param endDate 结束日期 
     * @param limit 限制数量
     * @return 分类统计结果
     */
    Map<String, Object> getCategoryStats(String scene, String datasource, 
                                        String startDate, String endDate, Integer limit);
    
    /**
     * 获取热门问题列表
     * 
     * @param scene 场景
     * @param datasource 数据源
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 热门问题列表
     */
    List<Map<String, Object>> getHotQuestions(String scene, String datasource, 
                                             String startDate, String endDate, Integer limit);
                                             
    /**
     * 按时间维度获取问题趋势
     * 
     * @param timeUnit 时间单位: day, week, month
     * @param scene 场景
     * @param datasource 数据源
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 问题趋势数据
     */
    List<Map<String, Object>> getQuestionTrend(String timeUnit, String scene, 
                                              String datasource, String startDate, String endDate);
                                              
    /**
     * 获取问题关键词统计
     * 
     * @param scene 场景
     * @param datasource 数据源
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 关键词统计列表
     */
    List<Map<String, Object>> getKeywordStats(String scene, String datasource, 
                                             String startDate, String endDate, Integer limit);
} 