package com.kefang.service;

import com.kefang.entity.User;
import com.kefang.vo.PageResult;
import java.util.List;
import java.util.Map;

public interface UserService {

    /**
     * 用户注册
     * @param user 用户信息
     * @return 注册结果
     */
    boolean register(User user);
    
    /**
     * 用户登录
     * @param phone 手机号
     * @param password 密码
     * @return 用户信息
     */
    User login(String phone, String password);
    
    /**
     * 根据ID查询用户
     * @param id 用户ID
     * @return 用户信息
     */
    User getUserById(Long id);
    
    /**
     * 根据手机号查询用户
     * @param phone 手机号
     * @return 用户信息
     */
    User getUserByPhone(String phone);
    
    /**
     * 更新用户信息
     * @param user 用户信息
     * @return 更新结果
     */
    boolean updateUser(User user);
    
    /**
     * 查询所有用户
     * @return 用户列表
     */
    List<User> getAllUsers();
    
    /**
     * 分页查询用户列表
     * @param params 查询参数，包括pageNum(页码)、pageSize(每页记录数)、userType(用户类型)、
     *               keyword(关键词)、startDate(注册开始时间)、endDate(注册结束时间)
     * @return 分页结果
     */
    PageResult<User> getUsersByPage(Map<String, Object> params);
} 