package com.kefang.service.impl;

import com.kefang.config.AIConfig;
import com.kefang.dto.AIRequestDto;
import com.kefang.dto.AIResponseDto;
import com.kefang.service.AIService;
import com.mysql.cj.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

/**
 * AI服务实现类
 */
@Service
public class AIServiceImpl implements AIService {
    
    private static final Logger logger = LoggerFactory.getLogger(AIServiceImpl.class);

    @Autowired
    private AIConfig aiConfig;
    
    @Autowired
    private RestTemplate restTemplate;

    /**
     * 调用AI服务获取回复
     */
    @Override
    public String getAIResponse(String messages, List<Map<String, String>> history, String collectionName) {
        try {
            logger.info("发送AI请求: {}, history size: {}", messages, history);
            // 判断messages是否为空
            if (StringUtils.isNullOrEmpty(messages)) {
                logger.info("messages为空");
                return "用户输入为空";
            }
            // 构建请求
            AIRequestDto request = new AIRequestDto();
            request.setMessages(messages);
            request.setHistory(history);
            request.setModel("123");  // 设置默认模型
            request.setStream(false);
            if (StringUtils.isNullOrEmpty(collectionName)){
                request.setCollection_name("information");
            }else {
                request.setCollection_name(collectionName);
            }

            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<AIRequestDto> entity = new HttpEntity<>(request, headers);
            
            // 发送请求
            AIResponseDto response = restTemplate.postForObject(
                aiConfig.getServiceUrl() + "/chat", 
                entity, 
                AIResponseDto.class
            );
            String result = response.getResponse();
            if (response != null) {
                logger.info("AI回复內容: {}", result);
                logger.info("AI回复成功, 耗时: {}s", response.getTimeCost());
                // 使用正则表达式删除 <think> 标签及其内部的所有内容
                String cleanedResult = result.replaceAll("(?s)<think>.*?</think>\\s*", "");
                return cleanedResult;
            } else {
                logger.error("AI回复为空");
                return "抱歉，我遇到了一些问题，无法正常回复。您可以尝试重新提问，或者输入\"转人工\"接入人工客服。";
            }
        } catch (Exception e) {
            logger.error("AI服务调用异常", e);
            return "系统繁忙，无法处理您的请求。您可以输入\"转人工\"接入人工客服。";
        }
    }
} 