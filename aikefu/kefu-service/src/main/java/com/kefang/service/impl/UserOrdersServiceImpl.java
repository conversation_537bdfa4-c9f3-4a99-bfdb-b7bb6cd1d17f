package com.kefang.service.impl;

import com.kefang.entity.UserOrders;
import com.kefang.mapper.UserOrdersMapper;
import com.kefang.service.UserOrdersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * UserOrdersService实现类
 */
@Service
public class UserOrdersServiceImpl implements UserOrdersService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserOrdersServiceImpl.class);
    
    @Autowired
    private UserOrdersMapper userOrdersMapper;

    @Override
    public UserOrders getUserOrdersByPhone(String phone) {
        try {
            return userOrdersMapper.selectByPhone(phone);
        } catch (Exception e) {
            logger.error("根据手机号查询用户订单信息失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean saveOrUpdateUserOrders(UserOrders userOrders) {
        try {
            if (userOrders == null || userOrders.getPhone() == null) {
                return false;
            }
            
            // 查询用户订单信息是否存在
            UserOrders existingUserOrders = userOrdersMapper.selectByPhone(userOrders.getPhone());
            
            if (existingUserOrders != null) {
                // 如果存在，则更新
                return userOrdersMapper.update(userOrders) > 0;
            } else {
                // 如果不存在，则插入
                return userOrdersMapper.insert(userOrders) > 0;
            }
        } catch (Exception e) {
            logger.error("保存或更新用户订单信息失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据ID查询用户订单信息
     * @param senderId
     * @return
     */
    @Override
    public UserOrders getUserOrdersByID(Long senderId) {
        try {
            return userOrdersMapper.selectByID(senderId);
        } catch (Exception e) {
            logger.error("根据ID查询用户信息失败: {}", e.getMessage(), e);
            return null;
        }
    }
} 