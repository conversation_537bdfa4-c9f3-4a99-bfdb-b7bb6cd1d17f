package com.kefang.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kefang.dto.SsoApiTokenDTO;
import com.kefang.dto.SsoUserLoginDTO;
import com.kefang.service.SsoService;
import com.kefang.utils.HttpClientUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * SSO服务实现类
 */
@Service
public class SsoServiceImpl implements SsoService {
    
    private static final Logger logger = LoggerFactory.getLogger(SsoServiceImpl.class);
    
    @Value("${sso.api.url}")
    private String ssoApiUrl;
    
    @Value("${sso.user.login-url}")
    private String ssoUserLoginUrl;
    
    @Autowired
    private HttpClientUtil httpClientUtil;
    
    @Override
    public SsoApiTokenDTO getApiToken() {
        try {
            // 调用SSO API获取Token和私钥
            JSONObject response = httpClientUtil.get(ssoApiUrl);
            
            // 将JSON响应转换为DTO对象
            return JSON.parseObject(response.toJSONString(), SsoApiTokenDTO.class);
        } catch (Exception e) {
            // 异常处理
            logger.error("获取SSO API Token失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取SSO API Token失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public SsoUserLoginDTO.Response userLogin(String mobile, String password, String privateKey, String apiToken) {
        try {
            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("privateKey", privateKey);
            headers.put("apitoken", apiToken);
            
            // 构建请求体
            SsoUserLoginDTO requestBody = new SsoUserLoginDTO();
            requestBody.setMobile(mobile);
            requestBody.setPassword(password);
            
            // 发送POST请求
            JSONObject response = httpClientUtil.post(ssoUserLoginUrl, requestBody, headers);
            logger.debug("SSO用户登录响应: {}", response.toJSONString());
            
            // 将JSON响应转换为DTO对象
            return JSON.parseObject(response.toJSONString(), SsoUserLoginDTO.Response.class);
        } catch (Exception e) {
            // 异常处理
            logger.error("SSO用户登录失败: {}", e.getMessage(), e);
            throw new RuntimeException("SSO用户登录失败: " + e.getMessage(), e);
        }
    }
} 