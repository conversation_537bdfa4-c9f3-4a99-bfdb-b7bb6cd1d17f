package com.kefang.service;

import com.kefang.entity.ConnectEntiy;
import com.kefang.vo.Result;
import java.util.List;
import java.util.Map;

public interface ConnectService {

    // 在线客服会话接口
    String onLine(ConnectEntiy connectEntiy);
    
    // 微信群消息处理接口
    Result<String> handleGroupMessage(ConnectEntiy connectEntiy);
    
    // 常见问题接口
    List<Map<String, Object>> getFaq(String scene, String datasource);
}
