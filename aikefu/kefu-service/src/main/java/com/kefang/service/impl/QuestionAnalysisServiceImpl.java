package com.kefang.service.impl;

import com.kefang.config.AIConfig;
import com.kefang.dto.FAQClusterRequestDto;
import com.kefang.dto.FAQClusterResponseDto;
import com.kefang.dto.FAQItemDto;
import com.kefang.mapper.ChatMessageMapper;
import com.kefang.service.QuestionAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 热门问题分析服务实现
 */
@Slf4j
@Service
public class QuestionAnalysisServiceImpl implements QuestionAnalysisService {

    @Autowired
    private ChatMessageMapper chatMessageMapper;
    
    @Autowired
    private AIConfig aiConfig;
    
    @Autowired
    private RestTemplate restTemplate;

    @Override
    public Map<String, Object> getCategoryStats(String scene, String datasource, 
                                               String startDate, String endDate, Integer limit) {
        // 获取场景统计
        List<Map<String, Object>> sceneStats = chatMessageMapper.getQuestionStatsByScene(
                scene, datasource, startDate, endDate);
        
        // 获取数据源统计
        List<Map<String, Object>> datasourceStats = chatMessageMapper.getQuestionStatsByDatasource(
                scene, datasource, startDate, endDate);
        
        // 组装结果
        Map<String, Object> result = new HashMap<>();
        result.put("sceneStats", sceneStats);
        result.put("datasourceStats", datasourceStats);
        
        // 获取问题总数
        Integer totalQuestions = chatMessageMapper.countTotalQuestions(
                scene, datasource, startDate, endDate);
        result.put("totalQuestions", totalQuestions);
        
        // 获取独立问题数
        Integer uniqueQuestions = chatMessageMapper.countUniqueQuestions(
                scene, datasource, startDate, endDate);
        result.put("uniqueQuestions", uniqueQuestions);
        
        return result;
    }

    @Override
    public List<Map<String, Object>> getHotQuestions(String scene, String datasource, 
                                                    String startDate, String endDate, Integer limit) {
        // 构建日期参数
        Date start = null;
        Date end = null;
        
        if (startDate != null && !startDate.isEmpty()) {
            try {
                start = new SimpleDateFormat("yyyy-MM-dd").parse(startDate);
            } catch (Exception e) {
                log.error("解析开始日期异常", e);
            }
        }
        
        if (endDate != null && !endDate.isEmpty()) {
            try {
                end = new SimpleDateFormat("yyyy-MM-dd").parse(endDate);
                // 设置结束时间为当天最后一秒
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(end);
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                end = calendar.getTime();
            } catch (Exception e) {
                log.error("解析结束日期异常", e);
            }
        }
        
        return chatMessageMapper.getHotQuestions(scene, datasource, start, end, limit);
    }

    @Override
    public List<Map<String, Object>> getQuestionTrend(String timeUnit, String scene, 
                                                    String datasource, String startDate, String endDate) {
        // 根据时间单位选择不同的统计查询
        if ("day".equals(timeUnit)) {
            return chatMessageMapper.getDailyQuestionTrend(scene, datasource, startDate, endDate);
        } else if ("week".equals(timeUnit)) {
            return chatMessageMapper.getWeeklyQuestionTrend(scene, datasource, startDate, endDate);
        } else if ("month".equals(timeUnit)) {
            return chatMessageMapper.getMonthlyQuestionTrend(scene, datasource, startDate, endDate);
        } else {
            // 默认按天统计
            return chatMessageMapper.getDailyQuestionTrend(scene, datasource, startDate, endDate);
        }
    }

    @Override
    public List<Map<String, Object>> getKeywordStats(String scene, String datasource, 
                                                   String startDate, String endDate, Integer limit) {
        // 获取原始关键词统计数据
        List<Map<String, Object>> keywords = chatMessageMapper.getContentKeywordStats(scene, datasource, startDate, endDate, limit);
        // 添加数据验证和格式化
        if (keywords == null) {
            log.warn("关键词统计查询返回null");
            return new ArrayList<>();
        }
        
        // 过滤和标准化数据
        List<Map<String, Object>> validKeywords = validateKeywords(keywords);
        
        // 如果有效数据超过5条，则调用FAQ聚类API
//        if (validKeywords.size() > 5) {
//            try {
//                // 转换为FAQ聚类API请求格式
//                FAQClusterRequestDto requestDto = convertToFAQRequest(validKeywords);
//
//                // 调用FAQ聚类API
//                List<Map<String, Object>> clusterResults = callFAQClusterApi(requestDto);
//
//                // 如果聚类成功并有结果，使用聚类结果
//                if (clusterResults != null && !clusterResults.isEmpty()) {
//                    log.info("FAQ聚类成功，原始问题数: {}, 聚类后问题数: {}", validKeywords.size(), clusterResults.size());
//                    System.out.println(clusterResults);
//                    return clusterResults;
//                } else {
//                    log.warn("FAQ聚类未返回结果，使用原始数据");
//                }
//            } catch (Exception e) {
//                log.error("FAQ聚类调用失败，使用原始数据", e);
//            }
//        } else {
//            log.info("有效问题数量不足5条，不执行聚类");
//        }
        
        // 如果聚类失败或数据不足，返回原始数据
        return validKeywords;
    }
    
    /**
     * 验证和标准化关键词数据
     */
    private List<Map<String, Object>> validateKeywords(List<Map<String, Object>> keywords) {
        List<Map<String, Object>> validKeywords = new ArrayList<>();
        for (Map<String, Object> item : keywords) {
            if (item != null && item.containsKey("keyword") && item.get("keyword") != null) {
                // 如果没有count字段或count为null，设置默认值为1
                if (!item.containsKey("count") || item.get("count") == null) {
                    item.put("count", 1);
                }
                validKeywords.add(item);
            }
        }
        
        log.info("过滤后的有效关键词数: {}", validKeywords.size());
        
        // 如果没有有效关键词，添加一个默认项
        if (validKeywords.isEmpty()) {
            Map<String, Object> defaultItem = new HashMap<>();
            defaultItem.put("keyword", "暂无数据");
            defaultItem.put("count", 1);
            validKeywords.add(defaultItem);
        }
        
        return validKeywords;
    }
    
    /**
     * 将原始数据转换为FAQ聚类API请求格式
     */
    private FAQClusterRequestDto convertToFAQRequest(List<Map<String, Object>> keywords) {
        FAQClusterRequestDto requestDto = new FAQClusterRequestDto();
        List<FAQItemDto> dataItems = new ArrayList<>();
        
        // 增加日志以便于调试
        log.info("开始文本预处理和FAQ请求构建，原始问题数: {}", keywords.size());
        
        for (Map<String, Object> item : keywords) {
            // 增强文本预处理，移除换行符和多余空白
            String originalKeyword = (String) item.get("keyword");
            String cleanedKeyword = originalKeyword
                .trim()                        // 移除前后空白
                .replaceAll("\\r\\n|\\r|\\n", "") // 移除换行符
                .replaceAll("\\s+", " ");      // 多个空格合并为一个
            
            // 如果清理后文本发生变化，记录日志
            if (!cleanedKeyword.equals(originalKeyword)) {
                log.debug("文本清理: '{}' -> '{}'", originalKeyword, cleanedKeyword);
            }
            
            int count = item.get("count") instanceof Integer 
                ? (Integer) item.get("count") 
                : Integer.parseInt(item.get("count").toString());
            
            dataItems.add(new FAQItemDto(count, cleanedKeyword));
        }
        
        requestDto.setData(dataItems);
        
        // 根据数据量设置不同的相似度阈值
        // 注意：现在聚类算法会根据相似度阈值自动决定聚类数量，不再使用n_clusters参数
        if (keywords.size() > 50) {
            // 数据量很大时，提高相似度阈值，要求更严格的匹配
            requestDto.setSimilarity_threshold(0.92);
            log.info("大量数据({}): 设置相似度阈值=0.92", keywords.size());
        } else if (keywords.size() > 20) {
            // 数据量较大
            requestDto.setSimilarity_threshold(0.90);
            log.info("较多数据({}): 设置相似度阈值=0.90", keywords.size());
        } else if (keywords.size() > 10) {
            // 数据量适中
            requestDto.setSimilarity_threshold(0.88);
            log.info("中等数据({}): 设置相似度阈值=0.88", keywords.size());
        } else {
            // 数据量较小
            requestDto.setSimilarity_threshold(0.85);
            log.info("少量数据({}): 设置相似度阈值=0.85", keywords.size());
        }
        
        log.info("FAQ聚类请求构建完成，问题数量: {}，相似度阈值: {}", 
                requestDto.getData().size(), requestDto.getSimilarity_threshold());
        return requestDto;
    }
    
    /**
     * 调用FAQ聚类API
     */
    private List<Map<String, Object>> callFAQClusterApi(FAQClusterRequestDto requestDto) {
        String apiUrl = aiConfig.getServiceUrl() + "/faq/cluster";
        log.info("调用FAQ聚类API: {}", apiUrl);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<FAQClusterRequestDto> entity = new HttpEntity<>(requestDto, headers);
        
        try {
            // 调用API前记录请求参数
            log.debug("FAQ聚类请求参数: 聚类数={}, 相似度阈值={}, 数据量={}",
                    requestDto.getN_clusters(), requestDto.getSimilarity_threshold(), requestDto.getData().size());
            
            // 调用API
            FAQClusterResponseDto response = restTemplate.postForObject(
                apiUrl, 
                entity, 
                FAQClusterResponseDto.class
            );
            
            if (response == null || response.getResults() == null) {
                log.warn("FAQ聚类API返回空结果");
                return null;
            }
            
            // 记录聚类结果的详细信息
            log.info("FAQ聚类成功, 耗时: {}s, 结果数量: {}", 
                    response.getTimeCost(), 
                    response.getResults().size());
            
            // 转换结果为前端所需格式
            List<Map<String, Object>> results = new ArrayList<>();
            for (FAQItemDto item : response.getResults()) {
                Map<String, Object> resultItem = new HashMap<>();
                resultItem.put("keyword", item.getKeyword());
                resultItem.put("count", item.getCount());
                results.add(resultItem);
                
                // 记录每个聚类结果
                log.debug("聚类结果项: keyword='{}', count={}", item.getKeyword(), item.getCount());
            }
            
            // 记录完整结果
            if (log.isDebugEnabled()) {
                log.debug("FAQ聚类完整结果: {}", results);
            } else {
                // 即使不是debug级别，也记录一个简洁的摘要
                StringBuilder summary = new StringBuilder("FAQ聚类结果摘要: ");
                for (int i = 0; i < Math.min(3, results.size()); i++) {
                    Map<String, Object> item = results.get(i);
                    summary.append("[").append(item.get("keyword")).append(":").append(item.get("count")).append("] ");
                }
                if (results.size() > 3) {
                    summary.append("... 等共").append(results.size()).append("项");
                }
                log.info(summary.toString());
            }
            
            return results;
        } catch (Exception e) {
            log.error("FAQ聚类API调用异常", e);
            throw e;
        }
    }
} 