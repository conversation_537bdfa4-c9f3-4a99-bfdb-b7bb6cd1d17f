package com.kefang.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.kefang.entity.ChatMessage;
import com.kefang.entity.ChatSession;
import com.kefang.entity.ConnectEntiy;
import com.kefang.entity.User;
import com.kefang.entity.Datasource;
import com.kefang.handler.MessageHandlerFactory;
import com.kefang.mapper.ChatMessageMapper;
import com.kefang.mapper.ChatSessionMapper;
import com.kefang.mapper.UserMapper;
import com.kefang.mapper.DatasourceMapper;
import com.kefang.service.AIService;
import com.kefang.service.ChatSessionService;
import com.kefang.service.ConnectService;
import com.kefang.vo.Result;

import com.mysql.cj.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.PreDestroy;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static com.kefang.utils.StrUtils.isNumeric;
import static com.kefang.utils.StrUtils.parseStringToString;

@Service
public class ConnectServiceImpl implements ConnectService {

    @Autowired
    private AIService aiService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ChatSessionMapper chatSessionMapper;


    @Autowired
    private ChatSessionService chatSessionService;

    @Autowired
    private ChatMessageMapper chatMessageMapper;

    @Autowired
    private DatasourceMapper datasourceMapper;

    @Autowired
    private MessageHandlerFactory messageHandlerFactory;

    private static final Logger logger = LoggerFactory.getLogger(ConnectServiceImpl.class);
    
    // 创建静态单例线程池
    private static final ExecutorService executorService = Executors.newFixedThreadPool(5);

    @Override
    public String onLine(ConnectEntiy connectEntiy) {
        // 检查 session_id 是否为空或为 null
        if (StringUtils.isNullOrEmpty(connectEntiy.getSession_id())) {
            logger.info("session_id不能为空");
            return "session_id不能为空";
        }

        // 获取传入的 messages 参数
        String messages = connectEntiy.getMessages();
        // 检查 messages 是否为空或为 null
        if (StringUtils.isNullOrEmpty(messages)) {
            logger.info("messages不能为空");
            return "";
        }
        logger.info("用户输入:"+messages);


        try {
            // 获取用户 ID，如果用户不存在则创建新用户
            Long userId = getUserOrCreate(connectEntiy);

            // 查询用户的会话
            List<ChatSession> chatSessions = chatSessionMapper.selectByUserId(userId);
            Long sessionId;
            Integer status = 3;// 0排队 1人工会话中 2已关闭 3AI会话中

            if (chatSessions == null || chatSessions.isEmpty()) {
                // 如果没有会话，创建新会话
                ChatSession chatSession = chatSessionService.createSession(userId,connectEntiy.getChannel(),connectEntiy.getDatasource(),connectEntiy.getCollection_name(),connectEntiy.getScene());
                sessionId = chatSession.getId();
                status = chatSession.getStatus();
            } else {
                // 如果有会话，使用第一个会话
                sessionId = chatSessions.get(0).getId();
                status = chatSessions.get(0).getStatus();
            }
            // 异步添加客户的聊天消息到数据库
            asyncAddChatMessage(sessionId, messages, 0, 0, connectEntiy,userId+"");

            // 如果当前会话不是人工会话中，则调用 AI 智能客服接口
            if (status != 1){
                // 获取历史对话
                // List<ChatHistoryDTO> historyMessages = chatMessageMapper.selectBySessionIdHistory(sessionId);
                // List<Map<String, String>> history = new ArrayList<>();

                // 转换历史消息格式以匹配Python代码的格式
//            for (ChatHistoryDTO msg : historyMessages) {
//                Map<String, String> historyItem = new HashMap<>();
//                historyItem.put("role", msg.getRole());
//                historyItem.put("content", msg.getContent());
//                history.add(historyItem);
//            }
                // 调用 AI 智能客服接口，获取 AI 的回复
                String aiResponse = aiService.getAIResponse(messages, new ArrayList<>(), connectEntiy.getCollection_name());
                // 异步添加 AI 的回复消息到数据库 6  是ai
                asyncAddChatMessage(sessionId, aiResponse, 0, 2, connectEntiy,"6");
                // 返回 AI 的回复
                return aiResponse;
            }else {
                // 当前会话正在人工会话中
                return "";
            }



        } catch (Exception e) {
            // 记录日志或进行其他错误处理
            logger.error("系统错误", e);
            return "系统错误，请稍后再试";
        }
    }


    /**
     * 处理微信群消息
     *
     * @param connectEntiy 连接实体对象
     * @return 处理结果
     */
    @Override
    public Result<String> handleGroupMessage(ConnectEntiy connectEntiy) {
        logger.info("开始处理微信群消息，群类型: {}", connectEntiy.getGroup_type());
        logger.info("开始处理微信群消息，消息: {}", connectEntiy.getMessages());
        try {
            // 获取用户 ID，如果用户不存在则创建新用户
            Long userId = getUserOrCreate(connectEntiy);

            // 查询用户的会话
            List<ChatSession> chatSessions = chatSessionMapper.selectByUserId(userId);
            Long sessionId;

            if (chatSessions == null || chatSessions.isEmpty()) {
                // 如果没有会话，创建新会话
                ChatSession chatSession = chatSessionService.createSession(userId, connectEntiy.getChannel(),
                        connectEntiy.getDatasource(), connectEntiy.getCollection_name(), connectEntiy.getScene());
                sessionId = chatSession.getId();
            } else {
                // 如果有会话，使用第一个会话
                sessionId = chatSessions.get(0).getId();
            }

            // 异步添加客户的聊天消息到数据库
            asyncAddChatMessage(sessionId, connectEntiy.getMessages(), 0, 0, connectEntiy,userId+"");

            // 使用处理器工厂处理消息
            Result<String> handlerResult = messageHandlerFactory.handleMessage(connectEntiy);

            // 如果成功处理了消息，将处理结果也保存到数据库
            if (handlerResult != null && handlerResult.getData() != null) {
                // ai对话异步添加处理结果消息到数据库 6  是ai
                asyncAddChatMessage(sessionId, handlerResult.getData(), 0, 2, connectEntiy,"6");
//                logger.info("微信群消息处理成功: {}", handlerResult.getData());
                return handlerResult;
            }

            // 如果没有处理器能够处理该消息，返回null
            logger.info("没有找到合适的处理器处理微信群消息");
            return null;

        } catch (Exception e) {
            logger.error("处理微信群消息异常: {}", e.getMessage(), e);
            return Result.error("处理微信群消息异常，请稍后再试");
        }
    }

    /**
     * 获取用户 ID，如果用户不存在则创建新用户
     *
     * @return 用户 ID
     */
    private Long getUserOrCreate(ConnectEntiy connectEntiy) {
        // 1. 根据 session_id 查询用户信息
        String sessionId = connectEntiy.getSession_id();
        User user = userMapper.selectByPhone(sessionId);

        // 2. 检查用户是否存在
        if (user == null) {
            // 用户不存在，需要创建新用户

            // 2.1 创建一个新的 User 对象，并设置基础信息
            User userNew = new User();
            userNew.setPhone(sessionId); // 使用 session_id 作为用户的手机号

            // 2.2 处理 datasource 字段
            // 判断 datasource 是否为数字，如果不是则默认设置为 "0"
            String datasourceStr = connectEntiy.getDatasource();
            String datasource = parseStringToString(datasourceStr, datasourceStr);
            userNew.setDatasource(datasourceStr);
            userNew.setNickname(datasourceStr + sessionId);
            // 2.3 根据 datasource 查询用户来源渠道
            // 如果 datasource 不为空且是数字，则尝试从数据库中获取对应的来源渠道信息
            if (!StringUtils.isNullOrEmpty(datasource) && isNumeric(datasource)) {
                try {
                    // 将 datasource 转换为整数，并查询对应的来源渠道
                    Datasource source = datasourceMapper.selectByNumber(Integer.parseInt(datasource));
                    logger.info("source ------------- "+source);
                    if (source != null) {
                        // 如果找到来源渠道，设置用户的昵称为来源渠道名称 + session_id
                        userNew.setNickname(source.getName() +  sessionId);
                        userNew.setDatasource(source.getName());
                    }
                } catch (NumberFormatException e) {
                    // 如果 datasource 格式不正确（无法转换为整数），记录警告日志
                    logger.warn("datasource 格式错误: {}", datasource);
                }
            }

            // 2.4 设置其他字段
            userNew.setIsMerchant(connectEntiy.getIsMerchant()); // 是否是商家
            userNew.setIsTechnician(connectEntiy.getIsTechnician()); // 是否是工程师
            userNew.setUserId(sessionId); // 使用 session_id 作为用户 ID
            userNew.setPassword("123456fsghsr!"); // 默认密码设置为 "123456"
            userNew.setAvatar(connectEntiy.getAvatar()); // 用户头像
            userNew.setRemark("第三方接口注册");
            userNew.setChannel(connectEntiy.getChannel());
            userNew.setExtra1(connectEntiy.getWeixinDate());

            logger.info("userNew ------------- "+userNew.toString());
            // 2.5 插入新用户到数据库
            userMapper.insert(userNew);

            // 返回新创建用户的 ID
            return userNew.getId();
        } else {
            // 用户已存在，直接返回用户的 ID
            return user.getId();
        }
    }
    /**
     * 异步添加聊天消息到数据库
     *
     * @param sessionId  会话 ID
     * @param content    消息内容
     * @param msgType    消息类型（0 表示文本消息）
     * @param senderType 发送者类型（0 表示客户，2 表示 AI 客服）
     */
    public void asyncAddChatMessage(Long sessionId, String content, int msgType, int senderType,ConnectEntiy connectEntiy,String senderId) {
        executorService.submit(() -> {
            // 添加重试机制
            int maxRetries = 10;
            int retryCount = 0;
            boolean success = false;

            while (!success && retryCount < maxRetries) {
                try {
                    ChatMessage message = new ChatMessage();
                    message.setSessionId(sessionId);
                    message.setContent(content);
                    message.setMsgType(msgType);
                    message.setSenderType(senderType);
                    message.setIsRead(0);
                    message.setCreatedAt(new Date());
                    message.setDatasource(connectEntiy.getDatasource());
                    message.setCollectionName(connectEntiy.getCollection_name());
                    message.setScene(connectEntiy.getScene());
                    message.setChannel(connectEntiy.getChannel());
                    message.setSenderId(senderId);
                    chatMessageMapper.insert(message);
                    success = true;
                } catch (Exception e) {
                    retryCount++;
                    if (e instanceof org.springframework.dao.DeadlockLoserDataAccessException) {
                        logger.warn("检测到数据库死锁，正在进行第 {} 次重试", retryCount);
                        try {
                            Thread.sleep(100 * retryCount);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    } else {
                        logger.error("异步添加聊天消息失败", e);
                        break;
                    }
                }
            }

            if (!success) {
                logger.error("经过 {} 次重试后，添加聊天消息仍然失败", maxRetries);
            }
        });
    }

    /**
     * 添加聊天消息到数据库
     *
     * @param sessionId  会话 ID
     * @param content    消息内容
     * @param msgType    消息类型（0 表示文本消息）
     * @param senderType 发送者类型（0 表示客户，2 表示 AI 客服）
     */
    private void addChatMessage(Long sessionId, String content, int msgType, int senderType) {
        addChatMessage(sessionId, content, msgType, senderType, null);
    }

    private void addChatMessage(Long sessionId, String content, int msgType, int senderType, String scene) {
        ChatMessage message = new ChatMessage();
        message.setSessionId(sessionId);
        message.setContent(content);
        message.setMsgType(msgType);
        message.setSenderType(senderType);
        message.setIsRead(0);
        message.setCreatedAt(new Date());
        message.setScene(scene);
        chatMessageMapper.insert(message);
    }

    /**
     * 常见问题接口实现
     * 根据场景和数据源查询常见问题列表
     * 
     * @param scene 场景
     * @param datasource 数据源
     * @return 常见问题列表
     */
    @Override
    public List<Map<String, Object>> getFaq(String scene, String datasource) {
        return chatMessageMapper.selectFaqBySceneAndDatasource(scene, datasource);
    }

    /**
     * 应用关闭时优雅关闭线程池
     */
    @PreDestroy
    public void shutdown() {
        logger.info("正在关闭聊天消息处理线程池...");
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
                logger.warn("线程池未能在60秒内完成关闭，强制关闭");
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            logger.error("关闭线程池时被中断", e);
            Thread.currentThread().interrupt();
        }
        logger.info("聊天消息处理线程池已关闭");
    }





}