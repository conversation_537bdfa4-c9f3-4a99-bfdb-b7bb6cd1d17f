package com.kefang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kefang.entity.Agent;
import com.kefang.entity.ChatCallManualRecord;
import com.kefang.entity.ChatSession;
import com.kefang.entity.ChatMessage;
import com.kefang.dto.WebSocketMessage;
import com.kefang.mapper.AgentMapper;
import com.kefang.mapper.ChatCallManualRecordMapper;
import com.kefang.mapper.ChatSessionMapper;
import com.kefang.mapper.ChatMessageMapper;
import com.kefang.service.ChatSessionService;
import com.kefang.service.AIService;
import com.kefang.vo.PageResult;
import com.mysql.cj.util.StringUtils;
import com.kefang.websocket.WebSocketServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.concurrent.TimeUnit;

import java.util.*;
import java.time.LocalDateTime;

@Slf4j
@Service
public class ChatSessionServiceImpl extends ServiceImpl<ChatSessionMapper, ChatSession> implements ChatSessionService {

    @Autowired
    private ChatSessionMapper chatSessionMapper;
    
    @Autowired
    private AgentMapper agentMapper;
    
    @Autowired
    private ChatMessageMapper chatMessageMapper;

    @Autowired
    private ChatCallManualRecordMapper chatCallManualRecordMapper;
    
    @Autowired
    private AIService aiService;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public ChatSession createSession(Long userId, String channel,String datasource,String collection_name, String scene) {
        ChatSession chatSession = new ChatSession();
        chatSession.setUserId(userId);
        chatSession.setStatus(3); // 默认为AI会话中状态
        chatSession.setCurrentAgentType(2); // 默认为ai客服类型
        chatSession.setTransferRequested(0); // 默认未请求转人工
        chatSession.setChannel(channel);
        chatSession.setDatasource(datasource);
        chatSession.setCollectionName(collection_name);
        chatSession.setScene(scene);
        
        // 查找AI客服并设置
        Agent aiAgent = agentMapper.findByAgentType(2); // 2表示AI客服

        if (aiAgent != null && aiAgent.getId() != null) {
            chatSession.setAgentId(aiAgent.getId());
        } else {
            chatSession.setAgentId(6L);
            // 记录日志或抛出异常，提示AI客服配置不正确
            System.out.printf("AI客服无法获取有效的ID");
        }
        
        if (chatSessionMapper.insert(chatSession) > 0) {
            // 添加系统欢迎消息
            ChatMessage welcomeMessage = new ChatMessage();
            welcomeMessage.setSessionId(chatSession.getId());
            welcomeMessage.setContent("您好，我是智能客服助手熊小智，请问有什么可以帮您？");
            welcomeMessage.setMsgType(0); // 0=文本消息
            welcomeMessage.setSenderType(2); // ai
            welcomeMessage.setIsRead(0); // 0=未读
            welcomeMessage.setScene("系统欢迎消息");
            welcomeMessage.setCreatedAt(new Date());
            
            chatMessageMapper.insert(welcomeMessage);
            
            return chatSessionMapper.selectById(chatSession.getId());
        }
        return null;
    }

    @Override
    public boolean assignAgent(Long sessionId) {
        // 查询在线客服
        List<Agent> onlineAgents = agentMapper.selectOnlineAgents();
        if (onlineAgents.isEmpty()) {
            return false;
        }
        
        // 简单负载均衡：找到处理会话数最少的客服
        Optional<Agent> selectedAgent = onlineAgents.stream()
            .map(agent -> {
                int activeCount = chatSessionMapper.selectActiveByAgentId(agent.getId()).size();
                agent.setStatus(activeCount); // 临时用status字段存储活跃会话数
                return agent;
            })
            .min((a1, a2) -> Integer.compare(a1.getStatus(), a2.getStatus()));
        
        if (selectedAgent.isPresent()) {
            // 分配客服后，会话状态应该从待处理(2)变为进行中(1)
            int result = chatSessionMapper.assignAgent(sessionId, selectedAgent.get().getId());
            if (result > 0) {
                // 查询会话信息，并修改状态为进行中
                ChatSession session = chatSessionMapper.selectById(sessionId);
                if (session != null && session.getStatus() != 1) {
                    session.setStatus(1); // 进行中
                    session.setCurrentAgentType(1); // 设置为人工客服类型
                    chatSessionMapper.update(session);
                }
                return true;
            }
        }
        
        return false;
    }

    @Override
    public boolean assignAgentManually(Long sessionId, Long agentId) {
        // 查询会话信息
        ChatSession chatSession = chatSessionMapper.selectById(sessionId);
        if (chatSession == null) {
            return false;
        }
        
        // 检查客服状态
        Agent agent = agentMapper.selectById(agentId);
        if (agent == null || agent.getStatus() != 1) { // 客服不存在或不在线
            return false;
        }
        Integer agentType = agent.getAgentType();// 1人工客服 2AI客服 3系统管理员

        // 更新会话信息
        chatSession.setAgentId(agentId);
        chatSession.setCurrentAgentType(agentType); // 客服类型
        if (agentType == 1){
            chatSession.setStatus(1);// 0排队 1人工会话中 2已关闭 3 AI会话中
        }
        if (agentType == 2){
            chatSession.setStatus(3);// 0排队 1人工会话中 2已关闭 3 AI会话中
        }

        
        return chatSessionMapper.update(chatSession) > 0;
    }

    @Override
    @Transactional
    public boolean acceptSession(Long sessionId, Long agentId) {
        // 查询会话信息
        ChatSession chatSession = chatSessionMapper.selectById(sessionId);
        if (chatSession == null) {
            return false;
        }
        
        // 检查客服状态
        Agent agent = agentMapper.selectById(agentId);
        if (agent == null || agent.getStatus() != 1) { // 客服不存在或不在线
            return false;
        }
        
        // 检查会话状态，仅允许接入排队(0)的会话
        if (chatSession.getStatus() != 0) {
            return false;
        }

        // 更新会话信息
        chatSession.setAgentId(agentId);
        chatSession.setCurrentAgentType(1); // 人工客服类型
        chatSession.setStatus(1); // 人工会话中状态
        
        // 添加系统提示消息
        ChatMessage message = new ChatMessage();
        message.setSessionId(sessionId);
        message.setSenderType(3); // 系统消息
        message.setContent("已成功转接到人工客服 " + agent.getName() + ":请问有什么可以帮您？");
        message.setMsgType(0); // 文本消息
        message.setIsRead(0); // 初始未读状态
        message.setCreatedAt(new Date());
        message.setDatasource(chatSession.getDatasource());
        message.setCollectionName(chatSession.getCollectionName());
        message.setScene(chatSession.getScene());
        message.setChannel(chatSession.getChannel());
        chatMessageMapper.insert(message);

        // 通过WebSocket发送成功通知给用户
        if (chatSession.getUserId() != null) {
            WebSocketServer.sendTransferToHumanNotification(
                    sessionId,
                    chatSession.getUserId(),
                    agentId,
                    "已成功转接到人工客服 " + agent.getName() + ":请问有什么可以帮您？"
            );
        }
        // 更新 转人工呼叫记录表
        List<ChatCallManualRecord> chatCallManualRecords = chatCallManualRecordMapper.selectByAgentIdAndUserId(
                chatSession.getUserId()+"",
                chatSession.getAgentId()+"",
                sessionId
        );
        if (chatCallManualRecords.size() > 0){
            // 全部更新
            for (ChatCallManualRecord chatCallManualRecord : chatCallManualRecords){
                chatCallManualRecord.setIsAccepted(1);// 接入
                chatCallManualRecord.setAcceptTime(new Date()); // 接入时间

                long durationMs = new Date().getTime() - chatCallManualRecord.getRequestTime().getTime();
                long durationSeconds = TimeUnit.MILLISECONDS.toSeconds(durationMs); // 毫秒转秒
                chatCallManualRecord.setResponseDuration(durationSeconds);// 接入耗时（秒）

                // 更新
                int update = chatCallManualRecordMapper.update(chatCallManualRecord);
                if (update < 1){
                    log.error("更新转人工呼叫记录失败，ID: {}", chatCallManualRecord.getId());
                }

            }
        }


        return chatSessionMapper.update(chatSession) > 0;
    }

    @Override
    public boolean closeSession(Long id, Integer closedBy) {
        String updateRecordsSql = "UPDATE chat_call_manual_records " +
                "SET " +
                "   end_time = NOW(), " +                                   // 设置结束时间为当前时间
                "   service_duration = TIMESTAMPDIFF(SECOND, accept_time, NOW()) " + // 计算服务耗时（秒）
                "WHERE " +
                "   session_id IN (" + id + ") " +           // 使用已查询的会话ID列表
                "   AND is_accepted = 1 " ;                                 // 未设置结束时间的记录

        int updatedRecordsCount = jdbcTemplate.update(updateRecordsSql);
        return chatSessionMapper.closeSession(id, closedBy) > 0;
    }

    @Override
    public ChatSession getSessionById(Long id) {
        return chatSessionMapper.selectById(id);
    }

    @Override
    public List<ChatSession> getSessionsByUserId(Long userId) {
        return chatSessionMapper.selectByUserId(userId);
    }

    @Override
    public List<ChatSession> getSessionsByAgentId(Long agentId) {
        return chatSessionMapper.selectByAgentId(agentId);
    }

    @Override
    public List<ChatSession> getActiveSessionsByAgentId(Long agentId) {
        return chatSessionMapper.selectActiveByAgentId(agentId);
    }

    @Override
    public List<ChatSession> getQueueSessions() {
        return chatSessionMapper.selectQueueSessions();
    }

    @Override
    public PageResult<ChatSession> getSessionsByAgentIdWithFilters(
            String type,
            Long agentId, 
            String startDate, 
            String endDate, 
            Integer agentType, 
            Integer status, 
            Integer isSolved, 
            Integer satisfactionLevel,
            String datasource,
            String collectionName,
            String scene,
            Integer page,
            Integer size) {
        
        // 构建过滤条件Map
        Map<String, Object> params = new HashMap<>();
        List<Long> agentIds = new ArrayList<>();

        // agentId是前端客服的id
        if ("历史会话".equals(type)){

        } else if ("待处理".equals(type)) {
            agentIds.add(agentId);
            params.put("agentId", agentIds);
        } else if ("进行中".equals(type)){
            agentIds.add(agentId);
            params.put("agentId", agentIds);
        }else if ("排队中".equals(type)){
            agentIds.add(agentId);
            params.put("agentId", agentIds);
        } else if ("AI回复中".equals(type)) {
            agentIds.add(agentId);
            agentIds.add(6L); // AI客服的编号
            params.put("agentId", agentIds);
        }


        
        // 添加其他过滤条件
        if (!StringUtils.isNullOrEmpty(startDate)) {
            params.put("startDate", startDate);
        }
        
        if (!StringUtils.isNullOrEmpty(endDate)) {
            params.put("endDate", endDate);
        }
        
        if (agentType != null) {
            params.put("agentType", agentType);
        }
        
        if (status != null) {
            params.put("status", status);
        }
        
        if (isSolved != null) {
            params.put("isSolved", isSolved);
        }
        
        if (satisfactionLevel != null) {
            params.put("satisfactionLevel", satisfactionLevel);
        }
        
        // 添加新增过滤条件
        if (!StringUtils.isNullOrEmpty(datasource)) {
            params.put("datasource", datasource);
        }
        
        if (!StringUtils.isNullOrEmpty(collectionName)) {
            params.put("collectionName", collectionName);
        }
        
        if (!StringUtils.isNullOrEmpty(scene)) {
            params.put("scene", scene);
        }
        // 计算总数
        int total = chatSessionMapper.countByFilters(params);
        
        // 设置分页参数
        params.put("offset", (page - 1) * size);
        params.put("size", size);
        
        // 查询数据
        List<ChatSession> sessions = chatSessionMapper.selectByFilters(params);
        
        // 返回分页结果
        return new PageResult<>(sessions, total, page, size);
    }

    @Override
    @Transactional
    public ChatSession createSessionWithAI(Long userId, String channel, String datasource,String collection_name, String scene) {
        // 根据用户id 查询是否有会话 如果有直接返回
        List<ChatSession> chatSessions = chatSessionMapper.selectByUserId(userId);
        if (chatSessions != null && chatSessions.size() > 0){
            ChatSession chatSession = chatSessions.get(0);
            return chatSession;
        }
        // 查找AI客服
        Agent aiAgent = agentMapper.findByAgentType(2); // 2表示AI客服
        
        if (aiAgent == null) {
            System.out.printf("系统中未配置AI客服");
        }

        // 创建新会话
        ChatSession chatSession = new ChatSession();
        chatSession.setUserId(userId);
        // 检查AI客服对象是否为空，以及其ID是否有效
        if (aiAgent != null && aiAgent.getId() != null) {
            chatSession.setAgentId(aiAgent.getId());
        } else {
            chatSession.setAgentId(6L);
            // 记录日志或抛出异常，提示AI客服配置不正确
            System.out.printf("AI客服无法获取有效的ID");
        }

        chatSession.setStatus(3); // AI会话中状态
        chatSession.setCurrentAgentType(2); // 2=AI客服
        chatSession.setTransferRequested(0); // 0=未请求转人工
        chatSession.setChannel(channel);
        chatSession.setDatasource(datasource);
        chatSession.setCollectionName(collection_name);
        chatSession.setScene(scene);
        
        if (chatSessionMapper.insert(chatSession) > 0) {
            // 添加系统欢迎消息
            ChatMessage welcomeMessage = new ChatMessage();
            welcomeMessage.setSessionId(chatSession.getId());
            welcomeMessage.setContent("您好，我是智能客服助手熊小智，请问有什么可以帮您？");
            welcomeMessage.setMsgType(0); // 0=文本消息
            welcomeMessage.setSenderType(2); // ai客服
            welcomeMessage.setIsRead(0); // 0=未读
            welcomeMessage.setCreatedAt(new Date());
            
            chatMessageMapper.insert(welcomeMessage);
            
            return chatSessionMapper.selectById(chatSession.getId());
        }
        return null;
    }

    @Override
    @Transactional
    public ChatMessage sendMessage(WebSocketMessage message) {
        // 获取会话信息
        ChatSession session = chatSessionMapper.selectById(message.getSessionId());
        
        if (session == null) {
            throw new RuntimeException("会话不存在");
        }
        
        // 检查会话是否已关闭
        if (session.getStatus() == 2) {
            throw new RuntimeException("会话已关闭，无法发送消息");
        }
        
        // 创建消息对象
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setSessionId(message.getSessionId());
        chatMessage.setContent(message.getContent());
        chatMessage.setMsgType(message.getType() == 1 ? 0 : 1); // 1=文本, 2=图片
        chatMessage.setSenderType(message.getFrom()); // 0=用户, 1=客服 , 2=ai
        chatMessage.setIsRead(0); // 0=未读
        chatMessage.setCreatedAt(new Date());
        

        
        // 如果是用户消息且会话绑定的是AI客服
        if (message.getFrom() == 0 && session.getCurrentAgentType() == 2) { // 0=用户发送，2=AI客服
            // 检查是否要求转人工
            if (containsTransferRequest(message.getContent())) {
                // 标记会话为请求转人工状态，并更新状态为排队中
                session.setTransferRequested(1);
                session.setStatus(0); // 更新为排队状态
                chatSessionMapper.update(session);
                
                // 创建系统消息提示转人工
                ChatMessage systemMessage = new ChatMessage();
                systemMessage.setSessionId(session.getId());
                systemMessage.setContent("正在为您转接人工客服，请稍候...");
                systemMessage.setMsgType(0); // 0=文本消息
                systemMessage.setSenderType(1); // 1=客服
                systemMessage.setIsRead(0); // 0=未读
                systemMessage.setCreatedAt(new Date());
                systemMessage.setScene("客服系统");
                
                chatMessageMapper.insert(systemMessage);
                
                // 执行转人工操作
                transferToHumanAgent(session.getId());
                return chatMessage;
            }

            // 消息内容不为空，调用AI服务生成回复
            String aiReply = "用户输入为空";
            if (StringUtils.isNullOrEmpty(message.getContent())) {
                aiReply = aiService.getAIResponse(message.getContent(),new ArrayList<>(), "");
            }

            
            // 创建AI回复消息
            ChatMessage aiMessage = new ChatMessage();
            aiMessage.setSessionId(session.getId());
            aiMessage.setContent(aiReply);
            aiMessage.setMsgType(0); // 0=文本消息
            aiMessage.setSenderType(2); // 2=ai
            aiMessage.setIsRead(0); // 0=未读
            aiMessage.setCreatedAt(new Date());
            aiMessage.setScene("客服系统");
            
            chatMessageMapper.insert(aiMessage);
            
            return aiMessage;
        }
        
        // 更新会话的最新消息
        updateSession(session.getId(), chatMessage.getContent());
        
        return chatMessage;
    }
    
    @Override
    @Transactional
    public boolean transferToHumanAgent(Long sessionId) {
        // 查询会话信息
        ChatSession session = chatSessionMapper.selectById(sessionId);
        if (session == null) {
            return false;
        }
        
        // 标记会话为请求转人工状态，并更新状态为排队中
        session.setTransferRequested(1);
        session.setStatus(0); // 更新为排队状态
        chatSessionMapper.update(session);
        
        // 通过WebSocket发送转接通知给用户，即使没有找到人工客服也先通知正在转接
//        if (session.getUserId() != null) {
//            WebSocketServer.sendTransferToHumanNotification(
//                sessionId,
//                session.getUserId(),
//                null,
//                "正在为您转接人工客服，请稍候..."
//            );
//        }
        
        // 查找空闲的人工客服
        Agent humanAgent = agentMapper.findAvailableHumanAgent();
        
        if (humanAgent == null) {
            // 无可用人工客服，添加系统提示消息，并恢复为AI会话状态
            session.setStatus(3); // 恢复为AI会话状态
            chatSessionMapper.update(session);
            
            ChatMessage notification = new ChatMessage();
            notification.setSessionId(sessionId);
            notification.setContent("当前所有人工客服都在忙，请稍后再试。AI客服将继续为您服务。人工客服工作时间为09:00-21:00");
            notification.setMsgType(0); // 0=文本消息
            notification.setSenderType(1); // 1=客服
            notification.setIsRead(0); // 0=未读
            notification.setCreatedAt(new Date());
            
            chatMessageMapper.insert(notification);
            
            // 通过WebSocket发送失败通知
            if (session.getUserId() != null) {
                WebSocketServer.sendTransferToHumanNotification(
                    sessionId,
                    session.getUserId(),
                    null,
                    "当前所有人工客服都在忙，请稍后再试。AI客服将继续为您服务。人工客服工作时间为09:00-21:00"
                );
            }
            
            return false;
        } else {
            // 更新会话的客服ID和类型，状态保持为排队中(0)
            session.setAgentId(humanAgent.getId());
            session.setCurrentAgentType(1); // 1=人工客服
            chatSessionMapper.update(session);
            
            // 添加系统提示消息
            ChatMessage notification = new ChatMessage();
            notification.setSessionId(sessionId);
            notification.setContent("正在为您转接人工客服，请稍候...");
            notification.setMsgType(0); // 0=文本消息
            notification.setSenderType(3); // 1=客服
            notification.setIsRead(0); // 0=未读
            notification.setCreatedAt(new Date());
            notification.setDatasource(session.getDatasource());
            notification.setScene(session.getScene());
            notification.setChannel(session.getChannel());
            notification.setCollectionName(session.getCollectionName());
            
            chatMessageMapper.insert(notification);
            
            // 通过WebSocket发送成功通知给用户
            if (session.getUserId() != null) {
                WebSocketServer.sendTransferToHumanNotification(
                    sessionId,
                    session.getUserId(),
                    humanAgent.getId(),
                    "正在为您转接人工客服，请稍候..."
                );
            }
            
            // 通过WebSocket发送转接通知给客服
            WebSocketServer.sendTransferToAgentNotification(
                sessionId,
                session.getUserId(),
                humanAgent.getId(),
                "用户 " + session.getUserId() + " 请求人工客服，已分配给您，请及时处理"
            );
            // 添加转人工呼叫记录表
            ChatCallManualRecord chatCallManualRecord = new ChatCallManualRecord();
            chatCallManualRecord.setAgentId(humanAgent.getId()+"");
            chatCallManualRecord.setUserId(session.getUserId()+"");
            chatCallManualRecord.setRequestTime(new Date());
            chatCallManualRecord.setIsAccepted(0);
            chatCallManualRecord.setSessionId(sessionId);
            chatCallManualRecordMapper.insert(chatCallManualRecord);

            return true;
        }
    }
    
    @Override
    public boolean addEvaluation(Long sessionId, Integer satisfactionLevel, String feedbackContent, Integer isSolved, String userSuggestion) {
        // 校验参数
        if (sessionId == null || satisfactionLevel == null) {
            return false;
        }
        
        // 检查会话是否存在
        ChatSession session = chatSessionMapper.selectById(sessionId);
        if (session == null) {
            return false;
        }
        
        // 添加评价
        int result = chatSessionMapper.addEvaluation(sessionId, satisfactionLevel, feedbackContent, isSolved, userSuggestion);
        
        if (result > 0) {
            // 可以添加一条系统消息，通知客服收到了用户评价
            ChatMessage notification = new ChatMessage();
            notification.setSessionId(sessionId);
            notification.setContent("用户已完成满意度评价：" + satisfactionLevel + "星");
            notification.setMsgType(0); // 0=文本消息
            notification.setSenderType(1); // 1=系统/客服
            notification.setIsRead(0); // 0=未读
            notification.setCreatedAt(new Date());
            
            chatMessageMapper.insert(notification);
            return true;
        }
        
        return false;
    }
    
    @Override
    public boolean addSolutionDescription(Long sessionId, String solutionDescription, Integer isSolved) {
        // 校验参数
        if (sessionId == null || solutionDescription == null || solutionDescription.trim().isEmpty()) {
            return false;
        }
        
        // 检查会话是否存在
        ChatSession session = chatSessionMapper.selectById(sessionId);
        if (session == null) {
            return false;
        }
        
        // 添加解决方案描述
        int result = chatSessionMapper.addSolutionDescription(sessionId, solutionDescription, isSolved);
        
        return result > 0;
    }
    
    @Override
    public List<ChatSession> getSolvedSessions() {
        return chatSessionMapper.selectSolvedSessions();
    }
    
    @Override
    public List<ChatSession> getSessionsBySatisfactionLevel(Integer satisfactionLevel) {
        return chatSessionMapper.selectBySatisfactionLevel(satisfactionLevel);
    }
    
    /**
     * 检查文本中是否包含转人工关键词
     */
    private boolean containsTransferRequest(String content) {
        if (content == null) {
            return false;
        }
        
        String[] keywords = {"人工", "转人工", "真人", "人工客服", "转接人工", "人工服务", "人工坐席"};
        for (String keyword : keywords) {
            if (content.contains(keyword)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 更新会话信息
     */
    private void updateSession(Long sessionId, String lastMessage) {
        // 简单更新会话，不需要设置lastMessage
        ChatSession chatSession = chatSessionMapper.selectById(sessionId);
        if (chatSession != null) {
            // 只更新会话，不设置额外字段
            chatSessionMapper.update(chatSession);
        }
    }
    
    @Override
    public boolean isAISession(Long sessionId) {
        ChatSession chatSession = chatSessionMapper.selectById(sessionId);
        if (chatSession == null) {
            return false;
        }
        
        // 判断当前客服类型是否为AI客服(2)
        if (chatSession.getCurrentAgentType() != null && chatSession.getCurrentAgentType() == 2) {
            return true;
        }
        
        // 判断是否已请求转人工
        if (chatSession.getTransferRequested() != null && chatSession.getTransferRequested() == 1) {
            return false;
        }
        
        // 判断客服ID是否为AI客服ID
        if (chatSession.getAgentId() != null) {
            Agent agent = agentMapper.selectById(chatSession.getAgentId());
            if (agent != null && agent.getAgentType() != null && agent.getAgentType() == 2) {
                return true;
            }
        }
        
        return false;
    }

    @Override
    public List<ChatSession> getSessionsByFilters(Map<String, Object> params) {
        return chatSessionMapper.selectByFiltersList(params);
    }

    @Override
    public PageResult<ChatSession> getSessionsByAgentIdList(Long agentId, String startDate, String endDate, Integer agentType, Integer status, Integer isSolved, Integer satisfactionLevel, Integer page, Integer size) {

        // 构建查询条件
        Map<String, Object> params = new HashMap<>();

        ArrayList<Integer> agentIdList = new ArrayList<>();
        agentIdList.add(Integer.parseInt(agentId+""));
        agentIdList.add(6); // AI客服的编号
        params.put("agentId", agentIdList);


        // 添加日期过滤
        if (startDate != null && !startDate.isEmpty()) {
            params.put("startDate", startDate);
        }
        if (endDate != null && !endDate.isEmpty()) {
            params.put("endDate", endDate);
        }

        // 添加客服类型过滤
        if (agentType != null) {
            params.put("agentType", agentType);
        }

        // 添加会话状态过滤
        if (status != null) {
            params.put("status", status);
        }

        // 添加问题解决状态过滤
        if (isSolved != null) {
            params.put("isSolved", isSolved);
        }

        // 添加满意度评分过滤
        if (satisfactionLevel != null) {
            params.put("satisfactionLevel", satisfactionLevel);
        }

        // 添加分页参数
        int offset = (page - 1) * size;
        params.put("offset", offset);
        params.put("size", size);

        // 查询总记录数
        int total = chatSessionMapper.countByFiltersList(params);

        // 查询当前页数据
        List<ChatSession> records = chatSessionMapper.selectByFiltersList(params);

        // 返回分页结果
        return new PageResult<>(records, total, page, size);
    }

    @Override
    public Map<String, Object> getMessageTypeAndSourceStats(String startDate, String endDate) {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> params = new HashMap<>();
        
        // 添加日期过滤条件（如果提供了日期范围）
        if (startDate != null && !startDate.isEmpty()) {
            params.put("startDate", startDate);
        }
        if (endDate != null && !endDate.isEmpty()) {
            params.put("endDate", endDate);
        }
        
        // 获取发送者类型统计数据
        List<Map<String, Object>> senderTypeStats = chatSessionMapper.getMessageStatsBySenderType(params);
        result.put("senderTypeStats", senderTypeStats);
        
        // 获取数据来源统计数据
        List<Map<String, Object>> datasourceStats = chatSessionMapper.getMessageStatsByDatasource(params);
        result.put("datasourceStats", datasourceStats);
        
        return result;
    }
    
    @Override
    public List<Map<String, Object>> getDatasourceOptions() {
        return chatSessionMapper.getDatasourceOptions();
    }
    
    @Override
    public List<Map<String, Object>> getCollectionOptions() {
        return chatSessionMapper.getCollectionOptions();
    }
    
    /**
     * 获取场景选项
     *
     * @return 场景选项列表
     */
    @Override
    public List<Map<String, Object>> getSceneOptions() {
        return chatSessionMapper.getSceneOptions();
    }

    @Override
    public List<Map<String, Object>> getChannelOptions() {
        return List.of();
    }

    /**
     * 统计客服的会话总数
     *
     * @param agentId 客服ID
     * @return 会话总数
     */
    @Override
    public int countSessionsByAgent(Long agentId) {
        try {
            // 构建查询条件
            QueryWrapper<ChatSession> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("agent_id", agentId.toString());
            
            return (int) this.count(queryWrapper);
        } catch (Exception e) {
            log.error("统计客服会话总数出错", e);
            return 0;
        }
    }

    /**
     * 统计指定时间范围内客服的会话数
     *
     * @param agentId 客服ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 会话数
     */
    @Override
    public int countSessionsByAgentAndTimeRange(Long agentId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 构建查询条件
            QueryWrapper<ChatSession> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("agent_id", agentId.toString())
                    .between("start_time", startTime, endTime);
            
            return (int) this.count(queryWrapper);
        } catch (Exception e) {
            log.error("统计时间范围内客服会话数出错", e);
            return 0;
        }
    }

    /**
     * 获取客服的平均对话时间
     *
     * @param agentId 客服ID
     * @return 平均对话时间（秒）
     */
    @Override
    public Double getAvgChatDurationByAgent(Long agentId) {
        try {
            // 构建查询条件
            QueryWrapper<ChatSession> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("agent_id", agentId.toString())
                    .eq("status", 0)  // 已结束的会话
                    .isNotNull("end_time");
            
            List<ChatSession> sessions = this.list(queryWrapper);
            
            if (sessions.isEmpty()) {
                return 0.0;
            }
            
            // 计算平均对话时间
            double totalDuration = 0;
            int validCount = 0;
            
            for (ChatSession session : sessions) {
                if (session.getEndTime() != null && session.getStartTime() != null) {
                    // 计算会话时长（秒）
                    long durationSeconds = java.time.Duration.between(
                            session.getStartTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime(), 
                            session.getEndTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime()
                    ).getSeconds();
                    totalDuration += durationSeconds;
                    validCount++;
                }
            }
            
            if (validCount == 0) {
                return 0.0;
            }
            
            return totalDuration / validCount;
        } catch (Exception e) {
            log.error("计算客服平均对话时间出错", e);
            return 0.0;
        }
    }

    /**
     * 获取指定时间范围内客服的平均对话时间
     *
     * @param agentId 客服ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 平均对话时间（秒）
     */
    @Override
    public Double getAvgChatDurationByAgentAndTimeRange(Long agentId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 构建查询条件
            QueryWrapper<ChatSession> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("agent_id", agentId.toString())
                    .eq("status", 0)  // 已结束的会话
                    .isNotNull("end_time")
                    .between("start_time", startTime, endTime);
            
            List<ChatSession> sessions = this.list(queryWrapper);
            
            if (sessions.isEmpty()) {
                return 0.0;
            }
            
            // 计算平均对话时间
            double totalDuration = 0;
            int validCount = 0;
            
            for (ChatSession session : sessions) {
                if (session.getEndTime() != null && session.getStartTime() != null) {
                    // 计算会话时长（秒）
                    long durationSeconds = java.time.Duration.between(
                            session.getStartTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime(), 
                            session.getEndTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime()
                    ).getSeconds();
                    totalDuration += durationSeconds;
                    validCount++;
                }
            }
            
            if (validCount == 0) {
                return 0.0;
            }
            
            return totalDuration / validCount;
        } catch (Exception e) {
            log.error("计算时间范围内客服平均对话时间出错", e);
            return 0.0;
        }
    }
} 