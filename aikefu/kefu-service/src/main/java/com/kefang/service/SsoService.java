package com.kefang.service;

import com.kefang.dto.SsoApiTokenDTO;
import com.kefang.dto.SsoUserLoginDTO;

/**
 * SSO服务接口
 */
public interface SsoService {
    
    /**
     * 获取SSO API Token和私钥
     * @return SSO API Token响应对象
     */
    SsoApiTokenDTO getApiToken();
    
    /**
     * SSO用户登录
     * @param mobile 手机号
     * @param password 密码
     * @param privateKey 私钥
     * @param apiToken API令牌
     * @return 登录响应对象
     */
    SsoUserLoginDTO.Response userLogin(String mobile, String password, String privateKey, String apiToken);
} 