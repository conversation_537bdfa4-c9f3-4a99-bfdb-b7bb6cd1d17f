package com.kefang.service.impl;

import com.kefang.mapper.ChatMessageMapper;
import com.kefang.mapper.ChatSessionMapper;
import com.kefang.service.StatisticsService;
import com.kefang.vo.SessionStatVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.text.SimpleDateFormat;
import java.util.stream.Collectors;

/**
 * 统计服务实现类
 */
@Service
public class StatisticsServiceImpl implements StatisticsService {
    
    @Autowired
    private ChatSessionMapper chatSessionMapper;
    
    @Autowired
    private ChatMessageMapper chatMessageMapper;
    
    /**
     * 获取工作台统计数据
     * @return 统计数据集合
     */
    @Override
    public Map<String, Object> getDashboardStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 今日对话数
        int todaySessionCount = chatSessionMapper.countTodaySessions();
        // 昨对话话数
        int yesterdaySessionCount = chatSessionMapper.countYesterdaySessions();
        // 计算增长率
        int todaySessionTrend = 0;
        if (yesterdaySessionCount > 0) {
            todaySessionTrend = (int) (((float) todaySessionCount / yesterdaySessionCount - 1) * 100);
        }
        
        // 待处理会话数
        int waitingSessionCount = chatSessionMapper.countWaitingSessions();
        
        // 今日已解决会话数
        int resolvedSessionCount = chatSessionMapper.countTodayResolvedSessions();
        // 昨日已解决会话数
        int yesterdayResolvedCount = chatSessionMapper.countYesterdayResolvedSessions();
        // 计算增长率
        int resolvedTrend = 0;
        if (yesterdayResolvedCount > 0) {
            resolvedTrend = (int) (((float) resolvedSessionCount / yesterdayResolvedCount - 1) * 100);
        }
        
        // 平均满意度
        double averageSatisfaction = chatSessionMapper.getAverageSatisfaction();
        
        // 获取AI已回复问题数
        int aiResolvedCount = chatMessageMapper.countTodayAIMessages();
        
        // 封装数据
        stats.put("todaySessionCount", todaySessionCount);
        stats.put("todaySessionTrend", todaySessionTrend);
        stats.put("waitingSessionCount", waitingSessionCount);
        stats.put("resolvedSessionCount", resolvedSessionCount);
        stats.put("resolvedTrend", resolvedTrend);
        stats.put("averageSatisfaction", averageSatisfaction);
        stats.put("aiResolvedCount", aiResolvedCount);
        
        return stats;
    }
    
    /**
     * 获取会话统计数据
     * @param period 时间周期：day, week, month
     * @return 会话统计数据列表
     */
    @Override
    public List<SessionStatVO> getSessionStats(String period) {
        List<Map<String, Object>> rawData;
        
        switch (period) {
            case "day":
                rawData = chatSessionMapper.getHourlySessionStats();
                return processHourlySessionStats(rawData);
            case "week":
                rawData = chatSessionMapper.getWeeklySessionStats();
                return processWeeklySessionStats(rawData);
            case "month":
                rawData = chatSessionMapper.getMonthlySessionStats();
                return processMonthlySessionStats(rawData);
            default:
                return new ArrayList<>();
        }
    }
    
    /**
     * 获取满意度统计数据
     * @param period 时间周期：day, week, month
     * @return 满意度统计数据列表
     */
    @Override
    public List<SessionStatVO> getSatisfactionStats(String period) {
        List<Map<String, Object>> rawData;
        
        switch (period) {
            case "day":
                rawData = chatSessionMapper.getHourlySatisfactionStats();
                return processHourlySatisfactionStats(rawData);
            case "week":
                rawData = chatSessionMapper.getWeeklySatisfactionStats();
                return processWeeklySatisfactionStats(rawData);
            case "month":
                rawData = chatSessionMapper.getMonthlySatisfactionStats();
                return processMonthlySatisfactionStats(rawData);
            default:
                return new ArrayList<>();
        }
    }
    
    /**
     * 处理按小时统计的会话数据
     */
    private List<SessionStatVO> processHourlySessionStats(List<Map<String, Object>> rawData) {
        // 创建24小时的数据结构
        List<SessionStatVO> result = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            SessionStatVO vo = new SessionStatVO();
            vo.setTimeLabel(String.format("%02d:00", i));
            vo.setSessionCount(0);
            result.add(vo);
        }
        
        // 填充实际数据
        for (Map<String, Object> data : rawData) {
            int hour = (int) data.get("hour");
            int count = ((Number) data.get("count")).intValue();
            
            if (hour >= 0 && hour < 24) {
                result.get(hour).setSessionCount(count);
            }
        }
        
        return result;
    }
    
    /**
     * 处理按周统计的会话数据
     */
    private List<SessionStatVO> processWeeklySessionStats(List<Map<String, Object>> rawData) {
        // 创建周一到周日的数据结构
        String[] weekDays = {"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
        List<SessionStatVO> result = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            SessionStatVO vo = new SessionStatVO();
            vo.setTimeLabel(weekDays[i]);
            vo.setSessionCount(0);
            result.add(vo);
        }
        
        // 填充实际数据
        for (Map<String, Object> data : rawData) {
            int weekday = ((Number) data.get("weekday")).intValue();
            int count = ((Number) data.get("count")).intValue();
            
            // MySQL的DAYOFWEEK是1(周日)到7(周六)，需要转换
            int index = (weekday + 5) % 7; // 转换为0(周一)到6(周日)
            result.get(index).setSessionCount(count);
        }
        
        return result;
    }
    
    /**
     * 处理按月统计的会话数据
     */
    private List<SessionStatVO> processMonthlySessionStats(List<Map<String, Object>> rawData) {
        // 创建本月1号到今天的数据结构
        Calendar cal = Calendar.getInstance();
        int today = cal.get(Calendar.DAY_OF_MONTH);
        List<SessionStatVO> result = new ArrayList<>();
        
        for (int i = 1; i <= today; i++) {
            SessionStatVO vo = new SessionStatVO();
            vo.setTimeLabel(i + "日");
            vo.setSessionCount(0);
            result.add(vo);
        }
        
        // 填充实际数据
        for (Map<String, Object> data : rawData) {
            int day = ((Number) data.get("day")).intValue();
            int count = ((Number) data.get("count")).intValue();
            
            if (day >= 1 && day <= today) {
                result.get(day - 1).setSessionCount(count);
            }
        }
        
        return result;
    }
    
    /**
     * 处理按小时统计的满意度数据
     */
    private List<SessionStatVO> processHourlySatisfactionStats(List<Map<String, Object>> rawData) {
        // 创建24小时的数据结构
        List<SessionStatVO> result = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            SessionStatVO vo = new SessionStatVO();
            vo.setTimeLabel(String.format("%02d:00", i));
            vo.setAvgSatisfaction(0.0);
            result.add(vo);
        }
        
        // 填充实际数据
        for (Map<String, Object> data : rawData) {
            int hour = (int) data.get("hour");
            double avgSatisfaction = ((Number) data.get("avg_satisfaction")).doubleValue();
            
            if (hour >= 0 && hour < 24) {
                result.get(hour).setAvgSatisfaction(avgSatisfaction);
            }
        }
        
        return result;
    }
    
    /**
     * 处理按周统计的满意度数据
     */
    private List<SessionStatVO> processWeeklySatisfactionStats(List<Map<String, Object>> rawData) {
        // 创建周一到周日的数据结构
        String[] weekDays = {"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
        List<SessionStatVO> result = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            SessionStatVO vo = new SessionStatVO();
            vo.setTimeLabel(weekDays[i]);
            vo.setAvgSatisfaction(0.0);
            result.add(vo);
        }
        
        // 填充实际数据
        for (Map<String, Object> data : rawData) {
            int weekday = ((Number) data.get("weekday")).intValue();
            double avgSatisfaction = ((Number) data.get("avg_satisfaction")).doubleValue();
            
            // MySQL的DAYOFWEEK是1(周日)到7(周六)，需要转换
            int index = (weekday + 5) % 7; // 转换为0(周一)到6(周日)
            result.get(index).setAvgSatisfaction(avgSatisfaction);
        }
        
        return result;
    }
    
    /**
     * 处理按月统计的满意度数据
     */
    private List<SessionStatVO> processMonthlySatisfactionStats(List<Map<String, Object>> rawData) {
        // 创建本月1号到今天的数据结构
        Calendar cal = Calendar.getInstance();
        int today = cal.get(Calendar.DAY_OF_MONTH);
        List<SessionStatVO> result = new ArrayList<>();
        
        for (int i = 1; i <= today; i++) {
            SessionStatVO vo = new SessionStatVO();
            vo.setTimeLabel(i + "日");
            vo.setAvgSatisfaction(0.0);
            result.add(vo);
        }
        
        // 填充实际数据
        for (Map<String, Object> data : rawData) {
            int day = ((Number) data.get("day")).intValue();
            double avgSatisfaction = ((Number) data.get("avg_satisfaction")).doubleValue();
            
            if (day >= 1 && day <= today) {
                result.get(day - 1).setAvgSatisfaction(avgSatisfaction);
            }
        }
        
        return result;
    }

    /**
     * 获取对话统计数据
     * @param period 时间周期：day, week, month
     * @return 对话统计数据列表
     */
    @Override
    public List<SessionStatVO> getMessageStats(String period) {
        List<Map<String, Object>> rawData;
        
        switch (period) {
            case "day":
                rawData = chatMessageMapper.getHourlyMessageStats();
                return processHourlyMessageStats(rawData);
            case "week":
                rawData = chatMessageMapper.getWeeklyMessageStats();
                return processWeeklyMessageStats(rawData);
            case "month":
                rawData = chatMessageMapper.getMonthlyMessageStats();
                return processMonthlyMessageStats(rawData);
            default:
                return new ArrayList<>();
        }
    }
    
    /**
     * 处理按小时统计的对话数据
     */
    private List<SessionStatVO> processHourlyMessageStats(List<Map<String, Object>> rawData) {
        // 创建24小时的数据结构
        List<SessionStatVO> result = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            SessionStatVO vo = new SessionStatVO();
            vo.setTimeLabel(String.format("%02d:00", i));
            vo.setSessionCount(0);
            result.add(vo);
        }
        
        // 填充实际数据
        for (Map<String, Object> data : rawData) {
            int hour = (int) data.get("hour");
            int count = ((Number) data.get("count")).intValue();
            
            if (hour >= 0 && hour < 24) {
                result.get(hour).setSessionCount(count);
            }
        }
        
        return result;
    }
    
    /**
     * 处理按周统计的对话数据
     */
    private List<SessionStatVO> processWeeklyMessageStats(List<Map<String, Object>> rawData) {
        // 创建周一到周日的数据结构
        String[] weekDays = {"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
        List<SessionStatVO> result = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            SessionStatVO vo = new SessionStatVO();
            vo.setTimeLabel(weekDays[i]);
            vo.setSessionCount(0);
            result.add(vo);
        }
        
        // 填充实际数据
        for (Map<String, Object> data : rawData) {
            int weekday = ((Number) data.get("weekday")).intValue();
            int count = ((Number) data.get("count")).intValue();
            
            // MySQL的DAYOFWEEK是1(周日)到7(周六)，需要转换
            int index = (weekday + 5) % 7; // 转换为0(周一)到6(周日)
            result.get(index).setSessionCount(count);
        }
        
        return result;
    }
    
    /**
     * 处理按月统计的对话数据
     */
    private List<SessionStatVO> processMonthlyMessageStats(List<Map<String, Object>> rawData) {
        // 创建本月1号到今天的数据结构
        Calendar cal = Calendar.getInstance();
        int today = cal.get(Calendar.DAY_OF_MONTH);
        List<SessionStatVO> result = new ArrayList<>();
        
        for (int i = 1; i <= today; i++) {
            SessionStatVO vo = new SessionStatVO();
            vo.setTimeLabel(i + "日");
            vo.setSessionCount(0);
            result.add(vo);
        }
        
        // 填充实际数据
        for (Map<String, Object> data : rawData) {
            int day = ((Number) data.get("day")).intValue();
            int count = ((Number) data.get("count")).intValue();
            
            if (day >= 1 && day <= today) {
                result.get(day - 1).setSessionCount(count);
            }
        }
        
        return result;
    }
} 