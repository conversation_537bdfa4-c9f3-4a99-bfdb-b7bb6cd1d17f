package com.kefang.service;

import com.kefang.entity.Faq;
import java.util.List;
import java.util.Map;

public interface FaqService {

    /**
     * 添加常见问题
     * @param faq 问题信息
     * @return 操作结果
     */
    boolean addFaq(Faq faq);
    
    /**
     * 更新常见问题
     * @param faq 问题信息
     * @return 操作结果
     */
    boolean updateFaq(Faq faq);
    
    /**
     * 删除常见问题
     * @param id 问题ID
     * @return 操作结果
     */
    boolean deleteFaq(Long id);
    
    /**
     * 根据ID查询问题
     * @param id 问题ID
     * @return 问题信息
     */
    Faq getFaqById(Long id);
    
    /**
     * 根据分类查询问题列表
     * @param category 分类
     * @return 问题列表
     */
    List<Faq> getFaqsByCategory(String category);
    
    /**
     * 查询所有问题
     * @return 问题列表
     */
    List<Faq> getAllFaqs();
    
    /**
     * 根据场景和数据源获取常见问题
     * 
     * @param scene 场景
     * @param datasource 数据源
     * @return 常见问题列表
     */
    List<Map<String, Object>> getFaqBySceneAndDatasource(String scene, String datasource);
} 