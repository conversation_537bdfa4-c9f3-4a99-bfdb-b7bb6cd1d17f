package com.kefang.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * HTTP客户端工具类，用于处理远程API调用
 */
@Component
public class HttpClientUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);
    
    @Autowired
    @Qualifier("ssoRestTemplate")
    private RestTemplate restTemplate;
    
    /**
     * 发送GET请求
     * @param url 请求URL
     * @return JSON响应
     */
    public JSONObject get(String url) {
        try {
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            return JSON.parseObject(response.getBody());
        } catch (Exception e) {
            logger.error("GET请求异常: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 发送带请求头的GET请求
     * @param url 请求URL
     * @param headers 请求头
     * @return JSON响应
     */
    public JSONObject get(String url, Map<String, String> headers) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            if (headers != null) {
                headers.forEach(httpHeaders::add);
            }
            
            HttpEntity<String> requestEntity = new HttpEntity<>(null, httpHeaders);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class);
            return JSON.parseObject(response.getBody());
        } catch (Exception e) {
            logger.error("带请求头的GET请求异常: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 发送POST请求
     * @param url 请求URL
     * @param requestBody 请求体
     * @return JSON响应
     */
    public JSONObject post(String url, Object requestBody) {
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(url, requestBody, String.class);
            return JSON.parseObject(response.getBody());
        } catch (Exception e) {
            logger.error("POST请求异常: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 发送带请求头的POST请求
     * @param url 请求URL
     * @param requestBody 请求体
     * @param headers 请求头
     * @return JSON响应
     */
    public JSONObject post(String url, Object requestBody, Map<String, String> headers) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            if (headers != null) {
                headers.forEach(httpHeaders::add);
            }
            
            HttpEntity<Object> requestEntity = new HttpEntity<>(requestBody, httpHeaders);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
            String responseBody = response.getBody();
            
            // 记录原始响应
            logger.debug("API原始响应: {}", responseBody);
            
            // 尝试解析JSON
            try {
                return JSON.parseObject(responseBody);
            } catch (Exception e) {
                logger.error("JSON解析异常: {}", e.getMessage());
                
                // 创建一个包含错误信息和原始响应的JSON对象
                JSONObject errorJson = new JSONObject();
                errorJson.put("error", "无法解析为JSON格式");
                errorJson.put("originalResponse", responseBody);
                errorJson.put("status", response.getStatusCodeValue());
                return errorJson;
            }
        } catch (Exception e) {
            logger.error("带请求头的POST请求异常: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 发送POST请求并返回原始响应，不尝试解析为JSON
     * @param url 请求URL
     * @param requestBody 请求体
     * @param headers 请求头
     * @return 原始响应字符串
     */
    public String postForRawResponse(String url, Object requestBody, Map<String, String> headers) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            if (headers != null) {
                headers.forEach(httpHeaders::add);
            }
            
            HttpEntity<Object> requestEntity = new HttpEntity<>(requestBody, httpHeaders);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
            return response.getBody();
        } catch (Exception e) {
            logger.error("请求原始响应异常: {}", e.getMessage(), e);
            throw e;
        }
    }
} 