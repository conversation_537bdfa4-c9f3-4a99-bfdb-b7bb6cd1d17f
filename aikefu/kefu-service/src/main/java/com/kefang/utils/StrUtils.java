package com.kefang.utils;

public class StrUtils {

    /**
     * 尝试将字符串转换为 Long，如果转换失败则返回默认值
     *
     * @param str        要转换的字符串
     * @param defaultValue 转换失败时的默认值
     * @return 转换后的字符串或默认值
     */
    public static String parseStringToString(String str, String defaultValue) {
        try {
            Long.parseLong(str); // 尝试解析为 Long
            return str; // 如果成功，返回原字符串
        } catch (NumberFormatException e) {
            return defaultValue; // 如果失败，返回默认值
        }
    }
    public static boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        return str.matches("-?\\d+(\\.\\d+)?"); // 支持整数和小数
    }
}
