package com.kefang.utils;

import com.kefang.constants.MessageConstants;
import com.kefang.vo.Result;
import com.mysql.cj.util.StringUtils;

/**
 * 断言工具类
 * 用于参数验证
 */
public class AssertUtils {
    
    /**
     * 断言字符串不为空
     * 
     * @param str 待验证的字符串
     * @param errorMessage 错误消息
     * @return 如果字符串为空，则返回包含错误信息的Result对象；否则返回null
     */
    public static Result<String> assertNotEmpty(String str, String errorMessage) {
        if (StringUtils.isNullOrEmpty(str)) {
            return Result.success(MessageConstants.RESPONSE_SUCCESS, errorMessage);
        }
        return null;
    }
    
    /**
     * 断言对象不为空
     * 
     * @param obj 待验证的对象
     * @param errorMessage 错误消息
     * @return 如果对象为空，则返回包含错误信息的Result对象；否则返回null
     */
    public static Result<String> assertNotNull(Object obj, String errorMessage) {
        if (obj == null) {
            return Result.success(MessageConstants.RESPONSE_SUCCESS, errorMessage);
        }
        return null;
    }

    /**
     * 断言字符串不为空
     * @param str
     * @return
     */
    public static boolean strNotEmpty(String str) {
        return StringUtils.isNullOrEmpty(str);
    }

} 