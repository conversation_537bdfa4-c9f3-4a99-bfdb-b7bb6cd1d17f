package com.kefang.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mysql.cj.util.StringUtils;

/**
 * 格式化工具类，用于格式化各种信息的显示
 */
public class FormatUtils {

    /**
     * 格式化订单信息
     *
     * @param orderData 订单数据
     * @return 格式化后的订单信息
     */
    public static String formatOrderInfo(JSONObject orderData) {
        StringBuilder sb = new StringBuilder();
        sb.append("您好，查询结果：\n");
        sb.append("---------------------------\n");

        // 回收单号
        sb.append("回收单号：").append(orderData.getString("code")).append("\n");

        // 三方单号，可能不存在 sale_order_code
        String saleOrderNo = orderData.getString("sale_order_code");
        if (!StringUtils.isNullOrEmpty(saleOrderNo)) {
            sb.append("三方单号：").append(saleOrderNo).append("\n");
        }

        // 订单类型
        String orderType = orderData.getString("order_type");
        if (!StringUtils.isNullOrEmpty(orderType)) {
            sb.append("订单类型：").append(orderType).append("\n");
        }

        // 下单渠道
        sb.append("下单渠道：").append(orderData.getString("source_name")).append("\n");

        // 下单时间
        sb.append("下单时间：").append(orderData.getString("create_time")).append("\n");

        // 商品名称
        JSONObject recoverItem = orderData.getJSONObject("recover_item");
        if (recoverItem != null) {
            String brandName = recoverItem.getString("item_brand");
            String itemName = recoverItem.getString("item_name");
            sb.append("商品名称：").append(brandName).append("-").append(itemName).append("\n");
        } else {
            sb.append("商品名称：无\n");
        }

        // 回收状态
        sb.append("回收状态：").append(orderData.getString("status_name")).append("\n");

        // 回收报价
        sb.append("回收报价：").append(orderData.getString("recover_price")).append("\n");

        // 物流信息
        JSONObject express = orderData.getJSONObject("express");
        if (express != null) {
            // 快递公司
            String companyName = express.getString("company_name");
            if (!StringUtils.isNullOrEmpty(companyName)) {
                sb.append("快递公司：").append(companyName).append("\n");
            }

            // 快递公司缩写
//            String company = express.getString("company");
//            if (!StringUtils.isNullOrEmpty(company)) {
//                sb.append("company：").append(company).append("\n");
//            }

            // 快递单号
            String expressNumber = express.getString("number");
            if (!StringUtils.isNullOrEmpty(expressNumber)) {
                sb.append("物流单号：").append(expressNumber).append("\n");
            }

            // 物流状态
            String expressStatus = express.getString("status_name");
            if (!StringUtils.isNullOrEmpty(expressStatus)) {
                sb.append("物流状态：").append(expressStatus).append("\n");
            }
        } else {
            // 如果没有express对象，尝试使用旧的字段
            String expressStatus = orderData.getString("express_type");
            if (!StringUtils.isNullOrEmpty(expressStatus)) {
                sb.append("物流类型：").append(expressStatus).append("\n");
            }
        }

        return sb.toString();
    }

    /**
     * 格式化物流信息
     *
     * @param expressData 物流数据
     * @param orderNumber 订单号
     * @return 格式化后的物流信息
     */
    public static String formatExpressInfo(JSONObject expressData, String orderNumber) {
        StringBuilder sb = new StringBuilder();
        sb.append("您好，物流查询结果：\n");
        sb.append("---------------------------\n");

        // 显示基本信息
        sb.append("订单号：").append(orderNumber).append("\n\n");

        // 快递公司
        String companyName = expressData.getString("company_name");
        if (!StringUtils.isNullOrEmpty(companyName)) {
            sb.append("快递公司：").append(companyName).append("\n");
        }

        // 快递单号
        String expressNumber = expressData.getString("number");
        if (!StringUtils.isNullOrEmpty(expressNumber)) {
            sb.append("物流单号：").append(expressNumber).append("\n");
        }

        // 物流状态
        String expressStatus = expressData.getString("statusName");
        if (!StringUtils.isNullOrEmpty(expressStatus)) {
            sb.append("物流状态：").append(expressStatus).append("\n\n");
        }

        // 处理物流轨迹
        JSONArray process = expressData.getJSONArray("process");
        if (process != null && !process.isEmpty()) {
            sb.append("物流轨迹：\n");

            // 显示最新的5条轨迹信息（process数组已经按时间倒序排列，最新的在前）
            int count = 0;
            int totalCount = process.size();
            for (int i = 0; i < totalCount && count < 5; i++) {
                JSONObject track = process.getJSONObject(i);
                String time = track.getString("time");
                String context = track.getString("context");
                String statusName = track.getString("status_name");
                String title = track.getString("title");

                if (!StringUtils.isNullOrEmpty(time) && !StringUtils.isNullOrEmpty(context)) {
                    sb.append(time).append(" [").append(statusName).append("]").append("\n");
                    if (!StringUtils.isNullOrEmpty(title)) {
                        sb.append(title).append(": ");
                    }
                    sb.append(context).append("\n\n");
                    count++;
                }
            }

            // 如果轨迹信息超过5条，显示查看更多提示
            if (totalCount > 5) {
                sb.append("...\n");
                sb.append("共").append(totalCount).append("条物流记录，仅显示最新5条");
            }
        } else {
            sb.append("暂无物流轨迹信息");
        }

        return sb.toString();
    }
} 