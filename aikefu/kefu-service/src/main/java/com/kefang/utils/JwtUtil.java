package com.kefang.utils;

import com.kefang.config.AuthConfig;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

/**
 * JWT工具类，用于生成和验证token
 */
@Component
public class JwtUtil {

    @Autowired
    private AuthConfig authConfig;

    /**
     * 生成JWT token
     *
     * @param claims 存储在JWT中的信息
     * @param expiration 过期时间，单位毫秒
     * @return JWT token字符串
     */
    public String generateToken(Map<String, Object> claims, long expiration) {
        Date createdDate = new Date();
        Date expirationDate = new Date(createdDate.getTime() + expiration);

        return Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(createdDate)
                .setExpiration(expirationDate)
                .signWith(SignatureAlgorithm.HS512, authConfig.getJwtSecret())
                .compact();
    }

    /**
     * 从token中获取JWT的负载信息
     *
     * @param token JWT token字符串
     * @return 存储在JWT中的信息
     */
    public Claims getClaimsFromToken(String token) {
        return Jwts.parser()
                .setSigningKey(authConfig.getJwtSecret())
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 验证token是否有效
     *
     * @param token JWT token字符串
     * @return 如果token有效返回true，否则返回false
     */
    public boolean validateToken(String token) {
        try {
            Jwts.parser().setSigningKey(authConfig.getJwtSecret()).parseClaimsJws(token);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查token是否过期
     *
     * @param token JWT token字符串
     * @return 如果token过期返回true，否则返回false
     */
    public boolean isTokenExpired(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            Date expiration = claims.getExpiration();
            return expiration.before(new Date());
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 从token中获取用户ID
     *
     * @param token JWT token字符串
     * @return 用户ID
     */
    public Long getUserIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return Long.valueOf(claims.get("userId").toString());
    }

    /**
     * 从token中获取用户角色
     *
     * @param token JWT token字符串
     * @return 用户角色
     */
    public String getRoleFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.get("role").toString();
    }
} 