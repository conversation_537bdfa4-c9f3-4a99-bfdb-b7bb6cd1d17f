package com.kefang.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/7/16 15:15
 * Copyright juranguanjia. All Rights Reserved.
 * Description:日期工具类
 */
public class DateUtil {

    public static final String PATTERN_STANDARD_DATE_TIME = "yyyy-MM-dd HH:mm:ss";

    private static final long ONE_MINUTE = 60000L;
    private static final long ONE_HOUR = 3600000L;
    private static final long ONE_DAY = 86400000L;
    private static final long ONE_WEEK = 604800000L;

    private static final String ONE_SECOND_AGO = "秒前";
    private static final String ONE_MINUTE_AGO = "分钟前";
    private static final String ONE_HOUR_AGO = "小时前";
    private static final String ONE_DAY_AGO = "天前";
    private static final String ONE_MONTH_AGO = "月前";
    private static final String ONE_YEAR_AGO = "年前";

    private DateUtil() {
    }

    /**
     * 按照指定的格式返回日期字符串，默认"yyyy-MM-dd"
     *
     * @param date    日期
     * @param pattern 指定格式
     * @return 指定格式的日期字符串
     */
    @SuppressWarnings("all")
    public static String formatDate(Date date, String pattern) {
        if (date == null) return "";
        if (pattern == null) pattern = "yyyy-MM-dd";
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return (sdf.format(date));
    }


    /**
     * 获取指定日期对象中的年
     * @param date 日期对象
     * @return 年
     */
    public static Integer getYear(Date date) {
        if(null == date) return null;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.YEAR);
    }

    /**
     * 获得指定日期中的月份(1-12)
     * @param date 日期对象
     * @return 月
     */
    public static Integer getMonth(Date date) {
        if(null == date) return null;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MONTH)+1;
    }

    /**
     * 获取指定日期中的在该月中的天（从1开始）
     * @param date 日期对象
     * @return 日
     */
    public static Integer getDay(Date date) {
        if(null == date) return null;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获得上个月
     * @param date 当前期间
     * @return 上个月
     */
    public static Integer getBeforeMonth(Date date){
        if(null == date) return null;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MONTH);
    }

    public static Date getDate(int year, int month, int day) {
        switch (month) {
            case 1:
                month = Calendar.JANUARY;
                break;
            case 2:
                month = Calendar.FEBRUARY;
                break;
            case 3:
                month = Calendar.MARCH;
                break;
            case 4:
                month = Calendar.APRIL;
                break;
            case 5:
                month = Calendar.MAY;
                break;
            case 6:
                month = Calendar.JUNE;
                break;
            case 7:
                month = Calendar.JULY;
                break;
            case 8:
                month = Calendar.AUGUST;
                break;
            case 9:
                month = Calendar.SEPTEMBER;
                break;
            case 10:
                month = Calendar.OCTOBER;
                break;
            case 11:
                month = Calendar.NOVEMBER;
                break;
            case 12:
                month = Calendar.DECEMBER;
                break;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month, day);
        return calendar.getTime();
    }

    public static Date getPreviousMonthFirstDay(Date date){
        int previousMonth = getBeforeMonth(date);
        int year = getYear(date);
        //判断是否跨年
        if(previousMonth==0){
            previousMonth = 12;
            year = year-1;
        }
        return getDate(year, previousMonth, 1);
    }

    public static String getPreviousMonthFirstDayDate(Date date){
        Date d = getPreviousMonthFirstDay(date);
        return formatDate(d, "yyyy-MM-dd 00:00:00");
    }

    public static String getLastDayOfMonthDate(Date date){
        int month = getMonth(date);
        int year = getYear(date);
        Date d = getLastDayOfMonth(year, month);
        return formatDate(d, "yyyy-MM-dd 23:59:59");
    }
    public static String getFirstDayOfMonthDate(Date date){
        int month = getMonth(date);
        int year = getYear(date);
        Date d = getDate(year, month, 1);
        return formatDate(d, "yyyy-MM-dd 00:00:00");
    }
    public static Date getFifTeenthDayOfMonthDate(Date date){
        int month = getMonth(date);
        int year = getYear(date);
        Date d = getDate(year, month, 15);
        return d;
    }

    /**
     *  获得当前周- 周一的日期
     */
    public static String getCurrentMonday() {
        int mondayPlus = getMondayPlus();
        GregorianCalendar currentDate = new GregorianCalendar();
        currentDate.add(GregorianCalendar.DATE, mondayPlus);
        Date monday = currentDate.getTime();
        return formatDate(monday, "yyyy-MM-dd 00:00:00");
    }

    /**
     * 获得当前周- 周日的日期
     * @return
     */
    public static String getPreviousSunday() {
        int mondayPlus = getMondayPlus();
        GregorianCalendar currentDate = new GregorianCalendar();
        currentDate.add(GregorianCalendar.DATE, mondayPlus +6);
        Date monday = currentDate.getTime();
        return formatDate(monday, "yyyy-MM-dd 23:59:59");
    }

    // 获得本周一与当前日期相差的天数
    public static  int getMondayPlus() {
        Calendar cd = Calendar.getInstance();
        int dayOfWeek = cd.get(Calendar.DAY_OF_WEEK);
        //由于Calendar提供的都是以星期日作为周一的开始时间
        if (dayOfWeek == 1) {
            return -6;
        } else {
            return 2 - dayOfWeek;
        }
    }

    public static Date getLastDayOfMonth(int year, int month) {
        Date date = getDate(year, month, 1);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        return calendar.getTime();
    }

    public static Date getLastDayOfMonth(Date date) {
        return getLastDayOfMonth(getYear(date), getMonth(date));
    }

    public static Date getFirstDayOfMonth(Date date) {
        return getDate(getYear(date), getMonth(date), 1);
    }
    /**
     * 字符串转时间
     * @param strdate 字符串时间（如：2017-01-01）
     * @param format 格式（如：yyyy-MM-dd）
     * @return Date时间
     */
    public static Date stringToDate(String strdate, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date date = null;
        try {
            date = sdf.parse(strdate);
        } catch (ParseException e) {
        }
        return date;
    }

    /**
     * 给时间加上或减去指定毫秒，秒，分，时，天、月或年等，返回变动后的时间
     *
     * @param date   要加减前的时间，如果不传，则为当前日期
     * @param field  时间域，有Calendar.MILLISECOND,Calendar.SECOND,Calendar.MINUTE,<br>
     *               Calendar.HOUR,Calendar.DATE, Calendar.MONTH,Calendar.YEAR
     * @param amount 按指定时间域加减的时间数量，正数为加，负数为减。
     * @return 变动后的时间
     */
    public static Date add(Date date, int field, int amount) {
        if (date == null) {
            date = new Date();
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(field, amount);

        return cal.getTime();
    }

    public static Date addMonth(Date date, int amount) {
        return add(date, Calendar.MONTH, amount);
    }

    /**
     * 获得上个期间
     * @param year 年
     * @param month 月
     * @return 上个期间
     */
    public static Date getBeforePeriod(int year, int month){
        Date date = stringToDate(year + "-" + month , "yyyy-MM");
        int beforeMonth = DateUtil.getBeforeMonth(date);
        if(12 == beforeMonth){
            return stringToDate(year-1 + "-" + beforeMonth, "yyyy-MM");
        }
        return stringToDate(year + "-" + beforeMonth, "yyyy-MM");
    }

    /**
     * 获得季度
     * @param date 期间
     * @return 季度
     */
    public static int getQuarter(Date date) {
        return ((getMonth(date) - 1) / 3 + 1);
    }

    /**
     * 取得季度月
     * @param date 期间
     * @return 季度月范围
     */
    public static Date[] getSeasonDate(Date date) {
        Date[] season = new Date[3];

        Calendar c = Calendar.getInstance();
        c.setTime(date);

        int nSeason = getQuarter(date);
        if (nSeason == 1) {// 第一季度
            c.set(Calendar.MONTH, Calendar.JANUARY);
            season[0] = c.getTime();
            c.set(Calendar.MONTH, Calendar.FEBRUARY);
            season[1] = c.getTime();
            c.set(Calendar.MONTH, Calendar.MARCH);
            season[2] = c.getTime();
        } else if (nSeason == 2) {// 第二季度
            c.set(Calendar.MONTH, Calendar.APRIL);
            season[0] = c.getTime();
            c.set(Calendar.MONTH, Calendar.MAY);
            season[1] = c.getTime();
            c.set(Calendar.MONTH, Calendar.JUNE);
            season[2] = c.getTime();
        } else if (nSeason == 3) {// 第三季度
            c.set(Calendar.MONTH, Calendar.JULY);
            season[0] = c.getTime();
            c.set(Calendar.MONTH, Calendar.AUGUST);
            season[1] = c.getTime();
            c.set(Calendar.MONTH, Calendar.SEPTEMBER);
            season[2] = c.getTime();
        } else if (nSeason == 4) {// 第四季度
            c.set(Calendar.MONTH, Calendar.OCTOBER);
            season[0] = c.getTime();
            c.set(Calendar.MONTH, Calendar.NOVEMBER);
            season[1] = c.getTime();
            c.set(Calendar.MONTH, Calendar.DECEMBER);
            season[2] = c.getTime();
        }
        return season;
    }

    /**
     * 比较两个期间相差几个月
     * @param start 起始期间
     * @param end 结束期间
     * @return 月份
     */
    public static int comparePeriod(Date start, Date end){
        int endYear = getYear(end);
        int endMonth = getMonth(end);
        int startYear = getYear(start);
        int startMonth = getMonth(start);
        return endYear*12+endMonth-startYear*12-startMonth;
    }

    public static Map getQuarterToMonth(int quarter){
        Map<String,String> map = new HashMap<>();
        String startMonth = null;
        String endMonth = null;
        if (quarter==1){
            startMonth = "01";
            endMonth = "03";
        }else if (quarter==2){
            startMonth = "04";
            endMonth = "06";
        }else if (quarter == 3){
            startMonth = "07";
            endMonth = "09";
        }else if (quarter == 4){
            startMonth = "10";
            endMonth = "12";
        }
        map.put("startMonth",startMonth);
        map.put("endMonth",endMonth);
        return map;
    }

    /**
     * 获取指定日期的开始日期 00:00:00或者结束日期23:59:59
     * @param date
     * @flag 0 返回yyyy-MM-dd 00:00:00日期<br>
     *       1 返回yyyy-MM-dd 23:59:59日期
     * @return
     */
    public static Date weeHours(Date date, int flag) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        int minute = cal.get(Calendar.MINUTE);
        int second = cal.get(Calendar.SECOND);
        //时分秒（毫秒数）
        long millisecond = hour*60*60*1000 + minute*60*1000 + second*1000;
        //凌晨00:00:00
        cal.setTimeInMillis(cal.getTimeInMillis()-millisecond);

        if (flag == 0) {
            return cal.getTime();
        } else if (flag == 1) {
            //凌晨23:59:59
            cal.setTimeInMillis(cal.getTimeInMillis()+23*60*60*1000 + 59*60*1000 + 59*1000);
        }
        return cal.getTime();
    }

    public static Long getBetweenDays(String date1,String date2){
        try {
            // 获取相差的天数
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(stringToDate(date1,"yyyy-MM-dd"));
            long timeInMillis1 = calendar.getTimeInMillis();
            calendar.setTime(stringToDate(date2,"yyyy-MM-dd"));
            long timeInMillis2 = calendar.getTimeInMillis();

            long betweenDays =  (timeInMillis2 - timeInMillis1) / (1000L*3600L*24L);
            return betweenDays;
        }catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;
    }

    public static String getCurrentDate()
    {
        return formatDate(new Date(),"yyyy-MM-dd");
    }

    public static String getCurrentTime()
    {
        return formatDate(new Date(),"yyyyMMddHHmmss");
    }

    public static String getCurrentDateTime()
    {
        return formatDate(new Date(),PATTERN_STANDARD_DATE_TIME);
    }

    public static String format(Date date) {
        long delta = new Date().getTime() - date.getTime();
        if (delta < 1L * ONE_MINUTE) {
            long seconds = toSeconds(delta);
            return (seconds <= 0 ? 1 : seconds) + ONE_SECOND_AGO;
        }
        if (delta < 45L * ONE_MINUTE) {
            long minutes = toMinutes(delta);
            return (minutes <= 0 ? 1 : minutes) + ONE_MINUTE_AGO;
        }
        if (delta < 24L * ONE_HOUR) {
            long hours = toHours(delta);
            return (hours <= 0 ? 1 : hours) + ONE_HOUR_AGO;
        }
        if (delta < 48L * ONE_HOUR) {
            return "昨天";
        }
        if (delta < 30L * ONE_DAY) {
            long days = toDays(delta);
            return (days <= 0 ? 1 : days) + ONE_DAY_AGO;
        }
        if (delta < 12L * 4L * ONE_WEEK) {
            long months = toMonths(delta);
            return (months <= 0 ? 1 : months) + ONE_MONTH_AGO;
        } else {
            long years = toYears(delta);
            return (years <= 0 ? 1 : years) + ONE_YEAR_AGO;
        }
    }

    private static long toSeconds(long date) {
        return date / 1000L;
    }

    private static long toMinutes(long date) {
        return toSeconds(date) / 60L;
    }

    private static long toHours(long date) {
        return toMinutes(date) / 60L;
    }

    private static long toDays(long date) {
        return toHours(date) / 24L;
    }

    private static long toMonths(long date) {
        return toDays(date) / 30L;
    }

    private static long toYears(long date) {
        return toMonths(date) / 365L;
    }



    public static boolean isBetweenTimes(String now,String clearStartTime,String clearEndTime) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
            Date startTime = sdf.parse(clearStartTime);
            Date endTime = sdf.parse(clearEndTime);
            Date nowTime = sdf.parse(now);
            Calendar date = Calendar.getInstance();
            date.setTime(nowTime);
            Calendar begin = Calendar.getInstance();
            begin.setTime(startTime);
            Calendar end = Calendar.getInstance();
            end.setTime(endTime);
            if (end.before(begin)) {
                if (date.before(end)) {
                    date.add(Calendar.DATE, 1);
                }
                end.add(Calendar.DATE, 1);
            }
            return date.after(begin) && date.before(end);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * a在b之前
     *
     * @param a
     * @param b
     * @return
     */
    public static boolean isBefore(Date a, Date b) {
        if (a == null) {
            return false;
        }
        return a.compareTo(b) < 0;
    }

    /**
     * a在b之后
     *
     * @param a
     * @param b
     * @return
     */
    public static boolean isAfter(Date a, Date b) {
        if (a == null) {
            return false;
        }
        return b.compareTo(a) < 0;
    }

    /**
     * 获取几天前的开始时间
     * @param n
     * @return
     */
    public static String getBeforeDateStartTime(int n) {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        calendar.add(Calendar.DATE, -n);
        Date date = calendar.getTime();
        String daytime = sdf.format(date);
        return daytime;
    }

    /**
     * 获取几天前的结束时间
     * @param n
     * @return
     */
    public static String getBeforeDateEndTime(int n) {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
        calendar.add(Calendar.DATE, -n);
        Date date = calendar.getTime();
        String daytime = sdf.format(date);
        return daytime;
    }

    /**
     * 获取时间戳(秒)
     * @param date
     * @return
     */
    public static Long getTimeStamp(Date date){
        return date.getTime()/1000;
    }

    public static Date getDateByUnit(String unit) {
        Date date = new Date();
        Integer total = 0;
        if("月".equals(unit)){
            total = 1;
        }
        if("季度".equals(unit)){
            total = 3;
        }
        if("年".equals(unit)){
            total = 12;
        }
        if(total > 0){
            return add(date,Calendar.MONTH, total);
        }
        return date;
    }

    public static void main(String[] args) {
//        System.out.println(getPreviousMonthFirstDayDate(new Date()));
//        System.out.println(getCurrentMonday());
//        System.out.println(getPreviousSunday());
//        System.out.println(getFirstDayOfMonthDate(new Date()));
//        System.out.println(getLastDayOfMonthDate(new Date()));
//        System.out.println(getDay(new Date()));
//        System.out.println(getBetweenDays("2021-12-14","2021-12-16"));
//        System.out.println(getCurrentDate());

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:m:s");
        Date date = null;
        try {
            date = format.parse("2021-7-25 14:00:35");
            System.out.println(date.getTime());
            System.out.println(getTimeStamp(date));
        } catch (ParseException e) {
            e.printStackTrace();
        }
//        System.out.println(format(date));

//        if(DateUtil.getDay(nowDate).intValue()<16){ //计算当天日期与16日比较
//            endDate =  DateUtil.getPreviousMonthFirstDay(nowDate);
//        }else{
//            endDate = DateUtil.getFirstDayOfMonth(nowDate);
//        }
//        Date nowDate = new Date();
//        Integer count = DateUtil.getDay(nowDate);
//        Date PreviousDay = DateUtil.getPreviousMonthFirstDay(nowDate);
//        Date firstDay = DateUtil.getFirstDayOfMonth(nowDate);
//        System.out.println(count);
//        System.out.println(DateUtil.formatDate(PreviousDay));
//        System.out.println(DateUtil.formatDate(firstDay));
        //System.out.println(nowDate.getTime());
//        Boolean f = isAfter(nowDate,date);
//        System.out.println(f);
    }


}
