package com.kefang.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 订单号工具类，用于处理订单号的提取和验证
 */
public class OrderUtils {

    /**
     * 订单号正则表达式：匹配HS开头，后跟至少18位数字，或RE开头，后跟至少13位数字 或以 “第三方” 或 “第三方单号” 开头
     */
    private static final String ORDER_NUMBER_PATTERN = "(HS\\d{18,}|RE\\d{13,}|第三方.*?[a-zA-Z0-9]{8,})";
    ;

    /**
     * 取消订单正则表达式：匹配"取消"和订单号，两者顺序不限
     */
    private static final String CANCEL_ORDER_PATTERN_PREFIX = "取消.*?订单.*?(" + ORDER_NUMBER_PATTERN + ")";
    private static final String CANCEL_ORDER_PATTERN_SUFFIX = "(" + ORDER_NUMBER_PATTERN + ").*?取消.*?订单";

    /**
     * 物流订单正则表达式：匹配包含"物流"关键词和订单号的文本，两者顺序不限
     */
    private static final String EXPRESS_ORDER_PATTERN_PREFIX = "物流.*?(" + ORDER_NUMBER_PATTERN + ")";
    private static final String EXPRESS_ORDER_PATTERN_SUFFIX = "(" + ORDER_NUMBER_PATTERN + ").*?物流";

    /**
     * 取消物流订单正则表达式：匹配包含"取消物流"关键词和订单号的文本，两者顺序不限
     */
    private static final String CANCEL_EXPRESS_ORDER_PATTERN_PREFIX = "取消.*?物流.*?(" + ORDER_NUMBER_PATTERN + ")";
    private static final String CANCEL_EXPRESS_ORDER_PATTERN_SUFFIX = "(" + ORDER_NUMBER_PATTERN + ").*?取消.*?物流";
    private static final String CANCEL_EXPRESS_ORDER_PATTERN_MIX = "物流.*?取消.*?(" + ORDER_NUMBER_PATTERN + ")";
    private static final String CANCEL_EXPRESS_ORDER_PATTERN_MIX2 = "(" + ORDER_NUMBER_PATTERN + ").*?物流.*?取消";

    /**
     * 从文本中提取第一个有效的订单号
     *
     * @param text 包含可能订单号的文本
     * @return 提取的订单号，如果没有找到则返回空字符串
     */
    public static String extractOrderNumber(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        Pattern pattern = Pattern.compile(ORDER_NUMBER_PATTERN);
        Matcher matcher = pattern.matcher(text);

        if (matcher.find()) {
            String matchedText = matcher.group();
            // 过滤掉所有非字母数字字符
            return matchedText.replaceAll("[^a-zA-Z0-9]", "");
        }

        return "";
    }


    /**
     * 验证字符串是否为有效的订单号格式
     *
     * @param orderNumber 需要验证的订单号
     * @return 如果是有效格式返回true，否则返回false
     */
    public static boolean isValidOrderNumber(String orderNumber) {
        if (orderNumber == null || orderNumber.isEmpty()) {
            return false;
        }

        return orderNumber.matches(ORDER_NUMBER_PATTERN);
    }

    /**
     * 从文本中提取"取消 订单号"或"订单号 取消"格式中的订单号
     *
     * @param text 包含可能"取消+订单号"或"订单号+取消"的文本
     * @return 提取的订单号，如果没有找到"取消"关键词或订单号则返回空字符串
     */
    public static String extractCancelOrderNumber(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        // 尝试匹配各种可能的格式
        // 1. "取消...订单...订单号"
        Pattern pattern1 = Pattern.compile(CANCEL_ORDER_PATTERN_PREFIX);
        Matcher matcher1 = pattern1.matcher(text);
        if (matcher1.find() && matcher1.groupCount() >= 1) {
            return matcher1.group(1).replaceAll("[^a-zA-Z0-9]", "");
        }

        // 2. "订单号...取消...订单"
        Pattern pattern2 = Pattern.compile(CANCEL_ORDER_PATTERN_SUFFIX);
        Matcher matcher2 = pattern2.matcher(text);
        if (matcher2.find() && matcher2.groupCount() >= 1) {
            return matcher2.group(1).replaceAll("[^a-zA-Z0-9]", "");
        }

        // 3. "订单...取消...订单号"
        Pattern pattern3 = Pattern.compile("订单.*?取消.*?(" + ORDER_NUMBER_PATTERN + ")");
        Matcher matcher3 = pattern3.matcher(text);
        if (matcher3.find() && matcher3.groupCount() >= 1) {
            return matcher3.group(1).replaceAll("[^a-zA-Z0-9]", "");
        }

        // 4. "订单号...订单...取消"
        Pattern pattern4 = Pattern.compile("(" + ORDER_NUMBER_PATTERN + ").*?订单.*?取消");
        Matcher matcher4 = pattern4.matcher(text);
        if (matcher4.find() && matcher4.groupCount() >= 1) {
            return matcher4.group(1).replaceAll("[^a-zA-Z0-9]", "");
        }

        // 兜底方案：如果文本中同时包含"取消"和"订单"关键词，并且有订单号，则返回订单号
        if (text.contains("取消") && text.contains("订单")) {
            Pattern orderPattern = Pattern.compile(ORDER_NUMBER_PATTERN);
            Matcher orderMatcher = orderPattern.matcher(text);
            if (orderMatcher.find()) {
                return orderMatcher.group().replaceAll("[^a-zA-Z0-9]", "");
            }
        }

        return "";
    }

    /**
     * 从文本中提取包含"物流"关键词和订单号的文本
     *
     * @param text 包含可能"物流+订单号"或"订单号+物流"的文本
     * @return 提取的订单号，如果没有找到"物流"关键词或订单号则返回空字符串
     */
    public static String extractExpressOrderNumber(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        // 先尝试匹配"物流"在前的格式
        Pattern prefixPattern = Pattern.compile(EXPRESS_ORDER_PATTERN_PREFIX);
        Matcher prefixMatcher = prefixPattern.matcher(text);

        if (prefixMatcher.find() && prefixMatcher.groupCount() >= 1) {
            return prefixMatcher.group(1).replaceAll("[^a-zA-Z0-9]", "");
        }

        // 如果匹配失败，尝试匹配"物流"在后的格式
        Pattern suffixPattern = Pattern.compile(EXPRESS_ORDER_PATTERN_SUFFIX);
        Matcher suffixMatcher = suffixPattern.matcher(text);

        if (suffixMatcher.find() && suffixMatcher.groupCount() >= 1) {
            return suffixMatcher.group(1).replaceAll("[^a-zA-Z0-9]", "");
        }

        // 如果上面两种模式都匹配失败，检查是否包含"物流"关键词和订单号
        if (text.contains("物流")) {
            Pattern orderPattern = Pattern.compile(ORDER_NUMBER_PATTERN);
            Matcher orderMatcher = orderPattern.matcher(text);
            if (orderMatcher.find()) {
                return orderMatcher.group().replaceAll("[^a-zA-Z0-9]", "");
            }
        }

        return "";
    }

    /**
     * 从文本中提取包含"取消物流"关键词和订单号的文本
     *
     * @param text 包含可能"取消物流+订单号"或"订单号+取消物流"的文本
     * @return 提取的订单号，如果没有找到"取消物流"关键词或订单号则返回空字符串
     */
    public static String extractCancelExpressOrderNumber(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        // 尝试匹配各种可能的格式
        // 1. "取消...物流...订单号"
        Pattern pattern1 = Pattern.compile(CANCEL_EXPRESS_ORDER_PATTERN_PREFIX);
        Matcher matcher1 = pattern1.matcher(text);
        if (matcher1.find() && matcher1.groupCount() >= 1) {
            return matcher1.group(1).replaceAll("[^a-zA-Z0-9]", "");
        }

        // 2. "订单号...取消...物流"
        Pattern pattern2 = Pattern.compile(CANCEL_EXPRESS_ORDER_PATTERN_SUFFIX);
        Matcher matcher2 = pattern2.matcher(text);
        if (matcher2.find() && matcher2.groupCount() >= 1) {
            return matcher2.group(1).replaceAll("[^a-zA-Z0-9]", "");
        }

        // 3. "物流...取消...订单号"
        Pattern pattern3 = Pattern.compile(CANCEL_EXPRESS_ORDER_PATTERN_MIX);
        Matcher matcher3 = pattern3.matcher(text);
        if (matcher3.find() && matcher3.groupCount() >= 1) {
            return matcher3.group(1).replaceAll("[^a-zA-Z0-9]", "");
        }

        // 4. "订单号...物流...取消"
        Pattern pattern4 = Pattern.compile(CANCEL_EXPRESS_ORDER_PATTERN_MIX2);
        Matcher matcher4 = pattern4.matcher(text);
        if (matcher4.find() && matcher4.groupCount() >= 1) {
            return matcher4.group(1).replaceAll("[^a-zA-Z0-9]", "");
        }

        // 兜底方案：如果文本中同时包含"取消"和"物流"关键词，并且有订单号，则返回订单号
        if (text.contains("取消") && text.contains("物流")) {
            Pattern orderPattern = Pattern.compile(ORDER_NUMBER_PATTERN);
            Matcher orderMatcher = orderPattern.matcher(text);
            if (orderMatcher.find()) {
                return orderMatcher.group().replaceAll("[^a-zA-Z0-9]", "");
            }
        }

        return "";
    }

    public static void main(String[] args) {
        // 测试提取订单号
        String[] testTexts = {
                "请帮我查询订单号HS123456789012345678",
                "订单HS987654321098765432怎么查询",
                "RE0125053000109485这个订单号还没收到货",
                "我要查询RE0125053000109485物流",
                "第三方单号  053000ff109485",
                "第三方单号444 053000ff109485",
                "订单RE0125053000109485已经收到"
        };

        System.out.println("=== 测试提取订单号 ===");
        for (String text : testTexts) {
            String orderNumber = extractOrderNumber(text);
            System.out.println("从文本 '" + text + "' 中提取订单号: " + orderNumber);
            System.out.println("是否有效订单号: " + isValidOrderNumber(orderNumber));
        }
    }
} 