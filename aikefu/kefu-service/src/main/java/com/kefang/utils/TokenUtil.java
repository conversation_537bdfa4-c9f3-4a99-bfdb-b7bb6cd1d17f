package com.kefang.utils;
import java.util.*;
import java.text.SimpleDateFormat;

public class TokenUtil {

    public static String[] withoutOl01chars = new String[] { "a", "b", "c", "d", "e", "f",
            "g", "h", "i", "j", "k", "m", "n", "o", "p", "q", "r", "s",
            "t", "u", "v", "w", "x", "y", "z", "2", "3", "4", "5",
            "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H", "I",
            "J", "K", "L", "M", "N", "P", "Q", "R", "S", "T", "U", "V",
            "W", "X", "Y", "Z" };
    public static Map<String, String> generatePrivateKeyAndApiToken() {
        // 日期格式 MMddyyyy
        String date = DateUtil.formatDate(new Date(), "MMddyyyy");
        String privateKey= generateShortUuid();
        String apitoken = MD5Util.MD5Encode(date + MD5Util.MD5Encode("JrWy2020Gj", "UTF-8") +privateKey, "UTF-8");


        Map<String, String> result = new HashMap<>();
        result.put("privateKey", privateKey);
        result.put("apitoken", apitoken);
        return result;
    }

    public static String generateShortUuid() {
        StringBuffer shortBuffer = new StringBuffer();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        for (int i = 0; i < 8; i++) {
            String str = uuid.substring(i * 4, i * 4 + 4);
            int x = Integer.parseInt(str, 16);
            shortBuffer.append(withoutOl01chars[x % 0x3A]);
        }
        return shortBuffer.toString();

    }
    public static void main(String[] args) {
        Map<String, String> result = generatePrivateKeyAndApiToken();
        System.out.println("privateKey: " + result.get("privateKey"));
        System.out.println("apitoken: " + result.get("apitoken"));
    }
}
