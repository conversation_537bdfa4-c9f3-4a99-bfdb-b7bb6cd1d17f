package com.kefang.utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.Random;
import java.security.Security;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class WXBizMsgCrypt {

    private final String token;
    private final String encodingAesKey;
    private final String appId;
    
    // 添加备用的AES密钥
    private final String[] backupEncodingAesKeys = new String[2];
    
    private static final Logger log = LoggerFactory.getLogger(WXBizMsgCrypt.class);
    
    // 静态初始化块，注册BouncyCastle提供程序
    static {
        try {
            Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
            // log.info("已注册BouncyCastle提供程序");
        } catch (Exception e) {
            // log.warn("注册BouncyCastle提供程序失败: {}", e.getMessage());
        }
     // 检查并解除AES密钥长度限制
     try {
        int maxKeyLength = Cipher.getMaxAllowedKeyLength("AES");
        // log.info("当前AES最大密钥长度: {} 位", maxKeyLength);
        if (maxKeyLength < 256) {
            log.warn("AES密钥长度受限，需要下载并安装JCE无限制权限策略文件");
        }
    } catch (Exception e) {
        log.warn("检查AES密钥长度限制失败: {}", e.getMessage());
    }

    }

    public WXBizMsgCrypt(String token, String encodingAesKey, String appId) {
        this.token = token;
        this.encodingAesKey = encodingAesKey;
        this.appId = appId;
        
        // 初始化备用密钥列表
        // 第一个备用密钥是当前密钥，我们还可以设置其他备用密钥（如果有的话）
        backupEncodingAesKeys[0] = encodingAesKey;
        // log.info("已加载当前EncodingAesKey作为备用密钥"+backupEncodingAesKeys.toString());
        
        // 读取配置文件中可能存在的旧密钥
//        try {
//            // 这里可以从配置文件或环境变量中读取旧的EncodingAesKey
//            String oldKey = System.getProperty("wechat.old.encodingaeskey");
//            if (oldKey != null && !oldKey.isEmpty() && !oldKey.equals(encodingAesKey)) {
//                backupEncodingAesKeys[1] = oldKey;
//                log.info("已加载旧的EncodingAesKey作为备用密钥");
//            }
//        } catch (Exception e) {
//            log.warn("读取旧密钥失败: {}", e.getMessage());
//        }
    }
    
    /**
     * 如果有需要，可以设置备用密钥
     * @param backupKey 备用密钥
     */
    public void setBackupKey(String backupKey) {
        if(backupKey != null && !backupKey.isEmpty()) {
            backupEncodingAesKeys[1] = backupKey;
        }
    }
    /**
     * 解密消息
     * @param encryptedMsg 加密后的消息
     * @return 解密后的XML
     * @throws Exception 解密失败时抛出异常
     */
    public String decrypt(String encryptedMsg) throws Exception {
        // log.info("开始使用官方方式解密: {}", encryptedMsg.length() > 50 ? encryptedMsg.substring(0, 50) + "..." : encryptedMsg);
        
        // 记录最后一个异常，如果所有密钥都解密失败，则抛出此异常
        Exception lastException = null;
        
        // 首先尝试使用当前密钥解密
        for (String currentAesKey : backupEncodingAesKeys) {
            // log.info("尝试使用密钥 {} 解密", currentAesKey);
            // 跳过空的备用密钥
            if (currentAesKey == null || currentAesKey.isEmpty()) {
                continue;
            }
            
            try {
                // 尝试使用当前密钥解密
                String result = tryDecrypt(encryptedMsg, currentAesKey);
                if (result != null) {
                    return result;
                }
            } catch (Exception e) {
                log.warn("使用密钥 {} 解密失败: {}", currentAesKey.substring(0, Math.min(8, currentAesKey.length())), e.getMessage());
                lastException = e;
                // 继续尝试下一个密钥
            }
        }
                // 长消息解密尝试
                try {
                    log.info("尝试使用长消息解密方式解密");
                    String result = decryptLongMessage(encryptedMsg, encodingAesKey);
                    if (result != null) {
                        return result;
                    }
                } catch (Exception e) {
                    log.warn("长消息解密方式失败: {}", e.getMessage());
                    if (lastException == null) {
                        lastException = e;
                    }
                }
        // 如果所有密钥都尝试过但都失败，抛出最后一个异常
        if (lastException != null) {
            throw lastException;
        } else {
            throw new Exception("所有密钥都解密失败，且没有捕获到异常");
        }
    }
    
    private String tryDecrypt(String encryptedMsg, String currentAesKey) throws Exception {
        try {
            // 对密文进行处理，确保它符合Base64格式
            String cleanEncrypted = encryptedMsg.trim().replace("\n", "").replace("\r", "");
            
            // 记录解密前密文的长度信息
            // log.info("解密前密文长度: {}", cleanEncrypted.length());
            
            // 确保编码长度符合Base64要求（必须是4的倍数）
            int mod = cleanEncrypted.length() % 4;
            if (mod != 0) {
                // log.info("Base64字符串长度需要调整，当前长度: {}, 余数: {}", cleanEncrypted.length(), mod);
                for (int i = 0; i < (4 - mod); i++) {
                    cleanEncrypted = cleanEncrypted + "=";
                }
            }
            
            // AES密钥必须是43位长度的Base64字符串，对应解码后为32字节的AES-256密钥
            // log.info("当前使用的encodingAesKey: {}, 长度: {}", currentAesKey, currentAesKey.length());
            
            // 确保encodingAesKey长度正确（微信的encodingAesKey是43位的，末尾可能会缺少=号）
            String fullAesKey = currentAesKey;
            if (fullAesKey.length() != 43) {
                // 微信的AES密钥是43位Base64编码，可能需要补充=
                int paddingLen = 43 - fullAesKey.length();
                if (paddingLen > 0) {
                    fullAesKey = fullAesKey + "=".repeat(paddingLen);
                } else {
                    // 如果长度超过43，可能包含了多余的字符，截取前43位
                    fullAesKey = fullAesKey.substring(0, 43);
                }
            }
            // log.info("处理后的AES密钥: {}, 长度: {}", fullAesKey.substring(0, Math.min(8, fullAesKey.length())) + "***", fullAesKey.length());
            
            // 解码AES密钥
            byte[] aesKey = Base64.getDecoder().decode(fullAesKey);
            // log.info("解码后的AES密钥长度: {} 字节", aesKey.length);
            
            // AES密钥必须是16/24/32字节，微信使用32字节(256位)的AES密钥
            if (aesKey.length != 32) {
                throw new IllegalArgumentException("解码后的AES密钥长度不正确，期望32字节，实际" + aesKey.length + "字节");
            }
            
            // 尝试多种可能的解密场景
            
            // 1. 标准解密
            try {
                return standardDecrypt(cleanEncrypted, aesKey);
            } catch (Exception e) {
                log.debug("标准解密失败，尝试其他方法: {}", e.getMessage());
            }
            
            // 2. URL安全的Base64格式解密
            try {
                return urlSafeDecrypt(cleanEncrypted, aesKey);
            } catch (Exception e) {
                log.debug("URL安全格式解密失败，尝试其他方法: {}", e.getMessage());
            }
            
            // 3. 尝试反转处理（微信有时会把+/-和//_混用）
            try {
                return reverseUrlSafeDecrypt(cleanEncrypted, aesKey);
            } catch (Exception e) {
                log.debug("反转URL安全格式解密失败，尝试其他方法: {}", e.getMessage());
            }
            
            // 4. 尝试原始/raw字节解密（适用于某些密钥格式）
            try {
                return rawBytesDecrypt(cleanEncrypted, fullAesKey);
            } catch (Exception e) {
                log.debug("原始字节解密失败: {}", e.getMessage());
                throw e; // 所有方法都失败，抛出最后一个异常
            }
        } catch (Exception e) {
            log.error("解密过程中发生错误: {}", e.getMessage());
            throw e;
        }
    }
    
    // 标准解密方法
    private String standardDecrypt(String cleanEncrypted, byte[] aesKey) throws Exception {
        // 解码加密后的消息
        byte[] encryptedBytes = Base64.getDecoder().decode(cleanEncrypted);
        // log.info("标准解密 - 解码后的密文长度: {} 字节", encryptedBytes.length);
        
        // 使用AES-CBC模式解密，IV为AES密钥的前16字节
        byte[] iv = Arrays.copyOfRange(aesKey, 0, 16);
        
        // 初始化解密器 - 尝试使用PKCS7Padding
        Cipher cipher;
        try {
            cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
            log.debug("使用PKCS7Padding解密");
        } catch (Exception e) {
            // 如果BC提供程序不可用，使用标准填充
            log.debug("BC提供程序不可用，使用PKCS5Padding: {}", e.getMessage());
            cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        }
        
        SecretKeySpec keySpec = new SecretKeySpec(aesKey, "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
        
        // 解密数据
        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        // log.info("标准解密 - 解密后的数据长度: {} 字节", decryptedBytes.length);
        
        return parseDecryptedData(decryptedBytes);
    }
    
    // URL安全的Base64格式解密方法
    private String urlSafeDecrypt(String cleanEncrypted, byte[] aesKey) throws Exception {
        // 处理URL安全的Base64格式
        String urlSafeEncrypted = cleanEncrypted.replace('+', '-').replace('/', '_');
        
        // 确保长度是4的倍数
        while (urlSafeEncrypted.length() % 4 != 0) {
            urlSafeEncrypted += "=";
        }
        
        // 解码为字节数组
        byte[] encryptedBytes = Base64.getUrlDecoder().decode(urlSafeEncrypted);
        // log.info("URL安全解密 - 解码后的密文长度: {} 字节", encryptedBytes.length);
        
        // 使用AES-CBC模式解密
        byte[] iv = Arrays.copyOfRange(aesKey, 0, 16);
        
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        SecretKeySpec keySpec = new SecretKeySpec(aesKey, "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
        
        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        // log.info("URL安全解密 - 解密后的数据长度: {} 字节", decryptedBytes.length);
        
        return parseDecryptedData(decryptedBytes);
    }
    
    // 反转URL安全的Base64格式解密方法
    private String reverseUrlSafeDecrypt(String cleanEncrypted, byte[] aesKey) throws Exception {
        // 反向处理，从标准Base64转为URL安全格式
        String reversedEncrypted = cleanEncrypted.replace('-', '+').replace('_', '/');
        
        // 确保长度是4的倍数
        while (reversedEncrypted.length() % 4 != 0) {
            reversedEncrypted += "=";
        }
        
        // 解码为字节数组
        byte[] encryptedBytes = Base64.getDecoder().decode(reversedEncrypted);
        // log.info("反转URL安全解密 - 解码后的密文长度: {} 字节", encryptedBytes.length);
        
        // 使用AES-CBC模式解密
        byte[] iv = Arrays.copyOfRange(aesKey, 0, 16);
        
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        SecretKeySpec keySpec = new SecretKeySpec(aesKey, "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
        
        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        // log.info("反转URL安全解密 - 解密后的数据长度: {} 字节", decryptedBytes.length);
        
        return parseDecryptedData(decryptedBytes);
    }
    
    // 原始字节解密方法，适用于某些特殊密钥格式
    private String rawBytesDecrypt(String cleanEncrypted, String fullAesKey) throws Exception {
        try {
            byte[] rawKeyBytes = fullAesKey.getBytes(StandardCharsets.UTF_8);
            // log.info("原始字节解密 - 密钥字节长度: {}", rawKeyBytes.length);
            
            // 根据密钥长度，构建合适的AES密钥
            byte[] aesKeyBytes;
            if (rawKeyBytes.length == 16 || rawKeyBytes.length == 24 || rawKeyBytes.length == 32) {
                aesKeyBytes = rawKeyBytes;
            } else if (rawKeyBytes.length > 32) {
                aesKeyBytes = Arrays.copyOf(rawKeyBytes, 32);
            } else {
                // 如果密钥小于16字节，填充到16字节
                aesKeyBytes = Arrays.copyOf(rawKeyBytes, 16);
            }
            
            // 解码加密后的消息
            byte[] encryptedBytes = Base64.getDecoder().decode(cleanEncrypted);
            // info("原始字节解密 - 解码后的密文长度: {} 字节", encryptedBytes.length);
            
            // 使用AES-CBC模式解密，IV为AES密钥的前16字节
            byte[] iv = new byte[16];
            System.arraycopy(aesKeyBytes, 0, iv, 0, Math.min(aesKeyBytes.length, 16));
            
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(aesKeyBytes, "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            // log.info("原始字节解密 - 解密后的数据长度: {} 字节", decryptedBytes.length);
            
            return parseDecryptedData(decryptedBytes);
        } catch (Exception e) {
            log.error("原始字节解密失败: {}", e.getMessage());
            throw e;
        }
    }
    
    // 从解密后的字节数组中解析出XML内容
    private String parseDecryptedData(byte[] decryptedBytes) throws Exception {
        // 按照微信的解密格式：随机字符串(16字节) + 内容长度(4字节) + 内容 + AppID
        // 检查解密后的数据是否足够长
        if (decryptedBytes.length < 20) { // 至少需要16字节随机字符串和4字节长度
            throw new Exception("解密后数据长度不足，无法提取内容");
        }
        
        // 计算实际的内容长度
        int xmlLength = 0;
        try {
            xmlLength = ((decryptedBytes[16] & 0xFF) << 24)
                    | ((decryptedBytes[17] & 0xFF) << 16)
                    | ((decryptedBytes[18] & 0xFF) << 8)
                    | (decryptedBytes[19] & 0xFF);
            
            // log.info("解析的XML内容长度: {} 字节", xmlLength);
            
            if (xmlLength < 0 || xmlLength > decryptedBytes.length - 20) {
                log.error("XML内容长度异常: {}", xmlLength);
                // 可能是填充格式导致的错误，尝试不同的处理方式
                
                // 检查是否有明显的XML标记
                String fullContent = new String(decryptedBytes, StandardCharsets.UTF_8);
                int xmlStartIndex = fullContent.indexOf("<xml");
                if (xmlStartIndex >= 0) {
                    int xmlEndIndex = fullContent.lastIndexOf("</xml>");
                    if (xmlEndIndex > xmlStartIndex) {
                        String xmlContent = fullContent.substring(xmlStartIndex, xmlEndIndex + 6);
                        log.info("通过XML标记提取内容，长度: {}", xmlContent.length());
                        return xmlContent;
                    }
                }
                
                throw new Exception("XML内容长度异常");
            }
            
            // 提取XML内容
            byte[] xmlBytes = new byte[xmlLength];
            System.arraycopy(decryptedBytes, 20, xmlBytes, 0, xmlLength);
            
            // 尝试验证并提取AppID
            if (decryptedBytes.length >= 20 + xmlLength) {
                int appIdLength = decryptedBytes.length - 20 - xmlLength;
                if (appIdLength > 0) {
                    byte[] appIdBytes = new byte[appIdLength];
                    System.arraycopy(decryptedBytes, 20 + xmlLength, appIdBytes, 0, appIdLength);
                    String extractedAppId = new String(appIdBytes, StandardCharsets.UTF_8);
                    log.debug("解析的AppID: {}", extractedAppId);

                }
            }
            
            // 转换为字符串
            String xmlContent = new String(xmlBytes, StandardCharsets.UTF_8);
            // log.info("解密后的内容: {}", xmlContent.length() > 100 ? xmlContent.substring(0, 100) + "..." : xmlContent);
            
            // 验证内容是否为XML格式
            if (!xmlContent.startsWith("<xml") && !xmlContent.contains("<xml>")) {
                log.warn("解密内容不是XML格式: {}", xmlContent);
                // 再尝试查找XML标记
                int xmlStart = xmlContent.indexOf("<xml");
                if (xmlStart >= 0) {
                    xmlContent = xmlContent.substring(xmlStart);
                    log.info("提取XML部分: {}", xmlContent.length() > 100 ? xmlContent.substring(0, 100) + "..." : xmlContent);
                }
            }
            
            return xmlContent;
        } catch (Exception e) {
            log.error("解析XML内容失败: {}", e.getMessage());
            
            // 最后尝试直接寻找XML标记
            try {
                String fullContent = new String(decryptedBytes, StandardCharsets.UTF_8);
                int xmlStartIndex = fullContent.indexOf("<xml");
                if (xmlStartIndex >= 0) {
                    int xmlEndIndex = fullContent.lastIndexOf("</xml>");
                    if (xmlEndIndex > xmlStartIndex) {
                        String xmlContent = fullContent.substring(xmlStartIndex, xmlEndIndex + 6);
                        log.info("通过XML标记提取内容，长度: {}", xmlContent.length());
                        return xmlContent;
                    }
                }
            } catch (Exception ex) {
                log.error("通过XML标记提取内容失败: {}", ex.getMessage());
            }
            
            throw e;
        }
    }

    /**
     * 加密消息
     * @param replyMsg
     * @param appId
     * @return
     * @throws Exception
     */
    public String encrypt(String replyMsg, String appId) throws Exception {
        try {
            // 随机生成 16 字节的随机字符串
            String randomStr = generateRandomString(16);
            // log.info("生成的随机字符串: {}", randomStr);

            // 拼接数据包：随机字符串 + 消息长度(4字节网络字节序) + 消息内容 + AppID
            byte[] replyMsgBytes = replyMsg.getBytes(StandardCharsets.UTF_8);
            int replyMsgLength = replyMsgBytes.length;
            
            // 将消息长度转换为网络字节序(4字节)
            byte[] netOrderLength = new byte[4];
            netOrderLength[0] = (byte) ((replyMsgLength >> 24) & 0xFF);
            netOrderLength[1] = (byte) ((replyMsgLength >> 16) & 0xFF);
            netOrderLength[2] = (byte) ((replyMsgLength >> 8) & 0xFF);
            netOrderLength[3] = (byte) (replyMsgLength & 0xFF);
            
            // 组合所有部分：随机字符串 + 消息长度 + 消息内容 + AppID
            byte[] randomStrBytes = randomStr.getBytes(StandardCharsets.UTF_8);
            byte[] appIdBytes = appId.getBytes(StandardCharsets.UTF_8);
            
            // 计算完整数据包的长度并创建数组
            int totalLength = randomStrBytes.length + netOrderLength.length + replyMsgBytes.length + appIdBytes.length;
            byte[] encryptData = new byte[totalLength];
            
            // 复制所有部分到数据包
            System.arraycopy(randomStrBytes, 0, encryptData, 0, randomStrBytes.length);
            System.arraycopy(netOrderLength, 0, encryptData, randomStrBytes.length, netOrderLength.length);
            System.arraycopy(replyMsgBytes, 0, encryptData, randomStrBytes.length + netOrderLength.length, replyMsgBytes.length);
            System.arraycopy(appIdBytes, 0, encryptData, randomStrBytes.length + netOrderLength.length + replyMsgBytes.length, appIdBytes.length);
            
            // log.info("待加密数据长度: {} 字节", encryptData.length);

            // 确保encodingAesKey长度正确（微信的encodingAesKey是43位的，末尾可能会缺少=号）
            String fullAesKey = encodingAesKey;
            // 如果长度不足43位，说明缺少=号补齐
            if (fullAesKey.length() < 43) {
                fullAesKey = fullAesKey + "=".repeat(43 - fullAesKey.length());
            }
            
            // 解码AES密钥
            byte[] aesKey = Base64.getDecoder().decode(fullAesKey);
            // log.info("AES密钥长度(加密): {} 字节", aesKey.length);

            // 提取 IV（前 16 字节）
            byte[] iv = Arrays.copyOfRange(aesKey, 0, 16);

            // 初始化加密器
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(aesKey, "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);

            // 加密数据
            byte[] encryptedBytes = cipher.doFinal(encryptData);
            // log.info("加密后的数据长度: {} 字节", encryptedBytes.length);

            // Base64 编码加密后的数据
            String encryptedBase64 = Base64.getEncoder().encodeToString(encryptedBytes);
            // .info("Base64编码后的长度: {}", encryptedBase64.length());

            return encryptedBase64;
        } catch (Exception e) {
            log.error("加密过程发生错误: {}", e.getMessage(), e);
            throw e;
        }
    }

    private String generateRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(chars.length());
            sb.append(chars.charAt(index));
        }
        return sb.toString();
    }
        /**
     * 专用于处理长消息的解密方法
     * 微信的长消息可能会使用不同的填充方式
     */
    private String decryptLongMessage(String encryptedMsg, String currentAesKey) throws Exception {
        try {
            // 清理Base64字符串
            String cleanEncrypted = encryptedMsg.trim().replace("\n", "").replace("\r", "");
            
            // 确保长度符合Base64要求
            int mod = cleanEncrypted.length() % 4;
            if (mod != 0) {
                for (int i = 0; i < (4 - mod); i++) {
                    cleanEncrypted = cleanEncrypted + "=";
                }
            }
            
            // 处理AES密钥
            String fullAesKey = currentAesKey;
            if (fullAesKey.length() < 43) {
                // 补齐=号
                fullAesKey = fullAesKey + "=".repeat(43 - fullAesKey.length());
            } else if (fullAesKey.length() > 43) {
                // 截取前43位
                fullAesKey = fullAesKey.substring(0, 43);
            }
            
            // 解码AES密钥
            byte[] aesKey = Base64.getDecoder().decode(fullAesKey);
            
            // 使用多种填充方式尝试解密
            for (String padding : new String[]{"PKCS7Padding", "PKCS5Padding", "NoPadding"}) {
                try {
                    // 解码加密消息
                    byte[] encryptedBytes = Base64.getDecoder().decode(cleanEncrypted);
                    
                    // 提取IV（AES密钥的前16字节）
                    byte[] iv = Arrays.copyOfRange(aesKey, 0, 16);
                    
                    // 尝试使用不同的填充方式
                    Cipher cipher;
                    try {
                        cipher = Cipher.getInstance("AES/CBC/" + padding, "BC");
                        log.debug("使用 {} 解密", padding);
                    } catch (Exception e) {
                        // 如果BC提供程序不支持此填充，尝试默认提供程序
                        cipher = Cipher.getInstance("AES/CBC/" + padding);
                    }
                    
                    SecretKeySpec keySpec = new SecretKeySpec(aesKey, "AES");
                    IvParameterSpec ivSpec = new IvParameterSpec(iv);
                    cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
                    
                    // 解密数据
                    byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
                    
                    // 如果是NoPadding，可能需要处理末尾的填充字节
                    if ("NoPadding".equals(padding)) {
                        // 查找最后一个非零字节
                        int lastNonZero = decryptedBytes.length - 1;
                        while (lastNonZero >= 0 && decryptedBytes[lastNonZero] == 0) {
                            lastNonZero--;
                        }
                        if (lastNonZero >= 0) {
                            decryptedBytes = Arrays.copyOfRange(decryptedBytes, 0, lastNonZero + 1);
                        }
                    }
                    
                    // 尝试解析为XML
                    return parseDecryptedData(decryptedBytes);
                } catch (Exception e) {
                    log.debug("{} 填充解密失败: {}", padding, e.getMessage());
                    // 继续尝试下一种填充方式
                }
            }
            
            // 所有填充方式都尝试失败
            throw new Exception("所有填充方式都解密失败");
        } catch (Exception e) {
            log.error("长消息解密过程中发生错误: {}", e.getMessage());
            throw e;
        }
    }
}