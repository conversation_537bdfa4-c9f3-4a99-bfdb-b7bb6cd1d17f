package com.kefang.utils;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.kefang.config.OssProperties;
import com.kefang.dto.FileUploadResult;
import com.kefang.exception.BusinessException;
import com.kefang.vo.ResultCode;
import cn.hutool.core.io.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import cn.hutool.core.date.DateUtil;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;

/**
 * OSS文件上传工具类
 * 提供文件上传到阿里云OSS的功能
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-08
 */
@Slf4j
@Component
public class OssFileUploadUtil {
    
    @Autowired
    private OSS ossClient;
    
    @Autowired
    private OssProperties ossProperties;
    
    /**
     * 上传文件到OSS
     * 
     * @param file 要上传的文件
     * @return 文件上传结果
     * @throws BusinessException 业务异常
     */
    public FileUploadResult uploadFile(MultipartFile file) throws BusinessException {
        // 参数校验
        if (file == null || file.isEmpty()) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "上传文件不能为空");
        }

        // 文件大小校验
        if (file.getSize() > ossProperties.getMaxFileSize()) {
            throw new BusinessException(ResultCode.PARAM_ERROR,
                    String.format("文件大小超过限制，最大允许%dMB", ossProperties.getMaxFileSize() / 1024 / 1024));
        }

        // 获取原始文件名和扩展名
        String originalFileName = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFileName)) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "文件名不能为空");
        }
        
        String fileExtension = FileUtil.extName(originalFileName).toLowerCase();
        
        // 文件类型校验
        if (!isAllowedFileType(fileExtension)) {
            throw new BusinessException(ResultCode.PARAM_ERROR,
                    String.format("不支持的文件类型：%s，支持的类型：%s",
                            fileExtension, Arrays.toString(ossProperties.getAllowedFileTypes())));
        }
        
        try {
            // 生成文件存储路径
            String ossPath = generateOssPath(originalFileName, fileExtension);
            
            // 创建对象元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentType(file.getContentType());
            metadata.setContentDisposition("inline;filename=" + originalFileName);
            
            // 上传文件到OSS
            InputStream inputStream = file.getInputStream();
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                    ossProperties.getBucketName(), ossPath, inputStream, metadata);
            
            PutObjectResult result = ossClient.putObject(putObjectRequest);
            
            // 构建文件访问URL
            String fileUrl = buildFileUrl(ossPath);
            
            log.info("文件上传成功：原文件名={}, OSS路径={}, 访问URL={}", 
                    originalFileName, ossPath, fileUrl);
            
            // 返回上传结果
            return FileUploadResult.builder()
                    .originalFileName(originalFileName)
                    .fileName(FileUtil.getName(ossPath))
                    .fileUrl(fileUrl)
                    .fileSize(file.getSize())
                    .fileType(fileExtension)
                    .contentType(file.getContentType())
                    .uploadTime(LocalDateTime.now())
                    .ossPath(ossPath)
                    .md5(result.getETag())
                    .build();
                    
        } catch (IOException e) {
            log.error("文件上传失败：{}", e.getMessage(), e);
            throw new BusinessException(ResultCode.ERROR, "文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("OSS上传异常：{}", e.getMessage(), e);
            throw new BusinessException(ResultCode.ERROR, "OSS上传异常：" + e.getMessage());
        }
    }
    
    /**
     * 生成OSS存储路径
     * 格式：dismantling/YYYY/MM/DD/原文件名_时间戳_UUID.扩展名
     * 
     * @param originalFileName 原始文件名
     * @param fileExtension 文件扩展名
     * @return OSS存储路径
     */
    private String generateOssPath(String originalFileName, String fileExtension) {
        // 获取当前日期，用于创建目录结构
        String datePath = DateUtil.format(new Date(), "yyyy/MM/dd");
        
        // 生成唯一文件名：UUID + 时间戳
        String fileNameWithoutExt = FileUtil.mainName(originalFileName);
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = IdUtil.simpleUUID();
        String uniqueFileName = String.format("%s_%s.%s",
                uuid, timestamp, fileExtension);
        
        // 构建完整的OSS路径
        return String.format("%s/%s/%s", ossProperties.getRootPath(), datePath, uniqueFileName);
    }
    
    /**
     * 构建文件访问URL
     * 
     * @param ossPath OSS存储路径
     * @return 完整的文件访问URL
     */
    private String buildFileUrl(String ossPath) {
        String fileHost = ossProperties.getFileHost();
        if (StrUtil.isBlank(fileHost)) {
            // 如果没有配置自定义域名，使用默认的OSS域名
            String bucketName = ossProperties.getBucketName();
            String endpoint = ossProperties.getEndpoint().replace("http://", "").replace("https://", "");
            fileHost = String.format("https://%s.%s", bucketName, endpoint);
        }
        
        // 确保fileHost不以/结尾，ossPath不以/开头
        if (fileHost.endsWith("/")) {
            fileHost = fileHost.substring(0, fileHost.length() - 1);
        }
        if (ossPath.startsWith("/")) {
            ossPath = ossPath.substring(1);
        }
        
        return String.format("%s/%s", fileHost, ossPath);
    }
    
    /**
     * 检查文件类型是否被允许
     * 
     * @param fileExtension 文件扩展名
     * @return 是否允许上传
     */
    private boolean isAllowedFileType(String fileExtension) {
        if (StrUtil.isBlank(fileExtension)) {
            return false;
        }
        
        String[] allowedTypes = ossProperties.getAllowedFileTypes();
        if (allowedTypes == null || allowedTypes.length == 0) {
            return true; // 如果没有配置限制，则允许所有类型
        }
        
        return Arrays.stream(allowedTypes)
                .anyMatch(type -> type.equalsIgnoreCase(fileExtension));
    }
    
    /**
     * 删除OSS中的文件
     * 
     * @param ossPath OSS文件路径
     * @return 是否删除成功
     */
    public boolean deleteFile(String ossPath) {
        try {
            if (StrUtil.isBlank(ossPath)) {
                return false;
            }
            
            ossClient.deleteObject(ossProperties.getBucketName(), ossPath);
            log.info("删除OSS文件成功：{}", ossPath);
            return true;
            
        } catch (Exception e) {
            log.error("删除OSS文件失败：{}, 错误：{}", ossPath, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 检查文件是否存在
     * 
     * @param ossPath OSS文件路径
     * @return 文件是否存在
     */
    public boolean fileExists(String ossPath) {
        try {
            if (StrUtil.isBlank(ossPath)) {
                return false;
            }
            
            return ossClient.doesObjectExist(ossProperties.getBucketName(), ossPath);
            
        } catch (Exception e) {
            log.error("检查OSS文件是否存在失败：{}, 错误：{}", ossPath, e.getMessage(), e);
            return false;
        }
    }
}
