package com.kefang.controller;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.kefang.entity.Agent;
import com.kefang.service.AgentService;
import com.kefang.service.ChatCallManualRecordService;
import com.kefang.service.ChatMessageService;
import com.kefang.service.ChatSessionService;
import com.kefang.vo.AgentStatsVO;
import com.kefang.vo.AgentWithStatsVO;
import com.kefang.vo.PageResult;
import com.kefang.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 客服管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/agent-manage")
public class AgentManagementController {

    @Autowired
    private AgentService agentService;

    @Autowired
    private ChatSessionService chatSessionService;

    @Autowired
    private ChatMessageService chatMessageService;

    @Autowired
    private ChatCallManualRecordService chatCallManualRecordService;

    /**
     * 获取客服分页列表（带统计数据）
     */
    @GetMapping("/page")
    public Result<PageResult<AgentWithStatsVO>> getAgentsByPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) Integer agentType,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false, defaultValue = "id") String orderBy,
            @RequestParam(required = false, defaultValue = "asc") String orderType,
            @RequestParam(required = false) String orderBy2,
            @RequestParam(required = false, defaultValue = "asc") String orderType2) {

        log.info("获取客服分页列表: pageNum={}, pageSize={}, agentType={}, status={}, startDate={}, endDate={}, keyword={}, orderBy={}, orderType={}, orderBy2={}, orderType2={}",
                pageNum, pageSize, agentType, status, startDate, endDate, keyword, orderBy, orderType, orderBy2, orderType2);

        // 默认查询当天数据
        if (startDate == null) {
            startDate = LocalDate.now();
        }
        if (endDate == null) {
            endDate = startDate;
        }

        // 获取所有客服
        List<Agent> agentList = agentService.getAllAgents();

        // 构建过滤条件
        List<Agent> filteredList = agentList.stream()
                .filter(agent -> {
                    // 客服类型过滤
                    if (agentType != null && !agentType.equals(agent.getAgentType())) {
                        return false;
                    }
                    // 在线状态过滤
                    if (status != null && !status.equals(agent.getStatus())) {
                        return false;
                    }
                    // 关键词搜索（工号或姓名）
                    if (StringUtils.isNotBlank(keyword)) {
                        return (agent.getAgentNo() != null && agent.getAgentNo().contains(keyword)) ||
                                (agent.getName() != null && agent.getName().contains(keyword));
                    }
                    return true;
                })
                .collect(Collectors.toList());

        // 分页处理前，先进行排序
        // 主排序
        applySort(filteredList, orderBy, orderType);
        
        // 如果有二级排序条件，且与主排序不同，则应用二级排序
        if (orderBy2 != null && !orderBy2.equalsIgnoreCase(orderBy)) {
            applySecondarySort(filteredList, orderBy2, orderType2);
        }
        
        // 分页处理
        int total = filteredList.size();
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        
        // 安全检查
        if (fromIndex > total) {
            fromIndex = 0;
            toIndex = Math.min(pageSize, total);
        }

        List<Agent> pageAgents = (fromIndex < toIndex)
                ? filteredList.subList(fromIndex, toIndex)
                : new ArrayList<>();

        // 转换为带统计数据的VO对象
        List<AgentWithStatsVO> resultList = new ArrayList<>();

        for (Agent agent : pageAgents) {
            // 获取客服的统计数据（使用日期范围）
            AgentWithStatsVO statsVO = getAgentStatsWithDateRange(agent, startDate, endDate);
            resultList.add(statsVO);
        }

        // 构造返回分页结果
        PageResult<AgentWithStatsVO> resultPage = new PageResult<>(resultList, total, pageNum, pageSize);

        return Result.success(resultPage);
    }
    
    /**
     * 获取客服统计数据（按日期）
     */
    @GetMapping("/stats")
    public Result<List<AgentWithStatsVO>> getAgentStatsByDate(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        
        // 默认查询当天数据
        final LocalDate finalStartDate = startDate != null ? startDate : LocalDate.now();
        final LocalDate finalEndDate = endDate != null ? endDate : finalStartDate;
        
        log.info("获取客服统计数据，日期范围: {} 至 {}", finalStartDate, finalEndDate);
        
        // 查询所有客服
        List<Agent> agentList = agentService.getAllAgents();
        
        // 转换为带统计数据的VO对象
        List<AgentWithStatsVO> resultList = agentList.stream()
                .map(agent -> getAgentStatsWithDateRange(agent, finalStartDate, finalEndDate))
                .collect(Collectors.toList());
        
        return Result.success(resultList);
    }
    
    /**
     * 添加客服
     */
//    @PostMapping("/add")
//    public Result<Agent> addAgent(@RequestBody Agent agent) {
//        log.info("添加客服: {}", agent);
//
//        // 检查工号是否已存在
//        Agent existAgent = agentService.getAgentByAgentNo(agent.getAgentNo());
//        if (existAgent != null) {
//            return Result.error("工号已存在");
//        }
//
//        // 设置创建时间
//        if (agent.getCreatedAt() == null) {
//            agent.setCreatedAt(new Date());
//        }
//
//        // 保存客服信息
//        boolean success = agentService.addAgent(agent);
//
//        if (success) {
//            return Result.success(agent);
//        } else {
//            return Result.error("添加失败");
//        }
//    }
    
    /**
     * 删除客服
     */
//    @DeleteMapping("/{id}")
//    public Result<Boolean> deleteAgent(@PathVariable Long id) {
//        log.info("删除客服: id={}", id);
//
//        Agent agent = agentService.getAgentById(id);
//        if (agent == null) {
//            return Result.error("客服不存在");
//        }
//
//        // 这里假设 updateAgent 方法可以处理删除逻辑，如果有专门的删除方法，应使用该方法
//        agent.setStatus(-1); // 假设-1表示删除状态
//        boolean success = agentService.updateAgent(agent);
//
//        if (success) {
//            return Result.success(true);
//        } else {
//            return Result.error("删除失败");
//        }
//    }
    
    /**
     * 获取客服会话统计数据
     */
    @GetMapping("/{agentId}/session-stats")
    public Result<List<AgentStatsVO>> getAgentSessionStats(
            @PathVariable Long agentId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        
        log.info("获取客服会话统计数据: agentId={}, startDate={}, endDate={}", agentId, startDate, endDate);
        
        // 检查日期范围的有效性
        if (startDate.isAfter(endDate)) {
            return Result.error("开始日期不能晚于结束日期");
        }
        
        // 最多查询500天的数据
        long days = ChronoUnit.DAYS.between(startDate, endDate) + 1;
        if (days > 500) {
            return Result.error("日期范围不能超过500天");
        }
        
        // 生成日期列表
        List<LocalDate> dateList = IntStream.iterate(0, i -> i + 1)
                .limit(days)
                .mapToObj(startDate::plusDays)
                .toList();
        
        // 查询每个日期的统计数据
        List<AgentStatsVO> statsList = new ArrayList<>();
        
        for (LocalDate date : dateList) {
            // 创建统计数据对象
            AgentStatsVO statsVO = new AgentStatsVO();
            statsVO.setDate(date.toString());
            
            // 设置统计数据
            setAgentDailyStats(agentId, date, statsVO);
            
            statsList.add(statsVO);
        }
        
        return Result.success(statsList);
    }
    
    /**
     * 获取单个客服的统计数据
     */
    private AgentWithStatsVO getAgentStats(Agent agent, LocalDate date) {
        AgentWithStatsVO vo = new AgentWithStatsVO();
        
        // 复制基本信息
        vo.setId(agent.getId());
        vo.setAgentNo(agent.getAgentNo());
        vo.setName(agent.getName());
        vo.setStatus(agent.getStatus());
        vo.setAgentType(agent.getAgentType());
        vo.setAvatar(agent.getAvatar());
        
        // 日期转换：从 Date 到 LocalDateTime
        if (agent.getCreatedAt() != null) {
            vo.setCreatedAt(agent.getCreatedAt().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime());
        }
        
        // 查询当日接入会话数
        LocalDateTime startOfDay = date.atStartOfDay();
        LocalDateTime endOfDay = date.plusDays(1).atStartOfDay();
        
        // 当日接入会话数 - 使用chat_call_manual_records表计算
        int todaySessionCount = chatCallManualRecordService.countAcceptedSessionsByAgentAndTimeRange(
                agent.getId(), startOfDay, endOfDay);
        vo.setTodaySessionCount(todaySessionCount);
        
        // 历史接入会话总数 - 使用chat_call_manual_records表计算
        int totalSessionCount = chatCallManualRecordService.countAcceptedSessionsByAgent(agent.getId());
        vo.setTotalSessionCount(totalSessionCount);
        
        // 查询当日回答数
        int todayResponseCount = chatMessageService.countMessagesByAgentIDAndTimeRange(
                agent.getId().toString(), startOfDay, endOfDay);
        vo.setTodayResponseCount(todayResponseCount);

        // 平均响应时间（从用户请求到客服接入的时间）
        Double avgResponseTime = chatCallManualRecordService.getAvgResponseTimeByAgent(agent.getId());
        vo.setAvgResponseTime(avgResponseTime != null ? avgResponseTime.intValue() : 0);
        
        // 平均对话时间（从接入开始到服务结束的时间）
        // 使用chat_call_manual_records表的serviceDuration字段计算
        Double avgChatDuration = chatCallManualRecordService.getAvgChatDurationByAgent(agent.getId());
        vo.setAvgChatDuration(avgChatDuration != null ? avgChatDuration.intValue() : 0);
        
        return vo;
    }
    
    /**
     * 设置客服每日统计数据
     */
    private void setAgentDailyStats(Long agentId, LocalDate date, AgentStatsVO statsVO) {
        // 查询日期范围
        LocalDateTime startOfDay = date.atStartOfDay();
        LocalDateTime endOfDay = date.plusDays(1).atStartOfDay();
        
        // 查询会话数量 - 使用chat_call_manual_records表计算
        int sessionCount = chatCallManualRecordService.countAcceptedSessionsByAgentAndTimeRange(
                agentId, startOfDay, endOfDay);
        statsVO.setSessionCount(sessionCount);
        
        // 查询平均响应时间
        Double avgResponseTime = chatCallManualRecordService.getAvgResponseTimeByAgentAndTimeRange(
                agentId, startOfDay, endOfDay);
        statsVO.setAvgResponseTime(avgResponseTime != null ? avgResponseTime.intValue() : 0);
        
        // 查询平均对话时间
        // 使用chat_call_manual_records表的serviceDuration字段计算
        Double avgChatDuration = chatCallManualRecordService.getAvgChatDurationByAgentAndTimeRange(
                agentId, startOfDay, endOfDay);
        statsVO.setAvgChatDuration(avgChatDuration != null ? avgChatDuration.intValue() : 0);
        
        // 回答数
        int todayResponseCount = chatMessageService.countMessagesByAgentIDAndTimeRange(
                agentId+"", startOfDay, endOfDay);
        statsVO.setResponseCount(todayResponseCount);
        
        // 获取指定时间范围内的会话ID列表，按照最近接入时间降序排序
        List<Long> sessionIds = chatCallManualRecordService.getSessionIdsByAgentAndTimeRangeOrderByAcceptTimeDesc(
                agentId, startOfDay, endOfDay);
        statsVO.setRecentSessionIds(sessionIds);
    }

    /**
     * 获取客服的统计数据（使用日期范围）
     */
    private AgentWithStatsVO getAgentStatsWithDateRange(Agent agent, LocalDate startDate, LocalDate endDate) {
        AgentWithStatsVO vo = new AgentWithStatsVO();
        
        // 复制基本信息
        vo.setId(agent.getId());
        vo.setAgentNo(agent.getAgentNo());
        vo.setName(agent.getName());
        vo.setStatus(agent.getStatus());
        vo.setAgentType(agent.getAgentType());
        vo.setAvatar(agent.getAvatar());
        
        // 日期转换：从 Date 到 LocalDateTime
        if (agent.getCreatedAt() != null) {
            vo.setCreatedAt(agent.getCreatedAt().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime());
        }
        
        // 当日统计 - 只统计查询日期范围的最后一天（作为"当日"）
        LocalDateTime startOfDay = endDate.atStartOfDay();
        LocalDateTime endOfDay = endDate.plusDays(1).atStartOfDay();
        
        // 当日接入会话数
        int todaySessionCount = chatCallManualRecordService.countAcceptedSessionsByAgentAndTimeRange(
                agent.getId(), startOfDay, endOfDay);
        vo.setTodaySessionCount(todaySessionCount);
        
        // 当日回答数
        int todayResponseCount = chatMessageService.countMessagesByAgentIDAndTimeRange(
                agent.getId().toString(), startOfDay, endOfDay);
        vo.setTodayResponseCount(todayResponseCount);
        
        // 当日平均响应时间
        Double todayAvgResponseTime = chatCallManualRecordService.getAvgResponseTimeByAgentAndTimeRange(
                agent.getId(), startOfDay, endOfDay);
        vo.setTodayAvgResponseTime(todayAvgResponseTime != null ? todayAvgResponseTime.intValue() : 0);
        
        // 当日平均对话时间
        Double todayAvgChatDuration = chatCallManualRecordService.getAvgChatDurationByAgentAndTimeRange(
                agent.getId(), startOfDay, endOfDay);
        vo.setTodayAvgChatDuration(todayAvgChatDuration != null ? todayAvgChatDuration.intValue() : 0);
        
        // 历史/总体统计 - 使用查询的日期范围
        LocalDateTime startOfRange = startDate.atStartOfDay();
        LocalDateTime endOfRange = endDate.plusDays(1).atStartOfDay();
        
        // 查询日期范围内的会话总数
        int totalSessionCount = chatCallManualRecordService.countAcceptedSessionsByAgentAndTimeRange(
                agent.getId(), startOfRange, endOfRange);
        vo.setTotalSessionCount(totalSessionCount);
        
        // 查询日期范围内的回答总数
        int totalResponseCount = chatMessageService.countMessagesByAgentIDAndTimeRange(
                agent.getId().toString(), startOfRange, endOfRange);
        vo.setTotalResponseCount(totalResponseCount);
        
        // 查询日期范围内的平均响应时间
        Double avgResponseTime = chatCallManualRecordService.getAvgResponseTimeByAgentAndTimeRange(
                agent.getId(), startOfRange, endOfRange);
        vo.setAvgResponseTime(avgResponseTime != null ? avgResponseTime.intValue() : 0);
        
        // 查询日期范围内的平均对话时间
        Double avgChatDuration = chatCallManualRecordService.getAvgChatDurationByAgentAndTimeRange(
                agent.getId(), startOfRange, endOfRange);
        vo.setAvgChatDuration(avgChatDuration != null ? avgChatDuration.intValue() : 0);
        
        return vo;
    }

    private void applySort(List<Agent> agents, String orderBy, String orderType) {
        if ("status".equalsIgnoreCase(orderBy)) {
            // 按在线状态排序
            if ("desc".equalsIgnoreCase(orderType)) {
                agents.sort((a, b) -> Integer.compare(
                        b.getStatus() != null ? b.getStatus() : 0,
                        a.getStatus() != null ? a.getStatus() : 0
                ));
            } else {
                agents.sort((a, b) -> Integer.compare(
                        a.getStatus() != null ? a.getStatus() : 0,
                        b.getStatus() != null ? b.getStatus() : 0
                ));
            }
        } else if ("agentType".equalsIgnoreCase(orderBy)) {
            // 按客服类型排序，人工客服(1)排在前面
            if ("desc".equalsIgnoreCase(orderType)) {
                agents.sort((a, b) -> {
                    // 如果a是人工客服(1)，排在非人工客服前面
                    if ((a.getAgentType() == null ? 0 : a.getAgentType()) == 1 && (b.getAgentType() == null ? 0 : b.getAgentType()) != 1) {
                        return -1;
                    }
                    // 如果b是人工客服(1)，排在非人工客服前面
                    if ((a.getAgentType() == null ? 0 : a.getAgentType()) != 1 && (b.getAgentType() == null ? 0 : b.getAgentType()) == 1) {
                        return 1;
                    }
                    // 其他情况按客服类型降序排列
                    return Integer.compare(
                            b.getAgentType() != null ? b.getAgentType() : 0,
                            a.getAgentType() != null ? a.getAgentType() : 0
                    );
                });
            } else {
                agents.sort((a, b) -> {
                    // 如果a是人工客服(1)，排在非人工客服前面
                    if ((a.getAgentType() == null ? 0 : a.getAgentType()) == 1 && (b.getAgentType() == null ? 0 : b.getAgentType()) != 1) {
                        return -1;
                    }
                    // 如果b是人工客服(1)，排在非人工客服前面
                    if ((a.getAgentType() == null ? 0 : a.getAgentType()) != 1 && (b.getAgentType() == null ? 0 : b.getAgentType()) == 1) {
                        return 1;
                    }
                    // 其他情况按客服类型升序排列
                    return Integer.compare(
                            a.getAgentType() != null ? a.getAgentType() : 0,
                            b.getAgentType() != null ? b.getAgentType() : 0
                    );
                });
            }
        } else {
            // 默认按ID排序
            if ("desc".equalsIgnoreCase(orderType)) {
                agents.sort((a, b) -> Long.compare(
                        b.getId() != null ? b.getId() : 0,
                        a.getId() != null ? a.getId() : 0
                ));
            } else {
                agents.sort((a, b) -> Long.compare(
                        a.getId() != null ? a.getId() : 0,
                        b.getId() != null ? b.getId() : 0
                ));
            }
        }
    }

    private void applySecondarySort(List<Agent> agents, String orderBy2, String orderType2) {
        if ("status".equalsIgnoreCase(orderBy2)) {
            // 按在线状态排序
            if ("desc".equalsIgnoreCase(orderType2)) {
                agents.sort((a, b) -> Integer.compare(
                        b.getStatus() != null ? b.getStatus() : 0,
                        a.getStatus() != null ? a.getStatus() : 0
                ));
            } else {
                agents.sort((a, b) -> Integer.compare(
                        a.getStatus() != null ? a.getStatus() : 0,
                        b.getStatus() != null ? b.getStatus() : 0
                ));
            }
        } else if ("agentType".equalsIgnoreCase(orderBy2)) {
            // 按客服类型排序，人工客服(1)排在前面
            if ("desc".equalsIgnoreCase(orderType2)) {
                agents.sort((a, b) -> {
                    // 如果a是人工客服(1)，排在非人工客服前面
                    if ((a.getAgentType() == null ? 0 : a.getAgentType()) == 1 && (b.getAgentType() == null ? 0 : b.getAgentType()) != 1) {
                        return -1;
                    }
                    // 如果b是人工客服(1)，排在非人工客服前面
                    if ((a.getAgentType() == null ? 0 : a.getAgentType()) != 1 && (b.getAgentType() == null ? 0 : b.getAgentType()) == 1) {
                        return 1;
                    }
                    // 其他情况按客服类型降序排列
                    return Integer.compare(
                            b.getAgentType() != null ? b.getAgentType() : 0,
                            a.getAgentType() != null ? a.getAgentType() : 0
                    );
                });
            } else {
                agents.sort((a, b) -> {
                    // 如果a是人工客服(1)，排在非人工客服前面
                    if ((a.getAgentType() == null ? 0 : a.getAgentType()) == 1 && (b.getAgentType() == null ? 0 : b.getAgentType()) != 1) {
                        return -1;
                    }
                    // 如果b是人工客服(1)，排在非人工客服前面
                    if ((a.getAgentType() == null ? 0 : a.getAgentType()) != 1 && (b.getAgentType() == null ? 0 : b.getAgentType()) == 1) {
                        return 1;
                    }
                    // 其他情况按客服类型升序排列
                    return Integer.compare(
                            a.getAgentType() != null ? a.getAgentType() : 0,
                            b.getAgentType() != null ? b.getAgentType() : 0
                    );
                });
            }
        } else {
            // 默认按ID排序
            if ("desc".equalsIgnoreCase(orderType2)) {
                agents.sort((a, b) -> Long.compare(
                        b.getId() != null ? b.getId() : 0,
                        a.getId() != null ? a.getId() : 0
                ));
            } else {
                agents.sort((a, b) -> Long.compare(
                        a.getId() != null ? a.getId() : 0,
                        b.getId() != null ? b.getId() : 0
                ));
            }
        }
    }
} 