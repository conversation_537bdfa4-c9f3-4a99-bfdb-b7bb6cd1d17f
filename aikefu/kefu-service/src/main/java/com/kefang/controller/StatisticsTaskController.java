package com.kefang.controller;

import com.kefang.job.SourceWxGroupStatisticsTask;
import com.kefang.utils.DateUtil;
import com.kefang.utils.MD5Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.UUID;

/**
 * 统计任务控制器，用于手动触发统计任务
 */
@RestController
@RequestMapping("/statisticsTask")
public class StatisticsTaskController {

    private static final Logger logger = LoggerFactory.getLogger(StatisticsTaskController.class);

    @Autowired
    private SourceWxGroupStatisticsTask sourceWxGroupStatisticsTask;

    /**
     * 手动触发微信群来源统计任务
     *
     * @return 统计结果
     */
//    @PostMapping("/triggerSourceWxGroup")
//    @NoAuth
//    public Result<String> triggerSourceWxGroupStatistics(@RequestBody SourceWxGroupForm form) {
//        logger.info("手动触发微信群来源统计任务");
//        try {
//            String response = sourceWxGroupStatisticsTask.fetchAndPrintStatistics(form.getDate(), form.getSourceList());
//            return Result.success("任务执行成功", response);
//        } catch (Exception e) {
//            logger.error("手动触发微信群来源统计任务失败", e);
//            return Result.error("任务执行失败: " + e.getMessage());
//        }
//    }

}
