package com.kefang.controller;

import com.kefang.vo.Result;
import com.kefang.vo.ResultCode;
import com.kefang.websocket.WebSocketServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket诊断控制器
 * 用于检查WebSocket连接状态和提供备用HTTP轮询功能
 */
@RestController
@RequestMapping("/api/websocket")
@Slf4j
public class WebSocketDiagnosisController {

    /**
     * 获取当前WebSocket服务器连接状态
     * @return 连接状态信息
     */
    @GetMapping("/status")
    public Result<Map<String, Object>> getConnectionStatus() {
        log.info("获取WebSocket连接状态");
        try {
            Map<String, Object> status = WebSocketServer.getConnectionStatus();
            return Result.success(status);
        } catch (Exception e) {
            log.error("获取WebSocket连接状态异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.ERROR, "获取WebSocket状态异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取指定用户的连接状态
     * @param userId 用户ID
     * @param role 角色(user或agent)
     * @return 用户连接状态
     */
    @GetMapping("/user-status")
    public Result<Map<String, Object>> getUserConnectionStatus(
            @RequestParam("userId") String userId,
            @RequestParam("role") String role) {
        log.info("获取用户WebSocket连接状态: userId={}, role={}", userId, role);
        
        if (userId == null || role == null) {
            return Result.error(ResultCode.PARAM_ERROR, "用户ID和角色参数不能为空");
        }
        
        try {
            Map<String, Object> status = new HashMap<>();
            boolean isConnected = WebSocketServer.isUserConnected(role, userId);
            
            status.put("userId", userId);
            status.put("role", role);
            status.put("connected", isConnected);
            
            return Result.success(status);
        } catch (Exception e) {
            log.error("获取用户连接状态异常: userId={}, role={}, 异常: {}", userId, role, e.getMessage(), e);
            return Result.error(ResultCode.ERROR, "获取用户连接状态异常: " + e.getMessage());
        }
    }
    
    /**
     * 发送诊断消息，测试WebSocket是否正常工作
     * @param userId 用户ID
     * @param role 角色(user或agent)
     * @return 发送结果
     */
    @PostMapping("/ping")
    public Result<Map<String, Object>> sendPingMessage(
            @RequestParam("userId") String userId,
            @RequestParam("role") String role) {
        log.info("向用户发送诊断消息: userId={}, role={}", userId, role);
        
        if (userId == null || role == null) {
            return Result.error(ResultCode.PARAM_ERROR, "用户ID和角色参数不能为空");
        }
        
        try {
            Map<String, Object> result = new HashMap<>();
            boolean success = WebSocketServer.sendDiagnosticMessage(role, userId);
            
            result.put("userId", userId);
            result.put("role", role);
            result.put("success", success);
            result.put("timestamp", System.currentTimeMillis());
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("发送诊断消息异常: userId={}, role={}, 异常: {}", userId, role, e.getMessage(), e);
            return Result.error(ResultCode.ERROR, "发送诊断消息异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取WebSocket服务器运行状态
     * @return 服务状态信息
     */
    @GetMapping("/server-info")
    public Result<Map<String, Object>> getServerInfo() {
        log.info("获取WebSocket服务器信息");
        
        try {
            Map<String, Object> info = new HashMap<>();
            // 操作系统信息
            info.put("os", System.getProperty("os.name") + " " + System.getProperty("os.version"));
            // Java版本
            info.put("java", System.getProperty("java.version"));
            // 服务器运行时间
            info.put("uptime", WebSocketServer.getServerUptime());
            // 最大连接数限制
            info.put("maxConnections", 10000); // 一般Tomcat默认最大连接数
            // WebSocket协议支持情况
            info.put("protocols", new String[]{"ws", "wss"});
            
            return Result.success(info);
        } catch (Exception e) {
            log.error("获取服务器信息异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.ERROR, "获取服务器信息异常: " + e.getMessage());
        }
    }
    
    /**
     * 对移动设备的连接建议
     * @param userAgent 用户代理字符串
     * @return 连接建议
     */
    @GetMapping("/connection-advice")
    public Result<Map<String, Object>> getConnectionAdvice(
            @RequestHeader(value = "User-Agent", required = false) String userAgent) {
        log.info("获取连接建议, User-Agent: {}", userAgent);
        
        try {
            Map<String, Object> advice = new HashMap<>();
            boolean isMobile = userAgent != null && 
                userAgent.toLowerCase().matches(".*android.*|.*iphone.*|.*ipad.*|.*mobile.*");
            
            advice.put("isMobile", isMobile);
            advice.put("recommendedMode", isMobile ? "polling" : "websocket");
            advice.put("timeout", isMobile ? 15000 : 10000);
            advice.put("reconnectStrategy", isMobile ? "immediate-fallback" : "retry-then-fallback");
            advice.put("userAgent", userAgent);
            
            return Result.success(advice);
        } catch (Exception e) {
            log.error("获取连接建议异常: User-Agent={}, 异常: {}", userAgent, e.getMessage(), e);
            return Result.error(ResultCode.ERROR, "获取连接建议异常: " + e.getMessage());
        }
    }
} 