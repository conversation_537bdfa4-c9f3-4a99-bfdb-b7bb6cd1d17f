package com.kefang.controller;

import com.kefang.service.QuestionAnalysisService;
import com.kefang.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 热门问题分析控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/question-analysis")
public class QuestionAnalysisController {
    
    @Autowired
    private QuestionAnalysisService questionAnalysisService;
    
    /**
     * 按场景和数据源获取问题统计
     */
    @GetMapping("/category-stats")
    public Result<Map<String, Object>> getCategoryStats(
            @RequestParam(required = false) String scene,
            @RequestParam(required = false) String datasource,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "20") Integer limit) {
        
        log.info("获取问题分类统计，场景：{}，数据源：{}，开始日期：{}，结束日期：{}，限制：{}", 
                scene, datasource, startDate, endDate, limit);
        
        try {
            Map<String, Object> stats = questionAnalysisService.getCategoryStats(
                    scene, datasource, startDate, endDate, limit);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取问题分类统计异常", e);
            return Result.error("获取问题分类统计异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取热门问题列表
     */
    @GetMapping("/hot-questions")
    public Result<List<Map<String, Object>>> getHotQuestions(
            @RequestParam(required = false) String scene,
            @RequestParam(required = false) String datasource,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "20") Integer limit) {
        
        log.info("获取热门问题，场景：{}，数据源：{}，开始日期：{}，结束日期：{}，限制：{}", 
                scene, datasource, startDate, endDate, limit);
        
        try {
            List<Map<String, Object>> hotQuestions = questionAnalysisService.getHotQuestions(
                    scene, datasource, startDate, endDate, limit);
            return Result.success(hotQuestions);
        } catch (Exception e) {
            log.error("获取热门问题异常", e);
            return Result.error("获取热门问题异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取问题趋势数据
     */
    @GetMapping("/question-trend")
    public Result<List<Map<String, Object>>> getQuestionTrend(
            @RequestParam(defaultValue = "day") String timeUnit,
            @RequestParam(required = false) String scene,
            @RequestParam(required = false) String datasource,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        log.info("获取问题趋势，时间单位：{}，场景：{}，数据源：{}，开始日期：{}，结束日期：{}", 
                timeUnit, scene, datasource, startDate, endDate);
        
        try {
            List<Map<String, Object>> trendData = questionAnalysisService.getQuestionTrend(
                    timeUnit, scene, datasource, startDate, endDate);
            return Result.success(trendData);
        } catch (Exception e) {
            log.error("获取问题趋势异常", e);
            return Result.error("获取问题趋势异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取问题关键词统计
     */
    @GetMapping("/keyword-stats")
    public Result<List<Map<String, Object>>> getKeywordStats(
            @RequestParam(required = false) String scene,
            @RequestParam(required = false) String datasource,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "50") Integer limit) {
        
        log.info("获取问题关键词统计，场景：{}，数据源：{}，开始日期：{}，结束日期：{}，限制：{}", 
                scene, datasource, startDate, endDate, limit);
        
        try {
            List<Map<String, Object>> keywordStats = questionAnalysisService.getKeywordStats(
                    scene, datasource, startDate, endDate, limit);
            return Result.success(keywordStats);
        } catch (Exception e) {
            log.error("获取问题关键词统计异常", e);
            return Result.error("获取问题关键词统计异常: " + e.getMessage());
        }
    }
} 