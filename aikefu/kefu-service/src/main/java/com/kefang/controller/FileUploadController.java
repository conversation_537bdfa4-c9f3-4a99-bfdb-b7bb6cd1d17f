package com.kefang.controller;


import com.kefang.dto.FileUploadResult;
import com.kefang.utils.ApiResponse;
import com.kefang.utils.OssFileUploadUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传控制器
 * 提供文件上传到阿里云OSS的REST API接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-08
 */
@Slf4j
@RestController
@RequestMapping("/api/file")
@Validated
public class FileUploadController {
    
    @Autowired
    private OssFileUploadUtil ossFileUploadUtil;
    
    /**
     * 上传单个文件到OSS
     * 
     * @param file 要上传的文件
     * @return 文件上传结果
     */
    @PostMapping("/upload")
    public ApiResponse<FileUploadResult> uploadFile(@RequestParam("file") MultipartFile file) {
        log.info("开始上传文件：{}, 大小：{} bytes", file.getOriginalFilename(), file.getSize());
        
        try {
            FileUploadResult result = ossFileUploadUtil.uploadFile(file);
            log.info("文件上传成功：{}", result.getFileUrl());
            
            return ApiResponse.success("文件上传成功", result);
            
        } catch (Exception e) {
            log.error("文件上传失败：{}", e.getMessage(), e);
            return ApiResponse.error("文件上传失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量上传文件到OSS
     * 
     * @param files 要上传的文件数组
     * @return 文件上传结果列表
     */
    @PostMapping("/upload/batch")
    public ApiResponse<FileUploadResult[]> uploadFiles(@RequestParam("files") MultipartFile[] files) {
        log.info("开始批量上传文件，数量：{}", files.length);
        
        try {
            FileUploadResult[] results = new FileUploadResult[files.length];
            
            for (int i = 0; i < files.length; i++) {
                MultipartFile file = files[i];
                log.info("上传第{}个文件：{}", i + 1, file.getOriginalFilename());
                
                FileUploadResult result = ossFileUploadUtil.uploadFile(file);
                results[i] = result;
                
                log.info("第{}个文件上传成功：{}", i + 1, result.getFileUrl());
            }
            
            log.info("批量文件上传完成，成功数量：{}", results.length);
            return ApiResponse.success("批量文件上传成功", results);
            
        } catch (Exception e) {
            log.error("批量文件上传失败：{}", e.getMessage(), e);
            return ApiResponse.error("批量文件上传失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除OSS中的文件
     * 
     * @param ossPath OSS文件路径
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    public ApiResponse<Boolean> deleteFile(@RequestParam("ossPath") String ossPath) {
        log.info("开始删除OSS文件：{}", ossPath);
        
        try {
            boolean success = ossFileUploadUtil.deleteFile(ossPath);
            
            if (success) {
                log.info("OSS文件删除成功：{}", ossPath);
                return ApiResponse.success("文件删除成功", true);
            } else {
                log.warn("OSS文件删除失败：{}", ossPath);
                return ApiResponse.error("文件删除失败");
            }
            
        } catch (Exception e) {
            log.error("删除OSS文件异常：{}", e.getMessage(), e);
            return ApiResponse.error("删除文件异常：" + e.getMessage());
        }
    }
    
    /**
     * 检查OSS文件是否存在
     * 
     * @param ossPath OSS文件路径
     * @return 文件是否存在
     */
    @GetMapping("/exists")
    public ApiResponse<Boolean> fileExists(@RequestParam("ossPath") String ossPath) {
        log.info("检查OSS文件是否存在：{}", ossPath);
        
        try {
            boolean exists = ossFileUploadUtil.fileExists(ossPath);
            log.info("OSS文件存在性检查结果：{} -> {}", ossPath, exists);
            
            return ApiResponse.success("文件存在性检查完成", exists);
            
        } catch (Exception e) {
            log.error("检查OSS文件存在性异常：{}", e.getMessage(), e);
            return ApiResponse.error("检查文件存在性异常：" + e.getMessage());
        }
    }
}
