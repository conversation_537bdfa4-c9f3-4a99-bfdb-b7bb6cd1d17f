package com.kefang.controller;

import com.kefang.api.ApiTokenHelper;
import com.kefang.interceptor.NoAuth;
import com.kefang.dto.SsoApiTokenDTO;
import com.kefang.dto.SsoUserLoginDTO.Response;
import com.kefang.entity.Agent;
import com.kefang.entity.ChatSession;
import com.kefang.service.AgentService;
import com.kefang.service.ChatService;
import com.kefang.service.SsoService;
import com.kefang.service.StatisticsService;
import com.kefang.utils.JwtUtil;
import com.kefang.vo.Result;
import com.kefang.vo.ResultCode;
import com.kefang.vo.SessionStatVO;
import com.kefang.vo.WaitingSessionVO;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
@RestController
@RequestMapping("/api/agent")
public class AgentController {
    
    @Autowired
    private AgentService agentService;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Autowired
    private ChatService chatService;
    @Autowired
    private ApiTokenHelper apiTokenHelper;
    
    @Autowired
    private StatisticsService statisticsService;
    @Autowired
    private SsoService ssoService;

    private String cachedPrivateKey;
    private String cachedApiToken;
    private static final Logger logger = LoggerFactory.getLogger(AgentController.class);

    /**
     * 刷新和获取SSO API Token
     */
    private void refreshSsoApiToken() {
        try {
            SsoApiTokenDTO ssoApiTokenDTO = apiTokenHelper.getApiToken();
            if (ssoApiTokenDTO != null && ssoApiTokenDTO.getCode() == 200 && ssoApiTokenDTO.getData() != null) {
                this.cachedPrivateKey = ssoApiTokenDTO.getData().getPrivateKey();
                this.cachedApiToken = ssoApiTokenDTO.getData().getApitoken();
                logger.info("SSO API Token 刷新成功");
            } else {
                logger.error("SSO API Token 刷新失败");
            }
        } catch (Exception var2) {
            logger.error("SSO API Token 刷新异常: {}", var2.getMessage(), var2);
        }

    }

    /**
     * 获取SSO API Token
     * @return
     */
    private Map<String, String> getSsoApiToken() {
        if (this.cachedPrivateKey == null || this.cachedApiToken == null) {
            logger.info("SSO API Token 未缓存，正在获取...");
            this.refreshSsoApiToken();
        }
        logger.info("SSO API Token 缓存中已存在，正在返回...");
        Map<String, String> tokenMap = new HashMap();
        tokenMap.put("privateKey", this.cachedPrivateKey);
        tokenMap.put("apiToken", this.cachedApiToken);
        return tokenMap;
    }


    /**
     * 客服登录
     */
    @PostMapping("/login")
    @NoAuth
    public Result<Map<String, Object>> login(@RequestBody Agent agent) {
        // 参数校验
        if (agent.getAgentNo() == null || agent.getPassword() == null) {
            return Result.error("工号和密码不能为空");
        }
        Response userLoginResponse = null;
        try {
            // 获取SSO API Token
            Map<String, String> tokenMap = this.getSsoApiToken();
            String privateKey = (String)tokenMap.get("privateKey");
            String apiToken = (String)tokenMap.get("apiToken");
            // 调用SSO API进行用户登录
            userLoginResponse = this.ssoService.userLogin(agent.getAgentNo(), agent.getPassword(), privateKey, apiToken);
            if (userLoginResponse != null && userLoginResponse.getMsg() != null && userLoginResponse.getMsg().contains("API密钥校验错误")) {
                logger.info("SSO API Token 已过期，正在刷新...");
                this.refreshSsoApiToken();
                tokenMap = this.getSsoApiToken();
                userLoginResponse = this.ssoService.userLogin(agent.getAgentNo(), agent.getPassword(), (String)tokenMap.get("privateKey"), (String)tokenMap.get("apiToken"));
            }

            logger.info("SSO用户登录成功，响应数据: {}", userLoginResponse);
        } catch (Exception var8) {
            logger.error("远程调用SSO服务失败: {}", var8.getMessage(), var8);
            return Result.error("远程调用SSO服务失败: " + var8.getMessage());
        }
        if (userLoginResponse.getCode() == 200){
            String isBearAdm = userLoginResponse.getData().getIs_bear_adm();
            if ("0".equals(isBearAdm)){
                return Result.error("您不是客服，请使用客服账号登录");
            }else {
                String mobile = userLoginResponse.getData().getMobile();// 获取手机号
                String userId = userLoginResponse.getData().getUser_id();// 获取用户ID
                String name = userLoginResponse.getData().getName();// 获取用户名
                String userName = userLoginResponse.getData().getUser_name();

                // 查询客服账号表是否有该用户，如果有则登录 没有则添加
                String agentNo = agent.getAgentNo(); // 或者 mobile，取决于你的业务逻辑
                Agent existingAgent = agentService.getAgentByAgentNo(agentNo);
                if (existingAgent == null) {
                    // ---------------------------------
                    // 分支一：用户不存在，执行新增逻辑
                    // ---------------------------------
                    System.out.println("用户不存在，执行新增操作...");
                    Agent agentToAdd = new Agent();
                    agentToAdd.setAgentNo(agentNo);
                    agentToAdd.setPassword(agent.getPassword());
                    agentToAdd.setName(name);
                    agentToAdd.setUserId(userId);
                    agentToAdd.setUserName(userName);
                    agentToAdd.setAvatar(userLoginResponse.getData().getAvatar());
                    agentToAdd.setUserType(userLoginResponse.getData().getUser_type());
                    agentToAdd.setAgentType(3);
                    agentToAdd.setStatus(0);
                    agentService.addAgent(agentToAdd);

                } else {
                    // ---------------------------------
                    // 分支二：用户已存在，执行更新逻辑
                    // ---------------------------------
                    System.out.println("用户已存在，执行更新操作...");
                    // 在查出来的 existingAgent 对象上更新需要修改的字段
                    existingAgent.setName(name);
                    existingAgent.setUserName(userName);
                    existingAgent.setAvatar(userLoginResponse.getData().getAvatar());
                    existingAgent.setUserType(userLoginResponse.getData().getUser_type());
                    existingAgent.setPassword(agent.getPassword());
                    // 调用 service 的更新方法
                    agentService.updateAgent(existingAgent);
                }
            }
        }


        Agent loginAgent = agentService.login(agent.getAgentNo(), agent.getPassword());
        if (loginAgent != null) {
            // 生成登录信息
            Map<String, Object> data = new HashMap<>();
            // 不返回密码
            loginAgent.setPassword(null);
            data.put("agent", loginAgent);

            // 使用JWT工具类生成token，有效期设置为30天
            Map<String, Object> claims = new HashMap<>();
            claims.put("userId", loginAgent.getId());
            claims.put("agentNo", loginAgent.getAgentNo());
            claims.put("role", loginAgent.getAgentType() == 3 ? "admin" : "agent");
            String token = jwtUtil.generateToken(claims, 5 * 60 * 60 * 1000L); // 5小时
            data.put("token", token);

            return Result.success("登录成功", data);
        } else {
            return Result.error("登录失败，工号或密码错误");
        }
    }
    
    /**
     * 获取客服信息
     */
    @GetMapping("/{id}")
    public Result<Agent> getAgentInfo(@PathVariable Long id) {
        Agent agent = agentService.getAgentById(id);
        if (agent != null) {
            // 不返回密码
            agent.setPassword(null);
            return Result.success(agent);
        } else {
            return Result.error("客服不存在");
        }
    }
    
    /**
     * 更新客服状态（上线/下线）
     */
    @PutMapping("/{id}/status")
    public Result<Agent> updateStatus(@PathVariable Long id, @RequestParam Integer status) {
        // 参数校验
        if (status != 0 && status != 1) {
            return Result.error("状态值无效，应为0（离线）或1（在线）");
        }
        
        boolean success = agentService.updateAgentStatus(id, status);
        if (success) {
            Agent agent = agentService.getAgentById(id);
            // 不返回密码
            agent.setPassword(null);
            return Result.success("状态更新成功", agent);
        } else {
            return Result.error("状态更新失败");
        }
    }
    
    /**
     * 添加客服（仅管理员使用）
     */
    @PostMapping("/add")
    public Result<Agent> addAgent(@RequestBody Agent agent) {
        // 参数校验
        if (agent.getAgentNo() == null || agent.getName() == null || agent.getPassword() == null) {
            return Result.error("工号、姓名和密码不能为空");
        }
        
        boolean success = agentService.addAgent(agent);
        if (success) {
            // 获取新增的客服信息
            Agent newAgent = agentService.getAgentByAgentNo(agent.getAgentNo());
            // 不返回密码
            newAgent.setPassword(null);
            return Result.success("添加成功", newAgent);
        } else {
            return Result.error("添加失败，工号可能已存在");
        }
    }
    
    /**
     * 获取所有客服（仅管理员使用）
     */
    @GetMapping("/list")
    public Result<List<Agent>> getAllAgents() {
        List<Agent> agents = agentService.getAllAgents();
        // 不返回密码
        agents.forEach(agent -> agent.setPassword(null));
        return Result.success(agents);
    }
    
    /**
     * 获取所有在线客服
     */
    @GetMapping("/online")
    public Result<List<Agent>> getOnlineAgents() {
        List<Agent> agents = agentService.getOnlineAgents();
        // 不返回密码
        agents.forEach(agent -> agent.setPassword(null));
        return Result.success(agents);
    }
    
    /**
     * 刷新token
     */
    @PostMapping("/refresh-token")
    @NoAuth
    public Result<Map<String, Object>> refreshToken(@RequestBody Map<String, Object> params) {
        Long agentId = Long.valueOf(params.get("agentId").toString());
        Agent agent = agentService.getAgentById(agentId);
        
        if (agent != null) {
            Map<String, Object> data = new HashMap<>();
            
            // 生成新的token
            Map<String, Object> claims = new HashMap<>();
            claims.put("userId", agent.getId());
            claims.put("agentNo", agent.getAgentNo());
            claims.put("role", agent.getAgentType() == 3 ? "admin" : "agent");
            String token = jwtUtil.generateToken(claims, 5 * 60 * 60 * 1000L); // 5小时
            data.put("token", token);
            
            return Result.success("刷新token成功", data);
        } else {
            return Result.error("刷新token失败，客服不存在");
        }
    }
    
    /**
     * 验证token是否有效
     */
    @PostMapping("/validateToken")
    @NoAuth
    public Result<Boolean> validateToken(@RequestBody Map<String, String> params) {
        String token = params.get("token");
        if (token == null || token.isEmpty()) {
            return Result.error(ResultCode.PARAM_ERROR, "token不能为空");
        }
        
        boolean isValid = jwtUtil.validateToken(token) && !jwtUtil.isTokenExpired(token);
        
        if (isValid) {
            return Result.success("token有效", true);
        } else {
            return Result.success("token无效或已过期", false);
        }
    }
    
    /**
     * 获取工作台统计数据
     */
    @GetMapping("/dashboardStats")
    public Result<Map<String, Object>> getDashboardStats() {
        Map<String, Object> stats = statisticsService.getDashboardStats();
        return Result.success(stats);
    }
    
    /**
     * 获取待处理会话列表
     */
    @GetMapping("/waitingSessions")
    public Result<List<WaitingSessionVO>> getWaitingSessions() {
        List<WaitingSessionVO> waitingSessions = chatService.getWaitingSessions();
        return Result.success(waitingSessions);
    }
    
    /**
     * 获取对话统计数据
     * @param period 时间周期：day, week, month
     */
    @GetMapping("/messageStats")
    public Result<List<SessionStatVO>> getMessageStats(@RequestParam String period) {
        List<SessionStatVO> messageStats = statisticsService.getMessageStats(period);
        return Result.success(messageStats);
    }
    
    /**
     * 获取满意度统计数据
     * @param period 时间周期：day, week, month
     */
    @GetMapping("/satisfactionStats")
    public Result<List<SessionStatVO>> getSatisfactionStats(@RequestParam String period) {
        List<SessionStatVO> satisfactionStats = statisticsService.getSatisfactionStats(period);
        return Result.success(satisfactionStats);
    }
    
    /**
     * 接受会话
     */
    @PutMapping("/acceptSession/{sessionId}")
    public Result<ChatSession> acceptSession(@PathVariable Long sessionId, @RequestParam Long agentId) {
        ChatSession session = chatService.acceptSession(sessionId, agentId);
        if (session != null) {
            return Result.success("接受会话成功", session);
        } else {
            return Result.error("接受会话失败");
        }
    }
    
    /**
     * 更新客服状态
     */
    @PutMapping("/updateStatus")
    public Result<Boolean> updateAgentStatus(@RequestBody Map<String, Object> params) {
        Long agentId = Long.valueOf(params.get("agentId").toString());
        Integer status = Integer.valueOf(params.get("status").toString());
        
        if (status != 0 && status != 1) {
            return Result.error("状态值无效，应为0（离线）或1（在线）");
        }
        
        boolean success = agentService.updateAgentStatus(agentId, status);
        if (success) {
            return Result.success("状态更新成功", true);
        } else {
            return Result.error("状态更新失败");
        }
    }
    
    /**
     * 检查是否是系统管理员
     */
    @GetMapping("/checkAdmin")
    public Result<Boolean> checkIsAdmin(@RequestParam Long agentId) {
        Agent agent = agentService.getAgentById(agentId);
        if (agent != null) {
            boolean isAdmin = agent.getAgentType() == 3;
            return Result.success(isAdmin);
        } else {
            return Result.error("客服不存在");
        }
    }
    
    /**
     * 更新客服个人信息
     */
    @PutMapping("/{id}")
    public Result<Agent> updateAgent(@PathVariable Long id, @RequestBody Agent agent) {
        logger.info("更新客服个人信息，id={}，请求数据={}, 是否包含新密码={}", id, agent, agent.getNewPassword() != null);
        
        // 设置ID
        agent.setId(id);
        
        try {
            // 密码处理逻辑
            if (agent.getNewPassword() != null && !agent.getNewPassword().isEmpty()) {
                // 直接设置新密码，无需验证旧密码
                agent.setPassword(agent.getNewPassword());
                logger.info("客服密码已更新，id={}, 新密码长度={}", id, agent.getNewPassword().length());
            } else {
                logger.info("未修改密码或新密码为空");
            }
            
            // 字段校验
            if (agent.getName() == null || agent.getName().trim().isEmpty()) {
                return Result.error("姓名不能为空");
            }
            
            // 头像校验：仅允许使用预设的头像URL
//            if (agent.getAvatar() != null && !isValidAvatarUrl(agent.getAvatar())) {
//                return Result.error("头像URL无效，请使用系统预设头像");
//            }
            
            // 客服类型校验：只能是1(工客服)或3(系统管理员)
            if (agent.getAgentType() != null) {
                if (agent.getAgentType() != 1 && agent.getAgentType() != 3) {
                    return Result.error("客服类型无效，只能为工客服(1)或系统管理员(3)");
                }
                
                // 简单验证：只允许修改自己的信息
                Agent currentAgent = agentService.getAgentById(id);
                if (currentAgent == null) {
                    return Result.error("客服不存在");
                }
                
                // 记录客服类型变更
                if (!currentAgent.getAgentType().equals(agent.getAgentType())) {
                    logger.info("客服类型已更改，id={}, 从 {} 更改为 {}", id, currentAgent.getAgentType(), agent.getAgentType());
                }
            }
            
            boolean success = agentService.updateAgent(agent);
            if (success) {
                Agent updatedAgent = agentService.getAgentById(id);
                // 不返回密码
                updatedAgent.setPassword(null);
                return Result.success("更新成功", updatedAgent);
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            logger.error("更新客服个人信息异常", e);
            return Result.error("系统异常，请稍后重试");
        }
    }
    
    /**
     * 验证头像URL是否有效
     */
    private boolean isValidAvatarUrl(String url) {
        // 预设的头像URL列表
        String[] validAvatars = {
            "https://file.juranguanjia.com/upfile/2025/04-18/8288c7361d1a40a280122bdd93519f59.png",
            "https://file.juranguanjia.com/upfile/2025/04-18/d70ffb09ffe24e1591ae6bdcb3f3f25e.png",
            "https://file.juranguanjia.com/upfile/2025/04-18/7c4d83fcb68444e9842c1205df145418.png",
            "https://file.juranguanjia.com/upfile/2025/04-18/197095ad9fa34129bf94ec65ca223ec8.png",
            "https://file.juranguanjia.com/upfile/2025/04-18/5368534b64a74b83b68acb8194c87cd7.png",
            "https://file.juranguanjia.com/upfile/2025/04-18/ce8a318cbaeb4b06b8f47f0ac8a34906.png",
            "https://file.juranguanjia.com/upfile/2025/04-18/1dc20d13ae0c465ab68e32154941fad9.png"
        };
        
        for (String validUrl : validAvatars) {
            if (validUrl.equals(url)) {
                return true;
            }
        }
        
        return false;
    }
} 