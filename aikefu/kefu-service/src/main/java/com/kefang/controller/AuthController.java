package com.kefang.controller;

import com.kefang.interceptor.NoAuth;
import com.kefang.vo.Result;
import com.kefang.vo.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/jd")
public class AuthController {

    @GetMapping("/auth")
    @NoAuth
    public Result<Map<String, String>> handleCallback(@RequestParam String code, @RequestParam String state) {
        // 打印接收到的 code 和 state
        log.info("收到京东授权回调, Code: {}, State: {}", code, state);

        try {
            // 使用 code 换取 access_token 的逻辑
            String accessToken = exchangeCodeForAccessToken(code);
            
            if (accessToken != null) {
                Map<String, String> data = new HashMap<>();
                data.put("accessToken", accessToken);
                data.put("code", code);
                data.put("state", state);
                return Result.success("授权成功", data);
            } else {
                return Result.error(ResultCode.ERROR, "获取访问令牌失败");
            }
        } catch (Exception e) {
            log.error("京东授权处理异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.ERROR, "授权处理异常: " + e.getMessage());
        }
    }

    private String exchangeCodeForAccessToken(String code) {
        try {
            String url = "https://oauth.jd.com/oauth/token";
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("grant_type", "authorization_code");
            params.add("client_id", "6873D28FD36D127F1F87F1420965B74D");
            params.add("client_secret", "03a7014cd52b4bee998161147be1d847");
            params.add("code", code);
            params.add("redirect_uri", "http://6348cf41.r6.cpolar.top/jd/auth");

            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);

            // 打印完整响应
            log.info("京东OAuth响应: {}", response.getBody());

            if (response.getBody().containsKey("access_token")) {
                return (String) response.getBody().get("access_token");
            } else if (response.getBody().containsKey("error")) {
                String error = (String) response.getBody().get("error");
                String errorDescription = (String) response.getBody().get("error_description");
                log.error("京东OAuth错误: {}, 描述: {}", error, errorDescription);
            }
        } catch (Exception e) {
            log.error("请求京东OAuth接口异常: {}", e.getMessage(), e);
        }
        return null;
    }
}