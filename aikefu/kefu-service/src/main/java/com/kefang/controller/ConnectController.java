package com.kefang.controller;

import com.kefang.constants.MessageConstants;
import com.kefang.entity.ConnectEntiy;
import com.kefang.handler.MessageHandlerFactory;
import com.kefang.interceptor.NoAuth;
import com.kefang.job.SourceWxGroupStatisticsTask;
import com.kefang.service.ConnectService;
import com.kefang.utils.AssertUtils;
import com.kefang.vo.Result;
import com.kefang.vo.SourceWxGroupForm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 连接控制器
 * 处理在线客服会话请求
 */
@Slf4j
@RestController
@RequestMapping("/connect")
public class ConnectController {

    @Autowired
    private ConnectService connectService;

    @Autowired
    private MessageHandlerFactory messageHandlerFactory;

    @Autowired
    private SourceWxGroupStatisticsTask sourceWxGroupStatisticsTask;


    @PostMapping("/onLine")
    @NoAuth
    public Result<String> onLine(@RequestBody ConnectEntiy connectEntiy) {
        // 1. 参数验证
        // 检查 session_id 是否为空
        Result<String> sessionIdCheck = AssertUtils.assertNotEmpty(connectEntiy.getSession_id(), MessageConstants.ERROR_SESSION_ID_EMPTY);
        if (sessionIdCheck != null) {
            log.info("session_id不能为空");
            return sessionIdCheck;
        }

        // 检查 messages 是否为空
        Result<String> messagesCheck = AssertUtils.assertNotEmpty(connectEntiy.getMessages(), MessageConstants.ERROR_MESSAGES_EMPTY);
        if (messagesCheck != null) {
            log.info("messages不能为空");
            return messagesCheck;
        }

        String messages = connectEntiy.getMessages();
        log.info("用户输入: {}", messages);
        log.info("请求在线 onLine 客服会话，参数: {}", connectEntiy);

        try {
            // 2. 判断是否是微信群消息（需要特殊处理的消息） 群类型 != 0 不为空
            if (connectEntiy.getGroup_type() != null && !connectEntiy.getGroup_type().isEmpty() && !"0".equals(connectEntiy.getGroup_type())) {
                // 使用ConnectService的handleGroupMessage方法处理微信群消息
                Result<String> groupResult = connectService.handleGroupMessage(connectEntiy);

                // 如果成功处理了该微信群消息，则直接返回处理结果
                if (groupResult != null) {
                    log.info("微信群消息处理结果: {}", groupResult.getData());
                    return Result.success(MessageConstants.RESPONSE_SUCCESS, groupResult.getData());
                }
            }

            // 3. 如果没有特殊处理器处理或非微信群消息，则使用AI服务处理
            String response = connectService.onLine(connectEntiy);
            return Result.success(MessageConstants.RESPONSE_SUCCESS, response);

        } catch (Exception e) {
            log.error("在线客服会话异常: {}", e.getMessage(), e);
            return Result.success("在线客服会话异常", "在线客服会话异常,请联系管理员处理");
        }
    }


    /**
     * 手动触发微信群来源统计任务
     *
     * @return 统计结果
     */
    @PostMapping("/triggerSourceWxGroup")
    @NoAuth
    public Result<String> triggerSourceWxGroupStatistics(@RequestBody SourceWxGroupForm form) {
        log.info("手动触发微信群来源统计任务");
        try {
            String response = sourceWxGroupStatisticsTask.fetchAndPrintStatistics(form.getDate(), form.getSourceList());
            return Result.success("任务执行成功", response);
        } catch (Exception e) {
            log.error("手动触发微信群来源统计任务失败", e);
            return Result.error("任务执行失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发回收商数据 微信群统计任务
     *
     * @return 统计结果
     */
    @PostMapping("/triggerSourceWxGroupRecyclerData")
    @NoAuth
    public Result<String> triggerSourceWxGroupRecyclerData(@RequestBody SourceWxGroupForm form) {
        log.info("手动触发回收商数据微信群统计任务");
        try {
            String recyclered = sourceWxGroupStatisticsTask.recyclerDataWxGroupStatistics();
            return Result.success("任务执行成功",recyclered);
        } catch (Exception e) {
            log.error("手动触发回收商数据微信群统计任务失败", e);
            return Result.error("任务执行失败: " + e.getMessage());
        }
    }
}