package com.kefang.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.kefang.interceptor.NoAuth;
import com.kefang.entity.Agent;
import com.kefang.service.AgentService;
import com.kefang.utils.HttpClientUtil;
import com.kefang.utils.JwtUtil;
import com.kefang.vo.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * SSO授权控制器
 * 处理第三方SSO登录跳转功能
 */
@RestController
@RequestMapping("/api/sso-auth")
public class SsoAuthController {

    private static final Logger logger = LoggerFactory.getLogger(SsoAuthController.class);

    @Value("${sso.bearhome.url}")
    private String bearHomeSsoUrl;

    @Autowired
    private HttpClientUtil httpClientUtil;

    @Autowired
    private AgentService agentService;

    @Autowired
    private JwtUtil jwtUtil;
    
    @Autowired
    @Qualifier("ssoRestTemplate")
    private RestTemplate restTemplate;

    /**
     * 直接使用RestTemplate发送请求，绕过HttpClientUtil
     * @param token 认证令牌
     * @return 响应结果
     */
    private String directRequest(String token) {
        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("version", "1.0");
            headers.set("token", token);
            headers.set("Accept", "*/*");
            headers.set("Accept-Encoding", "identity"); // 明确要求不压缩
            headers.set("Connection", "keep-alive");
            headers.set("Content-Type", "application/json");
            headers.set("Cookie", "jr_sso_token=dd0ca30726c2b489fc20aa28a4a36aae");
            headers.set("User-Agent", "PostmanRuntime-ApipostRuntime/1.1.0");
            
            // 构建请求体
            String requestBody = "{\"token\": \"" + token + "\"}";
            
            // 创建请求实体
            HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
            
            // 发送请求并获取响应
            ResponseEntity<String> response = restTemplate.exchange(
                bearHomeSsoUrl, 
                HttpMethod.POST, 
                entity, 
                String.class
            );
            
            // 获取响应体
            String responseBody = response.getBody();
            logger.info("直接请求响应状态: {}", response.getStatusCode());
            logger.info("直接请求响应头: {}", response.getHeaders());
            logger.info("直接请求响应体: {}", responseBody);
            
            return responseBody;
        } catch (Exception e) {
            logger.error("直接请求失败: {}", e.getMessage(), e);
            throw e;
        }
    }
    


    /**
     * 通过SSO系统的token进行登录
     * 请求成功后会查询客服账号表是否有该用户，有则登录，没有则添加再登录
     */
    @PostMapping("/login")
    @NoAuth
    public Result<Map<String, Object>> ssoLogin(@RequestBody Map<String, String> params) {
        // 获取token参数
        String token = params.get("token");
        if (token == null || token.isEmpty()) {
            return Result.error("token不能为空");
        }

        logger.info("SSO登录请求 - Token: {}", token);
        logger.info("SSO请求URL: {}", bearHomeSsoUrl);

        try {
            // 使用直接请求方式，绕过压缩问题
            String rawResponse = directRequest(token);
            logger.info("SSO直接请求响应: {}", rawResponse);
            
            // 解析JSON响应
            JSONObject response;
            try {
                response = JSON.parseObject(rawResponse);
            } catch (JSONException e) {
                logger.error("JSON解析失败: {}", e.getMessage(), e);
                return Result.error("无法解析SSO响应: " + e.getMessage());
            }

            // 检查请求是否成功
            int code = response.getInteger("code");
            if (code != 200) {
                logger.error("SSO认证失败，响应码: {}, 消息: {}", code, response.getString("msg"));
                return Result.error("SSO认证失败: " + response.getString("msg"));
            }

            // 解析用户数据
            JSONObject userData = response.getJSONObject("data");
            String userId = userData.getString("user_id");
            String userName = userData.getString("user_name");
            String mobile = userData.getString("mobile");
            String name = userData.getString("name");
            String avatar = userData.getString("avatar");
            Integer userType = userData.getInteger("user_type");

            logger.info("成功获取用户信息 - userId: {}, name: {}, mobile: {}", userId, name, mobile);

            // 验证用户是否有客服权限
            Integer isMerchant = userData.getInteger("is_merchant");
            Integer isBearAdm = userData.getInteger("is_bear_adm");
            
            if (isBearAdm == 0) {
                logger.warn("用户没有客服权限，is_bear_adm={}", isBearAdm);
                return Result.error("您不是客服，无法登录客服系统");
            }

            // 查询客服账号表是否有该用户
            Agent agentByAgentNo = agentService.getAgentByAgentNo(mobile);
            
            // 如果没有，则添加新客服
            if (agentByAgentNo == null) {
                logger.info("用户 {} 在客服系统中不存在，创建新账号", mobile);
                Agent agentAdd = new Agent();
                agentAdd.setAgentType(3); // 系统管理员
                agentAdd.setStatus(0); // 初始状态为离线
                agentAdd.setAgentNo(mobile);
                // 这里没有密码，因为是通过SSO登录的，可以设置一个随机密码或者固定密码
                agentAdd.setPassword("sso_" + userId); // 设置一个基于用户ID的密码
                agentAdd.setName(name);
                agentAdd.setUserId(userId);
                agentAdd.setUserName(userName);
                agentAdd.setAvatar(avatar);
                agentAdd.setUserType(userType.toString());
                
                boolean success = agentService.addAgent(agentAdd);
                if (!success) {
                    logger.error("创建客服账号失败");
                    return Result.error("创建客服账号失败");
                }
                
                // 重新获取完整的客服信息
                agentByAgentNo = agentService.getAgentByAgentNo(mobile);
            }

            // 获取更新后的客服信息
            agentByAgentNo = agentService.getAgentById(agentByAgentNo.getId());
            // 更新登录时间
            agentService.updateLoginTime(agentByAgentNo.getId());
            // 生成登录信息
            Map<String, Object> data = new HashMap<>();
            // 不返回密码
            agentByAgentNo.setPassword(null);
            data.put("agent", agentByAgentNo);

            // 生成JWT令牌，有效期设置为5小时
            Map<String, Object> claims = new HashMap<>();
            claims.put("userId", agentByAgentNo.getId());
            claims.put("agentNo", agentByAgentNo.getAgentNo());
            claims.put("role", agentByAgentNo.getAgentType() == 3 ? "admin" : "agent");
            claims.put("role", "agent");
            String jwtToken = jwtUtil.generateToken(claims, 5 * 60 * 60 * 1000L); // 5小时
            data.put("token", jwtToken);

            logger.info("SSO登录成功，生成的JWT令牌: {}", jwtToken);
            return Result.success("SSO登录成功", data);
        } catch (Exception e) {
            logger.error("SSO登录异常: {}", e.getMessage(), e);
            return Result.error("SSO登录异常: " + e.getMessage());
        }
    }
} 