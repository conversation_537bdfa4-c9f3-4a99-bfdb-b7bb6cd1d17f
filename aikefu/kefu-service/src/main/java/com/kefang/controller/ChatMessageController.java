package com.kefang.controller;

import com.kefang.entity.ChatMessage;
import com.kefang.service.ChatMessageService;
import com.kefang.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/message")
public class ChatMessageController {
    
    @Autowired
    private ChatMessageService chatMessageService;
    
    /**
     * 发送消息
     */
    @PostMapping("/send")
    public Result<ChatMessage> sendMessage(@RequestBody ChatMessage message) {
        // 参数校验
        if (message.getSessionId() == null || message.getSenderType() == null || message.getContent() == null) {
            return Result.error("会话ID、发送者类型和消息内容不能为空");
        }
        
        Long messageId = chatMessageService.sendMessage(message);
        if (messageId != null) {
            // 设置消息ID
            message.setId(messageId);
            return Result.success("发送成功", message);
        } else {
            return Result.error("发送失败");
        }
    }
    /**
     * 获取会话的消息列表
     */
    @GetMapping("/session/{sessionId}")
    public Result<List<ChatMessage>> getMessagesBySessionId(@PathVariable Long sessionId) {
        List<ChatMessage> messages = chatMessageService.getMessagesBySessionId(sessionId);
        return Result.success(messages);
    }

    /**
     * 获取最近用户消息
     * 仅查询用户(sender_type=0)发送的文本消息(msg_type=0)
     * 按创建时间倒序排序
     */
    @GetMapping("/recent-user-messages")
    public Result<List<Map<String, Object>>> getRecentUserMessages() {
        List<Map<String, Object>> messages = chatMessageService.getRecentUserMessages(50);
        return Result.success(messages);
    }
    
    /**
     * 分页获取会话的消息
     */
    @GetMapping("/session/{sessionId}/page")
    public Result<List<ChatMessage>> getMessagesByPage(
            @PathVariable Long sessionId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        List<ChatMessage> messages = chatMessageService.getMessagesByPage(sessionId, page, size);
        return Result.success(messages);
    }
    
    /**
     * 更新消息已读状态
     */
    @PutMapping("/session/{sessionId}/read")
    public Result<Boolean> updateReadStatus(
            @PathVariable Long sessionId,
            @RequestParam Integer senderType) {
        chatMessageService.updateMessageReadStatus(sessionId, senderType);
        return Result.success("更新已读状态成功", true);

    }
    
    /**
     * 获取未读消息数量
     */
    @GetMapping("/session/{sessionId}/unread")
    public Result<Integer> countUnreadMessages(
            @PathVariable Long sessionId,
            @RequestParam Integer senderType) {
        int count = chatMessageService.countUnreadMessages(sessionId, senderType);
        return Result.success(count);
    }
    
    /**
     * 获取会话最新一条消息
     */
    @GetMapping("/session/{sessionId}/latest")
    public Result<ChatMessage> getLatestMessage(@PathVariable Long sessionId) {
        ChatMessage message = chatMessageService.getLatestMessage(sessionId);
        return Result.success(message);
    }
    
    /**
     * 按类型统计消息数量
     */
    @GetMapping("/session/{sessionId}/count")
    public Result<Integer> countMessagesByType(
            @PathVariable Long sessionId,
            @RequestParam(required = false) Integer msgType) {
        int count = chatMessageService.countMessagesByType(sessionId, msgType);
        return Result.success(count);
    }
    
    /**
     * 按时间范围查询消息
     */
    @GetMapping("/session/{sessionId}/time-range")
    public Result<List<ChatMessage>> getMessagesByTimeRange(
            @PathVariable Long sessionId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        List<ChatMessage> messages = chatMessageService.getMessagesByTimeRange(sessionId, startTime, endTime);
        return Result.success(messages);
    }
    
    /**
     * 条件查询消息（支持关键词搜索、类型过滤和分页）
     */
    @GetMapping("/search")
    public Result<Map<String, Object>> searchMessages(
            @RequestParam(required = false) Long sessionId,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer msgType,
            @RequestParam(required = false) Integer senderType,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam(required = false) String datasource,
            @RequestParam(required = false) String scene,
            @RequestParam(required = false) String channel,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        
        // 必须指定会话ID
        if (sessionId == null) {
            return Result.error("会话ID不能为空");
        }
        
        // 组装查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("sessionId", sessionId);
        params.put("keyword", keyword);
        params.put("msgType", msgType);
        params.put("senderType", senderType);
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        params.put("datasource", datasource);
        params.put("scene", scene);
        params.put("channel", channel);
        params.put("page", page);
        params.put("size", size);
        
        // 执行查询
        Map<String, Object> result = chatMessageService.getMessagesByCondition(params);
        return Result.success(result);
    }
} 