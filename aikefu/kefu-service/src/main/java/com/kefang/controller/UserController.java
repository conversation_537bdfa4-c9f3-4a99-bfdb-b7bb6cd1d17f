package com.kefang.controller;

import com.kefang.api.ApiTokenHelper;
import com.kefang.dto.AutoRegister;
import com.kefang.dto.UserOrderInfo;
import com.kefang.interceptor.NoAuth;
import com.kefang.dto.SsoApiTokenDTO;
import com.kefang.dto.SsoUserLoginDTO;
import com.kefang.entity.User;
import com.kefang.entity.UserOrders;
import com.kefang.mapper.UserMapper;
import com.kefang.service.SsoService;
import com.kefang.service.UserService;
import com.kefang.service.UserOrdersService;
import com.kefang.utils.JwtUtil;
import com.kefang.vo.PageResult;
import com.kefang.vo.Result;
import com.kefang.vo.ResultCode;
import com.mysql.cj.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/user")
public class UserController {
    
    @Autowired
    private UserService userService;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private JwtUtil jwtUtil;
    @Autowired
    private SsoService ssoService;
    @Autowired
    private UserOrdersService userOrdersService;
    @Autowired
    private ApiTokenHelper apiTokenHelper;

    private String cachedPrivateKey;
    private String cachedApiToken;
    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    /**
     * 刷新和获取SSO API Token
     */
    private void refreshSsoApiToken() {
        try {
            SsoApiTokenDTO ssoApiTokenDTO = apiTokenHelper.getApiToken();
            if (ssoApiTokenDTO != null && ssoApiTokenDTO.getCode() == 200 && ssoApiTokenDTO.getData() != null) {
                this.cachedPrivateKey = ssoApiTokenDTO.getData().getPrivateKey();
                this.cachedApiToken = ssoApiTokenDTO.getData().getApitoken();
                logger.info("SSO API Token 刷新成功");
            } else {
                logger.error("SSO API Token 刷新失败");
            }
        } catch (Exception var2) {
            logger.error("SSO API Token 刷新异常: {}", var2.getMessage(), var2);
        }

    }

    /**
     * 获取SSO API Token
     * @return
     */
    private Map<String, String> getSsoApiToken() {
        if (this.cachedPrivateKey == null || this.cachedApiToken == null) {
            logger.info("SSO API Token 未缓存，正在获取...");
            this.refreshSsoApiToken();
        }
        logger.info("SSO API Token 缓存中已存在，正在返回...");
        Map<String, String> tokenMap = new HashMap();
        tokenMap.put("privateKey", this.cachedPrivateKey);
        tokenMap.put("apiToken", this.cachedApiToken);
        return tokenMap;
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Result<User> register(@RequestBody User user) {
        // 参数校验
        if (StringUtils.isNullOrEmpty(user.getPhone()) || StringUtils.isNullOrEmpty(user.getPassword())) {
            return Result.error(ResultCode.PARAM_ERROR, "手机号和密码不能为空");
        }
        // 如果没有昵称，使用手机号作为默认昵称
        if (user.getNickname() == null || user.getNickname().isEmpty()) {
            user.setNickname(user.getPhone());
        }
        
        boolean success = userService.register(user);
        if (success) {
            // 不返回密码
            user.setPassword(null);
            return Result.success("注册成功", user);
        } else {
            return Result.error(ResultCode.USER_ALREADY_EXIST, "注册失败，手机号可能已被注册");
        }
    }
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    @NoAuth
    public Result<Map<String, Object>> login(@RequestBody User user) {
        // 参数校验
        if (StringUtils.isNullOrEmpty(user.getPhone()) || StringUtils.isNullOrEmpty(user.getPassword())) {
            return Result.error(ResultCode.PARAM_ERROR, "手机号和密码不能为空");
        }

        try {
            String phone = user.getPhone();
            String password = user.getPassword();

            // 1. 获取SSO API Token
            Map<String, String> tokenMap = getSsoApiToken();
            String privateKey = tokenMap.get("privateKey");
            String apiToken = tokenMap.get("apiToken");

            // 2. 调用SSO API进行用户登录
            SsoUserLoginDTO.Response userLoginResponse = ssoService.userLogin(phone, password, privateKey, apiToken);

            // 3. 处理API Token过期情况
            if (userLoginResponse != null && userLoginResponse.getMsg() != null && userLoginResponse.getMsg().contains("API密钥校验错误")) {
                logger.info("SSO API Token已过期，正在刷新...");
                tokenMap = getSsoApiToken();
                userLoginResponse = ssoService.userLogin(phone, password,
                        tokenMap.get("privateKey"), tokenMap.get("apiToken"));
            }

            // 4. 验证SSO登录响应
            if (userLoginResponse.getCode() == 200){
                String isBearAdm = userLoginResponse.getData().getIs_bear_adm();
                if ("1".equals(isBearAdm)){
                    return Result.error(ResultCode.USER_ACCOUNT_ERROR, "您是客服账号，请登录客服系统");
                }else {
                    // 5. sso 登录成功，查询用户账号表是否有该用户，如果有则登录 没有则添加
                    String mobile = userLoginResponse.getData().getMobile();// 获取手机号
                    String userId = userLoginResponse.getData().getUser_id();// 获取用户ID
                    String name = userLoginResponse.getData().getName();// 获取用户名
                    String userName = userLoginResponse.getData().getUser_name();
                    String isMerchant = userLoginResponse.getData().getIs_merchant(); // 是否商家  1 是
                    String isTechnician = userLoginResponse.getData().getIs_technician(); // 是否工程师 1  是
                    String avatar = userLoginResponse.getData().getAvatar(); // 头像

                    User existUser = userMapper.selectByPhone(user.getPhone());
                    if (existUser == null){
                        User userAdd = new User();
                        userAdd.setPhone(mobile);
                        userAdd.setPassword(password);
                        userAdd.setUserId(userId);
                        userAdd.setNickname(name);
                        userAdd.setRemark(userName+"_SSO注册");
                        userAdd.setIsMerchant(isMerchant);
                        userAdd.setIsTechnician(isTechnician);
                        userAdd.setAvatar(avatar);
                        boolean success = userService.register(user);
                        if (!success){
                            return Result.error(ResultCode.ERROR, "登录失败，请重新登录");
                        }
                    }
                }
            } else {
                logger.warn("SSO登录失败：{}", userLoginResponse.getMsg());
                return Result.error(ResultCode.SSO_LOGIN_FAILED, userLoginResponse.getMsg());
            }

            // 6. 登录成功，返回用户信息及token
            User loginUser = userService.login(user.getPhone(), user.getPassword());
            if (loginUser != null) {
                // 生成登录信息
                return generateLoginResponse(loginUser);
            } else {
                return Result.error(ResultCode.USER_PASSWORD_ERROR, "登录失败，手机号或密码错误");
            }
        } catch (Exception e) {
            logger.error("登录异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.ERROR, "系统繁忙，请稍后再试");
        }
    }
    
    /**
     * 获取用户信息
     */
    @GetMapping("/{id}")
    public Result<List<Object>> getUserInfo(@PathVariable Long id) {
        User user = userService.getUserById(id);
        if (user != null) {
            // 不返回密码
            user.setPassword(null);
            // 用户订单信息
            UserOrders userOrders = userOrdersService.getUserOrdersByPhone(user.getPhone());
            
            // 处理嵌套的JSON字符串问题
            if (userOrders != null) {
                userOrders = processOrderJsonFormat(userOrders);
            }
            
            // 创建返回List
            List<Object> list = new ArrayList<>();
            list.add(user);
            list.add(userOrders);
            return Result.success(list);
        } else {
            return Result.error(ResultCode.USER_NOT_EXIST, "用户不存在");
        }
    }
    
    /**
     * 处理订单信息中的嵌套JSON字符串问题
     * @param userOrders 原始订单信息
     * @return 处理后的订单信息
     */
    private UserOrders processOrderJsonFormat(UserOrders userOrders) {
        try {
            // 处理currentOrder
            if (userOrders.getCurrentOrder() != null && !userOrders.getCurrentOrder().isEmpty()) {
                userOrders.setCurrentOrder(processNestedJsonFields(userOrders.getCurrentOrder()));
            }
            
            // 处理orderList
            if (userOrders.getOrderList() != null && !userOrders.getOrderList().isEmpty()) {
                userOrders.setOrderList(processNestedJsonFields(userOrders.getOrderList()));
            }
        } catch (Exception e) {
            logger.error("处理订单JSON格式失败: {}", e.getMessage());
        }
        return userOrders;
    }

    /**
     * 处理嵌套的JSON字段，修复多层嵌套的JSON字符串问题
     * @param jsonString 原始JSON字符串
     * @return 处理后的JSON字符串
     */
    private String processNestedJsonFields(String jsonString) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(jsonString);
            ObjectNode newRootNode = mapper.createObjectNode();
            
            // 需要处理的字段列表
            String[] fieldsToProcess = {"recycle", "flea_market", "market", "engineer"};
            
            // 检查并处理每个字段
            for (String field : fieldsToProcess) {
                if (rootNode.has(field) && rootNode.get(field).isTextual()) {
                    try {
                        // 解析内嵌的JSON字符串
                        String fieldValue = rootNode.get(field).asText();
                        JsonNode fieldNode = mapper.readTree(fieldValue);
                        // 添加到新节点
                        newRootNode.set(field, fieldNode);
                    } catch (Exception e) {
                        // 如果解析失败，保留原始值
                        newRootNode.set(field, rootNode.get(field));
                        logger.error("处理字段 {} 失败: {}", field, e.getMessage());
                    }
                } else if (rootNode.has(field)) {
                    // 如果字段存在但不是文本类型（可能已经是对象），直接添加
                    newRootNode.set(field, rootNode.get(field));
                }
            }
            
            // 添加其他未处理的字段
            rootNode.fields().forEachRemaining(entry -> {
                String fieldName = entry.getKey();
                if (!Arrays.asList(fieldsToProcess).contains(fieldName) || !newRootNode.has(fieldName)) {
                    newRootNode.set(fieldName, entry.getValue());
                }
            });
            
            return mapper.writeValueAsString(newRootNode);
        } catch (Exception e) {
            logger.error("处理嵌套JSON字段失败: {}", e.getMessage());
            return jsonString; // 失败时返回原始字符串
        }
    }

    /**
     * 处理JSON字符串，确保格式正确
     * @param jsonString 原始JSON字符串
     * @return 处理后的JSON字符串
     */
    private String processJsonString(String jsonString) {
        if (StringUtils.isNullOrEmpty(jsonString)) {
            return jsonString;
        }
        
        try {
            // 首先检查和处理可能存在的嵌套字段
            String processedJson = processNestedJsonFields(jsonString);
            
            // 再次解析和序列化，确保整体格式标准
            ObjectMapper mapper = new ObjectMapper();
            JsonNode node = mapper.readTree(processedJson);
            return mapper.writeValueAsString(node);
        } catch (Exception e) {
            logger.error("处理JSON字符串失败: {}", e.getMessage());
            return jsonString; // 失败时返回原始字符串
        }
    }
    
    /**
     * 更新用户信息
     */
    @PutMapping("/{id}")
    public Result<User> updateUser(@PathVariable Long id, @RequestBody User user) {
        // 设置ID
        user.setId(id);
        
        boolean success = userService.updateUser(user);
        if (success) {
            User updatedUser = userService.getUserById(id);
            // 不返回密码
            updatedUser.setPassword(null);
            return Result.success("更新成功", updatedUser);
        } else {
            return Result.error("更新失败");
        }
    }
    
    /**
     * 获取所有用户（仅管理员使用）
     * @deprecated 建议使用分页接口 {@link #getUsersByPage(Map)}
     */
    @GetMapping("/list")
    public Result<List<User>> getAllUsers() {
        List<User> users = userService.getAllUsers();
        // 不返回密码
        users.forEach(user -> user.setPassword(null));
        return Result.success(users);
    }
    
    /**
     * 分页查询用户列表
     * @param params 查询参数
     * @return 分页结果
     */
    @GetMapping("/page")
    public Result<PageResult<User>> getUsersByPage(@RequestParam Map<String, Object> params) {
        // 设置默认值
        if (params.get("pageNum") == null) {
            params.put("pageNum", 1);
        }
        if (params.get("pageSize") == null) {
            params.put("pageSize", 10);
        }
        
        PageResult<User> pageResult = userService.getUsersByPage(params);
        // 不返回密码
        if (pageResult != null && pageResult.getList() != null) {
            pageResult.getList().forEach(user -> user.setPassword(null));
        }
        return Result.success(pageResult);
    }
    
    /**
     * 刷新token
     */
    @PostMapping("/refresh-token")
    @NoAuth
    public Result<Map<String, Object>> refreshToken(@RequestBody Map<String, Object> params) {
        Long userId = Long.valueOf(params.get("userId").toString());
        User user = userService.getUserById(userId);
        
        if (user != null) {
            Map<String, Object> data = new HashMap<>();
            
            // 生成新的token
            Map<String, Object> claims = new HashMap<>();
            claims.put("userId", user.getId());
            claims.put("phone", user.getPhone());
            claims.put("role", "user");
            String token = jwtUtil.generateToken(claims, 5 * 60 * 60 * 1000L); // 5小时
            data.put("token", token);
            
            return Result.success("刷新token成功", data);
        } else {
            return Result.error("刷新token失败，用户不存在");
        }
    }
    
    /**
     * 验证token是否有效
     */
    @PostMapping("/validateToken")
    @NoAuth
    public Result<Boolean> validateToken(@RequestBody Map<String, String> params) {
        String token = params.get("token");
        if (token == null || token.isEmpty()) {
            return Result.error(ResultCode.PARAM_ERROR, "token不能为空");
        }
        
        boolean isValid = jwtUtil.validateToken(token) && !jwtUtil.isTokenExpired(token);
        
        if (isValid) {
            return Result.success("token有效", true);
        } else {
            return Result.success("token无效或已过期", false);
        }
    }
    
    /**
     * 移动端自动注册用户
     */
    @PostMapping("/auto-register")
    @NoAuth
    public Result<Map<String, Object>> autoRegister(@RequestBody AutoRegister autoRegister) {
        String phone = autoRegister.getPhone();

        // 检查是否传了autoRegister参数以及是否有phone
        if (!StringUtils.isNullOrEmpty(phone)) {
            User userByPhone = userService.getUserByPhone(phone);

            if (userByPhone != null) {
                return generateLoginResponse(userByPhone);
            } else {
                User newUser = createUserObject(phone, autoRegister, "H5注册");
                boolean registrationSuccess = userService.register(newUser);

                if (registrationSuccess) {
                    return generateLoginResponse(newUser);
                } else {
                    return Result.error("注册失败");
                }
            }
        } else {
            // 自动生成随机手机号
            String generatedPhone =  "1" + String.format("%010d", (long)(Math.random() * 10000000000L));
            User newUser = createUserObject(generatedPhone, null, "自动注册");
            boolean registrationSuccess = userService.register(newUser);

            if (registrationSuccess) {
                return generateLoginResponse(newUser);
            } else {
                return Result.error("注册失败");
            }
        }
    }
    /**
     * 新增或更新用户订单消息接口
     */
    @PostMapping("/user-orderInfo")
    @NoAuth
    public Result<Map<String, Object>> userOrderInfo(@RequestBody UserOrderInfo userOrderInfo) {
        String phone = userOrderInfo.getPhone();
        // 检查是否传了autoRegister参数以及是否有phone
        if (StringUtils.isNullOrEmpty(phone)) {
            return Result.error("无效的用户消息");
        }
        User userByPhone = userService.getUserByPhone(phone);

        if (userByPhone == null) {
            return Result.error("无效的用户消息");
        }
        if (StringUtils.isNullOrEmpty(userOrderInfo.getCurrenOrder()) && StringUtils.isNullOrEmpty(userOrderInfo.getOrderList())) {
            return Result.success("订单信息为空",null);
        }
        
        // 根据手机号更新订单信息 先查询手机号在订单表中是否存在 如果存在则更新 不存在则添加
        try {
            // 处理可能格式不规范的JSON数据，确保保存的是标准格式
            String currentOrder = processJsonString(userOrderInfo.getCurrenOrder());
            String orderList = processJsonString(userOrderInfo.getOrderList());
            
            UserOrders userOrders = new UserOrders();
            userOrders.setPhone(phone);
            userOrders.setCurrentOrder(currentOrder);
            userOrders.setOrderList(orderList);
            
            boolean result = userOrdersService.saveOrUpdateUserOrders(userOrders);
            
            if (result) {
                Map<String, Object> data = new HashMap<>();
                data.put("phone", phone);
                data.put("orderInfo", userOrders);
                return Result.success("更新订单成功", data);
            } else {
                return Result.error("更新订单失败");
            }
        } catch (Exception e) {
            logger.error("更新用户订单信息失败: {}", e.getMessage(), e);
            return Result.error("服务器内部错误");
        }
    }

    private User createUserObject(String phone, AutoRegister autoRegister, String remark) {
        User user = new User();
        user.setPhone(phone);
        String password = String.format("%06d", (int)(Math.random() * 1000000)) + "fsghsr!";
        user.setPassword(password);
        user.setRemark(remark);
        if (autoRegister != null) {
            user.setNickname(autoRegister.getNickname());
            user.setDatasource(autoRegister.getDatasource());
            user.setAvatar(autoRegister.getAvatar());
            user.setEmail(autoRegister.getEmail());
            user.setGender(autoRegister.getGender());
            user.setAddress(autoRegister.getAddress());
            user.setAge(autoRegister.getAge());
            user.setUserId(autoRegister.getUserId());
            user.setIsTechnician(autoRegister.getIsTechnician());
            user.setIsMerchant(autoRegister.getIsMerchant());
            user.setChannel(autoRegister.getChannel());
        }else {
            user.setNickname("用户" + phone.substring(7));
            user.setDatasource("移动端");
            user.setChannel("H5自动注册");
        }
        return user;
    }

    private Result<Map<String, Object>> generateLoginResponse(User user) {
        // 不返回密码
        user.setPassword(null);
        Map<String, Object> data = new HashMap<>();
        data.put("user", user);

        // 使用JWT工具类生成token，有效期设置为5小时
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", user.getId());
        claims.put("phone", user.getPhone());
        claims.put("role", "user");
        String token = jwtUtil.generateToken(claims, 5 * 60 * 60 * 1000L);
        data.put("token", token);

        return Result.success("登录成功", data);
    }
} 