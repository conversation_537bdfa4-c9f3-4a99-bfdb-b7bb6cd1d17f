package com.kefang.controller;

import com.kefang.entity.Faq;
import com.kefang.entity.FaqRequest;
import com.kefang.service.FaqService;
import com.kefang.vo.Result;
import com.kefang.vo.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/faq")
public class FaqController {
    
    @Autowired
    private FaqService faqService;
    
    /**
     * 添加常见问题
     */
    @PostMapping("/add")
    public Result<Faq> addFaq(@RequestBody Faq faq) {
        log.info("添加常见问题: {}", faq);
        // 参数校验
        if (faq.getQuestion() == null || faq.getAnswer() == null) {
            log.warn("问题和答案不能为空: {}", faq);
            return Result.error(ResultCode.PARAM_ERROR, "问题和答案不能为空");
        }
        
        try {
            boolean success = faqService.addFaq(faq);
            if (success) {
                log.info("添加常见问题成功: {}", faq);
                return Result.success("添加常见问题成功", faq);
            } else {
                log.warn("添加常见问题失败: {}", faq);
                return Result.error(ResultCode.ERROR, "添加常见问题失败");
            }
        } catch (Exception e) {
            log.error("添加常见问题异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.ERROR, "添加常见问题异常: " + e.getMessage());
        }
    }
    
    /**
     * 更新常见问题
     */
    @PutMapping("/{id}")
    public Result<Faq> updateFaq(@PathVariable Long id, @RequestBody Faq faq) {
        log.info("更新常见问题，ID: {}, 内容: {}", id, faq);
        // 设置ID
        faq.setId(id);
        
        try {
            boolean success = faqService.updateFaq(faq);
            if (success) {
                // 查询更新后的问题
                Faq updatedFaq = faqService.getFaqById(id);
                log.info("更新常见问题成功: {}", updatedFaq);
                return Result.success("更新常见问题成功", updatedFaq);
            } else {
                log.warn("更新常见问题失败，ID: {}", id);
                return Result.error(ResultCode.ERROR, "更新常见问题失败");
            }
        } catch (Exception e) {
            log.error("更新常见问题异常，ID: {}, 异常: {}", id, e.getMessage(), e);
            return Result.error(ResultCode.ERROR, "更新常见问题异常: " + e.getMessage());
        }
    }
    
    /**
     * 删除常见问题
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteFaq(@PathVariable Long id) {
        log.info("删除常见问题，ID: {}", id);
        try {
            boolean success = faqService.deleteFaq(id);
            if (success) {
                log.info("删除常见问题成功，ID: {}", id);
                return Result.success("删除常见问题成功", true);
            } else {
                log.warn("删除常见问题失败，ID: {}", id);
                return Result.error(ResultCode.ERROR, "删除常见问题失败");
            }
        } catch (Exception e) {
            log.error("删除常见问题异常，ID: {}, 异常: {}", id, e.getMessage(), e);
            return Result.error(ResultCode.ERROR, "删除常见问题异常: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID查询常见问题
     */
    @GetMapping("/{id}")
    public Result<Faq> getFaqById(@PathVariable Long id) {
        log.info("查询常见问题，ID: {}", id);
        try {
            Faq faq = faqService.getFaqById(id);
            if (faq != null) {
                return Result.success(faq);
            } else {
                log.warn("常见问题不存在，ID: {}", id);
                return Result.error(ResultCode.NOT_FOUND, "常见问题不存在");
            }
        } catch (Exception e) {
            log.error("查询常见问题异常，ID: {}, 异常: {}", id, e.getMessage(), e);
            return Result.error(ResultCode.ERROR, "查询常见问题异常: " + e.getMessage());
        }
    }
    
    /**
     * 根据分类查询常见问题列表
     */
    @GetMapping("/category/{category}")
    public Result<List<Faq>> getFaqsByCategory(@PathVariable String category) {
        log.info("查询分类常见问题，分类: {}", category);
        try {
            List<Faq> faqs = faqService.getFaqsByCategory(category);
            return Result.success(faqs);
        } catch (Exception e) {
            log.error("查询分类常见问题异常，分类: {}, 异常: {}", category, e.getMessage(), e);
            return Result.error(ResultCode.ERROR, "查询常见问题异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取所有常见问题
     */
    @GetMapping("/list")
    public Result<List<Faq>> getAllFaqs() {
        log.info("查询所有常见问题");
        try {
            List<Faq> faqs = faqService.getAllFaqs();
            return Result.success(faqs);
        } catch (Exception e) {
            log.error("查询所有常见问题异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.ERROR, "查询常见问题异常: " + e.getMessage());
        }
    }
    /***
     * 常见问题接口
     */
    @PostMapping("/faqShow")
    public Result<?> getFaq(@Valid @RequestBody FaqRequest request) {
        try {
            log.debug("请求常见问题，场景: {}, 数据源: {}", request.getScene(), request.getDatasource());
            return Result.success("查询成功", faqService.getFaqBySceneAndDatasource(request.getScene(), request.getDatasource()));
        } catch (Exception e) {
            log.error("常见问题查询异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.ERROR, "服务异常");
        }
    }
} 