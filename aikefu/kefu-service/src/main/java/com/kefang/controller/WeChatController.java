package com.kefang.controller;

import com.alibaba.fastjson.JSONObject;
import com.kefang.entity.ConnectEntiy;
import com.kefang.service.ConnectService;
import com.kefang.utils.WXBizMsgCrypt;
import com.mysql.cj.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.servlet.http.HttpServletRequest;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayInputStream;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("connect/wechat")
public class WeChatController {

    @Value("${wechat.token}")
    private String token;

    @Value("${wechat.appid}")
    private String wechatAppId;

    @Value("${wechat.encodingaeskey}")
    private String wechatEncodingAesKey;

    @Autowired
    private ConnectService connectService;
    
    @Autowired
    private RestTemplate restTemplate;
    
    // 用于消息去重的缓存，key为 userId:msg，value为处理时间
    private final Map<String, Long> processedMessages = new ConcurrentHashMap<>();
    
    // 消息缓存过期时间(毫秒)，10分钟
    private static final long MESSAGE_CACHE_EXPIRY = 10 * 60 * 1000;
    
    // 相同消息的处理间隔时间(毫秒)，5秒内的相同消息被视为重复
    private static final long MESSAGE_PROCESS_INTERVAL = 5 * 1000;

    // 缓存容量上限，防止无限增长
    private static final int MAX_CACHE_SIZE = 10000;

    @GetMapping("/callback")
    public String handleVerification(@RequestParam String signature,
                                     @RequestParam String timestamp,
                                     @RequestParam String nonce,
                                     @RequestParam String echostr) {
        try {
            String[] arr = {token, timestamp, nonce};
            Arrays.sort(arr);
            String joinedStr = String.join("", arr);

            MessageDigest md = MessageDigest.getInstance("SHA-1");
            byte[] digest = md.digest(joinedStr.getBytes(StandardCharsets.UTF_8));
            String calculatedSignature = bytesToHex(digest);

            if (calculatedSignature.equalsIgnoreCase(signature)) {
                log.info("验证成功: echostr={}", echostr);
                return echostr;
            } else {
                log.error("验证失败: 签名不匹配");
                return "Invalid signature";
            }
        } catch (Exception e) {
            log.error("验证过程出错", e);
            return "Error";
        }
    }

    @PostMapping("/callback")
    public String handleMessage(HttpServletRequest request) {
        try {
            // 读取请求体
            String requestData = request.getReader().lines().collect(Collectors.joining());
            log.debug("接收到微信回调的数据");

            // 解析JSON数据
            String encrypted = null;
            if (requestData.startsWith("{")) {
                // 处理JSON格式
                try {
                    // 尝试简单地提取encrypted字段值
                    int startIndex = requestData.indexOf("\"encrypted\":\"");
                    if (startIndex >= 0) {
                        startIndex += 13; // "encrypted":"的长度
                        int endIndex = requestData.indexOf("\"", startIndex);
                        if (endIndex > startIndex) {
                            encrypted = requestData.substring(startIndex, endIndex);
                        }
                    } else {
                        log.error("找不到encrypted字段");
                    }
                } catch (Exception e) {
                    log.error("解析JSON时出错", e);
                }
            } else if (requestData.contains("<Encrypt>")) {
                // 处理XML格式（兼容旧格式）
                DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
                DocumentBuilder builder = factory.newDocumentBuilder();
                Document doc = builder.parse(new ByteArrayInputStream(requestData.getBytes(StandardCharsets.UTF_8)));
                Element root = doc.getDocumentElement();
                encrypted = root.getElementsByTagName("Encrypt").item(0).getTextContent();
            }

            if (encrypted == null || encrypted.isEmpty()) {
                log.error("无法解析加密数据");
                return "success"; // 直接返回success，避免微信服务器重试
            }

            // 解密消息
            WXBizMsgCrypt wxBizMsgCrypt = new WXBizMsgCrypt(token, wechatEncodingAesKey, wechatAppId);
            
            try {
                String decryptedXml = wxBizMsgCrypt.decrypt(encrypted);


                // 尝试处理解密后的数据，如果是XML格式
                if (decryptedXml != null && (decryptedXml.startsWith("<xml") || decryptedXml.contains("<xml>"))) {
                    // 解析解密后的XML
                    DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
                    DocumentBuilder builder = factory.newDocumentBuilder();
                    Document doc = builder.parse(new ByteArrayInputStream(decryptedXml.getBytes(StandardCharsets.UTF_8)));

                    // 提取消息内容
                    String userId = doc.getElementsByTagName("userid").item(0).getTextContent();
                    String appId = doc.getElementsByTagName("appid").item(0).getTextContent();
                    Element contentElement = (Element) doc.getElementsByTagName("content").item(0);
                    String msg = contentElement.getElementsByTagName("msg").item(0).getTextContent();
                    String fromStr = doc.getElementsByTagName("from").item(0).getTextContent();
                    int from = Integer.parseInt(fromStr);
                    String channelStr = doc.getElementsByTagName("channel").item(0).getTextContent();
                    int channel = Integer.parseInt(channelStr);
                    
                    // 使用userId和消息内容作为唯一标识符，不再使用时间戳
                    String messageId = userId + ":" + msg;

                    
                    // 检查是否是重复消息
                    if (isDuplicateMessage(messageId)) {
                        log.info("检测到重复消息，已忽略处理: userId={}, msg={}, messageId={}", userId, msg, messageId);
                        return "success";
                    }
                    log.debug("解密后的数据: {}", decryptedXml);
                    
                    log.info("用户 {} 发送了消息: {}", userId, msg);
                    if(StringUtils.isNullOrEmpty(msg)){
                        log.info("用户 {} 发送了空消息", userId);
                        return "success";
                    }

                    // 只处理来自用户的消息
                    if (from == 0) {
                        // 准备发送到本地服务的数据
                        ConnectEntiy connectEntiy = new ConnectEntiy();
                        connectEntiy.setMessages(msg);
                        connectEntiy.setSession_id(userId);
                        connectEntiy.setChannel("微信公众号");
                        connectEntiy.setDatasource("微信");
                        // 把appId、channelWX、openId 转成JSON字符串，并设置到 weixinDate 字段中
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("appId", appId);
                        jsonObject.put("channelWX", channelStr);
                        jsonObject.put("openId", userId);
                        connectEntiy.setWeixinDate(jsonObject.toString());
                        connectEntiy.setCollection_name("information");

                        if ("wx0469e85c38e81fde".equals(appId)) {// appId 熊洞智家（公众号） wx0469e85c38e81fde
                            connectEntiy.setScene("熊洞智家");
                        } else if ("wxafe8168141ed6353".equals(appId)) { // 熊洞服务（公众号） wxafe8168141ed6353
                            connectEntiy.setScene("熊洞服务");
                        }else {
                            connectEntiy.setScene("微信-其他");
                        }
                        // 发送请求到本地服务获取回复内容
                        String replyContent = connectService.onLine(connectEntiy);
                        if (!StringUtils.isNullOrEmpty(replyContent)){
                            // 使用客服消息接口发送回复
                            sendCustomerServiceMessage(userId, replyContent, channel, appId);
                        }
                        // 由于我们直接使用客服消息接口发送了回复，这里返回success
                        return "success";
                    } else if (from == 2) {
                        // 客服在微信对话开放平台上进行回复时的内容
                        log.info("收到客服回复消息: {}", msg);
                        return "success";
                    } else {
                        log.info("收到非用户消息，from={}", from);
                        return "success";
                    }
                } else {
                    log.error("解密后的数据不是有效的XML: {}", decryptedXml);
                    return "success";
                }
            } catch (Exception e) {
                log.error("解密或处理消息失败: {}", e.getMessage(), e);
                
                // 尝试直接返回一个成功应答，让微信不再重试
                return "success";
            }
        } catch (Exception e) {
            log.error("处理消息时出错", e);
            return "success";
        }
    }
    
    /**
     * 检查消息是否已处理过（去重处理）
     * @param messageId 消息ID (userId:msg)
     * @return 如果消息已处理过且在指定时间间隔内返回true
     */
    private boolean isDuplicateMessage(String messageId) {
        long now = System.currentTimeMillis();
        
        // 如果缓存已达到上限，进行清理
        if (processedMessages.size() >= MAX_CACHE_SIZE) {
            log.warn("消息缓存达到上限({}), 执行清理", MAX_CACHE_SIZE);
            cleanupExpiredMessages(now);
            
            // 如果清理后仍然超过容量上限的80%，则清理最早的20%消息
            if (processedMessages.size() > MAX_CACHE_SIZE * 0.8) {
                removeOldestEntries((int)(MAX_CACHE_SIZE * 0.2));
            }
        }
        
        // 检查是否在短时间内处理过相同的消息
        Long lastProcessTime = processedMessages.get(messageId);
        if (lastProcessTime != null && now - lastProcessTime < MESSAGE_PROCESS_INTERVAL) {
            return true;
        }
        
        // 标记为已处理
        processedMessages.put(messageId, now);
        return false;
    }
    
    /**
     * 清理过期的消息缓存
     * @param currentTime 当前时间戳
     */
    private synchronized void cleanupExpiredMessages(long currentTime) {
        int sizeBefore = processedMessages.size();
        processedMessages.entrySet().removeIf(
            entry -> currentTime - entry.getValue() > MESSAGE_CACHE_EXPIRY
        );
        int sizeAfter = processedMessages.size();
        
        if (sizeBefore > sizeAfter) {
            log.info("已清理{}个过期消息缓存项，当前缓存大小: {}", sizeBefore - sizeAfter, sizeAfter);
        }
    }
    
    /**
     * 当缓存接近容量上限时，移除最早的消息记录
     * @param count 要移除的数量
     */
    private synchronized void removeOldestEntries(int count) {
        if (count <= 0 || processedMessages.isEmpty()) {
            return;
        }
        
        // 找出最早的消息记录
        processedMessages.entrySet().stream()
            .sorted(Map.Entry.comparingByValue())  // 按处理时间排序
            .limit(count)  // 取出最早的n条
            .map(Map.Entry::getKey)  // 获取key
            .collect(java.util.stream.Collectors.toList())  // 转为列表
            .forEach(processedMessages::remove);  // 移除这些记录
        
        log.info("已强制清理{}个最早的消息缓存项，当前缓存大小: {}", count, processedMessages.size());
    }
    
    /**
     * 发送客服消息给用户
     * @param openId 用户的openid
     * @param message 消息内容
     * @param channel 渠道ID
     * @param appId 公众号或小程序的appid
     */
    private void sendCustomerServiceMessage2(String openId, String message, int channel, String appId) {
        try {
            // 构建XML消息体
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.newDocument();
            
            Element rootElement = doc.createElement("xml");
            doc.appendChild(rootElement);
            
            // 添加appid
            Element appidElement = doc.createElement("appid");
            appidElement.appendChild(doc.createCDATASection(appId));
            rootElement.appendChild(appidElement);
            
            // 添加openid
            Element openidElement = doc.createElement("openid");
            openidElement.appendChild(doc.createCDATASection(openId));
            rootElement.appendChild(openidElement);
            
            // 添加消息内容
            Element msgElement = doc.createElement("msg");
            msgElement.appendChild(doc.createCDATASection(message));
            rootElement.appendChild(msgElement);
            
            // 添加渠道
            Element channelElement = doc.createElement("channel");
            channelElement.appendChild(doc.createTextNode(String.valueOf(channel)));
            rootElement.appendChild(channelElement);
            
            // 转换XML为字符串
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(doc), new StreamResult(writer));
            String xmlData = writer.toString();
            
            log.info("发送客服消息XML: {}", xmlData);
            
            // 加密消息
            WXBizMsgCrypt wxBizMsgCrypt = new WXBizMsgCrypt(token, wechatEncodingAesKey, wechatAppId);
            String encryptedData = wxBizMsgCrypt.encrypt(xmlData, wechatAppId);
            
            // 构建请求体
            String requestBody = "{\"encrypt\":\"" + encryptedData + "\"}";
            
            // 发送请求
            String url = "https://chatbot.weixin.qq.com/openapi/sendmsg/" + token;
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<String> request = new HttpEntity<>(requestBody, headers);
            String response = restTemplate.postForObject(url, request, String.class);
            
            log.info("微信客服消息发送结果: {}", response);
        } catch (Exception e) {
            log.error("发送客服消息失败", e);
        }
    }

    /**
     * 发送客服消息给用户   后期优化可以在系统直接给用户发送消息
     * @param openId 用户的微信openid
     * @param message
     * @param channel 渠道ID
     * @param appId 公众号或小程序的appid
     * @return
     */
    @PostMapping("/sendCustomerServiceMessage")
    public String sendCustomerServiceMessage(@RequestParam String openId, @RequestParam String message, @RequestParam int channel, @RequestParam String appId) {
        try {
            // 构建XML消息体
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.newDocument();
            
            Element rootElement = doc.createElement("xml");
            doc.appendChild(rootElement);
            
            // 添加appid
            Element appidElement = doc.createElement("appid");
            appidElement.appendChild(doc.createCDATASection(appId));
            rootElement.appendChild(appidElement);
            
            // 添加openid
            Element openidElement = doc.createElement("openid");
            openidElement.appendChild(doc.createCDATASection(openId));
            rootElement.appendChild(openidElement);
            
            // 添加消息内容
            Element msgElement = doc.createElement("msg");
            msgElement.appendChild(doc.createCDATASection(message));
            rootElement.appendChild(msgElement);
            
            // 添加渠道
            Element channelElement = doc.createElement("channel");
            channelElement.appendChild(doc.createTextNode(String.valueOf(channel)));
            rootElement.appendChild(channelElement);
            
            // 转换XML为字符串
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(doc), new StreamResult(writer));
            String xmlData = writer.toString();
            
            log.info("发送客服消息XML: {}", xmlData);
            
            // 加密消息
            WXBizMsgCrypt wxBizMsgCrypt = new WXBizMsgCrypt(token, wechatEncodingAesKey, wechatAppId);
            String encryptedData = wxBizMsgCrypt.encrypt(xmlData, wechatAppId);
            
            // 构建请求体
            String requestBody = "{\"encrypt\":\"" + encryptedData + "\"}";
            
            // 发送请求
            String url = "https://chatbot.weixin.qq.com/openapi/sendmsg/" + token;
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<String> request = new HttpEntity<>(requestBody, headers);
            String response = restTemplate.postForObject(url, request, String.class);
            
            log.info("微信客服消息发送结果: {}", response);
            return "success";
        } catch (Exception e) {
            log.error("发送客服消息失败", e);
            return "error";
        }
    }

    private String bytesToHex(byte[] bytes) {
        StringBuilder builder = new StringBuilder();
        for (byte b : bytes) {
            builder.append(String.format("%02x", b));
        }
        return builder.toString();
    }
}