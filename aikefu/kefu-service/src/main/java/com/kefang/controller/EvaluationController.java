package com.kefang.controller;

import com.kefang.entity.Evaluation;
import com.kefang.service.EvaluationService;
import com.kefang.vo.Result;
import com.kefang.vo.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/evaluation")
public class EvaluationController {
    
    @Autowired
    private EvaluationService evaluationService;
    
    /**
     * 添加评价
     */
    @PostMapping("/add")
    public Result<Evaluation> addEvaluation(@RequestBody Evaluation evaluation) {
        log.info("添加评价请求: {}", evaluation);
        // 参数校验
        if (evaluation.getSessionId() == null || evaluation.getUserId() == null || evaluation.getScore() == null) {
            log.warn("评价参数不完整: {}", evaluation);
            return Result.error(ResultCode.PARAM_ERROR, "会话ID、用户ID和评分不能为空");
        }
        
        try {
            boolean success = evaluationService.addEvaluation(evaluation);
            if (success) {
                // 查询刚添加的评价
                Evaluation newEvaluation = evaluationService.getEvaluationBySessionId(evaluation.getSessionId());
                log.info("评价添加成功: {}", newEvaluation);
                return Result.success("评价成功", newEvaluation);
            } else {
                log.warn("评价添加失败: {}", evaluation);
                return Result.error(ResultCode.ERROR, "评价失败");
            }
        } catch (Exception e) {
            log.error("评价添加异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.ERROR, "评价异常");
        }
    }
    
    /**
     * 根据ID查询评价
     */
    @GetMapping("/{id}")
    public Result<Evaluation> getEvaluationById(@PathVariable Long id) {
        log.info("查询评价，ID: {}", id);
        try {
            Evaluation evaluation = evaluationService.getEvaluationById(id);
            if (evaluation != null) {
                return Result.success(evaluation);
            } else {
                return Result.error(ResultCode.NOT_FOUND, "评价不存在");
            }
        } catch (Exception e) {
            log.error("查询评价异常，ID: {}, 异常: {}", id, e.getMessage(), e);
            return Result.error(ResultCode.ERROR, "查询异常");
        }
    }
    
    /**
     * 根据会话ID查询评价
     */
    @GetMapping("/session/{sessionId}")
    public Result<Evaluation> getEvaluationBySessionId(@PathVariable Long sessionId) {
        log.info("查询会话评价，会话ID: {}", sessionId);
        try {
            Evaluation evaluation = evaluationService.getEvaluationBySessionId(sessionId);
            if (evaluation != null) {
                return Result.success(evaluation);
            } else {
                return Result.error(ResultCode.NOT_FOUND, "该会话暂无评价");
            }
        } catch (Exception e) {
            log.error("查询会话评价异常，会话ID: {}, 异常: {}", sessionId, e.getMessage(), e);
            return Result.error(ResultCode.ERROR, "查询异常");
        }
    }
    
    /**
     * 根据用户ID查询评价列表
     */
    @GetMapping("/user/{userId}")
    public Result<List<Evaluation>> getEvaluationsByUserId(@PathVariable Long userId) {
        log.info("查询用户评价列表，用户ID: {}", userId);
        try {
            List<Evaluation> evaluations = evaluationService.getEvaluationsByUserId(userId);
            return Result.success(evaluations);
        } catch (Exception e) {
            log.error("查询用户评价列表异常，用户ID: {}, 异常: {}", userId, e.getMessage(), e);
            return Result.error(ResultCode.ERROR, "查询异常");
        }
    }
    
    /**
     * 获取所有评价（仅管理员使用）
     */
    @GetMapping("/list")
    public Result<List<Evaluation>> getAllEvaluations() {
        log.info("查询所有评价");
        try {
            List<Evaluation> evaluations = evaluationService.getAllEvaluations();
            return Result.success(evaluations);
        } catch (Exception e) {
            log.error("查询所有评价异常: {}", e.getMessage(), e);
            return Result.error(ResultCode.ERROR, "查询异常");
        }
    }
} 