package com.kefang.controller;

import com.kefang.entity.ChatSession;
import com.kefang.interceptor.NoAuth;
import com.kefang.service.ChatSessionService;
import com.kefang.vo.PageResult;
import com.kefang.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/session")
public class ChatSessionController {
    
    @Autowired
    private ChatSessionService chatSessionService;
    
    /**
     * 创建会话
     * @param userId 用户ID
     * @param assignHuman 是否分配人工客服（默认false，使用AI客服）
     * @param channel 数据源的渠道来源 如 ios Android 微信 支付宝 H5 等
     */
    @PostMapping("/create")
    public Result<ChatSession> createSession(@RequestParam Long userId,
                                             @RequestParam(defaultValue = "false") boolean assignHuman,
                                             @RequestParam(required = false) String channel,
                                             @RequestParam(required = false) String datasource,
                                             @RequestParam(required = false) String collection_name,
                                             @RequestParam(required = false) String scene) {
        ChatSession chatSession = chatSessionService.createSession(userId, channel,datasource,collection_name,scene);
        if (chatSession != null) {
            // 根据参数决定是否分配人工客服
            if (assignHuman) {
                boolean assigned = chatSessionService.assignAgent(chatSession.getId());
                if (assigned) {
                    // 重新获取会话信息，此时已包含了客服信息
                    chatSession = chatSessionService.getSessionById(chatSession.getId());
                    return Result.success("会话创建成功并已分配人工客服", chatSession);
                }
                return Result.success("会话创建成功，等待人工客服分配", chatSession);
            } else {
                return Result.success("会话创建成功，AI客服为您服务", chatSession);
            }
        } else {
            return Result.error("会话创建失败");
        }
    }
    
    /**
     * 系统自动分配客服
     */
    @PutMapping("/{id}/assign")
    public Result<Boolean> assignAgent(@PathVariable Long id) {
        boolean success = chatSessionService.assignAgent(id);
        if (success) {
            return Result.success("分配客服成功", true);
        } else {
            return Result.error("分配客服失败，可能没有在线客服");
        }
    }
    
    /**
     * 手动分配客服
     * @param id 会话ID
     * agentId 客服ID
     */
    @PutMapping("/{id}/assign/{agentId}")
    public Result<Boolean> assignAgentManually(@PathVariable Long id, @PathVariable Long agentId) {
        boolean success = chatSessionService.assignAgentManually(id, agentId);
        if (success) {
            return Result.success("手动分配客服成功", true);
        } else {
            return Result.error("手动分配客服失败，客服可能不在线");
        }
    }
    
    /**
     * 客服接入会话
     */
    @PutMapping("/{id}/accept")
    public Result<Boolean> acceptSession(@PathVariable Long id, @RequestParam Long agentId) {
        boolean success = chatSessionService.acceptSession(id, agentId);
        if (success) {
            return Result.success("会话接入成功", true);
        } else {
            return Result.error("会话接入失败，该会话可能已被其他客服接入或状态不允许接入");
        }
    }
    
    /**
     * 关闭会话
     */
    @PutMapping("/{id}/close")
    public Result<Boolean> closeSession(@PathVariable Long id, @RequestParam Integer closedBy) {
        boolean success = chatSessionService.closeSession(id, closedBy);
        if (success) {
            return Result.success("会话关闭成功", true);
        } else {
            return Result.error("会话关闭失败");
        }
    }
    
    /**
     * 获取会话详情
     */
    @GetMapping("/{id}")
    public Result<ChatSession> getSessionById(@PathVariable Long id) {
        ChatSession chatSession = chatSessionService.getSessionById(id);
        if (chatSession != null) {
            return Result.success(chatSession);
        } else {
            return Result.error("会话不存在");
        }
    }
    
    /**
     * 获取用户的会话列表
     */
    @GetMapping("/user/{userId}")
    public Result<List<ChatSession>> getSessionsByUserId(@PathVariable Long userId) {
        List<ChatSession> sessions = chatSessionService.getSessionsByUserId(userId);
        return Result.success(sessions);
    }
    
    /**
     * 获取客服的会话列表（分类返回待处理、进行中、AI回复中各10条数据）
     */
    @GetMapping("/agent/list/{agentId}")
    public Result<Map<String, Object>> getSessionsByAgentIdList(
            @PathVariable Long agentId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) Integer agentType,
            @RequestParam(required = false) Integer isSolved,
            @RequestParam(required = false) Integer satisfactionLevel,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "30") Integer size) {
        
        // 定义返回结果Map
        Map<String, Object> resultMap = new HashMap<>();
        
        // 待处理状态(状态码为0)的会话列表
        PageResult<ChatSession> pendingSessions = chatSessionService.getSessionsByAgentIdWithFilters(
                "待处理",agentId, startDate, endDate, agentType, 0, isSolved, satisfactionLevel,
                null, null, null, page, size);
        resultMap.put("pendingSessions", pendingSessions);
        
        // 进行中状态(状态码为1)的会话列表
        PageResult<ChatSession> inProgressSessions = chatSessionService.getSessionsByAgentIdWithFilters(
                "进行中",agentId, startDate, endDate, agentType, 1, isSolved, satisfactionLevel,
                null, null, null, page, size);
        resultMap.put("inProgressSessions", inProgressSessions);
        
        // AI回复中状态(状态码为3)的会话列表
        PageResult<ChatSession> aiReplySessions = chatSessionService.getSessionsByAgentIdWithFilters(
                "AI回复中",agentId, startDate, endDate, agentType, 3, isSolved, satisfactionLevel,
                null, null, null, page, size);
        resultMap.put("aiReplySessions", aiReplySessions);
        
        return Result.success(resultMap);
    }

    /**
     * 历史会话查询
     */
    @GetMapping("/agent/{agentId}")
    public Result<PageResult<ChatSession>> getSessionsByAgentId(
            @PathVariable Long agentId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) Integer agentType,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Integer isSolved,
            @RequestParam(required = false) Integer satisfactionLevel,
            @RequestParam(required = false) String datasource,
            @RequestParam(required = false) String collectionName,
            @RequestParam(required = false) String scene,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {

        // 调用服务层方法，传入所有过滤参数
        PageResult<ChatSession> result = chatSessionService.getSessionsByAgentIdWithFilters(
                "历史会话",agentId, startDate, endDate, agentType, status, isSolved, satisfactionLevel,
                datasource, collectionName, scene, page, size);

        return Result.success(result);
    }
    
    /**
     * 获取客服的活跃会话
     */
    @GetMapping("/agent/{agentId}/active")
    public Result<Map<String, Object>> getActiveSessionsByAgentId(
            @PathVariable Long agentId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        // 先调用原有方法获取数据
        List<ChatSession> sessions = chatSessionService.getActiveSessionsByAgentId(agentId);
        
        // 简单分页处理
        int total = sessions.size();
        int fromIndex = (page - 1) * size;
        int toIndex = Math.min(fromIndex + size, total);
        
        List<ChatSession> pageData = fromIndex < total ? 
                sessions.subList(fromIndex, toIndex) : 
                new ArrayList<>();
        
        // 构建分页结果返回
        Map<String, Object> result = new HashMap<>();
        result.put("records", pageData);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        
        return Result.success(result);
    }
    
    /**
     * 获取排队中的会话
     */
    @GetMapping("/queue")
    public Result<List<ChatSession>> getQueueSessions() {
        List<ChatSession> sessions = chatSessionService.getQueueSessions();
        return Result.success(sessions);
    }
    
    /**
     * 创建与AI客服的会话
     */
    @PostMapping("/create-ai")
    public Result<ChatSession> createAISession(@RequestParam Long userId, @RequestParam(required = false) String channel,
                                               @RequestParam(required = false) String datasource,
                                               @RequestParam(required = false) String collection_name,
                                               @RequestParam(required = false) String scene) {
        try {
            ChatSession chatSession = chatSessionService.createSessionWithAI(userId, channel, datasource, collection_name, scene);
            if (chatSession != null) {
                return Result.success("AI客服会话创建成功", chatSession);
            } else {
                return Result.error("AI客服会话创建失败");
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 转接到人工客服
     */
    @PutMapping("/{id}/transfer-to-human")
    public Result<Boolean> transferToHuman(@PathVariable Long id) {
        boolean success = chatSessionService.transferToHumanAgent(id);
        if (success) {
            return Result.success("已成功转接到人工客服", true);
        } else {
            return Result.error("目前人工客服暂时离线，我们的服务时间为：周一至周日 8:00-18:00，请您在服务时间内咨询人工客服，我们将尽快为您处理。");
        }
    }
    
    /**
     * 用户添加会话评价
     */
    @PostMapping("/{id}/evaluate")
    public Result<Boolean> addEvaluation(
            @PathVariable Long id,
            @RequestParam Integer satisfactionLevel,
            @RequestParam(required = false) String feedbackContent,
            @RequestParam(defaultValue = "0") Integer isSolved,
            @RequestParam(required = false) String userSuggestion) {
        
        boolean success = chatSessionService.addEvaluation(id, satisfactionLevel, feedbackContent, isSolved, userSuggestion);
        if (success) {
            return Result.success("评价提交成功", true);
        } else {
            return Result.error("评价提交失败");
        }
    }
    
    /**
     * 客服添加解决方案描述
     */
    @PostMapping("/{id}/solution")
    public Result<Boolean> addSolutionDescription(
            @PathVariable Long id,
            @RequestParam String solutionDescription,
            @RequestParam(defaultValue = "1") Integer isSolved) {
        
        boolean success = chatSessionService.addSolutionDescription(id, solutionDescription, isSolved);
        if (success) {
            return Result.success("解决方案添加成功", true);
        } else {
            return Result.error("解决方案添加失败");
        }
    }
    
    /**
     * 获取已解决的会话列表
     */
    @GetMapping("/solved")
    public Result<List<ChatSession>> getSolvedSessions() {
        List<ChatSession> sessions = chatSessionService.getSolvedSessions();
        return Result.success(sessions);
    }
    
    /**
     * 按满意度等级获取会话列表
     */
    @GetMapping("/satisfaction/{level}")
    public Result<List<ChatSession>> getSessionsBySatisfactionLevel(@PathVariable Integer level) {
        if (level < 1 || level > 5) {
            return Result.error("满意度等级必须在1-5之间");
        }
        
        List<ChatSession> sessions = chatSessionService.getSessionsBySatisfactionLevel(level);
        return Result.success(sessions);
    }
    
    /**
     * 获取不同状态的会话列表
     */
    @GetMapping("/list/{status}")
    public Result<List<ChatSession>> getSessionsByStatus(
            @PathVariable Integer status,
            @RequestParam(required = false) Long agentId) {
        Map<String, Object> params = new HashMap<>();
        params.put("status", status);
        if (agentId != null) {
            params.put("agentId", agentId);
        }
        List<ChatSession> sessions = chatSessionService.getSessionsByFilters(params);
        return Result.success(sessions);
    }
    
    /**
     * 获取消息统计数据（按发送者类型和数据来源分组）
     */
    @GetMapping("/message-stats")
    public Result<Map<String, Object>> getMessageStats(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        Map<String, Object> stats = chatSessionService.getMessageTypeAndSourceStats(startDate, endDate);
        return Result.success(stats);
    }
    
    /**
     * 获取数据源下拉选项
     */
    @GetMapping("/options/datasource")
    public Result<List<Map<String, Object>>> getDatasourceOptions() {
        List<Map<String, Object>> options = chatSessionService.getDatasourceOptions();
        return Result.success(options);
    }
    
    /**
     * 获取知识库集合下拉选项
     */
    @GetMapping("/options/collection")
    public Result<List<Map<String, Object>>> getCollectionOptions() {
        List<Map<String, Object>> options = chatSessionService.getCollectionOptions();
        return Result.success(options);
    }
    
    /**
     * 获取对话场景下拉选项
     */
    @GetMapping("/options/scene")
    public Result<List<Map<String, Object>>> getSceneOptions() {
        List<Map<String, Object>> options = chatSessionService.getSceneOptions();
        return Result.success(options);
    }
    
    /**
     * 获取渠道来源下拉选项
     */
    @GetMapping("/options/channel")
    public Result<List<Map<String, Object>>> getChannelOptions() {
        List<Map<String, Object>> options = chatSessionService.getChannelOptions();
        return Result.success(options);
    }
} 