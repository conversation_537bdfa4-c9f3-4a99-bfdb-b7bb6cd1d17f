package com.kefang.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kefang.api.ExecuteSqlQueryApi;
import com.kefang.api.SendMessageApi;
import com.kefang.api.SourceStatisticsApi;
import com.kefang.vo.Result;
import com.kefang.vo.SqlQueryResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 微信群来源统计定时任务
 * 每天上午9:15自动执行，获取微信群来源统计数据
 */
@Component
public class SourceWxGroupStatisticsTask {

    private static final Logger logger = LoggerFactory.getLogger(SourceWxGroupStatisticsTask.class);

    @Autowired
    private SendMessageApi sendMessageApi;

    @Autowired
    private ExecuteSqlQueryApi queryApi;

    @Autowired
    private SourceStatisticsApi sourceStatisticsApi;

    @Value("${server.port}")
    private String port;


    /**
     * 需求
     * 回收商数据 - 订单来源是小智，小智订单交付回收商回收，处置方式是回收商回收（recover_order_item表deal_type = 8 ）
     * 昨日派单量：指订单分配给回收商的时间是昨日的所有单量（按派单时间统计，派单时间（recover_order_log表中的分配服务商时间）从订单操作记录中获取）
     * 昨日取消单量：指当前回收商订单，取消时间是昨日的所有单量（按取消时间统计finish_time）
     * 昨日完成单量：指当前回收商订单，完成时间是昨日的所有单量（按完成时间统计finish_time）
     * 累计待完成单量：指当前回收商订单，所有未完成的总单量（不限制时间范围，统计订单状态为【回收处理中】的数据）
     *
     * 时代鸟 4390
     * 飞蚂蚁 4442
     * 金典   4480
     * 速回收 4423
     *
     *   昨日派单量 recover_order_log 表中的分配服务商时间
     *   15 回收处理中
     *   70 回收取消
     *   100 回收完成
     *
     */
    @Scheduled(cron = "0 10 9 * * ?")
    public void recyclerDataWxGroup() {
        if(!Objects.equals(port, "8819")){
            logger.info("当前端口不是8819，不执行定时回收商数据统计任务");
            return;
        }

        logger.info("开始执行回收商数据统计任务");
        recyclerDataWxGroupStatistics();

        logger.info("回收商数据统计任务执行完毕");
    }



    public String recyclerDataWxGroupStatistics() {

        // 构建SQL查询语句
        String sql = "SELECT " +
                "    roi.merchant_id, " +
                "    COUNT(DISTINCT CASE " +
                "        WHEN rol.type = '分配服务商' " +
                "             AND rol.create_time >= CURDATE() - INTERVAL 1 DAY " +
                "             AND rol.create_time < CURDATE() " +
                "        THEN rol.order_id " +
                "    END) AS dispatch_yesterday, " +
                "    COUNT(DISTINCT CASE " +
                "        WHEN ro.status = 70 " +
                "             AND ro.finish_time >= CURDATE() - INTERVAL 1 DAY " +
                "             AND ro.finish_time < CURDATE() " +
                "        THEN ro.id " +
                "    END) AS cancel_yesterday, " +
                "    COUNT(DISTINCT CASE " +
                "        WHEN ro.status = 100 " +
                "             AND ro.finish_time >= CURDATE() - INTERVAL 1 DAY " +
                "             AND ro.finish_time < CURDATE() " +
                "        THEN ro.id " +
                "    END) AS finish_yesterday, " +
                "    COUNT(DISTINCT CASE " +
                "        WHEN ro.status = 15 " +
                "        THEN ro.id " +
                "    END) AS pending_count " +
                "FROM " +
                "    recover_order_item roi " +
                "LEFT JOIN " +
                "    recover_order ro ON roi.order_id = ro.id " +
                "LEFT JOIN " +
                "    recover_order_log rol ON roi.order_id = rol.order_id " +
                "WHERE " +
                "    roi.deal_type = 8 " +
                "    AND roi.merchant_id IN (4390, 4442, 4480, 4423) " +
                "GROUP BY " +
                "    roi.merchant_id";

        SqlQueryResult sqlQueryResult = queryApi.executeSqlQueryXD(sql);
        if (!sqlQueryResult.isSuccess()){
            logger.error("执行SQL查询失败: {}", sqlQueryResult.getMessage());
            return "";
        }

        // 创建商家名称映射
        Map<String, String> merchantNames = new HashMap<>();
        merchantNames.put("4390", "时代鸟");
        merchantNames.put("4442", "飞蚂蚁");
        merchantNames.put("4480", "金典");
        merchantNames.put("4423", "速回收");

        // 处理查询结果并格式化输出
        List<Map<String, Object>> data = sqlQueryResult.getData();
        logger.info("查询到 {} 条回收商数据", data.size());

        if (data.isEmpty()) {
            logger.warn("未查询到任何回收商数据");
            return "";
        }

        // 按商家分别打印统计信息
        StringBuilder reportBuilder = new StringBuilder();
        reportBuilder.append("\n=== 回收商数据统计报告 ===\n");
        reportBuilder.append("查询时间: ").append(java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        reportBuilder.append("统计范围: 昨日数据及累计待完工单\n\n");

        for (Map<String, Object> record : data) {
            String merchantId = String.valueOf(record.get("merchant_id"));
            String merchantName = merchantNames.getOrDefault(merchantId, "未知商家(" + merchantId + ")");

            // 获取统计数据，处理可能的null值
            Object dispatchObj = record.get("dispatch_yesterday");
            Object cancelObj = record.get("cancel_yesterday");
            Object finishObj = record.get("finish_yesterday");
            Object pendingObj = record.get("pending_count");

            int dispatchCount = dispatchObj != null ? ((Number) dispatchObj).intValue() : 0;
            int cancelCount = cancelObj != null ? ((Number) cancelObj).intValue() : 0;
            int finishCount = finishObj != null ? ((Number) finishObj).intValue() : 0;
            int pendingCount = pendingObj != null ? ((Number) pendingObj).intValue() : 0;

            // 为当前商家单独创建消息内容
            StringBuilder singleMerchantReport = new StringBuilder();
            singleMerchantReport.append("  【"+merchantName+"数据日报】\n");
            singleMerchantReport.append("——————————————\n");
            singleMerchantReport.append(" 📦 - 昨日派单订单：").append(dispatchCount).append("\n \n");
            singleMerchantReport.append(" ❌ -  昨日取消订单：").append(cancelCount).append("\n \n");
            singleMerchantReport.append(" ✅ -  昨日完工订单：").append(finishCount).append("\n \n");
            singleMerchantReport.append(" ⏳ -  累计待完工单：").append(pendingCount).append("\n ");
            singleMerchantReport.append("——————————————");

            // 发送当前商家的单独消息
            String wxgroup = merchantId;
            logger.info("准备发送商家 {} ({}) 的数据", merchantName, merchantId);

            JSONObject message = new JSONObject();
            message.put("group_type", "9");
            if(!Objects.equals(port, "8819")){
                logger.info("当前端口不是8819，发送到测试群 4344");
                wxgroup = "4344";
            }
            message.put("group_type_id", wxgroup); // 发送到测试群 4344
            message.put("content", singleMerchantReport.toString()); // 只发送当前商家的数据

            Result<JSONObject> result = sendMessageApi.pushMessage(message);
            logger.info("商户 {} ({}) 推送结果: {}", merchantName, merchantId, result);

            // 将当前商家数据添加到总报告中（用于日志记录）
            reportBuilder.append("商家名称：").append(merchantName).append("\n");
            reportBuilder.append("昨日派单订单：").append(dispatchCount).append("\n");
            reportBuilder.append("昨日取消订单：").append(cancelCount).append("\n");
            reportBuilder.append("昨日完工订单：").append(finishCount).append("\n");
            reportBuilder.append("累计待完工单：").append(pendingCount).append("\n");
            reportBuilder.append("----------------------------------------\n");
        }

        // 输出完整报告到日志
        String report = reportBuilder.toString();
        logger.info("\n=== 完整统计报告 ===\n{}", report);

        logger.info("回收商数据统计任务执行完毕，已为 {} 个商家分别发送消息", data.size());
        return report;
    }


    /**
     * 每天上午9:15执行一次，获取微信群来源统计数据
     * cronExpression: "0 15 9 * * ?" 秒 分 时 日 月 星期
     * 下单量：  指创建时间是昨日的所有单量（按下单时间统计 create_time ）
     * 取消单量： 指取消时间是昨日的所有单量（按取消时间统计 finish_time ）  回收取消 70  + 回收退单 80
     * 回收完成单量： 指回收完成时间是昨日的所有单量（按完成时间统计 finish_time ） 回收完成 100
     * 近30天
     * 待派单单量： 下单时间是最近30天，并且订单状态是【等待寄回】的单量
     * 待揽收单量： 下单时间是最近30天，并且订单状态是【等待签收】物流状态是【等待揽收】
     * 待签收单量： 下单时间是最近30天，并且订单状态是【等待签收】物流状态是【运输中】或者【已妥投】单量
     * 待验机单量： 下单时间是最近30天，并且订单状态是【等待验机】的单量
     * 验机待确认单量： 下单时间是最近30天，并且订单状态是【验机确认】的单量
     * 待退回单量： 下单时间是最近30天，并且订单状态是【等待退回】的单量
     * ---------
     * 物流时间 按下单时间统计 create_time
     * 待揽收单量： 下单时间是最近30天，并且订单状态是【等待签收】物流状态是【揽收中】
     * 待签收单量： 下单时间是最近30天，并且订单状态是【等待签收】物流状态是【已揽收 7】【运输中 11】【派送中 1】【已妥投】单量
     * 物流已取消的对应的订单状态应该是【等待寄回】或者【回收取消】，所以实际已经包含在其他数据里了，不需要单独统计
     */
    @Scheduled(cron = "0 15 9 * * ?")
    public void fetchSourceWxGroupStatistics() {
        if(!Objects.equals(port, "8819")){
            logger.info("当前端口不是8819，不执行定时微信群来源统计任务");
            return;
        }
        logger.info("开始执行微信群来源统计任务");
        // 设置查询日期，格式：yyyy-MM-dd 前一天日期
        String date = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // 设置来源列表
        List<String> sourceList = new ArrayList<>();
        sourceList.add("65"); // 飞蚂蚁 65  渠道表   juran_platform.mer_appkey
        sourceList.add("59"); // 时代鸟 59
        sourceList.add("84"); // 湖北再生资源集团  84
        sourceList.add("87"); // 爱博绿 87
        sourceList.add("90"); // 汪回收 90
        sourceList.add("72"); // 爱裹回收
        sourceList.add("97"); // 海鲸回收
        sourceList.add("98"); // 绿袋回收
        sourceList.add("105"); // 蓝鲸鱼 5996  105

        fetchAndPrintStatistics(date, sourceList);
        logger.info("微信群来源统计任务执行完毕");
    }

    /**
     * 执行微信群来源统计数据获取
     * 可用于定时任务和手动触发
     *
     * @return 统计数据的JSON字符串
     */
    public String fetchAndPrintStatistics(String date, List<String> sourceList) {
        if(!Objects.equals(port, "8819")){
            logger.info("当前端口不是8819，不执行定时微信群来源统计任务");
            return "当前端口不是8819，不执行定时微信群来源统计任务" ;
        }
        try {
            // 调用API获取统计数据
           // String response = sourceStatisticsApi.getSourceWxGroupStatistics(date, sourceList);
           // logger.info("获取微信群来源统计数据成功: {}", response);
            String ourceStatisticsSql = "  SELECT\n" +
                    "    ma.merchant_id AS wxgroup,\n" +
                    "    ma.source,\n" +
                    "    ma.name, \n" +
                    "    COUNT(CASE \n" +
                    "              WHEN ro.create_time >= CURDATE() - INTERVAL 1 DAY AND ro.create_time < CURDATE() \n" +
                    "              THEN ro.id \n" +
                    "          END) AS orderCount, \n" +
                    "    COUNT(CASE \n" +
                    "              WHEN ro.finish_time >= CURDATE() - INTERVAL 1 DAY AND ro.finish_time < CURDATE() AND ro.status IN (70, 80) \n" +
                    "              THEN ro.id \n" +
                    "          END) AS cancelCount, \n" +
                    "    COUNT(CASE \n" +
                    "              WHEN ro.finish_time >= CURDATE() - INTERVAL 1 DAY AND ro.finish_time < CURDATE() AND ro.status = 100 \n" +
                    "              THEN ro.id \n" +
                    "          END) AS completedCount\n" +
                    "          \n" +
                    "FROM \n" +
                    "    juran_platform.mer_appkey ma\n" +
                    "LEFT JOIN\n" +
                    "    juran_order.recover_order ro ON ma.source = ro.source\n" +
                    "WHERE \n" +
                    "    ma.source IN (65, 59, 84, 87, 90, 72, 97, 98, 105)\n" +
                    "GROUP BY\n" +
                    "    ma.merchant_id,\n" +
                    "    ma.source,\n" +
                    "    ma.name\n" +
                    "ORDER BY\n" +
                    "    ma.source;";
            String response = queryApi.executeSqlQueryXDResponse(ourceStatisticsSql);
            logger.info("获取企业微信群昨日订单统计数据成功: {}", response);


            // 获取近一个月订单统计数据
            String monthlyResponse = fetchMonthlyOrderStatistics(sourceList);
            logger.info("获取近一个月订单统计数据完成: {}", monthlyResponse);

            // 获取近一个月物流订单统计数据
            String expressResponse = fetchExpressOrderStatistics(sourceList);
            logger.info("获取近一个月物流订单统计数据完成: {}", expressResponse);

            // 解析月度数据，用于后续展示
            JSONObject monthlyResponseJson = JSONObject.parseObject(monthlyResponse);
            // 按商户ID分组的月度状态统计
            Map<String, Map<String, Integer>> monthlyStatusByMerchantId = new HashMap<>();

            if (monthlyResponseJson != null && monthlyResponseJson.getInteger("statusCode") == 200
                    && monthlyResponseJson.containsKey("data")) {
                List<JSONObject> monthlyDataList = monthlyResponseJson.getJSONArray("data").toJavaList(JSONObject.class);
                for (JSONObject item : monthlyDataList) {
                    // 获取merchantId（注意：这里是整型，需要转为字符串作为key）
                    String merchantId = String.valueOf(item.getIntValue("merchantId"));
                    String statusName = item.getString("statusName");
                    int count = item.getIntValue("count");

                    // 如果该商户ID不存在，则创建一个新的状态统计Map
                    if (!monthlyStatusByMerchantId.containsKey(merchantId)) {
                        monthlyStatusByMerchantId.put(merchantId, new HashMap<>());
                    }

                    // 获取该商户的状态统计Map
                    Map<String, Integer> merchantStatusCount = monthlyStatusByMerchantId.get(merchantId);

                    // 如果该状态已存在，则累加数量
                    merchantStatusCount.put(statusName, merchantStatusCount.getOrDefault(statusName, 0) + count);
                }
                logger.info("按商户ID分组解析月度订单统计数据: {}", monthlyStatusByMerchantId);
            }

            // 解析物流数据，用于后续展示
            JSONObject expressResponseJson = JSONObject.parseObject(expressResponse);
            // 按商户ID分组的物流统计
            Map<String, JSONObject> expressStatsByMerchantId = new HashMap<>();

            // 响应是否成功
            JSONObject responseJson = JSONObject.parseObject(response);

            if (expressResponseJson != null && expressResponseJson.getInteger("statusCode") == 200
                    && expressResponseJson.containsKey("data")) {
                List<JSONObject> expressDataList = expressResponseJson.getJSONArray("data").toJavaList(JSONObject.class);
                for (JSONObject item : expressDataList) {
                    // 获取source作为key，API返回的source直接是字符串
                    String source = item.getString("source");
                    // 查找商户ID，使用responseJson作为参数
                    String wxgroup = findWxgroupBySource(responseJson, source);
                    if (wxgroup != null) {
                        expressStatsByMerchantId.put(wxgroup, item);
                    }
                }
                logger.info("按商户ID分组解析物流订单统计数据: {}", expressStatsByMerchantId);
            }

            // 响应是否成功
            if (responseJson != null && responseJson.getInteger("statusCode") == 200) {
                logger.info("微信群来源统计任务执行成功");
                // 提取数据
                if (responseJson.containsKey("data")) {
                    JSONObject dataObject = responseJson.getJSONObject("data");
                    List<JSONObject> dataList = dataObject.getJSONArray("data").toJavaList(JSONObject.class);
                    logger.info("获取到 {} 条统计数据", dataList.size());

                    // 按wxgroup分组
                    Map<String, List<JSONObject>> groupByMerchantId = new HashMap<>();

                    for (JSONObject item : dataList) {
                        String merchantId = item.getString("wxgroup");
                        // 如果 wxgroup 为空，则跳过
                        if (merchantId == null || merchantId.isEmpty()) {
                            continue;
                        }

                        if (!groupByMerchantId.containsKey(merchantId)) {
                            groupByMerchantId.put(merchantId, new ArrayList<>());
                        }
                        groupByMerchantId.get(merchantId).add(item);
                    }

                    logger.info("按wxgroup分组后有 {} 个不同的wxgroup", groupByMerchantId.size());

                    // 按wxgroup处理每组数据
                    for (Map.Entry<String, List<JSONObject>> entry : groupByMerchantId.entrySet()) {
                        String wxgroup = entry.getKey();
                        List<JSONObject> merchantItems = entry.getValue();

                        // 获取该商户第一条记录中的渠道名称
                        String channelName = "";
                        if (!merchantItems.isEmpty()) {
                            JSONObject firstItem = merchantItems.get(0);
                            if (firstItem.containsKey("name")) {
                                channelName = firstItem.getString("name");
                            }
                        }

                        // 创建消息
                        JSONObject message = new JSONObject();
                        message.put("group_type", "9");
                        if(!Objects.equals(port, "8819")){
                            logger.info("当前端口不是8819，发送到测试群 4344 测试群");
                            wxgroup = "4344";
                        }
                        message.put("group_type_id", wxgroup); // 使用 wxgroup 作为group_type_id  4344 测试群

                        // 构建消息内容，展示同一个 wxgroup 下不同状态的数据，使用新的格式
                        StringBuilder contentBuilder = new StringBuilder();

                        // 昨日日期
                        String yesterdayDate = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

                        // 直接从API响应中获取数据，不再通过状态名称计算
                        int totalOrderCount = 0;
                        int cancelOrderCount = 0;
                        int completeOrderCount = 0;

                        // 从第一条记录中获取orderCount、cancelCount和completedCount
                        if (!merchantItems.isEmpty()) {
                            JSONObject firstItem = merchantItems.get(0);
                            totalOrderCount = firstItem.getIntValue("orderCount");
                            cancelOrderCount = firstItem.getIntValue("cancelCount");
                            completeOrderCount = firstItem.getIntValue("completedCount");
                        }

                        // 2. 构建内容
                        contentBuilder.append("  【").append(channelName).append("】\n");
                        contentBuilder.append("──────────────\n");
                        contentBuilder.append("昨日订单统计（").append(yesterdayDate).append("）\n");
                        contentBuilder.append("──────────────\n");

                        // 直接添加汇总信息
                        contentBuilder.append(" 📝 - 下单量：").append(totalOrderCount).append("\n \n");
                        contentBuilder.append(" ↩️ - 取消单量：").append(cancelOrderCount).append("\n \n");
                        contentBuilder.append(" ✅ - 回收完成：").append(completeOrderCount).append("\n");
                        contentBuilder.append("──────────────\n");
                        contentBuilder.append("近30天订单统计\n");
                        contentBuilder.append("──────────────\n");

                        // 获取该商户的月度状态统计数据
                        Map<String, Integer> merchantMonthlyStatusCount = monthlyStatusByMerchantId.getOrDefault(wxgroup, new HashMap<>());
                        contentBuilder.append(" ⬅️ - 待派单单量：").append(merchantMonthlyStatusCount.getOrDefault("等待寄回", 0)).append("\n \n");

                        // 添加物流订单统计数据
                        JSONObject expressStats = expressStatsByMerchantId.get(wxgroup);
                        if (expressStats != null) {
                            // 待揽收单量 - 物流状态是【揽收中】
                            contentBuilder.append(" 📦 - 待揽收单量：").append(expressStats.getIntValue("pendingPickup")).append("\n \n");
                            // 待签收单量 - 物流状态是【已揽收】【运输中】【派送中】【已妥投】
                            contentBuilder.append(" 🚚 - 待签收单量：").append(expressStats.getIntValue("pendingReceive")).append("\n \n");
                        }


                        // 添加近30天的特定状态订单数量  emojiMap.put("等待验机", "🕵️ - "); "等待退回", "⬅️ - "
                        contentBuilder.append(" ⏳ - 待验机单量：").append(merchantMonthlyStatusCount.getOrDefault("等待验机", 0)).append("\n \n");
                        contentBuilder.append(" 🕵️ - 验机待确认单量：").append(merchantMonthlyStatusCount.getOrDefault("验机确认", 0)).append("\n \n");
                        contentBuilder.append(" 🔄 - 待退回单量：").append(merchantMonthlyStatusCount.getOrDefault("等待退回", 0)).append("\n \n");

                        message.put("content", contentBuilder.toString());

                        // 为当前wxgroup推送消息
                        logger.info("开始为商户ID {} 推送消息...", wxgroup);
                        logger.info("商户ID {} 推送消息内容: {}", wxgroup, message.getString("content"));
                        Result<JSONObject> result = sendMessageApi.pushMessage(message);
                        logger.info("商户ID {} 推送结果: {}", wxgroup, result);
                    }

                } else {
                    logger.warn("响应成功但未包含数据");
                }
            } else {
                String errorInfo = responseJson != null && responseJson.getString("errorInfo") != null ? responseJson.getString("errorInfo") : "未知错误";
                logger.error("微信群来源统计任务执行失败: {}", errorInfo);
            }

            return response;
        } catch (Exception e) {
            logger.error("微信群来源统计任务执行失败", e);
            return "执行失败: " + e.getMessage();
        }
    }

    /**
     * 获取月度订单统计数据
     *
     * @param sourceList 来源列表，需要转换为整型
     * @return 统计数据的JSON字符串
     */
    public String fetchMonthlyOrderStatistics(List<String> sourceList) {
        try {
            logger.info("开始获取近一个月订单统计数据");

            // 设置开始日期和结束日期，格式：yyyy-MM-dd，开始日期为30天前，结束日期为当前日期
            String endDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String startDate = LocalDate.now().minusDays(30).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            // 将字符串类型的sourceList转换为Integer类型
            List<Integer> intSourceList = new ArrayList<>();
            for (String source : sourceList) {
                try {
                    intSourceList.add(Integer.parseInt(source));
                } catch (NumberFormatException e) {
                    logger.warn("来源ID转换为整型失败，忽略: {}", source);
                }
            }

            // 调用API获取月度订单统计数据
            String response = sourceStatisticsApi.getMonthlyOrderStatistics(startDate, endDate, intSourceList);
            logger.info("获取近一个月订单统计数据成功: {}", response);

            // 响应是否成功
            JSONObject responseJson = JSONObject.parseObject(response);
            if (responseJson != null && responseJson.getInteger("statusCode") == 200) {
                logger.info("近一个月订单统计任务执行成功");
                // 提取数据
                if (responseJson.containsKey("data")) {
                    List<JSONObject> dataList = responseJson.getJSONArray("data").toJavaList(JSONObject.class);
                    logger.info("获取到 {} 条月度订单统计数据", dataList.size());

                    // 在此处可以添加更多数据处理逻辑

                } else {
                    logger.warn("响应成功但未包含数据");
                }
            } else {
                String errorInfo = responseJson != null && responseJson.getString("errorInfo") != null ? responseJson.getString("errorInfo") : "未知错误";
                logger.error("近一个月订单统计任务执行失败: {}", errorInfo);
            }

            return response;
        } catch (Exception e) {
            logger.error("获取近一个月订单统计数据失败", e);
            return "执行失败: " + e.getMessage();
        }
    }

    /**
     * 获取物流订单统计数据
     *
     * @param sourceList 来源列表，需要转换为整型
     * @return 统计数据的JSON字符串
     */
    public String fetchExpressOrderStatistics(List<String> sourceList) {
        try {
            logger.info("开始获取近一个月物流订单统计数据");

            // 设置开始日期和结束日期，格式：yyyy-MM-dd，开始日期为30天前，结束日期为当前日期
            String endDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String startDate = LocalDate.now().minusDays(30).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            // 将字符串类型的sourceList转换为Integer类型
            List<Integer> intSourceList = new ArrayList<>();
            for (String source : sourceList) {
                try {
                    intSourceList.add(Integer.parseInt(source));
                } catch (NumberFormatException e) {
                    logger.warn("来源ID转换为整型失败，忽略: {}", source);
                }
            }

            // 调用API获取物流订单统计数据
            String response = sourceStatisticsApi.getExpressOrderStatistics(startDate, endDate, intSourceList);
            logger.info("获取近一个月物流订单统计数据成功: {}", response);

            // 响应是否成功
            JSONObject responseJson = JSONObject.parseObject(response);
            if (responseJson != null && responseJson.getInteger("statusCode") == 200) {
                logger.info("近一个月物流订单统计任务执行成功");
                // 提取数据
                if (responseJson.containsKey("data")) {
                    List<JSONObject> dataList = responseJson.getJSONArray("data").toJavaList(JSONObject.class);
                    logger.info("获取到 {} 条物流订单统计数据", dataList.size());

                    // 在此处可以添加更多数据处理逻辑

                } else {
                    logger.warn("响应成功但未包含数据");
                }
            } else {
                String errorInfo = responseJson != null && responseJson.getString("errorInfo") != null ? responseJson.getString("errorInfo") : "未知错误";
                logger.error("近一个月物流订单统计任务执行失败: {}", errorInfo);
            }

            return response;
        } catch (Exception e) {
            logger.error("获取近一个月物流订单统计数据失败", e);
            return "执行失败: " + e.getMessage();
        }
    }

    /**
     * 根据source找到对应的wxgroup
     *
     * @param responseJson 原始响应JSON
     * @param source       来源ID
     * @return 对应的wxgroup，如果找不到则返回null
     */
    private String findWxgroupBySource(JSONObject responseJson, String source) {
        if (responseJson != null && responseJson.containsKey("data")) {
            JSONObject dataObject = responseJson.getJSONObject("data");
            if (dataObject == null || !dataObject.containsKey("data")) {
                return null;
            }
            List<JSONObject> dataList = dataObject.getJSONArray("data").toJavaList(JSONObject.class);
            for (JSONObject item : dataList) {
                if (source.equals(item.getString("source"))) {
                    return item.getString("wxgroup");
                }
            }
        }
        return null;
    }


} 