package com.kefang.job;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 自动转换长时间不活跃的人工会话为AI会话
 */
@Component
public class AutoSessionTransferTask {

    private static final Logger logger = LoggerFactory.getLogger(AutoSessionTransferTask.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 每5分钟执行一次，将长时间不活跃的人工会话转为AI会话
     * 将最后活跃时间超过15分钟的人工会话自动转为AI会话
     * 同时更新对应的人工呼叫记录表中的结束时间和服务耗时
     */
    @Scheduled(fixedRate = 300000) // 300000 毫秒 = 5分钟
    @Transactional
    public void transferInactiveSessionsToAI() {
        logger.debug("开始执行会话自动转换任务");

        try {
            // 第一步：获取需要转换的会话ID
            String selectSessionsSql = "SELECT id FROM chat_session " +
                    "WHERE status = 1 " +               // 当前状态为人工会话中
                    "AND last_active_time < NOW() - INTERVAL 15 MINUTE"; // 最后活跃时间超过15分钟

            // 查询需要转换的会话ID列表，供日志使用
            List<Long> sessionIds = jdbcTemplate.queryForList(selectSessionsSql, Long.class);
            logger.debug("查询需要自动转换的会话ID列表" + sessionIds);
            if (sessionIds.isEmpty()) {
                logger.info("没有需要自动转换的会话");
                return;
            }

            logger.debug("找到 {} 个长时间不活跃的人工会话需要转换为AI会话", sessionIds.size());

            // 第二步：更新会话状态
            String updateSessionsSql = "UPDATE chat_session " +
                    "SET " +
                    "   status = 3, " +              // 更新为 AI会话中
                    "   current_agent_type = 2, " +  // 更新为 AI客服
                    "   agent_id = 6, " +            // 更新为 AI客服
                    "   transfer_requested = 0 " +   // 取消转人工请求
                    "WHERE " +
                    "   status = 1 " +               // 当前状态为人工会话中
                    "   AND last_active_time < NOW() - INTERVAL 15 MINUTE"; // 最后活跃时间超过15分钟


            int updatedSessionsCount = jdbcTemplate.update(updateSessionsSql);

            // 第三步：更新人工呼叫记录表
            // 将会话ID列表转换为以逗号分隔的字符串，用于SQL的IN子句
            String sessionIdsString = sessionIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));

            String updateRecordsSql = "UPDATE chat_call_manual_records " +
                    "SET " +
                    "   end_time = NOW(), " +                                   // 设置结束时间为当前时间
                    "   service_duration = TIMESTAMPDIFF(SECOND, accept_time, NOW()) " + // 计算服务耗时（秒）
                    "WHERE " +
                    "   session_id IN (" + sessionIdsString + ") " +           // 使用已查询的会话ID列表
                    "   AND is_accepted = 1    AND end_time IS   NULL  AND end_time IS NULL AND service_duration IS NULL";                                 // 未设置结束时间的记录
            logger.debug("更新人工呼叫记录表  " + updateRecordsSql);
            int updatedRecordsCount = jdbcTemplate.update(updateRecordsSql);

            logger.info("会话自动转换任务完成：已将 {} 个最后活跃时间超过15分钟的人工会话转为AI会话, 更新了 {} 条人工呼叫记录",
                    updatedSessionsCount, updatedRecordsCount);
        } catch (Exception e) {
            logger.error("会话自动转换任务执行失败", e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }
} 