package com.kefang.websocket;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kefang.controller.WeChatController;
import com.kefang.dto.WebSocketMessage;
import com.kefang.entity.ChatMessage;
import com.kefang.entity.ChatSession;
import com.kefang.entity.User;
import com.kefang.entity.UserOrders;
import com.kefang.service.*;
import com.kefang.utils.SpringContextUtil;
import com.mysql.cj.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
@ServerEndpoint("/ws/{role}/{id}")
public class WebSocketServer {

    // 记录当前在线连接数
    private static AtomicInteger onlineCount = new AtomicInteger(0);
    
    // 存放所有客户端连接
    // 用户端 map结构: <用户ID, 会话对象>
    private static Map<String, Session> userSessionMap = new ConcurrentHashMap<>();
    // 客服端 map结构: <客服ID, 会话对象>
    private static Map<String, Session> agentSessionMap = new ConcurrentHashMap<>();
    
    // 由于WebSocket是多例的，需要使用静态注入Service
    private static ChatMessageService chatMessageService;
    
    // 服务器启动时间
    private static final long SERVER_START_TIME = System.currentTimeMillis();
    
    @Autowired
    public void setChatMessageService(ChatMessageService chatMessageService) {
        WebSocketServer.chatMessageService = chatMessageService;
    }

    private static AgentService agentService;
    private static UserService userService;
    private static WeChatController wechatController;

    @Autowired
    public void setAgentService(AgentService agentService) {
        WebSocketServer.agentService = agentService;
    }
    @Autowired
    public void setUserService(UserService userService) {
        WebSocketServer.userService = userService;
    }
    @Autowired
    public void setWeChatController(WeChatController wechatController) {
        WebSocketServer.wechatController = wechatController;
    }
    // 角色类型
    private String role;
    // 用户ID或客服ID
    private String id;
    // 当前会话
    private Session session;
    
    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("role") String role, @PathParam("id") String id) {
        this.session = session;
        this.role = role;
        this.id = id;
        
        log.info("新WebSocket连接 - role: {}, id: {}, sessionId: {}", role, id, session.getId());
        
        // 记录更多连接信息
        Map<String, List<String>> headers = session.getRequestParameterMap();
        log.debug("WebSocket连接参数: {}", headers);
        
        // 获取客户端信息
        try {
            Map<String, Object> clientInfo = new HashMap<>();
            // 尝试获取查询参数中的信息
            String userAgent = "Unknown";
            if (headers.containsKey("user-agent")) {
                userAgent = headers.get("user-agent").get(0);
            }
            
            // 检测是否为移动设备
            boolean isMobile = userAgent.toLowerCase().matches(".*android.*|.*iphone.*|.*ipad.*|.*mobile.*");
            clientInfo.put("userAgent", userAgent);
            clientInfo.put("isMobile", isMobile);
            clientInfo.put("remoteAddress", session.getBasicRemote().toString());
            
            log.debug("客户端设备信息: {}", clientInfo);
        } catch (Exception e) {
            log.warn("获取客户端信息失败", e);
        }
        
        // 记录原有会话映射信息
        log.debug("连接前会话映射 - 用户会话数: {}, 客服会话数: {}", userSessionMap.size(), agentSessionMap.size());
        
        if ("user".equals(role)) {
            // 检查是否已有相同ID的连接
            Session existingSession = userSessionMap.get(id);
            if (existingSession != null && existingSession.isOpen()) {
                log.warn("用户 {} 已有连接, 关闭旧连接", id);
                try {
                    existingSession.close(new CloseReason(CloseReason.CloseCodes.NORMAL_CLOSURE, "用户重复连接"));
                } catch (IOException e) {
                    log.error("关闭旧连接失败", e);
                }
            }
            
            // 使用synchronized确保线程安全
            synchronized (userSessionMap) {
                userSessionMap.put(id, session);
                log.info("用户 {} 的WebSocket连接已存储，连接ID: {}", id, session.getId());
            }
        } else if ("agent".equals(role)) {
            // 检查是否已有相同ID的连接
            Session existingSession = agentSessionMap.get(id);
            if (existingSession != null && existingSession.isOpen()) {
                log.warn("客服 {} 已有连接, 关闭旧连接", id);
                try {
                    existingSession.close(new CloseReason(CloseReason.CloseCodes.NORMAL_CLOSURE, "客服重复连接"));
                } catch (IOException e) {
                    log.error("关闭旧连接失败", e);
                }
            }
            
            // 使用synchronized确保线程安全
            synchronized (agentSessionMap) {
                agentSessionMap.put(id, session);
                log.info("客服 {} 的WebSocket连接已存储，连接ID: {}", id, session.getId());
            }
        }
        
        // 设置更大的超时时间，适应移动网络
        try {
            session.setMaxIdleTimeout(180000); // 3分钟
            log.debug("设置WebSocket连接超时时间为3分钟");
        } catch (Exception e) {
            log.warn("设置WebSocket连接超时时间失败", e);
        }
        
        onlineCount.incrementAndGet();
        log.info("有新连接加入！当前在线人数为：{}", onlineCount.get());
        
        // 登记当前在线用户和客服ID
        log.info("当前在线用户ID: {}", userSessionMap.keySet());
        log.info("当前在线客服ID: {}", agentSessionMap.keySet());


        if ("agent".equals(role)) {
            // 如果是客服断开连接，则需要处理
            agentService.updateAgentStatus(Long.valueOf(id), 1); // 0 表示离线 1 表示在线
        }


        // 发送上线通知
//        WebSocketMessage message = new WebSocketMessage();
//        message.setType(4); // 用户上线通知
//        message.setFrom("user".equals(role) ? 0 : 1);
//        message.setSenderId(Long.valueOf(id));
//        message.setContent(id + ("user".equals(role) ? "用户" : "客服") + "上线了");
//        message.setTimestamp(System.currentTimeMillis());

        // （仅在用户上线时）延迟发送欢迎消息
        if ("user".equals(role)) {
            // 使用异步方式延迟发送欢迎消息，确保连接完全建立
            new Thread(() -> {
                try {
                    // 延迟1秒发送，确保前端WebSocket连接完全建立
                    Thread.sleep(1000);

                    // 检查连接是否仍然有效
                    if (session.isOpen() && userSessionMap.containsKey(id)) {
                        sendWelcomeMessage(Long.valueOf(id), session);
                    } else {
                        log.warn("用户连接已断开，取消发送欢迎消息");
                    }
                } catch (InterruptedException e) {
                    log.warn("欢迎消息发送线程被中断");
                    Thread.currentThread().interrupt();
                } catch (Exception e) {
                    log.error("发送欢迎消息异常: {}", e.getMessage());
                }
            }).start();

            // 通知所有客服有新用户上线（保留原有逻辑，但暂时注释）
//            for (Map.Entry<String, Session> entry : agentSessionMap.entrySet()) {
//                try {
//                    log.info("向客服 {} 发送用户 {} 上线通知", entry.getKey(), id);
//                    entry.getValue().getBasicRemote().sendText(JSON.toJSONString(message));
//                } catch (IOException e) {
//                    log.error("发送消息出错：{}", e.getMessage());
//                }
//            }
        }
    }
    
    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        if ("user".equals(role)) {
            log.info(id+" 有一用户连接关闭！");
            userSessionMap.remove(id);
        } else if ("agent".equals(role)) {
            // 如果是客服断开连接，则需要处理
            log.info(id+" 有一客服连接关闭！");
            agentService.updateAgentStatus(Long.valueOf(id), 0); // 0 表示离线 1 表示在线
            agentSessionMap.remove(id);
        }

        onlineCount.decrementAndGet();
        log.info("当前在线人数为：{}", onlineCount.get());
        log.info("当前在线用户ID: {}", userSessionMap.keySet());
        log.info("当前在线客服ID: {}", agentSessionMap.keySet());
        
        // 发送下线通知
//        WebSocketMessage message = new WebSocketMessage();
//        message.setType(5); // 用户下线通知
//        message.setFrom("user".equals(role) ? 0 : 1);
//        message.setSenderId(Long.valueOf(id));
//        message.setContent(id + ("user".equals(role) ? "用户" : "客服") + "下线了");
//        message.setTimestamp(System.currentTimeMillis());
//
//        // 广播消息，通知所有在线客服（仅在用户下线时）
//        if ("user".equals(role)) {
//            for (Map.Entry<String, Session> entry : agentSessionMap.entrySet()) {
//                try {
//                    entry.getValue().getBasicRemote().sendText(JSON.toJSONString(message));
//                } catch (IOException e) {
//                    log.error("发送消息出错：{}", e.getMessage());
//                }
//            }
//        }
    }
    
    /**
     * 收到客户端消息后调用的方法
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        log.debug("收到来自{}的消息：{}", id, message);
        log.debug("当前在线人数为：{}", onlineCount.get());
        log.debug("当前在线用户ID: {}", userSessionMap.keySet());
        log.debug("当前在线客服ID: {}", agentSessionMap.keySet());
        
        try {
            WebSocketMessage webSocketMessage = JSON.parseObject(message, WebSocketMessage.class);
            // 设置发送时间
            webSocketMessage.setTimestamp(System.currentTimeMillis());
            // 心跳消息特殊处理：直接回复，不做其他处理
            if (webSocketMessage.getType() == 6 || webSocketMessage.getType() == 3) {
                try {
                    // 立即回复心跳
                    WebSocketMessage heartbeatResponse = new WebSocketMessage();
                    heartbeatResponse.setType(6);
                    heartbeatResponse.setTimestamp(System.currentTimeMillis());
                    session.getBasicRemote().sendText(JSON.toJSONString(heartbeatResponse));
                    log.debug("回复心跳消息");
                    return;
                } catch (IOException e) {
                    log.error("发送心跳响应失败: {}", e.getMessage());
                    return;
                }
            }
            // 将字符串解析为 Map 或自定义对象
            // Map<String, Object> orderInfoMap = JSON.parseObject(webSocketMessage.getOrderInfo(), Map.class);
            // Object list = orderInfoMap.get("list");
            // 添加详细日志
            log.info("""
                
                ------- 收到websocket消息 -------
                  消息: {}
                  发送者ID: {} 发送方: {} 数据源: 【{}】
                  接收方ID: {}  会话ID: {} 类型: {}
                  在线状态： 在线人数：{}  在线用户ID：{} 在线客服ID：{}""",
                    webSocketMessage.getContent(),
                    webSocketMessage.getSenderId(),getFromDescription(webSocketMessage.getFrom()),webSocketMessage.getDatasource(),
                    webSocketMessage.getReceiverId(),
                    webSocketMessage.getSessionId(), webSocketMessage.getType(), onlineCount.get(),
                    userSessionMap.keySet(), agentSessionMap.keySet());


            // 安全检查：验证发送者ID与连接中的ID是否匹配
            if (webSocketMessage.getSenderId() != null && !id.equals(webSocketMessage.getSenderId().toString())) {
                log.info("消息发送者ID({})与连接ID({})不匹配，可能是伪造消息", webSocketMessage.getSenderId(), id);
                // 返回错误消息给客户端
                WebSocketMessage errorMessage = new WebSocketMessage();
                errorMessage.setType(8);
                // errorMessage.setContent("非法消息");
                try {
                    session.getBasicRemote().sendText(JSON.toJSONString(errorMessage));
                    return;
                } catch (IOException e) {
                    log.error("发送错误消息失败: {}", e.getMessage());
                }
            }
            

            
            // 处理消息类型
            if (webSocketMessage.getType() <= 2) {
                // 保存消息到数据库
                String datasource = webSocketMessage.getDatasource();
                ChatMessage chatMessage = new ChatMessage();
                chatMessage.setSessionId(webSocketMessage.getSessionId());
                chatMessage.setSenderType(webSocketMessage.getFrom());
                chatMessage.setContent(webSocketMessage.getContent());
                chatMessage.setMsgType(webSocketMessage.getType() - 1); // 1->0,2->1
                chatMessage.setIsRead(0); // 未读
                chatMessage.setDatasource(datasource);
                chatMessage.setCollectionName(webSocketMessage.getCollectionName());
                chatMessage.setScene(webSocketMessage.getScene());
                chatMessage.setChannel(webSocketMessage.getChannel());
                chatMessage.setSenderId(webSocketMessage.getSenderId()+"");

                // 如果是客服和微信用户发消息，则需要转发到微信服务器
                if( webSocketMessage.getFrom() == 1 && "微信".equals(datasource)){
                    Long receiverId = webSocketMessage.getReceiverId();
                    User userById = userService.getUserById(receiverId);
                    String extra1 = userById.getExtra1();
                    if (StringUtils.isNullOrEmpty(extra1)){
                        log.info("转发微信消息错误");
                    }else {
                        // extra1是JSON格式 {"channelWX":"0","openId":"ozenVjpbKN1ZxGsIhblPkfT3Y82c","appId":"wxafe8168141ed6353"} 的，需要解析
                        JSONObject jsonObject = JSONObject.parseObject(extra1);
                        String channelWX = jsonObject.getString("channelWX");
                        String openId = jsonObject.getString("openId");
                        String appId = jsonObject.getString("appId");
                        String content = chatMessage.getContent();
                        String result = wechatController.sendCustomerServiceMessage(openId, content, Integer.parseInt(channelWX), appId);
                        if (!"success".equals(result)){
                            log.info("转发微信消息错误");
                        }
                    }

                }

                // 保存到数据库并获取ID
                Long messageId = null;
                try {
                    messageId = chatMessageService.sendMessage(chatMessage);
                } catch (Exception e) {
                    log.error("保存消息到数据库失败: {}", e.getMessage());
                    // 尝试恢复服务
                    try {
                        chatMessageService = SpringContextUtil.getBean(ChatMessageService.class);
                        if (chatMessageService != null) {
                            log.info("重新获取ChatMessageService成功，再次尝试保存");
                            messageId = chatMessageService.sendMessage(chatMessage);
                        }
                    } catch (Exception ex) {
                        log.error("重新获取ChatMessageService失败", ex);
                    }
                }
                
                // 为消息设置ID，便于前端识别
                if (messageId != null) {
                    webSocketMessage.setId(messageId);
                    log.debug("消息已保存到数据库，ID: {}", messageId);
                    
                    // 判断消息发送方是否为用户，且消息类型是否为文本
                    boolean isUserTextMessage = webSocketMessage.getFrom() == 0 && webSocketMessage.getType() == 1;
                    
                    // 检查ChatSession是否为AI客服模式(如果需要)，这里简化处理
                    boolean isAISession = false;
                    try {
                        // 获取ChatSessionService的bean实例
                        ChatSessionService chatSessionService = SpringContextUtil.getBean(ChatSessionService.class);
                        if (chatSessionService != null) {
                            // 判断会话当前的客服类型是否为AI(agentType = 2)
                            isAISession = chatSessionService.isAISession(webSocketMessage.getSessionId());
                            // log.info("会话 {} 是否为AI客服模式: {}", webSocketMessage.getSessionId(), isAISession);
                        }
                    } catch (Exception e) {
                        log.error("检查AI会话状态失败", e);
                    }
                    
                    // 如果是用户发送给AI客服的消息，则不在WebSocket中处理AI回复
                    // 前端会通过流式API直接获取AI回复
                    if (isUserTextMessage && isAISession) {
                        log.info("用户发送给AI客服的消息，跳过WebSocket的AI回复处理");

                        Long receiverId = webSocketMessage.getReceiverId(); // 获取接收者的ID
                        // 只转发用户消息给客服
                        for (Map.Entry<String, Session> entry : agentSessionMap.entrySet()) {
                            try {
                                // 之转发给接受客服的逻辑
                                if (receiverId != null && receiverId.equals(Long.parseLong(entry.getKey()))) {
                                    log.info("转发用户消息给客服: {}", entry.getKey());
                                    String jsonMessage = JSON.toJSONString(webSocketMessage);
                                    log.info("发送的JSON消息: {}", jsonMessage);
                                    entry.getValue().getBasicRemote().sendText(jsonMessage);
                                }
                            } catch (IOException e) {
                                log.error("转发消息给客服出错：{}", e.getMessage());
                            }
                        }
                    } else {
                        // 非AI客服对话或非文本消息，使用原有处理逻辑
                        // 添加：使用ChatSessionService处理消息
                        if (!isAISession) {
                            try {
                                // 获取ChatSessionService的bean实例
                                ChatSessionService chatSessionService = SpringContextUtil.getBean(ChatSessionService.class);
                                if (chatSessionService != null) {
                                    // 调用ChatSessionService的sendMessage方法处理AI回复等逻辑
                                    ChatMessage aiResponse = chatSessionService.sendMessage(webSocketMessage);
                                    
                                    // 如果返回了AI回复消息，发送给用户
                                    if (aiResponse != null && aiResponse.getSenderType() == 2) {
                                        log.info("收到AI回复，发送给用户: {}, 内容: {}", webSocketMessage.getSenderId(), aiResponse.getContent());
                                        
                                        // 构建WebSocket消息
                                        WebSocketMessage aiMessage = new WebSocketMessage();
                                        aiMessage.setId(aiResponse.getId());
                                        aiMessage.setSessionId(aiResponse.getSessionId());
                                        aiMessage.setContent(aiResponse.getContent());
                                        aiMessage.setType(1); // 文本消息
                                        aiMessage.setFrom(2); // 2=AI
                                        aiMessage.setTimestamp(System.currentTimeMillis());
                                        
                                        // 获取用户的WebSocket会话
                                        Session userSession = userSessionMap.get(webSocketMessage.getSenderId().toString());
                                        if (userSession != null && userSession.isOpen()) {
                                            userSession.getBasicRemote().sendText(JSON.toJSONString(aiMessage));
                                            log.info("AI回复已发送给用户: {}", webSocketMessage.getSenderId());
                                        } else {
                                            log.warn("用户{}不在线，AI回复无法发送", webSocketMessage.getSenderId());
                                        }
                                    }
                                } else {
                                    log.warn("无法获取ChatSessionService的bean实例");
                                }
                            } catch (Exception e) {
                                log.error("调用ChatSessionService处理消息失败", e);
                            }
                        }

                        // 确保from字段是正确的数值类型
                        if (webSocketMessage.getFrom() != 0 && webSocketMessage.getFrom() != 1) {
                            log.warn("消息from字段不正确，当前值: {}，将设置为默认值", webSocketMessage.getFrom());
                            if ("user".equals(role)) {
                                webSocketMessage.setFrom(0); // 用户发送
                            } else {
                                webSocketMessage.setFrom(1); // 客服发送
                            }
                        }

                        // 转发消息给接收方
                        if (webSocketMessage.getFrom() == 0) { // 用户发送给客服
                            log.debug("处理用户发送给客服的消息");
                            // 如果receiverId为空或为0，则广播给所有在线客服
                            if (webSocketMessage.getReceiverId() == null || webSocketMessage.getReceiverId() == 0) {
                                log.info("用户消息未指定客服ID，广播给所有在线客服");
                                // 广播给所有在线客服
                                int successCount = 0;
                                for (Map.Entry<String, Session> entry : agentSessionMap.entrySet()) {
                                    try {
                                        log.info("广播消息给客服: {}", entry.getKey());
                                        String jsonMessage = JSON.toJSONString(webSocketMessage);
                                        log.debug("发送的JSON消息: {}", jsonMessage);
                                        entry.getValue().getBasicRemote().sendText(jsonMessage);
                                        successCount++;
                                    } catch (IOException e) {
                                        log.error("广播消息给客服出错：{}", e.getMessage());
                                    }
                                }
                                log.info("广播消息给客服，成功: {}/总数: {}", successCount, agentSessionMap.size());
                            } else {
                                // 发送给指定客服
                                // log.info("用户消息指定客服ID: {}", webSocketMessage.getReceiverId());
                                Session agentSession = agentSessionMap.get(webSocketMessage.getReceiverId().toString());
                                if (agentSession != null) {
                                    try {
                                        if (agentSession.isOpen()) {
                                            log.info("发送消息给指定客服: {}", webSocketMessage.getReceiverId());
                                            String jsonMessage = JSON.toJSONString(webSocketMessage);
                                            log.debug("发送的JSON消息: {}", jsonMessage);
                                            agentSession.getBasicRemote().sendText(jsonMessage);
                                        } else {
                                            log.warn("客服(ID:{})会话已存在但已关闭，尝试重新获取", webSocketMessage.getReceiverId());
                                            // 移除无效会话
                                            agentSessionMap.remove(webSocketMessage.getReceiverId().toString());
                                        }
                                    } catch (IOException e) {
                                        log.error("发送消息给客服出错：{}", e.getMessage());
                                    }
                                } else {
                                    log.warn("指定的客服(ID:{})不在线，消息无法发送", webSocketMessage.getReceiverId());
                                    // 不再广播给所有客服，而是记录日志
                                    log.info("客服不在线，消息已保存但未转发");
                                }
                            }
                        } else if (webSocketMessage.getFrom() == 1) { // 客服发送给用户
                            log.debug("处理客服发送给用户的消息");
                            // 同样处理receiverId可能为空的情况
                            if (webSocketMessage.getReceiverId() != null) {
                                log.debug("客服消息指定用户ID: {}", webSocketMessage.getReceiverId());
                                Session userSession = userSessionMap.get(webSocketMessage.getReceiverId().toString());
                                if (userSession != null) {
                                    try {
                                        if (userSession.isOpen()) {
                                            String jsonMessage = JSON.toJSONString(webSocketMessage);
                                            log.debug("发送的JSON消息: {}", jsonMessage);
                                            userSession.getBasicRemote().sendText(jsonMessage);
                                            log.debug("消息已发送给用户: {}", webSocketMessage.getReceiverId());
                                        } else {
                                            log.warn("用户(ID:{})会话已存在但已关闭，尝试重新获取", webSocketMessage.getReceiverId());
                                            // 移除无效会话
                                            userSessionMap.remove(webSocketMessage.getReceiverId().toString());
                                        }
                                    } catch (IOException e) {
                                        log.error("发送消息给用户出错：{}", e.getMessage());
                                    }
                                } else {
                                    log.warn("指定的用户(ID:{})不在线，消息无法发送", webSocketMessage.getReceiverId());
                                }
                            } else {
                                log.info("客服消息没有指定接收的用户ID，消息已保存但未转发");
                            }
                        }
                    }
                } else {
                    log.warn("消息保存失败，无ID");
                }
            } else if (webSocketMessage.getType() == 3) { // 系统通知
                // 根据通知类型处理
                log.debug("处理系统通知: {}", webSocketMessage.getContent());
                
                // 检查是否是转接人工客服的系统通知
                Map<String, Object> data = webSocketMessage.getData();
                if (data != null && data.containsKey("transferType") && "toHuman".equals(data.get("transferType"))) {
                    log.info("收到转接人工客服的系统通知，会话ID: {}, 用户ID: {}", 
                        webSocketMessage.getSessionId(), webSocketMessage.getReceiverId());
                    
                    // 获取用户会话并发送通知
                    if (webSocketMessage.getReceiverId() != null) {
                        Session userSession = userSessionMap.get(webSocketMessage.getReceiverId().toString());
                        if (userSession != null && userSession.isOpen()) {
                            try {
                                userSession.getBasicRemote().sendText(JSON.toJSONString(webSocketMessage));
                                log.info("已通过WebSocket发送转接通知给用户: {}", webSocketMessage.getReceiverId());
                            } catch (IOException e) {
                                log.error("发送转接通知失败: {}", e.getMessage());
                            }
                        } else {
                            log.warn("用户 {} 不在线，无法发送WebSocket转接通知", webSocketMessage.getReceiverId());
                        }
                    }
                }
                
                // 处理其他系统通知，如会话结束、会话转接等
                // ...
            } else if (webSocketMessage.getType() == 6) { // 心跳消息
                // 心跳消息只是为了保持连接活跃，已在前面处理
                log.debug("处理心跳消息");
            } else if (webSocketMessage.getType() == 7) { // 连接确认消息
                log.info("处理连接确认消息");
                // 回复确认收到
                try {
                    WebSocketMessage confirmResponse = new WebSocketMessage();
                    confirmResponse.setType(7);
                    confirmResponse.setFrom(1); // 系统
                    confirmResponse.setContent("确认收到");
                    confirmResponse.setTimestamp(System.currentTimeMillis());
                    session.getBasicRemote().sendText(JSON.toJSONString(confirmResponse));
                } catch (IOException e) {
                    log.error("发送确认回复失败: {}", e.getMessage());
                }
            }
            
            // log.info("消息处理完成");
        } catch (Exception e) {
            log.error("处理WebSocket消息异常: {}", e.getMessage(), e);
            
            // 尝试发送错误消息给客户端，让客户端知道出了问题
            try {
                WebSocketMessage errorMessage = new WebSocketMessage();
                errorMessage.setType(5); // 错误消息类型
                errorMessage.setFrom(1); // 系统发送
                errorMessage.setContent("服务器处理消息时发生错误: " + e.getMessage());
                errorMessage.setTimestamp(System.currentTimeMillis());
                
                session.getBasicRemote().sendText(JSON.toJSONString(errorMessage));
            } catch (IOException ioEx) {
                log.error("发送错误消息失败: {}", ioEx.getMessage());
            }
        }
    }
    
    /**
     * 发生错误时调用
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("发生错误：{}, Session ID: {}", error.getMessage(), session.getId());
        error.printStackTrace();
    }
    
    /**
     * 发送消息
     */
    public static void sendMessage(String message, String role, String id) {
        try {
            if ("user".equals(role)) {
                Session session = userSessionMap.get(id);
                if (session != null) {
                    session.getBasicRemote().sendText(message);
                }
            } else if ("agent".equals(role)) {
                Session session = agentSessionMap.get(id);
                if (session != null) {
                    session.getBasicRemote().sendText(message);
                }
            }
        } catch (IOException e) {
            log.error("发送消息出错：{}", e.getMessage());
        }
    }
    
    /**
     * 广播消息到会话
     * @param message WebSocket消息对象
     */
    public void broadcastMessage(WebSocketMessage message) {
        try {
            String messageJson = JSON.toJSONString(message);
            log.info("广播消息: {}", messageJson);
            
            Long sessionId = message.getSessionId();
            Integer senderType = message.getFrom(); // 0-用户, 1-客服
            
            // 获取此会话涉及的用户ID和客服ID
            ChatSession chatSession = null;
            try {
                // 使用Spring上下文获取ChatSessionService
                ChatSessionService chatSessionService = SpringContextUtil.getBean(ChatSessionService.class);
                if (chatSessionService != null) {
                    chatSession = chatSessionService.getSessionById(sessionId);
                }
            } catch (Exception e) {
                log.error("获取会话信息失败", e);
            }
            
            if (chatSession != null) {
                Long userId = chatSession.getUserId();
                Long agentId = chatSession.getAgentId();
                
                // 如果消息来自用户，发送给相应的客服
                if (senderType == 0 && agentId != null) {
                    Session agentSession = agentSessionMap.get(agentId.toString());
                    if (agentSession != null && agentSession.isOpen()) {
                        agentSession.getBasicRemote().sendText(messageJson);
                        log.info("消息已发送给客服: {}", agentId);
                    } else {
                        log.warn("客服 {} 不在线，消息未发送", agentId);
                    }
                }
                // 如果消息来自客服，发送给相应的用户
                else if (senderType == 1 && userId != null) {
                    Session userSession = userSessionMap.get(userId.toString());
                    if (userSession != null && userSession.isOpen()) {
                        userSession.getBasicRemote().sendText(messageJson);
                        log.info("消息已发送给用户: {}", userId);
                    } else {
                        log.warn("用户 {} 不在线，消息未发送", userId);
                    }
                }
            } else {
                log.warn("未找到会话 {}, 消息未广播", sessionId);
            }
        } catch (Exception e) {
            log.error("广播消息失败", e);
        }
    }
    
    /**
     * 获取WebSocket连接状态信息，用于诊断
     */
    public static Map<String, Object> getConnectionStatus() {
        Map<String, Object> status = new HashMap<>();
        
        // 基本计数信息
        status.put("onlineCount", onlineCount.get());
        status.put("userSessionCount", userSessionMap.size());
        status.put("agentSessionCount", agentSessionMap.size());
        
        // 在线用户信息
        List<Map<String, Object>> onlineUsers = new ArrayList<>();
        for (Map.Entry<String, Session> entry : userSessionMap.entrySet()) {
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("userId", entry.getKey());
            userInfo.put("sessionId", entry.getValue().getId());
            userInfo.put("isOpen", entry.getValue().isOpen());
            onlineUsers.add(userInfo);
        }
        status.put("onlineUsers", onlineUsers);
        
        // 在线客服信息
        List<Map<String, Object>> onlineAgents = new ArrayList<>();
        for (Map.Entry<String, Session> entry : agentSessionMap.entrySet()) {
            Map<String, Object> agentInfo = new HashMap<>();
            agentInfo.put("agentId", entry.getKey());
            agentInfo.put("sessionId", entry.getValue().getId());
            agentInfo.put("isOpen", entry.getValue().isOpen());
            onlineAgents.add(agentInfo);
        }
        status.put("onlineAgents", onlineAgents);
        
        return status;
    }
    
    /**
     * 检查指定用户是否已连接
     * @param role 角色（user或agent）
     * @param userId 用户ID
     * @return 用户是否已连接
     */
    public static boolean isUserConnected(String role, String userId) {
        if (userId == null) {
            return false;
        }
        
        if ("user".equals(role)) {
            Session session = userSessionMap.get(userId);
            return session != null && session.isOpen();
        } else if ("agent".equals(role)) {
            Session session = agentSessionMap.get(userId);
            return session != null && session.isOpen();
        }
        
        return false;
    }
    
    /**
     * 发送诊断消息给指定用户
     * @param role 角色（user或agent）
     * @param userId 用户ID
     * @return 是否成功发送
     */
    public static boolean sendDiagnosticMessage(String role, String userId) {
        try {
            if (userId == null) {
                return false;
            }
            
            // 获取用户会话
            Session session = null;
            if ("user".equals(role)) {
                session = userSessionMap.get(userId);
            } else if ("agent".equals(role)) {
                session = agentSessionMap.get(userId);
            }
            
            if (session == null || !session.isOpen()) {
                log.warn("用户{}(角色:{})不在线，无法发送诊断消息", userId, role);
                return false;
            }
            
            // 构建诊断消息
            WebSocketMessage message = new WebSocketMessage();
            message.setType(8); // 诊断消息类型
            message.setFrom(3); // 系统诊断
            message.setSenderId(0L);
            message.setReceiverId(Long.valueOf(userId));
            message.setContent("诊断消息: " + System.currentTimeMillis());
            message.setTimestamp(System.currentTimeMillis());
            message.setData(new HashMap<String, Object>() {{
                put("diagnostic", true);
                put("timestamp", System.currentTimeMillis());
            }});
            
            // 发送消息
            session.getBasicRemote().sendText(JSON.toJSONString(message));
            log.info("诊断消息已发送给{}(角色:{})", userId, role);
            return true;
        } catch (Exception e) {
            log.error("发送诊断消息失败: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 获取服务器运行时间（毫秒）
     * @return 服务运行时间
     */
    public static long getServerUptime() {
        return System.currentTimeMillis() - SERVER_START_TIME;
    }
    
    /**
     * 发送转接人工客服通知给用户
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @param agentId 客服ID（如有分配）
     * @param message 转接消息内容
     * @return 是否成功发送
     */
    public static boolean sendTransferToHumanNotification(Long sessionId, Long userId, Long agentId, String message) {
        if (userId == null || sessionId == null) {
            log.warn("发送转接通知失败：用户ID或会话ID为空");
            return false;
        }
        
        try {
            log.info("尝试发送转接人工通知 - 会话ID: {}, 用户ID: {}, 客服ID: {}", sessionId, userId, agentId);
            
            // 获取用户会话
            Session userSession = userSessionMap.get(userId.toString());
            if (userSession == null || !userSession.isOpen()) {
                log.warn("用户 {} 不在线，无法发送WebSocket转接通知", userId);
                return false;
            }
            
            // 构建通知消息
            WebSocketMessage transferMessage = new WebSocketMessage();
            transferMessage.setType(3);  // 系统通知
            transferMessage.setFrom(1);  // 来自系统/客服
            transferMessage.setSenderId(0L); // 系统发送
            transferMessage.setReceiverId(userId);
            transferMessage.setSessionId(sessionId);
            transferMessage.setContent(message != null ? message : "正在为您转接人工客服，请稍候...");
            transferMessage.setTimestamp(System.currentTimeMillis());
            
            // 添加额外数据，标识为转接通知
            Map<String, Object> data = new HashMap<>();
            data.put("transferType", "toHuman");
            data.put("agentId", agentId);
            transferMessage.setData(data);
            
            // 发送消息
            userSession.getBasicRemote().sendText(JSON.toJSONString(transferMessage));
            log.info("已通过WebSocket发送转接通知给用户: {}", userId);
            return true;
        } catch (Exception e) {
            log.error("发送转接通知失败: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 发送转接通知给客服
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @param agentId 客服ID
     * @param message 转接消息内容
     * @return 是否成功发送
     */
    public static boolean sendTransferToAgentNotification(Long sessionId, Long userId, Long agentId, String message) {
        if (agentId == null || sessionId == null) {
            log.warn("发送转接通知给客服失败：客服ID或会话ID为空");
            return false;
        }
        
        try {
            log.info("尝试发送转接通知给客服 - 会话ID: {}, 用户ID: {}, 客服ID: {}", sessionId, userId, agentId);
            
            // 获取客服会话
            Session agentSession = agentSessionMap.get(agentId.toString());
            if (agentSession == null || !agentSession.isOpen()) {
                log.warn("客服 {} 不在线，无法发送WebSocket转接通知", agentId);
                return false;
            }
            
            // 构建通知消息
            WebSocketMessage transferMessage = new WebSocketMessage();
            transferMessage.setType(3);  // 系统通知
            transferMessage.setFrom(1);  // 来自系统
            transferMessage.setSenderId(0L); // 系统发送
            transferMessage.setReceiverId(agentId);
            transferMessage.setSessionId(sessionId);
            transferMessage.setContent(message != null ? message : "用户请求转接人工客服，请及时处理");
            transferMessage.setTimestamp(System.currentTimeMillis());
            
            // 添加额外数据，标识为转接通知
            Map<String, Object> data = new HashMap<>();
            data.put("transferType", "requestAgent");
            data.put("userId", userId);
            transferMessage.setData(data);
            
            // 发送消息
            agentSession.getBasicRemote().sendText(JSON.toJSONString(transferMessage));
            log.info("已通过WebSocket发送转接通知给客服: {}", agentId);
            return true;
        } catch (Exception e) {
            log.error("发送转接通知给客服失败: " + e.getMessage(), e);
            return false;
        }
    }
    // 在类中添加工具方法
    private static String getFromDescription(int from) {
        switch (from) {
            case 0: return "用户";
            case 1: return "客服";
            case 2: return "AI";
            case 3: return "系统诊断";
            default: return "未知角色(" + from + ")";
        }
    }

    /**
     * 发送欢迎消息给用户（包含完整的消息处理流程）
     * @param userId 用户ID
     * @param userSession 用户WebSocket会话
     */
    private static void sendWelcomeMessage(Long userId, Session userSession) {
        try {
            log.info("开始发送欢迎消息给用户: {}", userId);

            // 创建欢迎消息
            WebSocketMessage welcomeMessage = new WebSocketMessage();
            welcomeMessage.setType(1); // 文本消息类型
            welcomeMessage.setFrom(2); // 来自客服/系统
            welcomeMessage.setSenderId(6L); // 系统发送者ID
            welcomeMessage.setReceiverId(userId); // 用户作为接收者
            welcomeMessage.setContent("您好，请问有什么可以帮您，可描述您想咨询的问题点击发送给我们。人工客服工作时间为09:00-21:00");
            welcomeMessage.setTimestamp(System.currentTimeMillis());

            // 尝试获取用户的最新会话ID（如果存在的话）
            Long sessionId = null;
            try {
                ChatSessionService chatSessionService = SpringContextUtil.getBean(ChatSessionService.class);
                if (chatSessionService != null) {
                    // 获取用户的会话列表，找到最新的会话
                    List<ChatSession> userSessions = chatSessionService.getSessionsByUserId(userId);
                    if (userSessions != null && !userSessions.isEmpty()) {
                        // 获取最新的会话（假设列表按时间排序，取第一个）
                        ChatSession latestSession = userSessions.get(0);
                        sessionId = latestSession.getId();
                        welcomeMessage.setSessionId(sessionId);
                        log.info("为欢迎消息设置会话ID: {}", sessionId);
                    } else {
                        log.info("用户暂无会话记录，欢迎消息将不设置sessionId");
                    }
                }
            } catch (Exception e) {
                log.warn("获取用户会话ID失败，将不设置sessionId: {}", e.getMessage());
            }

            // 保存欢迎消息到数据库
            Long messageId = null;
            try {
                if (chatMessageService != null) {
                    ChatMessage chatMessage = new ChatMessage();
                    chatMessage.setSessionId(sessionId);
                    chatMessage.setSenderType(2); // 客服发送
                    chatMessage.setContent(welcomeMessage.getContent());
                    chatMessage.setMsgType(0); // 文本消息
                    chatMessage.setIsRead(0); // 未读
                    chatMessage.setSenderId("0"); // 系统发送者

                    messageId = chatMessageService.sendMessage(chatMessage);
                    welcomeMessage.setId(messageId);
                    log.info("欢迎消息已保存到数据库，ID: {}", messageId);
                } else {
                    log.warn("ChatMessageService为空，无法保存欢迎消息到数据库");
                }
            } catch (Exception e) {
                log.error("保存欢迎消息到数据库失败: {}", e.getMessage());
            }

            // 发送欢迎消息给用户
            if (userSession.isOpen()) {
                String jsonMessage = JSON.toJSONString(welcomeMessage);
                log.info("发送欢迎消息JSON: {}", jsonMessage);
                userSession.getBasicRemote().sendText(jsonMessage);
                log.info("欢迎消息已成功发送给用户: {}", userId);
            } else {
                log.warn("用户会话已关闭，无法发送欢迎消息");
            }

        } catch (Exception e) {
            log.error("发送欢迎消息失败: {}", e.getMessage(), e);
        }
    }
}