package com.kefang.api;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * 来源统计相关API
 */
@Component
public class SourceStatisticsApi {

    private static final Logger logger = LoggerFactory.getLogger(SourceStatisticsApi.class);

//    @Value("${xd.base-url666666:http://localhost:9088}")
    @Value("${xd.base-url:https://uat.juranguanjia.com/hsapi/recovery/order/}")
    private String baseUrl;

    @Autowired
    @Qualifier("ssoRestTemplate")
    private RestTemplate restTemplate;
    
    @Autowired
    private ApiTokenHelper apiTokenHelper;
    
    /**
     * 获取微信群来源统计数据
     * 
     * @param date 日期，格式：yyyy-MM-dd
     * @param sourceList 来源列表
     * @return 统计数据JSON字符串
     */
    public String getSourceWxGroupStatistics(String date, List<String> sourceList) {
        try {
            // 构建请求URL
            String url = baseUrl + "/statistics/sourceWxGroup";

            // 创建请求头并设置SSO认证信息
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers = apiTokenHelper.setSsoAuthHeaders(headers);
            logger.info("设置SSO认证信息: {}", headers);

            // 创建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("date", date);
            requestBody.put("source", sourceList);

            logger.info("发送微信群来源统计请求: URL={}, 请求体={}", url, requestBody.toJSONString());

            // 发送POST请求
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody.toJSONString(), headers);
            String response = restTemplate.postForObject(url, requestEntity, String.class);

            // 记录响应
            logger.info("微信群来源统计任务响应: {}", response);
            
            return response;
        } catch (Exception e) {
            logger.error("获取微信群来源统计数据失败", e);
            throw new RuntimeException("获取微信群来源统计数据失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取近一个月的订单统计数据
     * 
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @param sourceList 来源列表
     * @return 统计数据JSON字符串
     */
    public String getMonthlyOrderStatistics(String startDate, String endDate, List<Integer> sourceList) {
        try {
            // 构建请求URL
            String url = baseUrl + "/statistics/monthlyOrder";

            // 创建请求头并设置SSO认证信息
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers = apiTokenHelper.setSsoAuthHeaders(headers);
            logger.info("设置SSO认证信息: {}", headers);

            // 创建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("startDate", startDate);
            requestBody.put("endDate", endDate);
            requestBody.put("sourceList", sourceList);

            logger.info("发送月度订单统计请求: URL={}, 请求体={}", url, requestBody.toJSONString());

            // 发送POST请求
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody.toJSONString(), headers);
            String response = restTemplate.postForObject(url, requestEntity, String.class);

            // 记录响应
            logger.info("月度订单统计任务响应: {}", response);
            
            return response;
        } catch (Exception e) {
            logger.error("获取月度订单统计数据失败", e);
            throw new RuntimeException("获取月度订单统计数据失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取近一个月的物流订单统计数据
     * 
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @param sourceList 来源列表
     * @return 统计数据JSON字符串
     */
    public String getExpressOrderStatistics(String startDate, String endDate, List<Integer> sourceList) {
        try {
            // 构建请求URL
            String url = baseUrl + "/statistics/expressOrder";

            // 创建请求头并设置SSO认证信息
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers = apiTokenHelper.setSsoAuthHeaders(headers);
            logger.info("设置SSO认证信息: {}", headers);

            // 创建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("startDate", startDate);
            requestBody.put("endDate", endDate);
            requestBody.put("sourceList", sourceList);

            logger.info("发送物流订单统计请求: URL={}, 请求体={}", url, requestBody.toJSONString());

            // 发送POST请求
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody.toJSONString(), headers);
            String response = restTemplate.postForObject(url, requestEntity, String.class);

            // 记录响应
            logger.info("物流订单统计任务响应: {}", response);
            
            return response;
        } catch (Exception e) {
            logger.error("获取物流订单统计数据失败", e);
            throw new RuntimeException("获取物流订单统计数据失败: " + e.getMessage(), e);
        }
    }
} 