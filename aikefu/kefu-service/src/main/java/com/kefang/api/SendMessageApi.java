package com.kefang.api;

import com.alibaba.fastjson.JSONObject;
import com.kefang.utils.HttpClientUtil;
import com.kefang.vo.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.boot.web.client.RestTemplateBuilder;

import java.util.Map;

/**
 * 消息推送API
 * 用于发送消息到微信群
 */
@Component
public class SendMessageApi {

    private static final Logger log = LoggerFactory.getLogger(SendMessageApi.class);

    @Autowired
    private HttpClientUtil httpClientUtil;

    @Autowired
    private ApiTokenHelper apiTokenHelper;

    /**
     * 基础URL，从配置中读取
     */
    @Value("${hs.app.order.base-url:https://papi.bearhome.cn}")
    private String baseUrl;

    /**
     * 推送消息到JMS服务
     *
     * @param message 要推送的消息内容
     * @return 推送结果
     */
    public Result<JSONObject> pushMessage(JSONObject message) {
        try {
            // 参数校验
            if (message == null) {
                return Result.error("消息内容不能为空");
            }

            log.info("开始推送消息到JMS服务: {}", message);

            // 设置请求URL
            String url = baseUrl + "/open/jms/push/message";

            // 获取带有SSO认证信息的请求头
            Map<String, String> headers = apiTokenHelper.getSsoApiToken();

            log.info("已获取SSO认证请求头: {}", headers);


            // 发送POST请求
            JSONObject response = httpClientUtil.post(url, message, headers);
            log.info("JMS消息推送响应: {}", response);

            // 如果  Result(code=500, message=API密钥校验错误#1, data=null, success=false)

            // 处理响应
            return handleApiResponse(response);
        } catch (Exception e) {
            log.error("推送JMS消息异常: {}", e.getMessage(), e);
            return Result.error("推送JMS消息异常: " + e.getMessage());
        }
    }

    /**
     * 处理API响应
     *
     * @param response API响应JSON对象
     * @return 统一格式的结果
     */
    private Result<JSONObject> handleApiResponse(JSONObject response) {
        if (response == null) {
            return Result.error("未收到有效响应");
        }

        Integer code = response.getInteger("code");
        String msg = response.getString("msg");

        if (code != null && code == 200) {
            return Result.success(msg != null ? msg : "操作成功", response);
        } else {
            String errorMsg = msg != null ? msg : "操作失败";
            log.error("API调用失败: {}", errorMsg);
            return Result.error(errorMsg);
        }
    }
    
    /**
     * 测试方法，用于测试JMS消息推送功能
     */
    public static void main(String[] args) {
        try {
            // 硬编码测试参数
            String baseUrl = "https://papi.bearhome.cn";
            String ssoApiUrl = "https://papi.bearhome.cn/sso/jms/api";  // 配置正确的SSO API URL
            String messageContent = "测试 - " + System.currentTimeMillis();
            
            System.out.println("开始测试JMS消息推送，使用以下配置:");
            System.out.println("基础URL: " + baseUrl);
            System.out.println("SSO API URL: " + ssoApiUrl);
            System.out.println("消息内容: " + messageContent);
            
            // 创建RestTemplate
            RestTemplate restTemplate = new RestTemplateBuilder().build();
            
            // 创建HttpClientUtil
            HttpClientUtil httpClientUtil = new HttpClientUtil();
            // 通过反射设置RestTemplate属性，因为没有setter方法
            java.lang.reflect.Field restTemplateField = HttpClientUtil.class.getDeclaredField("restTemplate");
            restTemplateField.setAccessible(true);
            restTemplateField.set(httpClientUtil, restTemplate);
            
            // 创建SsoServiceImpl
            com.kefang.service.impl.SsoServiceImpl ssoService = new com.kefang.service.impl.SsoServiceImpl();
            
            // 通过反射设置必要的属性
            java.lang.reflect.Field httpClientUtilField = com.kefang.service.impl.SsoServiceImpl.class.getDeclaredField("httpClientUtil");
            httpClientUtilField.setAccessible(true);
            httpClientUtilField.set(ssoService, httpClientUtil);
            
            java.lang.reflect.Field ssoApiUrlField = com.kefang.service.impl.SsoServiceImpl.class.getDeclaredField("ssoApiUrl");
            ssoApiUrlField.setAccessible(true);
            ssoApiUrlField.set(ssoService, ssoApiUrl);
            
            // 创建SsoTokenHelper
            ApiTokenHelper apiTokenHelper = new ApiTokenHelper();
            
            // 通过反射设置SsoService
            java.lang.reflect.Field ssoServiceField = ApiTokenHelper.class.getDeclaredField("ssoService");
            ssoServiceField.setAccessible(true);
            ssoServiceField.set(apiTokenHelper, ssoService);
            
            // 创建SendMessageApi
            SendMessageApi sendMessageApi = new SendMessageApi();
            
            // 通过反射设置必要的属性
            java.lang.reflect.Field httpClientUtilField2 = SendMessageApi.class.getDeclaredField("httpClientUtil");
            httpClientUtilField2.setAccessible(true);
            httpClientUtilField2.set(sendMessageApi, httpClientUtil);
            
            java.lang.reflect.Field ssoTokenHelperField = SendMessageApi.class.getDeclaredField("apiTokenHelper");
            ssoTokenHelperField.setAccessible(true);
            ssoTokenHelperField.set(sendMessageApi, apiTokenHelper);
            
            java.lang.reflect.Field baseUrlField = SendMessageApi.class.getDeclaredField("baseUrl");
            baseUrlField.setAccessible(true);
            baseUrlField.set(sendMessageApi, baseUrl);
            
            // 创建测试消息
            JSONObject message = new JSONObject();
            message.put("group_type", "9");
            message.put("group_type_id", "4344");
            message.put("content", messageContent);
            
            // 测试消息推送
            System.out.println("开始推送消息...");
            Result<JSONObject> result = sendMessageApi.pushMessage(message);
            
            // 输出结果
            System.out.println("推送结果:");
            System.out.println("成功: " + result.getSuccess());
            System.out.println("状态码: " + result.getCode());
            System.out.println("消息: " + result.getMessage());
            if (result.getData() != null) {
                System.out.println("数据: " + result.getData().toJSONString());
            }
        } catch (Exception e) {
            System.out.println("测试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}