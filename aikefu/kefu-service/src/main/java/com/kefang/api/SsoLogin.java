package com.kefang.api;

import com.kefang.dto.SsoUserLoginDTO;
import com.kefang.service.SsoService;
import com.kefang.service.impl.SsoServiceImpl;
import com.kefang.utils.HttpClientUtil;
import com.kefang.vo.Result;
import com.mysql.cj.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.boot.web.client.RestTemplateBuilder;

import java.util.HashMap;
import java.util.Map;

/**
 * SSO单点登录服务
 * 提供登录认证和API令牌获取功能
 */
@Component
public class SsoLogin {

    private final SsoService ssoService;
    private final ApiTokenHelper apiTokenHelper;
    
    private static final Logger logger = LoggerFactory.getLogger(SsoLogin.class);

    @Autowired
    public SsoLogin(SsoService ssoService, ApiTokenHelper apiTokenHelper) {
        this.ssoService = ssoService;
        this.apiTokenHelper = apiTokenHelper;
    }

    /**
     * SSO用户登录
     *
     * @param mobile 登录手机号
     * @param password 登录密码
     * @return 登录结果，包含用户信息
     */
    public Result<Map<String, Object>> ssoLogin(String mobile, String password) {
        // 参数校验
        if (StringUtils.isNullOrEmpty(mobile) || StringUtils.isNullOrEmpty(password)) {
            return Result.error("手机号和密码不能为空");
        }
        
        try {
            // 获取SSO API Token并尝试登录
            Result<SsoUserLoginDTO.Response> loginResult = tryLoginWithTokenRefresh(mobile, password);
            SsoUserLoginDTO.Response userLoginResponse = loginResult.getData();
            
            // 处理登录响应结果
            if (userLoginResponse != null) {
                if (userLoginResponse.getCode() == 200 && userLoginResponse.getData() != null) {
                    // 登录成功，转换并返回用户信息
                    return Result.success("登录成功", convertUserData(userLoginResponse.getData()));
                } else {
                    // 登录失败，返回错误信息
                    return Result.error(userLoginResponse.getMsg() != null ? userLoginResponse.getMsg() : "登录失败");
                }
            }
            return Result.error(loginResult.getMessage() != null ? loginResult.getMessage() : "登录请求失败，未收到有效响应");
        } catch (Exception e) {
            logger.error("远程调用SSO服务失败: {}", e.getMessage(), e);
            return Result.error("远程调用SSO服务失败: " + e.getMessage());
        }
    }
    
    /**
     * 尝试登录，如果令牌过期则自动刷新
     * 
     * @param mobile 手机号
     * @param password 密码
     * @return 登录响应结果
     */
    private Result<SsoUserLoginDTO.Response> tryLoginWithTokenRefresh(String mobile, String password) {
        try {
            // 获取SSO API Token
            Map<String, String> tokenMap = apiTokenHelper.getSsoApiToken();
            String privateKey = tokenMap.get("privateKey");
            String apiToken = tokenMap.get("apiToken");
            
            // 调用SSO API进行用户登录
            SsoUserLoginDTO.Response userLoginResponse = ssoService.userLogin(mobile, password, privateKey, apiToken);
            
            // 检查是否因为Token过期导致的失败
            if (userLoginResponse != null && userLoginResponse.getMsg() != null 
                    && userLoginResponse.getMsg().contains("API密钥校验错误")) {
                logger.info("SSO API Token 已过期，正在强制刷新...");
                
                // 强制刷新Token
                // 此处调用保留了与原代码相同的处理方式
                try {
                    tokenMap = apiTokenHelper.getSsoApiToken();
                    
                    // 重新尝试登录
                    userLoginResponse = ssoService.userLogin(
                        mobile, password, tokenMap.get("privateKey"), tokenMap.get("apiToken"));
                    
                    logger.info("Token刷新后重新登录" + (userLoginResponse.getCode() == 200 ? "成功" : "失败"));
                } catch (Exception e) {
                    logger.error("刷新Token后重新登录失败: {}", e.getMessage());
                }
            }
            
            logger.debug("SSO用户登录结果，响应数据: {}", userLoginResponse);
            return Result.success("登录处理完成", userLoginResponse);
        } catch (Exception e) {
            logger.error("登录过程异常: {}", e.getMessage(), e);
            return Result.error("登录过程异常: " + e.getMessage());
        }
    }
    
    /**
     * 将用户数据DTO转换为Map
     * 
     * @param userData 用户数据DTO
     * @return 转换后的用户信息Map
     */
    private Map<String, Object> convertUserData(SsoUserLoginDTO.Response.ResponseData userData) {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("userId", userData.getUser_id());
        userInfo.put("userName", userData.getUser_name());
        userInfo.put("name", userData.getName());
        userInfo.put("mobile", userData.getMobile());
        userInfo.put("avatar", userData.getAvatar());
        userInfo.put("token", userData.getToken());
        userInfo.put("userType", userData.getUser_type());
        userInfo.put("isTechnician", userData.getIs_technician());
        userInfo.put("isMerchant", userData.getIs_merchant());
        userInfo.put("isBearAdm", userData.getIs_bear_adm());
        return userInfo;
    }

    /**
     * 测试方法，用于测试SsoLogin的功能
     * 
     * 使用方法：
     * 1. 编译项目
     * 2. 运行此方法，传入手机号和密码及环境配置作为参数
     *    示例：java -cp target/classes:target/dependency/* com.kefang.api.SsoLogin <手机号> <密码> <ssoApiUrl> <ssoUserLoginUrl>
     */
    public static void main(String[] args) {
        // 检查参数
        if (args.length < 4) {
            System.out.println("请提供必要的参数!");
            System.out.println("用法: java -cp target/classes:target/dependency/* com.kefang.api.SsoLogin <手机号> <密码> <ssoApiUrl> <ssoUserLoginUrl>");
            return;
        }
        
        String mobile = args[0];
        String password = args[1];
        String ssoApiUrl = args[2];
        String ssoUserLoginUrl = args[3];
        
        try {
            System.out.println("开始测试SSO登录，使用以下配置:");
            System.out.println("API URL: " + ssoApiUrl);
            System.out.println("Login URL: " + ssoUserLoginUrl);
            System.out.println("手机号: " + mobile);
            
            // 创建RestTemplate
            RestTemplate restTemplate = new RestTemplateBuilder().build();
            
            // 创建HttpClientUtil
            HttpClientUtil httpClientUtil = new HttpClientUtil();
            // 通过反射设置RestTemplate属性，因为没有setter方法
            java.lang.reflect.Field restTemplateField = HttpClientUtil.class.getDeclaredField("restTemplate");
            restTemplateField.setAccessible(true);
            restTemplateField.set(httpClientUtil, restTemplate);
            
            // 创建真实的SsoServiceImpl
            SsoServiceImpl ssoService = new SsoServiceImpl();
            
            // 通过反射设置必要的属性
            java.lang.reflect.Field httpClientUtilField = SsoServiceImpl.class.getDeclaredField("httpClientUtil");
            httpClientUtilField.setAccessible(true);
            httpClientUtilField.set(ssoService, httpClientUtil);
            
            java.lang.reflect.Field ssoApiUrlField = SsoServiceImpl.class.getDeclaredField("ssoApiUrl");
            ssoApiUrlField.setAccessible(true);
            ssoApiUrlField.set(ssoService, ssoApiUrl);
            
            java.lang.reflect.Field ssoUserLoginUrlField = SsoServiceImpl.class.getDeclaredField("ssoUserLoginUrl");
            ssoUserLoginUrlField.setAccessible(true);
            ssoUserLoginUrlField.set(ssoService, ssoUserLoginUrl);
            
            // 创建ApiTokenHelper
            ApiTokenHelper apiTokenHelper = new ApiTokenHelper();
            
            // 通过反射设置必要的属性
            java.lang.reflect.Field httpClientUtilField2 = ApiTokenHelper.class.getDeclaredField("httpClientUtil");
            httpClientUtilField2.setAccessible(true);
            httpClientUtilField2.set(apiTokenHelper, httpClientUtil);
            
            java.lang.reflect.Field ssoApiUrlField2 = ApiTokenHelper.class.getDeclaredField("ssoApiUrl");
            ssoApiUrlField2.setAccessible(true);
            ssoApiUrlField2.set(apiTokenHelper, ssoApiUrl);
            
            // 创建SsoLogin实例
            SsoLogin ssoLogin = new SsoLogin(ssoService, apiTokenHelper);
            
            // 调用ssoLogin方法
            System.out.println("开始测试登录...");
            Result<Map<String, Object>> result = ssoLogin.ssoLogin(mobile, password);
            
            // 输出结果
            System.out.println("登录结果状态码: " + result.getCode());
            System.out.println("登录结果消息: " + result.getMessage());
            System.out.println("登录是否成功: " + result.getSuccess());
            
            if (result.getSuccess() && result.getData() != null) {
                System.out.println("用户信息:");
                Map<String, Object> userInfo = result.getData();
                for (Map.Entry<String, Object> entry : userInfo.entrySet()) {
                    if (!"token".equals(entry.getKey())) {  // 不输出token详情，避免泄露敏感信息
                        System.out.println(entry.getKey() + ": " + entry.getValue());
                    } else {
                        System.out.println("token: [已获取]");
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
