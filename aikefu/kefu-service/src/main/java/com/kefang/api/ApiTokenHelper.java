package com.kefang.api;

import com.kefang.dto.SsoApiTokenDTO;
import com.kefang.utils.TokenUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * SSO Token 辅助工具类
 * 提供SSO Token的获取及请求头设置功能
 */
@Component
public class ApiTokenHelper {

    private static final Logger logger = LoggerFactory.getLogger(ApiTokenHelper.class);


    public SsoApiTokenDTO getApiToken() {
        try {
            // 使用TokenUtil本地生成Token
            Map<String, String> tokenMap = TokenUtil.generatePrivateKeyAndApiToken();

            // 构造SsoApiTokenDTO对象
            SsoApiTokenDTO ssoApiTokenDTO = new SsoApiTokenDTO();
            ssoApiTokenDTO.setCode(200);

            // 构造内部Data对象
            SsoApiTokenDTO.SsoApiTokenData tokenData = new SsoApiTokenDTO.SsoApiTokenData();
            tokenData.setPrivateKey(tokenMap.get("privateKey"));
            tokenData.setApitoken(tokenMap.get("apitoken"));

            // 设置Data对象
            ssoApiTokenDTO.setData(tokenData);

            return ssoApiTokenDTO;
        } catch (Exception e) {
            // 异常处理
            logger.error("生成SSO API Token失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成SSO API Token失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取SSO API Token
     * 直接使用TokenUtil生成新的Token
     *
     * @return 包含privateKey和apiToken的Map
     */
    public Map<String, String> getSsoApiToken() {
        logger.debug("生成新的SSO API Token...");
        Map<String, String> tokenMap = TokenUtil.generatePrivateKeyAndApiToken();

        // 将apitoken的键名改为apiToken以保持接口一致性
        String apitoken = tokenMap.get("apitoken");
        tokenMap.remove("apitoken");
        tokenMap.put("apiToken", apitoken);

        return tokenMap;
    }


    /**
     * 设置带有SSO认证信息的请求头
     *
     * @param headers 原始请求头
     * @return 更新后的请求头，包含SSO认证信息
     */
    public HttpHeaders setSsoAuthHeaders(HttpHeaders headers) {
        if (headers == null) {
            headers = new HttpHeaders();
        }

        Map<String, String> tokenMap = getSsoApiToken();
        String privateKey = tokenMap.get("privateKey");
        String apiToken = tokenMap.get("apiToken");

        if (privateKey != null && apiToken != null) {
            headers.set("privateKey", privateKey);
            headers.set("apitoken", apiToken);
            logger.debug("已设置SSO认证请求头");
        } else {
            logger.warn("未能获取SSO认证信息，请求可能会被拒绝");
        }

        return headers;
    }

    /**
     * 获取SSO认证请求头Map
     * 用于需要Map形式的请求头的场景
     *
     * @return 包含SSO认证信息的Map
     */
    public Map<String, String> getSsoAuthHeadersMap() {
        Map<String, String> headers = new HashMap<>();

        Map<String, String> tokenMap = getSsoApiToken();
        String privateKey = tokenMap.get("privateKey");
        String apiToken = tokenMap.get("apiToken");

        if (privateKey != null && apiToken != null) {
            headers.put("privateKey", privateKey);
            headers.put("apitoken", apiToken);
            logger.debug("已获取SSO认证请求头Map");
        } else {
            logger.warn("未能获取SSO认证信息，请求可能会被拒绝");
        }

        return headers;
    }


} 