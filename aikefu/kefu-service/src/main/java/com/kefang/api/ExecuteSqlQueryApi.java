package com.kefang.api;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kefang.vo.SqlQueryResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;



/**
 * 执行XD服务sql
 */
@Component
public class ExecuteSqlQueryApi {

    private static final Logger logger = LoggerFactory.getLogger(ExecuteSqlQueryApi.class);

    //           @Value("${xd.base-url666666:http://localhost:9088}")
    @Value("${xd.base-url:https://uat.juranguanjia.com/hsapi/recovery/order/}")
    private String baseUrl;

    @Autowired
    @Qualifier("ssoRestTemplate")
    private RestTemplate restTemplate;
    
    @Autowired
    private ApiTokenHelper apiTokenHelper;

    public String executeSqlQueryXDResponse(String sql) {
        try {
            // 构建请求URL
            String url = baseUrl + "/statistics/executeSqlQuery";

            // 创建请求头并设置SSO认证信息
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers = apiTokenHelper.setSsoAuthHeaders(headers);
            logger.info("设置SSO认证信息: {}", headers);


            // 创建请求体，匹配远程API的SqlQueryForm结构
            JSONObject requestBody = new JSONObject();
            requestBody.put("sql", sql);

            logger.info("发送SQL查询请求: URL={}, SQL={}", url, sql);
            logger.info("请求体JSON: {}", requestBody.toJSONString());

            // 发送POST请求
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody.toJSONString(), headers);
            String response = restTemplate.postForObject(url, requestEntity, String.class);

            // 详细打印返回数据
            logger.info("SQL查询响应成功");
            logger.info("响应数据: {}", response);
            return response;

        } catch (Exception e) {
            logger.error("SQL查询执行失败: SQL={}, 错误信息={}", sql, e.getMessage(), e);
            return "";
        }

    }


    /**
     * 执行XD安全的SQL查询接口
     * 自动校验响应结果，返回包含状态码和查询数据的结果对象
     *
     * @param sql SQL查询语句
     * @return SqlQueryResult对象，包含状态码、消息、查询数据等信息
     */
    public SqlQueryResult executeSqlQueryXD(String sql) {
        try {
            // 构建请求URL
            String url = baseUrl + "/statistics/executeSqlQuery";

            // 创建请求头并设置SSO认证信息
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers = apiTokenHelper.setSsoAuthHeaders(headers);
            logger.info("设置SSO认证信息: {}", headers);



            // 创建请求体，匹配远程API的SqlQueryForm结构
            JSONObject requestBody = new JSONObject();
            requestBody.put("sql", sql);

            logger.info("发送SQL查询请求: URL={}, SQL={}", url, sql);
            logger.info("请求体JSON: {}", requestBody.toJSONString());

            // 发送POST请求
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody.toJSONString(), headers);
            String response = restTemplate.postForObject(url, requestEntity, String.class);

            // 详细打印返回数据
            logger.info("SQL查询响应成功");
            logger.info("响应数据: {}", response);

            // 解析响应并校验结果
            try {
                JSONObject responseJson = JSONObject.parseObject(response);

                // 检查HTTP状态码
                Integer statusCode = responseJson.getInteger("statusCode");
                if (statusCode == null || statusCode != 200) {
                    String errorMsg = "SQL查询失败，HTTP状态码: " + statusCode;
                    logger.error(errorMsg);
                    return SqlQueryResult.error(statusCode != null ? statusCode : 500, errorMsg);
                }

                // 获取数据部分
                JSONObject dataObj = responseJson.getJSONObject("data");
                if (dataObj == null) {
                    String errorMsg = "SQL查询响应中缺少data字段";
                    logger.error(errorMsg);
                    return SqlQueryResult.error(500, errorMsg);
                }

                // 检查查询状态
                String queryStatus = dataObj.getString("status");
                if (!"SUCCESS".equals(queryStatus)) {
                    String errorMsg = "SQL查询执行失败，状态: " + queryStatus +
                                    ", 消息: " + dataObj.getString("message");
                    logger.error(errorMsg);
                    return SqlQueryResult.error(500, errorMsg);
                }

                // 提取查询结果数据
                Object queryData = dataObj.get("data");
                Integer recordCount = dataObj.getInteger("recordCount");
                Integer executionTime = dataObj.getInteger("executionTime");

                logger.info("SQL查询成功 - 记录数: {}, 执行时间: {}ms", recordCount, executionTime);
                logger.info("查询结果: {}", queryData);

                // 将JSON数组转换为List<Map<String, Object>>
                List<Map<String, Object>> resultList = new ArrayList<>();
                if (queryData != null) {
                    if (queryData instanceof JSONArray) {
                        JSONArray jsonArray = (JSONArray) queryData;
                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject jsonObject = jsonArray.getJSONObject(i);
                            resultList.add(jsonObject.getInnerMap());
                        }
                    } else {
                        // 如果不是数组格式，尝试转换
                        JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(queryData));
                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject jsonObject = jsonArray.getJSONObject(i);
                            resultList.add(jsonObject.getInnerMap());
                        }
                    }
                }

                // 返回成功结果
                return SqlQueryResult.success(
                    resultList,
                    recordCount != null ? recordCount : resultList.size(),
                    executionTime != null ? executionTime : 0
                );

            } catch (Exception parseException) {
                logger.error("响应数据解析失败: {}", parseException.getMessage());
                logger.warn("原始响应: {}", response);
                return SqlQueryResult.error(500, "响应数据解析失败: " + parseException.getMessage());
            }
        } catch (Exception e) {
            logger.error("SQL查询执行失败: SQL={}, 错误信息={}", sql, e.getMessage(), e);
            return SqlQueryResult.error(500, "SQL查询执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行XD安全的SQL查询接口（获取原始响应）
     * 返回完整的API响应，不进行数据提取和校验
     *
     * @param sql SQL查询语句
     * @return 完整的API响应JSON字符串
     */
    public String executeSqlQueryXDRaw(String sql) {
        try {
            // 构建请求URL
            String url = baseUrl + "/statistics/executeSqlQuery";

            // 创建请求头并设置SSO认证信息
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers = apiTokenHelper.setSsoAuthHeaders(headers);
            logger.info("设置SSO认证信息: {}", headers);

            // 创建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("sql", sql);
            requestBody.put("limit", 1000);
            requestBody.put("timeout", 30);
            requestBody.put("description", "SQL查询原始响应");

            logger.info("发送SQL查询请求(原始响应): URL={}, SQL={}", url, sql);

            // 发送POST请求
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody.toJSONString(), headers);
            String response = restTemplate.postForObject(url, requestEntity, String.class);

            logger.info("SQL查询原始响应: {}", response);
            return response;

        } catch (Exception e) {
            logger.error("SQL查询执行失败: SQL={}, 错误信息={}", sql, e.getMessage(), e);
            throw new RuntimeException("SQL查询执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 测试main方法 - 用于验证SQL查询API调用
     * 运行前请确保：
     * 1. 配置文件中的xd.base-url666666正确设置
     * 2. 网络连接正常
     * 3. 远程服务正在运行
     */
    public static void main(String[] args) {
        System.out.println("=== SQL查询API测试开始 ===");

        try {
            // 手动创建测试实例（模拟Spring环境）
            ExecuteSqlQueryApi api = new ExecuteSqlQueryApi();

            // 手动设置baseUrl（实际项目中由@Value注入）
            api.baseUrl = "http://localhost:9088"; // 请根据实际情况修改

            // 创建RestTemplate（实际项目中由Spring注入）
            api.restTemplate = new org.springframework.web.client.RestTemplate();

            // 创建ApiTokenHelper（实际项目中由Spring注入）
            api.apiTokenHelper = new ApiTokenHelper();

            // 测试SQL语句 - 查询特定商户的订单统计
            String testSql = "SELECT roi.merchant_id,COUNT(*) num " +
                           "FROM recover_order ro " +
                           "JOIN recover_order_item roi " +
                           "  ON ro.id = roi.order_id " +
                           "WHERE roi.deal_type = 8 " +
                           "  AND roi.merchant_id IN (4390, 4442, 4480, 4423) " +
                           "  AND ro.status = 15 " +
                           "GROUP BY roi.merchant_id";

            System.out.println("=== 执行的SQL查询 ===");
            System.out.println(testSql);
            System.out.println("\n目标URL: " + api.baseUrl + "/statistics/executeSqlQuery");
            System.out.println("查询说明: 统计特定商户(4390,4442,4480,4423)在deal_type=8且订单状态=15的订单数量");

            // 调用测试方法（新版本 - 返回包含状态码的结果对象）
            SqlQueryResult result = api.executeSqlQueryXD(testSql);

            System.out.println("\n=== 查询结果状态 ===");
            System.out.println("执行状态: " + (result.isSuccess() ? "成功" : "失败"));
            System.out.println("状态码: " + result.getStatusCode());
            System.out.println("消息: " + result.getMessage());
            System.out.println("记录数量: " + result.getRecordCount());
            System.out.println("执行时间: " + result.getExecutionTime() + "ms");

            // 根据状态码判断是否成功
            if (result.isSuccess()) {
                System.out.println("\n=== 查询记录详情 ===");
                List<Map<String, Object>> queryRecords = result.getData();

                if (queryRecords.isEmpty()) {
                    System.out.println("未查询到任何记录");
                } else {
                    System.out.println("查询到 " + queryRecords.size() + " 条记录:");
                    for (int i = 0; i < queryRecords.size(); i++) {
                        Map<String, Object> record = queryRecords.get(i);
                        System.out.println("  记录" + (i+1) + ":");
                        for (Map.Entry<String, Object> entry : record.entrySet()) {
                            System.out.println("    " + entry.getKey() + ": " + entry.getValue());
                        }
                    }

                    // 演示如何使用查询结果
                    System.out.println("\n=== 使用示例 ===");
                    Map<String, Object> firstRecord = queryRecords.get(0);
                    System.out.println("第一条记录的merchant_id: " + firstRecord.get("merchant_id"));
                    System.out.println("第一条记录的num: " + firstRecord.get("num"));
                }
            } else {
                System.err.println("\n=== 查询失败 ===");
                System.err.println("失败原因: " + result.getMessage());
            }

            // 如果需要原始完整响应，可以调用Raw方法
            System.out.println("\n=== 获取原始完整响应（用于调试） ===");
            String rawResponse = api.executeSqlQueryXDRaw(testSql);
            System.out.println("原始响应: " + rawResponse);

        } catch (Exception e) {
            System.err.println("=== 测试失败 ===");
            System.err.println("错误信息: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("=== SQL查询API测试结束 ===");
    }


} 