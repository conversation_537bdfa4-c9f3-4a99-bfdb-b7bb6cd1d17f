package com.kefang.api;

import com.alibaba.fastjson.JSONObject;
import com.kefang.utils.HttpClientUtil;
import com.kefang.vo.Result;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 回收订单相关API操作类
 */
@Slf4j
@Component
public class HsAppOrder {

    @Autowired
    private HttpClientUtil httpClientUtil;

    @Autowired
    private SsoLogin ssoLogin;

    @Value("${hs.app.order.base-url:https://papi.bearhome.cn}")
    private String baseUrl;

    /**
     * -- GETTER --
     * 获取登录用户名
     */
    @Getter
    @Value("${login.mobile:13301103100}")
    private String mobile;

    /**
     * -- GETTER --
     * 获取登录密码
     */
    @Getter
    @Value("${login.password:jeffy28683...}")
    private String password;

    // 缓存Token及其过期时间
    private String cachedToken;
    private long tokenExpireTime;
    private static final long TOKEN_VALID_DURATION = 7200000; // token有效期默认2小时(毫秒)

    // 添加ReentrantLock用于保护token的更新操作
    private final ReentrantLock tokenLock = new ReentrantLock();

    /**
     * 获取有效的token，如果缓存的token已过期或不存在，则重新获取
     *
     * @param mobile   登录手机号
     * @param password 登录密码
     * @return token字符串，如果获取失败则返回null
     */
    private String getValidToken(String mobile, String password) {
        try {
            // 检查是否有有效的缓存token (无需加锁，仅读取)
            long currentTime = System.currentTimeMillis();
            if (cachedToken != null && !cachedToken.isEmpty() && currentTime < tokenExpireTime) {
                log.debug("使用缓存的token");
                return cachedToken;
            }

            // 需要重新获取token，使用锁保护，避免并发问题
            tokenLock.lock();
            try {
                // 双重检查，防止其他线程已经更新了token
                if (cachedToken != null && !cachedToken.isEmpty() && System.currentTimeMillis() < tokenExpireTime) {
                    log.debug("双重检查后使用缓存的token");
                    return cachedToken;
                }

                // 需要重新获取token
                log.info("Token不存在或已过期，重新获取token");
                Result<Map<String, Object>> loginResult = ssoLogin.ssoLogin(mobile, password);

                // 检查登录结果
                if (!loginResult.getSuccess() || loginResult.getCode() != 200 || loginResult.getData() == null) {
                    String errorMsg = loginResult.getMessage() != null ? loginResult.getMessage() : "登录失败";
                    log.error("登录失败: {}", errorMsg);
                    return null;
                }

                // 提取token并缓存
                Map<String, Object> userInfo = loginResult.getData();
                String token = (String) userInfo.get("token");
                if (token == null || token.isEmpty()) {
                    log.error("登录成功但未获取到token");
                    return null;
                }

                // 更新缓存
                cachedToken = token;
                tokenExpireTime = System.currentTimeMillis() + TOKEN_VALID_DURATION;
                log.info("获取新token成功: {}, 过期时间: {}", token, tokenExpireTime);

                return token;
            } finally {
                tokenLock.unlock();
            }
        } catch (Exception e) {
            log.error("获取token异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取订单信息
     *
     * @param orderNumber 订单号
     * @return 订单信息
     */
    public Result<JSONObject> getOrderInfo(String orderNumber) {
        try {
            // 参数校验
            validateParam(orderNumber, "订单号");

            // 获取有效token
            String token = getValidToken(mobile, password);
            if (token == null) {
                return Result.error("获取物流列表失败: 无法获取有效token");
            }
            log.debug("开始查询订单信息，订单号: {}", orderNumber);

            // 设置请求URL
            String url = baseUrl + "/hs/app/order/" + orderNumber + "/info";

            // 设置请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("token", token);

            // 发送GET请求
            JSONObject response = httpClientUtil.get(url, headers);
            log.debug("订单查询结果: {}", response);

            // 使用通用响应处理方法
            return handleApiResponse(response, "获取订单信息成功", "获取订单信息失败");
        } catch (IllegalArgumentException e) {
            log.warn("参数校验失败: {}", e.getMessage());
            return Result.error("参数校验失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("查询订单信息异常: {}", e.getMessage(), e);
            return Result.error("查询订单信息异常: " + e.getMessage());
        }
    }

    /**
     * 获取物流列表
     *
     * @param sourceCode 订单号
     * @param pageNo     页码
     * @param pageSize   每页记录数
     * @return 物流列表
     */
    public Result<JSONObject> getExpressList(String sourceCode, int pageNo, int pageSize) {
        try {
            // 参数校验
            validateParam(sourceCode, "订单号");
            if (pageNo <= 0) {
                throw new IllegalArgumentException("页码必须大于0");
            }
            if (pageSize <= 0) {
                throw new IllegalArgumentException("每页记录数必须大于0");
            }

            log.info("开始查询物流列表，订单号: {}", sourceCode);

            // 获取有效token
            String token = getValidToken(mobile, password);
            if (token == null) {
                return Result.error("获取物流列表失败: 无法获取有效token");
            }
            // 设置请求URL
            String url = baseUrl + "/open/express/list";

            // 设置请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("token", token);

            // 设置请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("page_no", pageNo);
            requestBody.put("page_size", pageSize);
            requestBody.put("source_code", sourceCode);

            // 发送POST请求
            JSONObject response = httpClientUtil.post(url, requestBody, headers);
            log.debug("物流列表查询结果: {}", response);

            // 使用通用响应处理方法
            return handleApiResponse(response, "获取物流列表成功", "获取物流列表失败");
        } catch (IllegalArgumentException e) {
            log.warn("参数校验失败: {}", e.getMessage());
            return Result.error("参数校验失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("查询物流列表异常: {}", e.getMessage(), e);
            return Result.error("查询物流列表异常: " + e.getMessage());
        }
    }

    /**
     * 获取物流轨迹
     *
     * @param company 物流公司代码
     * @param number  物流单号
     * @return 物流轨迹信息
     */
    public Result<JSONObject> getExpressListMap(String company, String number) {
        try {
            // 参数校验
            validateParam(company, "物流公司代码");
            validateParam(number, "物流单号");

            log.info("开始查询物流轨迹，公司: {}, 单号: {}", company, number);

            // 获取有效token
            String token = getValidToken(mobile, password);
            if (token == null) {
                return Result.error("获取物流轨迹失败: 无法获取有效token");
            }
            
            // 设置请求URL
            String url = baseUrl + "/open/express/process";

            // 设置请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("token", token);

            // 设置请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("company", company);
            requestBody.put("number", number);

            // 发送POST请求
            JSONObject response = httpClientUtil.post(url, requestBody, headers);
            log.info("物流轨迹查询结果: {}", response);

            // 使用通用响应处理方法
            return handleApiResponse(response, "获取物流轨迹成功", "获取物流轨迹失败");
        } catch (IllegalArgumentException e) {
            log.warn("参数校验失败: {}", e.getMessage());
            return Result.error("参数校验失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("查询物流轨迹异常: {}", e.getMessage(), e);
            return Result.error("查询物流轨迹异常: " + e.getMessage());
        }
    }

    /**
     * 取消物流单号
     *
     * @param company 物流公司代码
     * @param number  物流单号
     * @param remark  取消原因
     * @return 取消物流单号结果
     */
    public Result<JSONObject> cancelExpress(String company, String number, String remark) {
        try {
            // 参数校验
            validateParam(company, "物流公司代码");
            validateParam(number, "物流单号");

            log.info("开始取消物流单号，公司: {}, 单号: {}, 取消原因: {}", company, number, remark);

            // 获取有效token
            String token = getValidToken(mobile, password);

            // 设置请求URL
            String url = baseUrl + "/open/express/cancel?is_beta=1";

            // 设置请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("token", token);

            // 设置请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("company", company);
            requestBody.put("number", number);
            requestBody.put("remark", remark);

            // 发送POST请求
            JSONObject response = httpClientUtil.post(url, requestBody, headers);
            log.info("取消物流单号请求已发送");
            log.debug("取消物流单号结果: {}", response);

            // 使用通用响应处理方法
            return handleApiResponse(response, "取消物流单号成功", "取消物流单号失败");
        } catch (IllegalArgumentException e) {
            log.warn("参数校验失败: {}", e.getMessage());
            return Result.error("参数校验失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("取消物流单号异常: {}", e.getMessage(), e);
            return Result.error("取消物流单号异常: " + e.getMessage());
        }
    }

    /**
     * 取消订单
     *
     * @param orderNumber 订单号
     * @param remark      取消原因
     * @return 取消订单结果
     */
    public Result<JSONObject> closeOrder(String orderNumber, String remark) {
        try {
            // 参数校验
            validateParam(orderNumber, "订单号");
            validateParam(remark, "取消原因");

            log.info("开始取消订单，订单号: {}", orderNumber);

            // 获取有效token
            String token = getValidToken(mobile, password);

            // 设置请求URL
            String url = baseUrl + "/hs/app/order/" + orderNumber + "/close";

            // 设置请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("token", token);

            // 设置请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("sub_tag_name", "取消订单");
            requestBody.put("remark", remark);

            // 发送POST请求
            JSONObject response = httpClientUtil.post(url, requestBody, headers);
            log.info("订单取消请求已发送");
            log.debug("订单取消结果: {}", response);

            // 使用字符串响应处理方法
            return handleStringApiResponse(response, "取消订单成功", "取消订单失败");
        } catch (IllegalArgumentException e) {
            log.warn("参数校验失败: {}", e.getMessage());
            return Result.error("参数校验失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("取消订单异常: {}", e.getMessage(), e);
            return Result.error("取消订单异常: " + e.getMessage());
        }
    }

    /**
     * 处理API响应并返回结果
     *
     * @param response            API响应的JSONObject
     * @param successMessage      成功消息
     * @param defaultErrorMessage 默认错误消息
     * @return 处理后的结果
     */
    private Result<JSONObject> handleApiResponse(JSONObject response, String successMessage, String defaultErrorMessage) {
        // 解析结果
        Integer code = response.getInteger("code");
        String msg = response.getString("msg");

        if (code != null && code == 200) {
            // 请求成功
            return Result.success(successMessage, response.getJSONObject("data"));
        } else {
            // 请求失败
            String errorMsg = msg != null ? msg : defaultErrorMessage;
            return Result.error(code != null ? code : 500, errorMsg);
        }
    }

    /**
     * 处理返回字符串数据的API响应并返回结果
     *
     * @param response            API响应的JSONObject
     * @param successMessage      成功消息
     * @param defaultErrorMessage 默认错误消息
     * @return 处理后的结果，data为字符串值封装到JSONObject中
     */
    private Result<JSONObject> handleStringApiResponse(JSONObject response, String successMessage, String defaultErrorMessage) {
        // 解析结果
        Integer code = response.getInteger("code");
        String msg = response.getString("msg");

        if (code != null && code == 200) {
            // 请求成功
            // 获取字符串数据并封装到新的JSONObject中返回
            String dataStr = response.getString("data");
            JSONObject dataObj = new JSONObject();
            dataObj.put("value", dataStr);
            return Result.success(successMessage, dataObj);
        } else {
            // 请求失败
            String errorMsg = msg != null ? msg : defaultErrorMessage;
            return Result.error(code != null ? code : 500, errorMsg);
        }
    }
    /**
     * 检查参数是否为空
     *
     * @param param     参数值
     * @param paramName 参数名称
     * @throws IllegalArgumentException 如果参数为空抛出异常
     */
    private void validateParam(String param, String paramName) {
        if (param == null || param.trim().isEmpty()) {
            throw new IllegalArgumentException(paramName + " 不能为空");
        }
    }
    /**
     * 测试方法
     * <p>
     * 使用方法：
     * 1. 编译项目
     * 2. 运行此方法，传入必要的参数
     * 示例：java -cp target/classes:target/dependency/* com.kefang.api.HsAppOrder <token> <订单号> <环境baseUrl>
     */
    public static void main(String[] args) {
        try {
            // 检查参数
            if (args.length < 2) {
                System.out.println("请提供必要的参数!");
                System.out.println("用法: java -cp target/classes:target/dependency/* com.kefang.api.HsAppOrder <token> <订单号> [baseUrl]");
                return;
            }

            String token = args[0];
            String orderNumber = args[1];
            // 默认API基础URL
            String baseUrl = args.length > 2 ? args[2] : "https://papi.bearhome.cn";

            System.out.println("开始测试，使用 URL: " + baseUrl);

            // 创建RestTemplate
            org.springframework.web.client.RestTemplate restTemplate = new org.springframework.boot.web.client.RestTemplateBuilder().build();

            // 创建HttpClientUtil
            HttpClientUtil httpClientUtil = new HttpClientUtil();
            // 通过反射设置RestTemplate属性
            java.lang.reflect.Field restTemplateField = HttpClientUtil.class.getDeclaredField("restTemplate");
            restTemplateField.setAccessible(true);
            restTemplateField.set(httpClientUtil, restTemplate);

            // 创建HsAppOrder实例
            HsAppOrder hsAppOrder = new HsAppOrder();

            // 设置HttpClientUtil
            java.lang.reflect.Field httpClientUtilField = HsAppOrder.class.getDeclaredField("httpClientUtil");
            httpClientUtilField.setAccessible(true);
            httpClientUtilField.set(hsAppOrder, httpClientUtil);

            // 设置baseUrl
            java.lang.reflect.Field baseUrlField = HsAppOrder.class.getDeclaredField("baseUrl");
            baseUrlField.setAccessible(true);
            baseUrlField.set(hsAppOrder, baseUrl);

            // 测试直接查询
            System.out.println("开始测试查询订单: " + orderNumber);

            Result<JSONObject> result = hsAppOrder.getOrderInfo(orderNumber);

            System.out.println("查询结果: " + result.getCode() + " - " + result.getMessage());
            if (result.getSuccess() && result.getData() != null) {
                System.out.println("订单数据: " + result.getData());
            }

            // 测试查询物流列表
            System.out.println("\n开始测试查询相同订单的物流列表");

            Result<JSONObject> expressResult = hsAppOrder.getExpressList(orderNumber, 1, 5);

            System.out.println("物流列表查询结果: " + expressResult.getCode() + " - " + expressResult.getMessage());
            if (expressResult.getSuccess() && expressResult.getData() != null) {
                System.out.println("物流数据: " + expressResult.getData());
            }

            /*
             * 注意：getOrderInfoWithLogin和getExpressListWithLogin方法需要依赖SsoLogin和SsoService的实例
             * 在测试环境中难以创建有效的SsoLogin实例，因此这里不测试该方法
             * 在实际运行时，Spring会自动注入所需的依赖
             */
            System.out.println("\n在Spring环境中，可以使用getOrderInfoWithLogin和getExpressListWithLogin方法进行登录和查询");
        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
