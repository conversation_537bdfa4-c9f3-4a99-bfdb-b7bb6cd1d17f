package com.kefang.api;

import com.alibaba.fastjson.JSONObject;
import com.kefang.utils.HttpClientUtil;
import com.kefang.vo.Result;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 第三方订单接口封装类
 * 第三方订单号相关API操作类
 */
@Slf4j
@Component
public class ReAppOrder {

    @Autowired
    private HttpClientUtil httpClientUtil;

    /**
     * 基础URL，默认与HS API相同，从配置中读取
     */
    @Value("${hs.app.order.base-url:https://papi.bearhome.cn}")
    private String baseUrl;

    /**
     * 登录手机号，默认与HS API相同
     */
    @Getter
    @Value("${login.mobile:13301103100}")
    private String mobile;

    /**
     * 登录密码，默认与HS API相同
     */
    @Getter
    @Value("${login.password:jeffy28683...}")
    private String password;

    // 缓存Token及其过期时间
    private String cachedToken;
    private long tokenExpireTime;
    private static final long TOKEN_VALID_DURATION = 60 * 60 * 1000; // token有效期默认1小时(毫秒)

    // 添加ReentrantLock用于保护token的更新操作
    private final ReentrantLock tokenLock = new ReentrantLock();

    /**
     * 登录并获取token
     *
     * @return 登录结果
     */
    public Result<String> login() {
        try {
            // 检查是否有有效的缓存token
            long currentTime = System.currentTimeMillis();
            if (cachedToken != null && !cachedToken.isEmpty() && currentTime < tokenExpireTime) {
                log.debug("使用第三方订单号缓存的token");
                return Result.success("使用缓存token", cachedToken);
            }

            // 需要重新获取token，使用锁保护，避免并发问题
            tokenLock.lock();
            try {
                // 双重检查，防止其他线程已经更新了token
                if (cachedToken != null && !cachedToken.isEmpty() && System.currentTimeMillis() < tokenExpireTime) {
                    log.debug("第三方订单号双重检查后使用缓存的token");
                    return Result.success("使用缓存token", cachedToken);
                }

                // 需要重新获取token
                log.info("第三方订单号Token不存在或已过期，重新获取token");
                
                // 设置请求URL
                String url = baseUrl + "/sys/test/login";
                
                // 设置请求头
                Map<String, String> headers = new HashMap<>();
                headers.put("version", "1.0");
                
                // 设置请求体
                JSONObject requestBody = new JSONObject();
                requestBody.put("mobile", mobile);
                requestBody.put("password", password);
                
                // 发送POST请求
                JSONObject response = httpClientUtil.post(url, requestBody, headers);
                log.debug("第三方订单号登录响应结果: {}", response);
                
                // 解析结果
                Integer code = response.getInteger("code");
                
                if (code != null && code == 200 && response.getJSONObject("data") != null) {
                    // 提取token并缓存
                    String token = response.getJSONObject("data").getString("admToken");
                    if (token == null || token.isEmpty()) {
                        log.error("第三方订单号登录成功但未获取到token");
                        return Result.error("登录成功但未获取到token");
                    }
                    
                    // 更新缓存
                    cachedToken = token;
                    tokenExpireTime = System.currentTimeMillis() + TOKEN_VALID_DURATION;
                    log.info("第三方订单号获取新token成功: {}, 过期时间: {}", token, tokenExpireTime);
                    
                    return Result.success("登录成功", token);
                } else {
                    String errorMsg = response.getString("msg") != null ? response.getString("msg") : "登录失败";
                    log.error("第三方订单号登录失败: {}", errorMsg);
                    return Result.error("登录失败: " + errorMsg);
                }
            } finally {
                tokenLock.unlock();
            }
        } catch (Exception e) {
            log.error("第三方订单号获取token异常: {}", e.getMessage(), e);
            return Result.error("登录异常: " + e.getMessage());
        }
    }

    /**
     * 获取有效的token，如果缓存的token已过期或不存在，则重新获取
     *
     * @return token字符串，如果获取失败则返回null
     */
    private String getValidToken() {
        try {
            Result<String> loginResult = login();
            if (loginResult.getSuccess()) {
                return loginResult.getData();
            }
            return null;
        } catch (Exception e) {
            log.error("第三方订单号获取token异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取第三方订单号信息
     *
     * @param orderNumber 第三方订单号号
     * @return 订单信息
     */
    public Result<JSONObject> getOrderInfo(String orderNumber) {
        try {
            // 参数校验
            if (orderNumber == null || orderNumber.isEmpty()) {
                return Result.error("订单号不能为空");
            }
            


            log.debug("开始查询第三方订单号信息，订单号: {}", orderNumber);

            // 获取有效token
            String token = getValidToken();
            if (token == null) {
                return Result.error("获取第三方订单号信息失败: 无法获取有效token");
            }

            // 设置请求URL
            String url = baseUrl + "/hs/admin/order";

            // 设置请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("admtoken", token);

            // 设置请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("page_size", 10);
            requestBody.put("page_no", 1);
            requestBody.put("key_id", "sale_order_code");
            requestBody.put("key_code", orderNumber);

            // 发送POST请求
            JSONObject response = httpClientUtil.post(url, requestBody, headers);
            log.debug("第三方订单号查询结果: {}", response);

            // 解析结果
            Integer code = response.getInteger("code");
            if (code != null && code == 200 && response.getJSONObject("data") != null) {
                JSONObject data = response.getJSONObject("data");
                JSONObject result = new JSONObject();
                
                if (data.containsKey("list") && !data.getJSONArray("list").isEmpty()) {
                    JSONObject order = data.getJSONArray("list").getJSONObject(0);
                    String hsOrderCode = order.getString("code");
                    log.debug("第三方订单号 {} 对应的HS订单号为: {}", orderNumber, hsOrderCode);
                    
                    // 添加HS订单号到结果中
                    result.put("code", hsOrderCode);
                    result.put("re_code", orderNumber);
                    
                    // 复制订单信息到结果中
                    result.putAll(order);
                    
                    return Result.success("获取第三方订单号信息成功", result);
                } else {
                    log.error("未找到第三方订单号信息: {}", orderNumber);
                    return Result.error("未找到订单信息");
                }
            } else {
                String errorMsg = response.getString("msg") != null ? response.getString("msg") : "获取第三方订单号信息失败";
                log.error("获取第三方订单号信息失败: {}", errorMsg);
                return Result.error("获取第三方订单号信息失败: " + errorMsg);
            }
        } catch (Exception e) {
            log.error("查询第三方订单号信息异常: {}", e.getMessage(), e);
            return Result.error("查询第三方订单号信息异常: " + e.getMessage());
        }
    }


    /**
     *
     * @param keyCode 订单号
     * @param keyId 订单类型
     * @return
     */
    public Result<JSONObject> getOrderInfoNew(String keyCode,String keyId) {
        try {
            // 参数校验
            if (keyCode == null || keyCode.isEmpty()) {
                return Result.error("keyCode不能为空");
            }
            log.debug("开始查询信息，单号: {}", keyCode);

            // 获取有效token
            String token = getValidToken();
            if (token == null) {
                return Result.error("获取订单号信息失败: 无法获取有效token");
            }

            // 设置请求URL
            String url = baseUrl + "/hs/admin/order";

            // 设置请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("admtoken", token);

            // 设置请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("page_size", 10);
            requestBody.put("page_no", 1);
            requestBody.put("key_id", keyId);
            requestBody.put("key_code", keyCode);

            // 发送POST请求
            JSONObject response = httpClientUtil.post(url, requestBody, headers);
            log.debug("单号查询结果: {}", response);

            // 解析结果
            Integer code = response.getInteger("code");
            if (code != null && code == 200 && response.getJSONObject("data") != null) {
                JSONObject data = response.getJSONObject("data");
                log.info(data.toJSONString());
                JSONObject result = new JSONObject();

                if (data.containsKey("list") && !data.getJSONArray("list").isEmpty()) {
                    JSONObject order = data.getJSONArray("list").getJSONObject(0);
                    String hsOrderCode = order.getString("code");

                    // 添加HS订单号到结果中
                    result.put("code", hsOrderCode);
                    result.put("re_code", keyCode);

                    // 复制订单信息到结果中
                    result.putAll(order);

                    return Result.success("获取订单信息成功", result);
                } else {
                    log.error("未找到订单号信息: {}", keyCode);
                    return Result.error("未找到订单信息");
                }
            } else {
                String errorMsg = response.getString("msg") != null ? response.getString("msg") : "获取订单号信息失败";
                log.error("获取单号信息失败: {}", errorMsg);
                return Result.error("获取订单号信息失败: " + errorMsg);
            }
        } catch (Exception e) {
            log.error("查询订单号信息异常: {}", e.getMessage(), e);
            return Result.error("查询订单号信息异常: " + e.getMessage());
        }
    }


} 