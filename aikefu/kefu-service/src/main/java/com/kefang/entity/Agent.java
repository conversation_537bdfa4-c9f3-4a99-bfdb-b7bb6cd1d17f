package com.kefang.entity;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonIgnore;

@Data
public class Agent implements Serializable {
    private Long id;
    private String agentNo;
    private String name;
    private String password;
    private Integer status;  // 0离线 1在线
    private Integer agentType;  // 1人工客服 2AI客服 3系统管理员
    private Date createdAt;
    private String userId;  // 用户ID
    private String userName;  // 用户名
    private String avatar;  // 头像URL
    private String userType;  // 用户类型
    private Date loginAt;   // 登录时间
    
    // 非数据库字段，仅用于密码修改
    private String newPassword;
} 