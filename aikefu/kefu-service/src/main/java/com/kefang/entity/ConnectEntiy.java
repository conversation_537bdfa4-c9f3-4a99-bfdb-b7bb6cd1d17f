package com.kefang.entity;

import lombok.Data;


@Data
public class ConnectEntiy {
    private String messages; // 用户输入的文本
    private String session_id; // 会话ID 唯一的
    private String scene;  // 对话应用场景
    private String collection_name; // 知识库名称
    private String datasource; // 数据源

    /**
     * 群类型
     * "0" =>  "未分类",
     * "1" =>  "工程师",
     * "2" =>  "服务供应商",
     * "3" =>  "服务商",
     * "8" =>  "普通商家",
     * "4" =>  "慧家用户",
     * "5" =>  "耗材用户",
     * "6" =>  "小智回收",
     * "7" =>  "智能客户",
     * "8" =>  "其它商家",
     * "9" =>  "回收商家",
     */
    private String group_type; // 群类型
    private String user_type; // 用户类型
    private String user_id; // 当前帐号的意思
    private String group_type_id; // 群类型对象ID，用户ID和商家ID
    private String order_source; // 商家对应渠道

    private String userName;  // 用户名
    private String avatar;  // 头像URL
    private String isMerchant;  // 是否商家 (1:是)
    private String isTechnician;  // 是否是工程师 (1:是)
    private String channel; // 数据源下的渠道来源 如 ios Android 微信 支付宝 H5 等

    // 接收微信数据
    private String weixinDate; // 接收微信数据 用户的微信openid 微信渠道ID 微信公众号或小程序的appid

}
