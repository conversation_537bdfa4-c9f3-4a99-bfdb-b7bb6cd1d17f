package com.kefang.entity;

import lombok.Data;

/**
 * 传送带控制实体类
 * 用于接收传送带控制的相关参数
 */
@Data
public class ConveyorControlEntity {
    private String city; //  城市
    private String warehouse_name; // 仓库名称
    private String orderId;    // 订单编号
    private Double weight;     // 重量
    private String timestamp;  // 时间
    private String conveyorId; // 传送带设备编号

    
    // 备用字段
    private String remark;     // 备注信息
    private String operator;   // 操作员
    private String deviceId;   // 设备ID
} 