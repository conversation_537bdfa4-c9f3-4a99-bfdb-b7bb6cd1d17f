package com.kefang.entity;

import lombok.Data;
import java.util.Date;

@Data
public class ChatSession {
    private Long id;
    private Long userId;
    private Long agentId;
    private Integer status;  // 0排队 1人工会话中 2已关闭 3AI会话中
    private Integer currentAgentType; // 1=人工客服 2=AI客服
    private Integer transferRequested; // 0=未请求转人工 1=已请求转人工
    private Date startTime;
    private Date endTime;
    private Integer closedBy;  // 关闭方（0用户 1客服）
    
    // 新增评价相关字段
    private Integer satisfactionLevel; // 满意度评分(1-5星)
    private String feedbackContent; // 评价内容/反馈
    private Integer isSolved; // 问题是否解决(0-未解决，1-已解决)
    private Date evaluationTime; // 评价时间
    private String solutionDescription; // 客服描述的问题解决方案
    private String userSuggestion; // 用户建议内容，用于改进服务
    private Date lastActiveTime; // 最后活跃时间
    
    // 新增数据来源相关字段
    private String datasource; // 数据源
    private String collectionName; // 查询知识库集合
    private String scene; // 对话应用场景
    private String channel; // 数据源下的渠道来源 如 ios Android 微信 支付宝 H5 等
    
    // 非数据库字段
    private User user;
    private Agent agent;
} 