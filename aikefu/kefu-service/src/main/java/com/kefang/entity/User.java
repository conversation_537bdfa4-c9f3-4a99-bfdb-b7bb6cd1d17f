package com.kefang.entity;

import lombok.Data;
import java.util.Date;

@Data
public class User {
    private Long id;
    private String userId;  // SSO登入认证userId
    private String phone;
    private String password;
    private String nickname;
    private String avatar;
    private String remark;
    private String email;
    private String address;
    private Integer age;
    private String gender;
    private Integer vipLevel;
    private String datasource;  // 数据来源
    private String isMerchant;  // 是否商家 (1:是)
    private String isTechnician;  // 是否是工程师 (1:是)
    private String channel;     // 数据源的渠道
    private String extra1;
    private String extra2;
    private String extra3;
    private Date createdAt;
    private Date updatedAt;
} 