package com.kefang.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 转人工呼叫记录实体类
 */
@Data
@TableName("chat_call_manual_records")
public class ChatCallManualRecord {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 客服ID
     */
    private String agentId;

    /**
     * 关联会话ID
     */
    private Long sessionId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 接入耗时（秒），从请求到接入的时间差，NULL表示未接入
     */
    private Long responseDuration;

    /**
     * 用户请求时间
     */
    private Date requestTime;

    /**
     * 客服接入时间（NULL表示未接入）
     */
    private Date acceptTime;

    /**
     * 是否接入：0-未接入 1-已接入
     */
    private int isAccepted;
    
    /**
     * 结束人工对话时间
     */
    private Date endTime;
    
    /**
     * 人工对话耗时（秒）
     */
    private Long serviceDuration;
} 