package com.kefang.entity;

import lombok.Data;
import java.util.Date;

@Data
public class ChatMessage {
    private Long id;
    private Long sessionId;
    private String senderId;  // 发送者ID
    private Integer senderType;  // 发送者类型（0用户 1客服 2ai 3系统）
    private String content;
    private Integer msgType;  // 消息类型（0文字 1图片）
    private Integer isRead;  // 是否已读（0未读 1已读）
    private Date createdAt;
    private String scene;  // 对话应用场景
    private String datasource;  // 数据源
    private String collectionName;  // 查询知识库集合
    private String channel; // 数据源下的渠道来源 如 ios Android 微信 支付宝 H5 等

    private String senderName; // 只给前端显示
} 