package com.kefang.entity;

import lombok.Data;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 常见问题查询请求实体
 */
@Data
public class FaqRequest {
    @NotNull(message = "对话应用场景不能为空")
    @NotEmpty(message = "对话应用场景不能为空")
    private String scene;    // 对话应用场景

    @NotNull(message = "数据源不能为空")
    @NotEmpty(message = "数据源不能为空")
    private String datasource;  // 数据源

    private String collection_name; // 知识库名称
}
