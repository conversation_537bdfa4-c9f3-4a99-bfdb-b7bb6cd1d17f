package com.kefang.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kefang.utils.JwtUtil;
import com.kefang.vo.Result;
import com.kefang.vo.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 鉴权拦截器
 * 用于验证API请求的权限
 */
@Slf4j
@Component
public class AuthInterceptor implements HandlerInterceptor {

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 如果不是请求controller方法，直接通过
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        // 获取请求方法上的NoAuth注解
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        NoAuth noAuth = handlerMethod.getMethodAnnotation(NoAuth.class);
        
        // 如果有NoAuth注解，不需要验证
        if (noAuth != null) {
            return true;
        }

        // 从请求头获取token
        String token = request.getHeader("Authorization");
        
        // 如果token为空，返回未授权结果
        if (!StringUtils.hasText(token)) {
            log.warn("请求未携带token: {}", request.getRequestURI());
            returnUnauthorized(response, "请求未携带token");
            return false;
        }

        // 验证token
        if (!jwtUtil.validateToken(token)) {
            log.warn("无效的token: {}", token);
            returnUnauthorized(response, "无效的token");
            return false;
        }

        // 检查token是否过期
        if (jwtUtil.isTokenExpired(token)) {
            log.warn("token已过期: {}", token);
            returnUnauthorized(response, "token已过期");
            return false;
        }

        // 验证通过，继续处理请求
        return true;
    }

    /**
     * 返回未授权的错误信息
     */
    private void returnUnauthorized(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        Result<Object> result = Result.error(ResultCode.UNAUTHORIZED, message);
        response.getWriter().write(objectMapper.writeValueAsString(result));
    }
} 