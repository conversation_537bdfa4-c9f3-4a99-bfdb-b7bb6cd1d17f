package com.kefang.constants;

/**
 * 消息常量类
 * 统一管理消息类型和响应消息
 */
public class MessageConstants {
    
    /**
     * 消息类型
     */
    public static final String MESSAGE_TYPE_ORDER_QUERY = "order_query";       // 订单查询
    public static final String MESSAGE_TYPE_EXPRESS_QUERY = "express_query";   // 物流查询
    public static final String MESSAGE_TYPE_CANCEL_ORDER = "cancel_order";     // 取消订单
    public static final String MESSAGE_TYPE_CANCEL_EXPRESS = "cancel_express"; // 取消物流
    public static final String MESSAGE_TYPE_NORMAL = "normal";                 // 普通消息
    
    /**
     * 响应提示消息
     */
    public static final String RESPONSE_SUCCESS = "响应正常";
    public static final String RESPONSE_CANCEL_ORDER = "取消订单信息";
    public static final String RESPONSE_CANCEL_EXPRESS = "取消物流单";
    
    /**
     * 功能开发中提示
     */
    public static final String DEVELOPING_CANCEL_ORDER = "您好，取消订单功能正抓紧在开发中... ";
    public static final String DEVELOPING_CANCEL_EXPRESS = "您好，取消物流功能正在抓紧开发中... ";
    
    /**
     * 错误提示
     */
    public static final String ERROR_SESSION_ID_EMPTY = "session_id不能为空";
    public static final String ERROR_MESSAGES_EMPTY = "";
    public static final String ERROR_GET_ORDER_FAILED = "获取订单信息失败: ";
    public static final String ERROR_GET_EXPRESS_FAILED = "获取物流信息失败: ";
    public static final String ERROR_SERVICE_EXCEPTION = "服务异常";
} 