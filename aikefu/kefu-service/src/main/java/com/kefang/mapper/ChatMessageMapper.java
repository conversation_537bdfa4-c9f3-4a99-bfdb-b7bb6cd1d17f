package com.kefang.mapper;

import com.kefang.dto.ChatHistoryDTO;
import com.kefang.entity.ChatMessage;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;
import java.util.Map;
import java.util.Date;

public interface ChatMessageMapper {
    
    // 保存消息
    int insert(ChatMessage chatMessage);
    
    // 根据ID查询消息
    ChatMessage selectById(Long id);
    
    // 根据会话ID查询消息列表
    List<ChatMessage> selectBySessionId(Long sessionId);
    
    // 分页查询会话消息
    List<ChatMessage> selectPageBySessionId(@Param("sessionId") Long sessionId, 
                                         @Param("offset") Integer offset, 
                                         @Param("limit") Integer limit);
    
    // 更新消息已读状态
    int updateReadStatus(@Param("sessionId") Long sessionId, 
                      @Param("senderType") Integer senderType);
    
    // 获取未读消息数量
    int countUnreadMessages(@Param("sessionId") Long sessionId, 
                          @Param("senderType") Integer senderType);
                         
    // 查询会话最近一条消息
    ChatMessage selectLatestBySessionId(Long sessionId);
    
    // 按类型查询会话消息数量
    int countMessagesByType(@Param("sessionId") Long sessionId, 
                           @Param("msgType") Integer msgType);
    
    // 按时间范围查询会话消息
    List<ChatMessage> selectByTimeRange(@Param("sessionId") Long sessionId, 
                                      @Param("startTime") Date startTime, 
                                      @Param("endTime") Date endTime);
    
    // 按条件查询消息（支持关键词搜索和类型过滤）
    List<ChatMessage> selectMessagesByCondition(@Param("sessionId") Long sessionId,
                                             @Param("keyword") String keyword,
                                             @Param("msgType") Integer msgType,
                                             @Param("senderType") Integer senderType,
                                             @Param("startTime") Date startTime,
                                             @Param("endTime") Date endTime,
                                             @Param("offset") Integer offset,
                                             @Param("limit") Integer limit);
                                             
    // 统计条件查询的消息总数
    int countMessagesByCondition(@Param("sessionId") Long sessionId,
                               @Param("keyword") String keyword,
                               @Param("msgType") Integer msgType,
                               @Param("senderType") Integer senderType,
                               @Param("startTime") Date startTime,
                               @Param("endTime") Date endTime);

    @Select("SELECT " +
            "CASE " +
            "   WHEN sender_type = 0 THEN 'user' " +
            "   WHEN sender_type = 2 THEN 'assistant' " +
            "END as role, " +
            "content " +
            "FROM chat_message " +
            "WHERE session_id = #{sessionId} " +
            "ORDER BY created_at DESC " +
            "LIMIT 2")
    List<ChatHistoryDTO> selectBySessionIdHistory(Long sessionId);

    /**
     * 获取会话最近一条消息内容
     */
    @Select("SELECT content FROM chat_message WHERE session_id = #{sessionId} ORDER BY created_at DESC LIMIT 1")
    String getLastMessageContent(@Param("sessionId") Long sessionId);
    
    /**
     * 将会话中的所有消息标记为已读
     */
    @Select("UPDATE chat_message SET is_read = 1 WHERE session_id = #{sessionId} AND sender_type = #{senderType} AND is_read = 0")
    int markMessagesAsRead(@Param("sessionId") Long sessionId, @Param("senderType") Integer senderType);

    /**
     * 按小时统计消息数量
     */
    @Select("SELECT HOUR(created_at) AS hour, COUNT(*) AS count " +
           "FROM chat_message " +
           "WHERE DATE(created_at) = CURDATE() " +
           "GROUP BY HOUR(created_at)")
    List<Map<String, Object>> getHourlyMessageStats();
    
    /**
     * 按周统计消息数量
     */
    @Select("SELECT DAYOFWEEK(created_at) AS weekday, COUNT(*) AS count " +
           "FROM chat_message " +
           "WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY) " +
           "  AND created_at < DATE_ADD(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 7 DAY) " +
           "GROUP BY DAYOFWEEK(created_at)")
    List<Map<String, Object>> getWeeklyMessageStats();
    
    /**
     * 按月统计消息数量
     */
    @Select("SELECT DAY(created_at) AS day, COUNT(*) AS count " +
           "FROM chat_message " +
           "WHERE MONTH(created_at) = MONTH(CURDATE()) " +
           "  AND YEAR(created_at) = YEAR(CURDATE()) " +
           "GROUP BY DAY(created_at)")
    List<Map<String, Object>> getMonthlyMessageStats();
    
    /**
     * 统计今日AI回复消息数量
     */
    @Select("SELECT COUNT(*) FROM chat_message " +
           "WHERE DATE(created_at) = CURDATE() " +
           "AND sender_type = 2")
    int countTodayAIMessages();
    
    /**
     * 查询常见问题列表
     * 
     * @param scene 场景
     * @param datasource 数据源
     * @return 常见问题列表
     */
    @Select("SELECT content, scene, datasource, COUNT(1) AS num FROM chat_message " +
           "WHERE sender_type = 0 AND scene = #{scene} AND datasource = #{datasource} " +
           "GROUP BY content, scene, datasource ORDER BY num DESC LIMIT 5")
    List<Map<String, Object>> selectFaqBySceneAndDatasource(@Param("scene") String scene, @Param("datasource") String datasource);

    /**
     * 按场景统计用户问题数量
     */
    @Select("<script>" +
            "SELECT scene, COUNT(1) as count FROM chat_message " +
            "WHERE sender_type = 0 " +
            "<if test='scene != null and scene != \"\"'>AND scene = #{scene}</if> " +
            "<if test='datasource != null and datasource != \"\"'>AND datasource = #{datasource}</if> " +
            "<if test='startDate != null and startDate != \"\"'>AND DATE(created_at) &gt;= #{startDate}</if> " +
            "<if test='endDate != null and endDate != \"\"'>AND DATE(created_at) &lt;= #{endDate}</if> " +
            "GROUP BY scene ORDER BY count DESC" +
            "</script>")
    List<Map<String, Object>> getQuestionStatsByScene(@Param("scene") String scene,
                                                    @Param("datasource") String datasource,
                                                    @Param("startDate") String startDate,
                                                    @Param("endDate") String endDate);
    
    /**
     * 按数据源统计用户问题数量
     */
    @Select("<script>" +
            "SELECT datasource, COUNT(1) as count FROM chat_message " +
            "WHERE sender_type = 0 " +
            "<if test='scene != null and scene != \"\"'>AND scene = #{scene}</if> " +
            "<if test='datasource != null and datasource != \"\"'>AND datasource = #{datasource}</if> " +
            "<if test='startDate != null and startDate != \"\"'>AND DATE(created_at) &gt;= #{startDate}</if> " +
            "<if test='endDate != null and endDate != \"\"'>AND DATE(created_at) &lt;= #{endDate}</if> " +
            "GROUP BY datasource ORDER BY count DESC" +
            "</script>")
    List<Map<String, Object>> getQuestionStatsByDatasource(@Param("scene") String scene,
                                                         @Param("datasource") String datasource,
                                                         @Param("startDate") String startDate,
                                                         @Param("endDate") String endDate);
    
    /**
     * 统计用户问题总量
     */
    @Select("<script>" +
            "SELECT COUNT(1) FROM chat_message " +
            "WHERE sender_type = 0 " +
            "<if test='scene != null and scene != \"\"'>AND scene = #{scene}</if> " +
            "<if test='datasource != null and datasource != \"\"'>AND datasource = #{datasource}</if> " +
            "<if test='startDate != null and startDate != \"\"'>AND DATE(created_at) &gt;= #{startDate}</if> " +
            "<if test='endDate != null and endDate != \"\"'>AND DATE(created_at) &lt;= #{endDate}</if> " +
            "</script>")
    Integer countTotalQuestions(@Param("scene") String scene,
                              @Param("datasource") String datasource,
                              @Param("startDate") String startDate,
                              @Param("endDate") String endDate);
    
    /**
     * 统计独立问题数量（去重）
     */
    @Select("<script>" +
            "SELECT COUNT(DISTINCT content) FROM chat_message " +
            "WHERE sender_type = 0 " +
            "<if test='scene != null and scene != \"\"'>AND scene = #{scene}</if> " +
            "<if test='datasource != null and datasource != \"\"'>AND datasource = #{datasource}</if> " +
            "<if test='startDate != null and startDate != \"\"'>AND DATE(created_at) &gt;= #{startDate}</if> " +
            "<if test='endDate != null and endDate != \"\"'>AND DATE(created_at) &lt;= #{endDate}</if> " +
            "</script>")
    Integer countUniqueQuestions(@Param("scene") String scene,
                                @Param("datasource") String datasource,
                                @Param("startDate") String startDate,
                                @Param("endDate") String endDate);
    
    /**
     * 获取热门问题列表
     */
    @Select("<script>" +
            "SELECT content, scene, datasource, COUNT(1) AS num FROM chat_message " +
            "WHERE sender_type = 0 " +
            "<if test='scene != null and scene != \"\"'>AND scene = #{scene}</if> " +
            "<if test='datasource != null and datasource != \"\"'>AND datasource = #{datasource}</if> " +
            "<if test='startDate != null'>AND created_at &gt;= #{startDate}</if> " +
            "<if test='endDate != null'>AND created_at &lt;= #{endDate}</if> " +
            "GROUP BY content, scene, datasource ORDER BY num DESC LIMIT #{limit}" +
            "</script>")
    List<Map<String, Object>> getHotQuestions(@Param("scene") String scene,
                                           @Param("datasource") String datasource,
                                           @Param("startDate") Date startDate,
                                           @Param("endDate") Date endDate,
                                           @Param("limit") Integer limit);
    
    /**
     * 按天统计问题趋势
     */
    @Select("<script>" +
            "SELECT DATE_FORMAT(created_at, '%Y-%m-%d') as date, COUNT(1) as count " +
            "FROM chat_message " +
            "WHERE sender_type = 0 " +
            "<if test='scene != null and scene != \"\"'>AND scene = #{scene}</if> " +
            "<if test='datasource != null and datasource != \"\"'>AND datasource = #{datasource}</if> " +
            "<if test='startDate != null and startDate != \"\"'>AND DATE(created_at) &gt;= #{startDate}</if> " +
            "<if test='endDate != null and endDate != \"\"'>AND DATE(created_at) &lt;= #{endDate}</if> " +
            "GROUP BY DATE_FORMAT(created_at, '%Y-%m-%d') " +
            "ORDER BY date ASC" +
            "</script>")
    List<Map<String, Object>> getDailyQuestionTrend(@Param("scene") String scene,
                                                  @Param("datasource") String datasource,
                                                  @Param("startDate") String startDate,
                                                  @Param("endDate") String endDate);
    
    /**
     * 按周统计问题趋势
     */
    @Select("<script>" +
            "SELECT CONCAT(YEAR(created_at), '-', WEEK(created_at)) as week, " +
            "MIN(DATE_FORMAT(created_at, '%Y-%m-%d')) as start_date, " +
            "COUNT(1) as count " +
            "FROM chat_message " +
            "WHERE sender_type = 0 " +
            "<if test='scene != null and scene != \"\"'>AND scene = #{scene}</if> " +
            "<if test='datasource != null and datasource != \"\"'>AND datasource = #{datasource}</if> " +
            "<if test='startDate != null and startDate != \"\"'>AND DATE(created_at) &gt;= #{startDate}</if> " +
            "<if test='endDate != null and endDate != \"\"'>AND DATE(created_at) &lt;= #{endDate}</if> " +
            "GROUP BY CONCAT(YEAR(created_at), '-', WEEK(created_at)) " +
            "ORDER BY week ASC" +
            "</script>")
    List<Map<String, Object>> getWeeklyQuestionTrend(@Param("scene") String scene,
                                                   @Param("datasource") String datasource,
                                                   @Param("startDate") String startDate,
                                                   @Param("endDate") String endDate);
    
    /**
     * 按月统计问题趋势
     */
    @Select("<script>" +
            "SELECT DATE_FORMAT(created_at, '%Y-%m') as month, COUNT(1) as count " +
            "FROM chat_message " +
            "WHERE sender_type = 0 " +
            "<if test='scene != null and scene != \"\"'>AND scene = #{scene}</if> " +
            "<if test='datasource != null and datasource != \"\"'>AND datasource = #{datasource}</if> " +
            "<if test='startDate != null and startDate != \"\"'>AND DATE(created_at) &gt;= #{startDate}</if> " +
            "<if test='endDate != null and endDate != \"\"'>AND DATE(created_at) &lt;= #{endDate}</if> " +
            "GROUP BY DATE_FORMAT(created_at, '%Y-%m') " +
            "ORDER BY month ASC" +
            "</script>")
    List<Map<String, Object>> getMonthlyQuestionTrend(@Param("scene") String scene,
                                                    @Param("datasource") String datasource,
                                                    @Param("startDate") String startDate,
                                                    @Param("endDate") String endDate);
    
    /**
     * 获取内容关键词统计
     */
    @Select("<script>" +
            "SELECT content keyword, COUNT(1) count " +
            "FROM chat_message " +
            "WHERE sender_type = 0  and msg_type = 0" +
            "<if test='scene != null and scene != \"\"'>AND scene = #{scene}</if> " +
            "<if test='datasource != null and datasource != \"\"'>AND datasource = #{datasource}</if> " +
            "<if test='startDate != null and startDate != \"\"'>AND DATE(created_at) &gt;= #{startDate}</if> " +
            "<if test='endDate != null and endDate != \"\"'>AND DATE(created_at) &lt;= #{endDate}</if> " +
            "GROUP BY keyword " +
            "ORDER BY count DESC " +
            "LIMIT #{limit}" +
            "</script>")
    List<Map<String, Object>> getContentKeywordStats(@Param("scene") String scene,
                                                  @Param("datasource") String datasource,
                                                  @Param("startDate") String startDate,
                                                  @Param("endDate") String endDate,
                                                  @Param("limit") Integer limit);

    /**
     * 获取最近的用户文本消息
     * 
     * @param limit 限制条数
     * @return 用户消息列表
     */
    @Select("SELECT content, created_at, session_id FROM chat_message WHERE sender_type = 0 AND msg_type = 0 ORDER BY created_at DESC LIMIT #{limit}")
    List<Map<String, Object>> getRecentUserMessages(@Param("limit") Integer limit);
    
    /**
     * 统计指定客服在时间范围内的消息数量
     * 
     * @param agentId 客服ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 消息数量
     */
    @Select("SELECT COUNT(*) FROM chat_message WHERE sender_id = #{agentId} AND sender_type = 1 AND created_at BETWEEN #{startTime} AND #{endTime}")
    int countAgentMessages(@Param("agentId") String agentId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
} 