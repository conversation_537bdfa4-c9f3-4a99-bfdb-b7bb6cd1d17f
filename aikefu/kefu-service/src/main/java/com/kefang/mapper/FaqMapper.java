package com.kefang.mapper;

import com.kefang.entity.Faq;
import java.util.List;

public interface FaqMapper {
    
    // 添加常见问题
    int insert(Faq faq);
    
    // 更新常见问题
    int update(Faq faq);
    
    // 删除常见问题
    int deleteById(Long id);
    
    // 根据ID查询常见问题
    Faq selectById(Long id);
    
    // 根据分类查询常见问题列表
    List<Faq> selectByCategory(String category);
    
    // 查询所有常见问题
    List<Faq> selectAll();
} 