package com.kefang.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kefang.entity.ChatCallManualRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Date;

/**
 * 人工呼叫记录Mapper
 */
@Mapper
public interface ChatCallManualRecordMapper extends BaseMapper<ChatCallManualRecord> {

    /**
     * 保存呼叫记录
     */
    int insert(ChatCallManualRecord record);

    /**
     * 根据ID查询呼叫记录
     */
    ChatCallManualRecord selectById(Long id);

    /**
     * 更新呼叫记录
     */
    int update(ChatCallManualRecord record);

    /**
     * 更新呼叫记录为已接入状态
     */
    int updateAcceptStatus(@Param("id") Long id, @Param("acceptTime") Date acceptTime, @Param("responseDuration") Long responseDuration);

    /**
     * 根据用户ID查询呼叫记录
     */
    List<ChatCallManualRecord> selectByUserId(String userId);

    /**
     * 根据客服ID查询呼叫记录
     */
    List<ChatCallManualRecord> selectByAgentId(String agentId);

    /**
     * 根据用户ID和客服ID查询呼叫记录
     */
    List<ChatCallManualRecord> selectByAgentIdAndUserId(@Param("userId") String userId,
                                                        @Param("agentId") String agentId,
                                                        @Param("sessionId") Long sessionId);

    /**
     * 根据sessionId查询呼叫记录
     */
    List<ChatCallManualRecord> selectBysessionId(Long sessionId);

    /**
     * 根据时间范围查询呼叫记录
     */
    List<ChatCallManualRecord> selectByTimeRange(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询未接入的呼叫记录
     */
    List<ChatCallManualRecord> selectUnaccepted();

    /**
     * 查询所有呼叫记录
     */
    List<ChatCallManualRecord> selectAll();

    /**
     * 统计特定时间段内的呼叫量
     */
    int countByTimeRange(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 统计特定时间段内已接入的呼叫量
     */
    int countAcceptedByTimeRange(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 统计平均响应时间（单位：秒）
     */
    Double avgResponseDuration(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
} 