package com.kefang.mapper;

import com.kefang.entity.Agent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface AgentMapper {
    
    // 根据ID查询客服
    Agent selectById(Long id);
    
    // 根据工号查询客服
    Agent selectByAgentNo(String agentNo);
    
    // 添加客服
    int insert(Agent agent);
    
    // 更新客服信息
    int update(Agent agent);
    
    // 更新客服状态
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);
    
    // 更新客服登录时间
    int updateLoginTime(@Param("id") Long id);
    
    // 查询所有客服
    List<Agent> selectAll();
    
    // 查询所有在线客服
    List<Agent> selectOnlineAgents();
    
    // 根据客服类型查询客服 (1=人工, 2=AI)
    Agent findByAgentType(Integer agentType);
    
    // 查找可用的人工客服
    Agent findAvailableHumanAgent();
} 