package com.kefang.mapper;

import com.kefang.entity.Evaluation;
import java.util.List;

public interface EvaluationMapper {
    
    // 添加评价
    int insert(Evaluation evaluation);
    
    // 根据ID查询评价
    Evaluation selectById(Long id);
    
    // 根据会话ID查询评价
    Evaluation selectBySessionId(Long sessionId);
    
    // 根据用户ID查询评价列表
    List<Evaluation> selectByUserId(Long userId);
    
    // 查询所有评价
    List<Evaluation> selectAll();
} 