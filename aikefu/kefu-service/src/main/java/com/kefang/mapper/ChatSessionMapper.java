package com.kefang.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kefang.entity.ChatSession;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * 聊天会话Mapper
 */
@Mapper
public interface ChatSessionMapper extends BaseMapper<ChatSession> {
    
    /**
     * 根据ID查询会话
     */
    ChatSession selectById(Long id);
    
    /**
     * 根据用户ID查询会话列表
     */
    List<ChatSession> selectByUserId(Long userId);
    
    /**
     * 根据客服ID查询会话列表
     */
    List<ChatSession> selectByAgentId(Long agentId);
    
    /**
     * 查询客服正在处理的会话
     */
    List<ChatSession> selectActiveByAgentId(Long agentId);
    
    /**
     * 查询等待分配客服的会话
     */
    List<ChatSession> selectQueueSessions();
    
    /**
     * 查询已解决的会话列表
     */
    List<ChatSession> selectSolvedSessions();
    
    /**
     * 按满意度等级查询会话列表
     */
    List<ChatSession> selectBySatisfactionLevel(Integer satisfactionLevel);
    
    /**
     * 根据多个条件过滤查询会话列表
     * 支持客服ID、日期范围、客服类型、会话状态、问题解决状态、满意度等级等条件
     */
    List<ChatSession> selectByFilters(Map<String, Object> params);
    
    /**
     * 根据多个条件统计会话数量
     * 支持客服ID、日期范围、客服类型、会话状态、问题解决状态、满意度等级等条件
     */
    int countByFilters(Map<String, Object> params);
    
    /**
     * 插入会话
     */
    int insert(ChatSession chatSession);
    
    /**
     * 更新会话
     */
    int update(ChatSession chatSession);
    
    /**
     * 关闭会话
     */
    int closeSession(@Param("id") Long id, @Param("closedBy") Integer closedBy);
    
    /**
     * 分配客服
     */
    int assignAgent(@Param("id") Long id, @Param("agentId") Long agentId);
    
    /**
     * 添加评价
     */
    int addEvaluation(@Param("id") Long id, @Param("satisfactionLevel") Integer satisfactionLevel, 
                      @Param("feedbackContent") String feedbackContent, @Param("isSolved") Integer isSolved,
                      @Param("userSuggestion") String userSuggestion);
    
    /**
     * 添加解决方案描述
     */
    int addSolutionDescription(@Param("id") Long id, @Param("solutionDescription") String solutionDescription,
                               @Param("isSolved") Integer isSolved);

    List<ChatSession> selectList(QueryWrapper<ChatSession> chatSessionQueryWrapper);

    /**
     * 更新会话
     */
    int updateById(ChatSession session);
    
    /**
     * 获取等待中的会话
     */
    @Select("SELECT * FROM chat_session WHERE status = 0 ORDER BY start_time ASC")
    List<ChatSession> getWaitingSessions();
    
    /**
     * 统计今日对话数
     */
    @Select("SELECT COUNT(*) FROM chat_message WHERE DATE(created_at) = CURDATE()")
    int countTodaySessions();
    
    /**
     * 统计昨日对话数
     */
    @Select("SELECT COUNT(*) FROM chat_message WHERE DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)")
    int countYesterdaySessions();
    
    /**
     * 统计待处理会话数
     */
    @Select("SELECT COUNT(*) FROM chat_session WHERE status = 0")
    int countWaitingSessions();
    
    /**
     * 统计今日已解决会话数
     */
    @Select("SELECT COUNT(*) FROM chat_session WHERE is_solved = 1 AND DATE(last_active_time) = CURDATE()")
    int countTodayResolvedSessions();
    
    /**
     * 统计昨日已解决会话数
     */
    @Select("SELECT COUNT(*) FROM chat_session WHERE is_solved = 1 AND DATE(evaluation_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)")
    int countYesterdayResolvedSessions();
    
    /**
     * 获取平均满意度
     */
    @Select("SELECT IFNULL(AVG(satisfaction_level), 0) FROM chat_session WHERE satisfaction_level IS NOT NULL AND DATE(evaluation_time) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)")
    double getAverageSatisfaction();
    
    /**
     * 获取按小时统计的会话数据
     */
    @Select("SELECT HOUR(start_time) as hour, COUNT(*) as count FROM chat_session WHERE DATE(start_time) = CURDATE() GROUP BY HOUR(start_time)")
    List<Map<String, Object>> getHourlySessionStats();
    
    /**
     * 获取按周统计的会话数据
     */
    @Select("SELECT DAYOFWEEK(start_time) as weekday, COUNT(*) as count FROM chat_session WHERE start_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) GROUP BY DAYOFWEEK(start_time)")
    List<Map<String, Object>> getWeeklySessionStats();
    
    /**
     * 获取按月统计的会话数据
     */
    @Select("SELECT DAY(start_time) as day, COUNT(*) as count FROM chat_session WHERE MONTH(start_time) = MONTH(CURDATE()) AND YEAR(start_time) = YEAR(CURDATE()) GROUP BY DAY(start_time)")
    List<Map<String, Object>> getMonthlySessionStats();
    
    /**
     * 获取按小时统计的满意度数据
     */
    @Select("SELECT HOUR(evaluation_time) as hour, AVG(satisfaction_level) as avg_satisfaction FROM chat_session WHERE DATE(evaluation_time) = CURDATE() AND satisfaction_level IS NOT NULL GROUP BY HOUR(evaluation_time)")
    List<Map<String, Object>> getHourlySatisfactionStats();
    
    /**
     * 获取按周统计的满意度数据
     */
    @Select("SELECT DAYOFWEEK(evaluation_time) as weekday, AVG(satisfaction_level) as avg_satisfaction FROM chat_session WHERE evaluation_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND satisfaction_level IS NOT NULL GROUP BY DAYOFWEEK(evaluation_time)")
    List<Map<String, Object>> getWeeklySatisfactionStats();
    
    /**
     * 获取按月统计的满意度数据
     */
    @Select("SELECT DAY(evaluation_time) as day, AVG(satisfaction_level) as avg_satisfaction FROM chat_session WHERE MONTH(evaluation_time) = MONTH(CURDATE()) AND YEAR(evaluation_time) = YEAR(CURDATE()) AND satisfaction_level IS NOT NULL GROUP BY DAY(evaluation_time)")
    List<Map<String, Object>> getMonthlySatisfactionStats();

    int countByFiltersList(Map<String, Object> params);

    List<ChatSession> selectByFiltersList(Map<String, Object> params);

    /**
     * 根据过滤条件查询会话
     */
    List<ChatSession> getSessionsByFilters(Map<String, Object> params);
    
    /**
     * 获取消息发送者类型的统计数据
     */
    List<Map<String, Object>> getMessageStatsBySenderType(Map<String, Object> params);
    
    /**
     * 获取消息数据来源的统计数据
     */
    List<Map<String, Object>> getMessageStatsByDatasource(Map<String, Object> params);

    /**
     * 获取数据源下拉选项
     */
    List<Map<String, Object>> getDatasourceOptions();
    
    /**
     * 获取知识库集合下拉选项
     */
    List<Map<String, Object>> getCollectionOptions();
    
    /**
     * 获取对话场景下拉选项
     */
    List<Map<String, Object>> getSceneOptions();
}