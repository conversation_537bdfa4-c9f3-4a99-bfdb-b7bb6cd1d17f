package com.kefang.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * FAQ聚类响应DTO
 */
public class FAQClusterResponseDto {
    private List<FAQItemDto> results;
    
    @JsonProperty("time_cost")
    private float timeCost;

    public List<FAQItemDto> getResults() {
        return results;
    }

    public void setResults(List<FAQItemDto> results) {
        this.results = results;
    }

    public float getTimeCost() {
        return timeCost;
    }

    public void setTimeCost(float timeCost) {
        this.timeCost = timeCost;
    }
} 