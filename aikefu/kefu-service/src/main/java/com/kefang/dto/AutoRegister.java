package com.kefang.dto;

import lombok.Data;

import java.util.Date;
@Data
public class AutoRegister {
    private String userId;  // SSO登入认证userId
    private String phone; //
    private String password; // 密码
    private String nickname; // 昵称
    private String avatar; // 头像
    private String remark; // 备注
    private String email; // 邮箱
    private String address; // 地址
    private Integer age; // 年龄
    private String gender; // 性别
    private Integer vipLevel; // vip等级
    private String datasource;  // 数据来源
    private String isMerchant;  // 是否商家 (1:是)
    private String isTechnician;  // 是否是工程师 (1:是)
    private String channel;     // 数据源的渠道

}
