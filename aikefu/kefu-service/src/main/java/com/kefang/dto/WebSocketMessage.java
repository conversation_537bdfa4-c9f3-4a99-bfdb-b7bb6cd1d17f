package com.kefang.dto;

import lombok.Data;
import java.util.Map;

@Data
public class WebSocketMessage {
    /**
     * 消息ID
     */
    private Long id;
    
    /**
     * 消息类型
     * 1. 文本消息
     * 2. 图片消息
     * 3. 系统通知
     * 4. 用户上线通知
     * 5. 用户下线通知
     * 6. 心跳消息
     * 7. 连接确认消息
     * 8. 诊断消息
     */
    private Integer type;
    
    /**
     * 消息来源: 0-用户, 1-客服, 2-ai, 3-系统诊断
     */
    private Integer from;
    
    /**
     * 发送者ID
     */
    private Long senderId;
    
    /**
     * 接收者ID
     */
    private Long receiverId;
    
    /**
     * 会话ID
     */
    private Long sessionId;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 发送时间
     */
    private Long timestamp;
    /**
     * 数据源下的渠道来源 如 ios Android 微信 支付宝 H5 等
     */
    private String channel;
    
    /**
     * 额外数据，用于存储消息相关的非标准信息
     * 例如诊断数据、心跳信息等
     */
    private Map<String, Object> data;
    private String scene;  // 对话应用场景
    private String datasource;  // 数据源
    private String collectionName;  // 查询知识库集合
} 