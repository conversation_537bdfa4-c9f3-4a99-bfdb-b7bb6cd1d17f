package com.kefang.dto;

import java.util.List;

/**
 * FAQ聚类请求DTO
 */
public class FAQClusterRequestDto {
    private List<FAQItemDto> data;
    
    /**
     * 已废弃：聚类数量参数不再使用，聚类数量现在根据similarity_threshold自动确定
     * 保留此字段仅为了保持API兼容性
     */
    @Deprecated
    private int n_clusters = 5;
    
    /**
     * 相似度阈值，用于确定是否将问题归为同一类
     * 值越高，聚类要求越严格，聚类数量可能越多
     * 值越低，聚类越宽松，聚类数量可能越少
     */
    private double similarity_threshold = 0.85;

    public List<FAQItemDto> getData() {
        return data;
    }

    public void setData(List<FAQItemDto> data) {
        this.data = data;
    }

    /**
     * 获取聚类数量（已废弃）
     * @return 聚类数量
     * @deprecated 聚类数量现在根据相似度阈值自动确定
     */
    @Deprecated
    public int getN_clusters() {
        return n_clusters;
    }

    /**
     * 设置聚类数量（已废弃）
     * @param n_clusters 聚类数量
     * @deprecated 聚类数量现在根据相似度阈值自动确定
     */
    @Deprecated
    public void setN_clusters(int n_clusters) {
        this.n_clusters = n_clusters;
    }

    public double getSimilarity_threshold() {
        return similarity_threshold;
    }

    public void setSimilarity_threshold(double similarity_threshold) {
        this.similarity_threshold = similarity_threshold;
    }
} 