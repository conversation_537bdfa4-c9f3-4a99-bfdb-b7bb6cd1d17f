package com.kefang.dto;

/**
 * FAQ项DTO，用于表示单个FAQ项
 */
public class FAQItemDto {
    private int count;
    private String keyword;

    // 默认构造函数
    public FAQItemDto() {
    }

    // 带参数的构造函数
    public FAQItemDto(int count, String keyword) {
        this.count = count;
        this.keyword = keyword;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
} 