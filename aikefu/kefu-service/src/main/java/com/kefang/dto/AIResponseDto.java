package com.kefang.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * AI响应数据传输对象
 */
public class AIResponseDto {
    private String response;
    
    @JsonProperty("time_cost")
    private float timeCost;

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public float getTimeCost() {
        return timeCost;
    }

    public void setTimeCost(float timeCost) {
        this.timeCost = timeCost;
    }
} 