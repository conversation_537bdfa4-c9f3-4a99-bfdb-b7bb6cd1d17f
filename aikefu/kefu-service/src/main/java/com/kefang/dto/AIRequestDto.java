package com.kefang.dto;

import java.util.List;
import java.util.Map;

/**
 * AI请求数据传输对象
 */
public class AIRequestDto {
    private String messages;
    private List<Map<String, String>> history;
    private String model;
    private boolean stream;
    private String collection_name; // 指定从哪个集合检索

    public String getCollection_name() {
        return collection_name;
    }

    public void setCollection_name(String collection_name) {
        this.collection_name = collection_name;
    }

    public String getMessages() {
        return messages;
    }

    public void setMessages(String messages) {
        this.messages = messages;
    }

    public List<Map<String, String>> getHistory() {
        return history;
    }

    public void setHistory(List<Map<String, String>> history) {
        this.history = history;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public boolean isStream() {
        return stream;
    }

    public void setStream(boolean stream) {
        this.stream = stream;
    }
} 