package com.kefang.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 文件上传结果DTO
 * 封装文件上传成功后的相关信息
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileUploadResult {
    
    /**
     * 原始文件名
     */
    private String originalFileName;
    
    /**
     * 存储在OSS中的文件名（包含路径）
     */
    private String fileName;
    
    /**
     * 文件完整访问URL
     */
    private String fileUrl;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 文件类型/扩展名
     */
    private String fileType;
    
    /**
     * 文件MIME类型
     */
    private String contentType;
    
    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;
    
    /**
     * 文件在OSS中的存储路径
     */
    private String ossPath;
    
    /**
     * 文件MD5值（可选）
     */
    private String md5;
}
