package com.kefang.dto;

import lombok.Data;

/**
 * SSO 用户登录请求DTO
 */
@Data
public class SsoUserLoginDTO {
    // 请求参数
    private String mobile;
    private String password;
    
    // 响应数据
    @Data
    public static class Response {
        private Integer code;
        private ResponseData data;
        private String msg;
        
        @Data
        public static class ResponseData {
            private String is_technician; // 是否是工程师 (1:是)
            private String last_login_time; // 最后登录时间
            private String user_name; // 用户名
            private String is_bear_adm; // 是否是管理员 (1:是)
            private String mobile; // 手机号
            private String b_token; // 业务token
            private String avatar; // 头像URL
            private String is_merchant; // 是否是商户 (1:是)
            private String token; // 登录token
            private String update_time; // 更新时间
            private String no_password; // 是否需要密码
            private String user_type; // 用户类型
            private String user_id; // 用户ID
            private String name; // 用户姓名
        }
    }
} 