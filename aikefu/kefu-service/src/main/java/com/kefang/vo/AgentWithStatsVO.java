package com.kefang.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客服信息与统计数据VO
 */
@Data
public class AgentWithStatsVO {

    /**
     * 客服ID
     */
    private Long id;

    /**
     * 客服工号
     */
    private String agentNo;

    /**
     * 客服姓名
     */
    private String name;

    /**
     * 在线状态: 0-离线 1-在线
     */
    private Integer status;

    /**
     * 客服类型: 1-人工客服 2-AI客服 3-系统管理员
     */
    private Integer agentType;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 当日接入会话数
     */
    private Integer todaySessionCount;

    /**
     * 历史接入会话数
     */
    private Integer totalSessionCount;

    /**
     * 平均响应时间(秒)
     */
    private Integer avgResponseTime;

    /**
     * 平均对话时间(秒)
     */
    private Integer avgChatDuration;

    /**
     * 当日回答数
     */
    private Integer todayResponseCount;
    
    /**
     * 当日平均响应时间(秒)
     */
    private Integer todayAvgResponseTime;
    
    /**
     * 当日平均对话时间(秒)
     */
    private Integer todayAvgChatDuration;
    
    /**
     * 总回答数
     */
    private Integer totalResponseCount;
} 