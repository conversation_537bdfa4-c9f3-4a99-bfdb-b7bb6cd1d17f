package com.kefang.vo;

import lombok.Data;
import java.util.List;

/**
 * 客服统计数据VO
 */
@Data
public class AgentStatsVO {

    /**
     * 日期，格式：yyyy-MM-dd
     */
    private String date;

    /**
     * 会话数量
     */
    private Integer sessionCount;

    /**
     * 平均响应时间(秒)
     */
    private Integer avgResponseTime;

    /**
     * 平均对话时间(秒)
     */
    private Integer avgChatDuration;

    /**
     * 回答数
     */
    private Integer responseCount;
    
    /**
     * 会话ID列表，按最近接入时间降序排序
     */
    private List<Long> recentSessionIds;
} 