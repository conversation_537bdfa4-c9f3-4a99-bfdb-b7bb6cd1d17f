package com.kefang.vo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * SQL查询结果封装类
 * 用于封装SQL查询的执行状态、数据和统计信息
 */
public class SqlQueryResult {
    private boolean success;           // 是否成功
    private int statusCode;           // 状态码
    private String message;           // 消息
    private List<Map<String, Object>> data;  // 查询数据
    private int recordCount;          // 记录数量
    private int executionTime;        // 执行时间(毫秒)
    
    public SqlQueryResult() {}
    
    public SqlQueryResult(boolean success, int statusCode, String message, 
                         List<Map<String, Object>> data, int recordCount, int executionTime) {
        this.success = success;
        this.statusCode = statusCode;
        this.message = message;
        this.data = data;
        this.recordCount = recordCount;
        this.executionTime = executionTime;
    }
    
    /**
     * 创建成功结果的静态工厂方法
     * 
     * @param data 查询数据
     * @param recordCount 记录数量
     * @param executionTime 执行时间(毫秒)
     * @return 成功的查询结果对象
     */
    public static SqlQueryResult success(List<Map<String, Object>> data, int recordCount, int executionTime) {
        return new SqlQueryResult(true, 200, "查询执行成功", data, recordCount, executionTime);
    }
    
    /**
     * 创建失败结果的静态工厂方法
     * 
     * @param statusCode 错误状态码
     * @param message 错误消息
     * @return 失败的查询结果对象
     */
    public static SqlQueryResult error(int statusCode, String message) {
        return new SqlQueryResult(false, statusCode, message, new ArrayList<>(), 0, 0);
    }
    
    // Getter和Setter方法
    public boolean isSuccess() { 
        return success; 
    }
    
    public void setSuccess(boolean success) { 
        this.success = success; 
    }
    
    public int getStatusCode() { 
        return statusCode; 
    }
    
    public void setStatusCode(int statusCode) { 
        this.statusCode = statusCode; 
    }
    
    public String getMessage() { 
        return message; 
    }
    
    public void setMessage(String message) { 
        this.message = message; 
    }
    
    public List<Map<String, Object>> getData() { 
        return data; 
    }
    
    public void setData(List<Map<String, Object>> data) { 
        this.data = data; 
    }
    
    public int getRecordCount() { 
        return recordCount; 
    }
    
    public void setRecordCount(int recordCount) { 
        this.recordCount = recordCount; 
    }
    
    public int getExecutionTime() { 
        return executionTime; 
    }
    
    public void setExecutionTime(int executionTime) { 
        this.executionTime = executionTime; 
    }
    
    @Override
    public String toString() {
        return "SqlQueryResult{" +
                "success=" + success +
                ", statusCode=" + statusCode +
                ", message='" + message + '\'' +
                ", recordCount=" + recordCount +
                ", executionTime=" + executionTime +
                ", dataSize=" + (data != null ? data.size() : 0) +
                '}';
    }
}
