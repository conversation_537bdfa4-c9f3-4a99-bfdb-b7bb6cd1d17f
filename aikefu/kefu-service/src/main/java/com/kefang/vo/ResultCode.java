package com.kefang.vo;

import lombok.Getter;

/**
 * 统一响应状态码
 */
@Getter
public enum ResultCode {
    // 通用状态码
    SUCCESS(200, "成功"),
    ERROR(500, "系统错误"),
    PARAM_ERROR(400, "参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    
    // 用户模块错误码 (1000-1999)
    USER_NOT_EXIST(1001, "用户不存在"),
    USER_PASSWORD_ERROR(1002, "密码错误"),
    USER_ACCOUNT_LOCKED(1003, "账号已锁定"),
    USER_ACCOUNT_ERROR(1004, "账号异常"),
    USER_ALREADY_EXIST(1005, "用户已存在"),
    
    // 会话模块错误码 (2000-2999)
    SESSION_NOT_EXIST(2001, "会话不存在"),
    SESSION_CLOSED(2002, "会话已关闭"),
    SESSION_AGENT_NOT_AVAILABLE(2003, "无可用客服"),
    
    // SSO模块错误码 (3000-3999)
    SSO_LOGIN_FAILED(3001, "SSO登录失败"),
    SSO_TOKEN_EXPIRED(3002, "SSO令牌已过期");
    
    private final Integer code;
    private final String message;
    
    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
} 