package com.kefang.vo;

import java.util.List;

/**
 * 分页结果封装类
 */
public class PageResult<T> {
    // 总记录数
    private long total;
    
    // 当前页结果列表
    private List<T> records;
    
    // 当前页码
    private int pageNum;
    
    // 每页记录数
    private int pageSize;
    
    // 总页数
    private int pages;
    
    public PageResult() {
    }
    
    public PageResult(List<T> records, long total, int pageNum, int pageSize) {
        this.records = records;
        this.total = total;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.pages = (int) Math.ceil((double) total / pageSize);
    }
    
    public long getTotal() {
        return total;
    }
    
    public void setTotal(long total) {
        this.total = total;
    }
    
    public List<T> getRecords() {
        return records;
    }
    
    public void setRecords(List<T> records) {
        this.records = records;
    }
    
    public List<T> getList() {
        return records;
    }
    
    public void setList(List<T> list) {
        this.records = list;
    }
    
    public int getPageNum() {
        return pageNum;
    }
    
    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }
    
    public int getPageSize() {
        return pageSize;
    }
    
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
    
    public int getPages() {
        return pages;
    }
    
    public void setPages(int pages) {
        this.pages = pages;
    }
} 