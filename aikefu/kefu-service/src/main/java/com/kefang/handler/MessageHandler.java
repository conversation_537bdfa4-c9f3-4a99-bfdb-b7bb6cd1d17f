package com.kefang.handler;

import com.kefang.entity.ConnectEntiy;
import com.kefang.vo.Result;

/**
 * 消息处理器接口
 * 用于处理不同类型的消息请求
 */
public interface MessageHandler {
    
    /**
     * 检查当前处理器是否能处理指定的消息
     * 
     * @param message 用户发送的消息内容
     * @return 如果能处理返回true，否则返回false
     */
    boolean canHandle(String message);
    
    /**
     * 处理消息并返回结果
     * 
     * @param connectEntiy 连接实体对象
     * @return 处理结果
     */
    Result<String> handle(ConnectEntiy connectEntiy);
} 