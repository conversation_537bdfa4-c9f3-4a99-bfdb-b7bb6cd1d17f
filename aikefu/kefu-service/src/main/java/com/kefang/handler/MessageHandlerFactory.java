package com.kefang.handler;

import com.kefang.entity.ConnectEntiy;
import com.kefang.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * 消息处理器工厂
 * 负责管理所有消息处理器并选择合适的处理器处理消息
 */
@Slf4j
@Component
public class MessageHandlerFactory {
    
    @Autowired
    private List<MessageHandler> handlers;
    
    // 处理器链
    private List<MessageHandler> handlerChain;
    
    @PostConstruct
    public void init() {
        // 初始化处理器链
        handlerChain = new ArrayList<>();
        
        // 添加所有注册的处理器
        if (handlers != null) {
            handlerChain.addAll(handlers);
            log.info("消息处理器链初始化完成，共加载{}个处理器", handlers.size());
        } else {
            log.warn("未找到任何消息处理器");
        }
    }
    
    /**
     * 处理消息
     * 遍历所有处理器，找到第一个能处理该消息的处理器并处理
     * 
     * @param connectEntiy 连接实体对象
     * @return 处理结果
     */
    public Result<String> handleMessage(ConnectEntiy connectEntiy) {
        String message = connectEntiy.getMessages();
        
        // 遍历处理器链，寻找能够处理当前消息的处理器
        for (MessageHandler handler : handlerChain) {
            // 检查处理器是否能够处理当前消息
            if (handler.canHandle(message)) {
                log.info("选择处理器: {}", handler.getClass().getSimpleName());
                // 当找到合适的处理器时，使用当前的连接实体调用处理器进行处理，并返回处理结果
                return handler.handle(connectEntiy);
            }
        }
        
        // 如果没有找到合适的处理器，则返回空结果
        log.warn("未找到合适的处理器处理消息: {}", message);
        return null;
    }
    
    /**
     * 注册新的处理器
     * 
     * @param handler 要注册的处理器
     */
    public void registerHandler(MessageHandler handler) {
        handlerChain.add(handler);
        log.info("注册新的消息处理器: {}", handler.getClass().getSimpleName());
    }
} 