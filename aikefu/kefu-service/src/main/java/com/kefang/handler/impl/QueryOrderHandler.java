package com.kefang.handler.impl;

import com.alibaba.fastjson.JSONObject;
import com.kefang.api.HsAppOrder;
import com.kefang.api.ReAppOrder;
import com.kefang.entity.ConnectEntiy;
import com.kefang.handler.MessageHandler;
import com.kefang.utils.FormatUtils;
import com.kefang.vo.Result;
import com.mysql.cj.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.kefang.utils.OrderUtils.extractOrderNumber;

/**
 * 订单查询处理器
 * 处理用户发送的订单查询消息
 */
@Slf4j
@Component
public class QueryOrderHandler implements MessageHandler {

    @Autowired
    private HsAppOrder hsAppOrder;

    @Autowired
    private ReAppOrder reAppOrder;

    /**
     * 判断消息中是否包含有效的订单号
     * 执行步骤：
     * 1. 使用工具类从消息文本中提取订单号
     * 2. 判断提取的订单号是否为空
     *
     * @param message 用户发送的消息内容
     * @return 如果消息中包含有效订单号返回true，否则返回false
     */
    @Override
    public boolean canHandle(String message) {
        // 步骤1：从消息文本中提取订单号
        String orderNumber = extractOrderNumber(message);

        // 步骤2：判断提取的订单号是否为空
        return !StringUtils.isNullOrEmpty(orderNumber);
    }

    /**
     * 处理查询订单请求
     * <p>
     * 执行步骤：
     * 1. 从用户消息中提取订单号
     * 2. 判断订单号前缀，调用相应的订单服务
     * 3. 验证API返回的订单数据
     * 4. 格式化订单信息并返回结果
     * 5. 处理异常情况并返回适当的消息
     *
     * @param connectEntiy 连接实体对象
     * @return 处理结果，包含格式化后的订单信息或错误消息
     */
    @Override
    public Result<String> handle(ConnectEntiy connectEntiy) {
        // 步骤1：从用户消息中提取订单号
        String messages = connectEntiy.getMessages();
        String orderNumber = extractOrderNumber(messages);
        log.info("检测到查询订单请求，订单号: {}", orderNumber);

        try {
            // 步骤2：根据订单号前缀判断使用哪个服务
            Result<JSONObject> orderResult;

            if (orderNumber.startsWith("HS")) {
                // 使用HS订单服务
                orderResult = hsAppOrder.getOrderInfo(orderNumber);
            } else {
                // 使用第三方订单号服务获取对应的HS订单号
                Result<JSONObject> reOrderResult = reAppOrder.getOrderInfo(orderNumber);
                if (reOrderResult.getSuccess() && reOrderResult.getData() != null && reOrderResult.getData().containsKey("code")) {
                    // 获取对应的HS订单号
                    String hsOrderNumber = reOrderResult.getData().getString("code");
                    log.info("第三方订单号 {} 对应的HS订单号为: {}, 使用HS订单号查询详情", orderNumber, hsOrderNumber);

                    // 使用HS订单号查询详情
                    orderResult = hsAppOrder.getOrderInfo(hsOrderNumber);
                } else {
                    // 如果获取HS订单号失败，直接返回第三方订单号查询结果
                    log.error("获取第三方订单号 {} 对应的HS订单号失败", orderNumber);
                    return Result.success("获取订单号: " + orderNumber + " 的信息失败，无法获取对应HS订单号");
                }
            }

            // 步骤3：验证API返回的订单数据
            if (orderResult.getSuccess() && orderResult.getData() != null) {
                JSONObject orderData = orderResult.getData();
                log.debug("订单详细数据: {}", orderData);

                // 步骤4：验证订单数据是否包含必要字段
                if (!orderData.isEmpty() && orderData.containsKey("code")) {
                    log.info("订单 {} 存在有效信息", orderNumber);

                    String merchantId = orderData.get("merchant_id")+"";
                    String source = orderData.get("source")+"";
                    String orderSource = connectEntiy.getOrder_source();
                    String groupTypeId = connectEntiy.getGroup_type_id();
                    
                    log.info("merchantId: {}, source: {}, 接口上传orderSource: {}, groupTypeId: {}", 
                            merchantId, source, orderSource, groupTypeId);
                    
                    // 步骤4.5：判断商户渠道和订单渠道是否匹配
                    // 条件1：source 与 order_source 匹配，或 merchant_id 与 group_type_id 匹配
                    // 条件2：order_source 和 group_type_id 不能同时为0
                    if ((source.equals(orderSource) || merchantId.equals(groupTypeId)) 
                            && !("0".equals(orderSource) && "0".equals(groupTypeId))) {
                        // 步骤5：格式化订单信息
                        String formattedInfo = FormatUtils.formatOrderInfo(orderData);

                        // 步骤6：返回成功结果
                        return Result.success("响应正常", formattedInfo);
                    } else {
                        log.info("订单 {} 的渠道与当前渠道不匹配", orderNumber);
                        return Result.success("您好，查询渠道和订单渠道不匹配，无法执行查询订单操作。");
                    }
                } else {
                    log.info("订单 {} 没有有效的订单信息记录", orderNumber);
                    // 步骤6（替代路径）：返回未找到订单的提示信息
                    return Result.success("响应正常", "您好，没有找到订单号: " + orderNumber + " 的信息");
                }
            } else {
                // 步骤6（异常路径）：API调用成功但返回错误结果
                String errorMessage = orderResult.getMessage() != null ? orderResult.getMessage() : "未知错误";
                log.error("获取订单信息失败: {}", errorMessage);
                return Result.success("获取订单号: " + orderNumber + " 的信息失败，请稍后再试");
            }
        } catch (Exception e) {
            // 步骤6（异常路径）：处理未捕获的异常
            log.error("处理订单查询请求时发生异常，订单号: {}, 错误: {}", orderNumber, e.getMessage(), e);
            return Result.success("处理订单查询请求时发生错误，请稍后再试");
        }
    }
} 