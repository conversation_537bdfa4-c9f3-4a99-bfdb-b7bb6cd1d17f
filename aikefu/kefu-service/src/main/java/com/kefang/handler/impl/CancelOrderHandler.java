package com.kefang.handler.impl;

import com.alibaba.fastjson.JSONObject;
import com.kefang.api.HsAppOrder;
import com.kefang.api.ReAppOrder;
import com.kefang.entity.ConnectEntiy;
import com.kefang.handler.MessageHandler;
import com.kefang.vo.Result;
import com.mysql.cj.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.kefang.utils.OrderUtils.extractCancelOrderNumber;

/**
 * 取消订单处理器
 * 处理用户发送的取消订单消息
 */
@Slf4j
@Component
public class CancelOrderHandler implements MessageHandler {

    @Autowired
    private HsAppOrder hsAppOrder;

    @Autowired
    private ReAppOrder reAppOrder;

    /**
     * 判断是否可以处理该消息
     *
     * @param message 用户发送的消息内容
     * @return true表示可以处理，false表示不可以处理
     */
    @Override
    public boolean canHandle(String message) {
        // 只要消息中包含"取消"、"订单"关键词和有效的订单号，就认为是取消订单请求
        String cancelOrderNumber = extractCancelOrderNumber(message);
        return !StringUtils.isNullOrEmpty(cancelOrderNumber);
    }

    /**
     * 处理用户发送的取消订单消息
     *
     * @param connectEntiy 连接实体对象
     * @return 处理结果
     */
    @Override
    public Result<String> handle(ConnectEntiy connectEntiy) {
        String messages = connectEntiy.getMessages();
        String orderNumber = extractCancelOrderNumber(messages);
        log.info("检测到取消订单请求，订单号: {}", orderNumber);

        try {
            // 判断订单号前缀，获取HS订单号
            String hsOrderNumber = orderNumber;

            if (!orderNumber.startsWith("HS")) {
                // 使用第三方订单号服务获取对应的HS订单号
                Result<JSONObject> reOrderResult = reAppOrder.getOrderInfo(orderNumber);
                if (reOrderResult.getSuccess() && reOrderResult.getData() != null && reOrderResult.getData().containsKey("code")) {
                    // 获取对应的HS订单号
                    hsOrderNumber = reOrderResult.getData().getString("code");
                    log.info("第三方订单号 {} 对应的HS订单号为: {}, 使用HS订单号取消订单", orderNumber, hsOrderNumber);
                } else {
                    // 如果获取HS订单号失败，返回错误信息
                    log.error("获取第三方订单号 {} 对应的HS订单号失败", orderNumber);
                    return Result.success("您好，获取订单信息失败，请联系管理人员。");
                }
            }

            // 步骤1: 获取订单信息
            Result<JSONObject> orderResult = hsAppOrder.getOrderInfo(hsOrderNumber);
            if (!orderResult.getSuccess() || orderResult.getData() == null) {
                log.error("获取订单信息失败: {}", orderResult.getMessage());
                return Result.success("您好，获取订单信息失败，请联系管理人员。");
            }

            // 步骤2: 验证订单信息
            JSONObject orderData = orderResult.getData();
            try {
                if (hsOrderNumber.isEmpty()) {
                    log.info("订单 {} 没有有效信息", orderNumber);
                    return Result.success("您好，无法处理此订单，请确认订单号是否正确");
                }
            } catch (Exception e) {
                log.error("验证订单信息异常，订单号: {}，错误: {}", orderNumber, e.getMessage(), e);
                return Result.success("您好，获取订单信息失败，请联系管理人员。");
            }

            // 步骤2.5: 判断商户渠道和订单渠道是否匹配
            String merchantId = orderData.get("merchant_id")+"";
            String source = orderData.get("source")+"";
            String orderSource = connectEntiy.getOrder_source();
            String groupTypeId = connectEntiy.getGroup_type_id();

            log.info("merchantId: {}, source: {}, 接口上传orderSource: {}, groupTypeId: {}", 
                    merchantId, source, orderSource, groupTypeId);

            // 判断商户渠道和订单渠道是否匹配
            // 条件1：source 与 order_source 匹配，或 merchant_id 与 group_type_id 匹配
            // 条件2：order_source 和 group_type_id 不能同时为0
            if ((source.equals(orderSource) || merchantId.equals(groupTypeId)) 
                    && !("0".equals(orderSource) && "0".equals(groupTypeId))) {
                // 继续执行后续操作
            } else {
                log.info("订单 {} 的渠道与当前渠道不匹配", hsOrderNumber);
                return Result.success("您好，查询渠道和订单渠道不匹配，无法执行取消订单操作。");
            }

            // 步骤3: 执行取消订单操作
            log.info("准备取消订单，订单号: {}", hsOrderNumber);
            Result<JSONObject> cancelResult = hsAppOrder.closeOrder(hsOrderNumber, "取消订单");

            // 步骤4: 处理取消结果
            if (cancelResult.getSuccess()) {
                log.info("成功取消订单: {}", hsOrderNumber);
                return Result.success("您已成功取消订单: " + orderNumber);
            } else {
                log.error("取消订单失败，订单号: {}，错误: {}", hsOrderNumber, cancelResult.getMessage());
                return Result.success("您好，取消订单失败，请联系管理人员。"+cancelResult.getMessage());
            }
        } catch (Exception e) {
            log.error("取消订单处理异常，订单号: {}，错误: {}", orderNumber, e.getMessage(), e);
            return Result.success("您好，处理取消订单请求时发生异常，请联系管理人员。");
        }
    }
}