package com.kefang.handler.impl;

import com.alibaba.fastjson.JSONObject;
import com.kefang.api.HsAppOrder;
import com.kefang.api.ReAppOrder;
import com.kefang.entity.ConnectEntiy;
import com.kefang.handler.MessageHandler;
import com.kefang.utils.FormatUtils;
import com.kefang.vo.Result;
import com.mysql.cj.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.kefang.utils.OrderUtils.extractExpressOrderNumber;

/**
 * 物流查询处理器
 * 处理用户发送的物流查询消息
 */
@Slf4j
@Component
public class QueryExpressHandler implements MessageHandler {

    @Autowired
    private HsAppOrder hsAppOrder;

    @Autowired
    private ReAppOrder reAppOrder;

    /***
     * 判断是否可以处理物流查询请求
     * 执行步骤：
     * 1. 使用工具类从消息文本中提取物流订单号
     * 2. 判断提取的订单号是否为空
     *
     * @param message 用户发送的消息内容
     * @return 如果消息中包含物流关键词和有效订单号返回true，否则返回false
     */
    @Override
    public boolean canHandle(String message) {
        // 步骤1：从消息文本中提取物流订单号
        String expressOrderNumber = extractExpressOrderNumber(message);

        // 步骤2：判断提取的订单号是否为空
        return !StringUtils.isNullOrEmpty(expressOrderNumber);
    }

    /**
     * 处理物流查询请求
     * <p>
     * 执行步骤：
     * 1. 从用户消息中提取物流订单号
     * 2. 判断订单号前缀，调用相应的服务获取HS订单号
     * 3. 调用物流API查询物流信息
     * 4. 验证API返回的物流数据
     * 5. 检查是否有物流记录
     * 6. 格式化物流信息并返回结果
     * 7. 处理异常情况并返回适当的消息
     *
     * @param connectEntiy 连接实体对象
     * @return 处理结果，包含格式化后的物流信息或错误消息
     */
    @Override
    public Result<String> handle(ConnectEntiy connectEntiy) {
        // 步骤1：从用户消息中提取物流订单号
        String messages = connectEntiy.getMessages();
        String expressOrderNumber = extractExpressOrderNumber(messages);
        log.info("检测到查询物流请求，订单号: {}", expressOrderNumber);

        try {
            // 步骤2：判断订单号前缀，获取HS订单号
            String hsOrderNumber = expressOrderNumber;

            if (!expressOrderNumber.startsWith("HS")) {
                // 使用第三方订单号服务获取对应的HS订单号
                Result<JSONObject> reOrderResult = reAppOrder.getOrderInfo(expressOrderNumber);
                if (reOrderResult.getSuccess() && reOrderResult.getData() != null && reOrderResult.getData().containsKey("code")) {
                    // 获取对应的HS订单号
                    hsOrderNumber = reOrderResult.getData().getString("code");
                    log.info("第三方订单号 {} 对应的HS订单号为: {}, 使用HS订单号查询物流", expressOrderNumber, hsOrderNumber);
                } else {
                    // 如果获取HS订单号失败，返回错误信息
                    log.error("获取第三方订单号 {} 对应的HS订单号失败", expressOrderNumber);
                    return Result.success("获取订单号: " + expressOrderNumber + " 的物流信息失败，无法获取对应HS订单号");
                }
            }
            // 使用HS订单服务
            Result<JSONObject> orderResult = hsAppOrder.getOrderInfo(hsOrderNumber);
            if (!orderResult.getSuccess() || orderResult.getData() == null) {
                log.error("获取订单信息失败: {}", orderResult.getMessage());
                return Result.success("获取订单号: " + expressOrderNumber + " 的物流信息失败，请稍后再试");
            }
            
            // 步骤2.5: 判断商户渠道和订单渠道是否匹配
            JSONObject orderData = orderResult.getData();
            String merchantId = orderData.get("merchant_id")+"";
            String source = orderData.get("source")+"";
            String orderSource = connectEntiy.getOrder_source();
            String groupTypeId = connectEntiy.getGroup_type_id();

            log.info("merchantId: {}, source: {}, 接口上传orderSource: {}, groupTypeId: {}", 
                    merchantId, source, orderSource, groupTypeId);

            // 判断商户渠道和订单渠道是否匹配
            // 条件1：source 与 order_source 匹配，或 merchant_id 与 group_type_id 匹配
            // 条件2：order_source 和 group_type_id 不能同时为0
            if ((source.equals(orderSource) || merchantId.equals(groupTypeId)) 
                    && !("0".equals(orderSource) && "0".equals(groupTypeId))) {
                // 继续执行后续操作
            } else {
                log.info("订单 {} 的渠道与当前渠道不匹配", hsOrderNumber);
                return Result.success("您好，查询渠道和订单渠道不匹配，无法执行查询物流操作。");
            }

            JSONObject expressInfo = extractExpressInfo(orderData, hsOrderNumber);
            if (expressInfo == null) {
                return Result.success("您好，订单号: " + expressOrderNumber + " 暂无物流信息");
            }
            // 步骤3：调用物流API查询物流信息
            Result<JSONObject> expressResult = hsAppOrder.getExpressListMap(expressInfo.getString("company"), expressInfo.getString("number"));

            // 步骤4：验证API返回的物流数据
            if (expressResult.getSuccess() && expressResult.getData() != null) {
                JSONObject expressData = expressResult.getData();
                log.info("成功获取到物流信息");
                log.info("物流详细数据: {}", expressData);

                // 步骤5：检查是否有物流记录
                if (expressData.containsKey("process") && expressData.getJSONArray("process") != null
                        && !expressData.getJSONArray("process").isEmpty()) {
                    log.info("订单 {} 存在物流记录", expressOrderNumber);

                    // 步骤6：格式化物流信息
                    String formattedInfo = FormatUtils.formatExpressInfo(expressData, expressOrderNumber);

                    // 步骤7：返回成功结果
                    return Result.success("响应正常", formattedInfo);
                } else {
                    log.info("订单 {} 没有物流信息记录", expressOrderNumber);
                    // 步骤7（替代路径）：返回没有物流信息的提示
                    return Result.success("响应正常", "您好，订单号: " + expressOrderNumber + " 暂无物流信息");
                }
            } else {
                // 步骤7（异常路径）：API调用成功但返回错误结果
                String errorMessage = expressResult.getMessage() != null ? expressResult.getMessage() : "未知错误";
                log.error("获取物流信息失败: {}", errorMessage);
                return Result.success("获取订单号: " + expressOrderNumber + " 的物流信息失败，请稍后再试");
            }
        } catch (Exception e) {
            // 步骤7（异常路径）：处理未捕获的异常
            log.error("处理物流查询请求时发生异常，订单号: {}, 错误: {}", expressOrderNumber, e.getMessage(), e);
            return Result.success("处理物流查询请求时发生错误，请稍后再试");
        }
    }

    /**
     * 从订单数据中提取物流信息
     *
     * @param orderData   订单数据
     * @param orderNumber 订单号(用于日志)
     * @return 物流信息对象，包含company和number字段；如果无法获取则返回null
     */
    private JSONObject extractExpressInfo(JSONObject orderData, String orderNumber) {
        try {
            if (orderData == null) {
                log.info("订单 {} 没有物流信息", orderNumber);
                return null;
            }
            JSONObject express = orderData.getJSONObject("express");
            if (express == null) {
                log.info("订单 {} 没有物流信息", orderNumber);
                return null;
            }

            // 检查物流公司
            String company = express.getString("company");
            if (StringUtils.isNullOrEmpty(company)) {
                log.info("订单 {} 没有物流公司编号", orderNumber);
                return null;
            }

            // 检查物流单号
            String expressNumber = express.getString("number");
            if (StringUtils.isNullOrEmpty(expressNumber)) {
                log.info("订单 {} 没有物流单号", orderNumber);
                return null;
            }

            // 创建返回对象
            JSONObject result = new JSONObject();
            result.put("company", company);
            result.put("number", expressNumber);
            return result;
        } catch (Exception e) {
            log.error("提取物流信息异常，订单号: {}，错误: {}", orderNumber, e.getMessage(), e);
            return null;
        }
    }
} 