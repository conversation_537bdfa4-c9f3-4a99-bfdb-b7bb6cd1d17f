package com.kefang.handler.impl;

import com.alibaba.fastjson.JSONObject;
import com.kefang.api.HsAppOrder;
import com.kefang.api.ReAppOrder;
import com.kefang.entity.ConnectEntiy;
import com.kefang.handler.MessageHandler;
import com.kefang.vo.Result;
import com.mysql.cj.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.kefang.utils.OrderUtils.extractCancelExpressOrderNumber;

/**
 * 取消物流处理器
 * 处理用户发送的取消物流消息
 */
@Slf4j
@Component
public class CancelExpressHandler implements MessageHandler {

    @Autowired
    private HsAppOrder hsAppOrder;
    
    @Autowired
    private ReAppOrder reAppOrder;

    /***
     * 判断消息是否可以处理为取消物流请求
     * @param message 用户发送的消息内容
     */
    @Override
    public boolean canHandle(String message) {
        // 只要消息中包含"取消"、"物流"关键词和有效的订单号，就认为是取消物流请求
        String cancelExpressOrderNumber = extractCancelExpressOrderNumber(message);
        return !StringUtils.isNullOrEmpty(cancelExpressOrderNumber);
    }

    /**
     * 处理用户发送的取消物流请求
     * @param connectEntiy 连接实体对象
     */
    @Override
    public Result<String> handle(ConnectEntiy connectEntiy) {
        String messages = connectEntiy.getMessages();
        String orderNumber = extractCancelExpressOrderNumber(messages);
        log.info("检测到取消物流请求，订单号: {}", orderNumber);

        try {
            // 判断订单号前缀，获取HS订单号
            String hsOrderNumber = orderNumber;

            if (!orderNumber.startsWith("HS")) {
                // 使用第三方订单号服务获取对应的HS订单号
                Result<JSONObject> reOrderResult = reAppOrder.getOrderInfo(orderNumber);
                if (reOrderResult.getSuccess() && reOrderResult.getData() != null && reOrderResult.getData().containsKey("code")) {
                    // 获取对应的HS订单号
                    hsOrderNumber = reOrderResult.getData().getString("code");
                    log.info("第三方订单号 {} 对应的HS订单号为: {}, 使用HS订单号取消物流", orderNumber, hsOrderNumber);
                } else {
                    // 如果获取HS订单号失败，返回错误信息
                    log.error("获取第三方订单号 {} 对应的HS订单号失败", orderNumber);
                    return Result.success("您好，获取订单信息失败，请联系管理人员。");
                }
            }
            
            // 步骤1: 获取订单信息
            Result<JSONObject> orderResult = hsAppOrder.getOrderInfo(hsOrderNumber);
            if (!orderResult.getSuccess() || orderResult.getData() == null) {
                log.error("获取订单信息失败: {}", orderResult.getMessage());
                return Result.success("您好，获取订单信息失败，请联系管理人员。");
            }

            // 步骤1.5: 判断商户渠道和订单渠道是否匹配
            JSONObject orderData = orderResult.getData();
            String merchantId = orderData.get("merchant_id")+"";
            String source = orderData.get("source")+"";
            String orderSource = connectEntiy.getOrder_source();
            String groupTypeId = connectEntiy.getGroup_type_id();

            log.info("merchantId: {}, source: {}, 接口上传orderSource: {}, groupTypeId: {}", 
                    merchantId, source, orderSource, groupTypeId);

            // 判断商户渠道和订单渠道是否匹配
            // 条件1：source 与 order_source 匹配，或 merchant_id 与 group_type_id 匹配
            // 条件2：order_source 和 group_type_id 不能同时为0
            if ((source.equals(orderSource) || merchantId.equals(groupTypeId)) 
                    && !("0".equals(orderSource) && "0".equals(groupTypeId))) {
                // 继续执行后续操作
            } else {
                log.info("订单 {} 的渠道与当前渠道不匹配", hsOrderNumber);
                return Result.success("您好，查询渠道和订单渠道不匹配，无法执行取消物流操作。");
            }

            // 步骤2: 提取物流信息
            JSONObject expressInfo = extractExpressInfo(orderData, hsOrderNumber);
            if (expressInfo == null) {
                return Result.success("您好，无法获取物流信息，请确认订单是否已创建物流单。");
            }

            String company = expressInfo.getString("company");
            String expressNumber = expressInfo.getString("number");

            // 步骤3: 执行取消物流操作
            log.info("准备取消物流单，订单号: {}，物流公司: {}，物流单号: {}", hsOrderNumber, company, expressNumber);
            Result<JSONObject> cancelResult = hsAppOrder.cancelExpress(company, expressNumber, "对话框申请取消物流");

            // 步骤4: 处理取消结果
            if (cancelResult.getSuccess()) {
                log.info("成功取消订单 {} 的物流单: {}", hsOrderNumber, expressNumber);
                return Result.success("您已成功取消订单 " + orderNumber + " 的物流单号：" + expressNumber);
            } else {
                log.error("取消物流单失败，订单号: {}，物流单号: {}，错误: {}", hsOrderNumber, expressNumber, cancelResult.getMessage());
                return Result.success("您好，取消物流单失败，请联系管理人员。");
            }
        } catch (Exception e) {
            log.error("取消物流处理异常，订单号: {}，错误: {}", orderNumber, e.getMessage(), e);
            return Result.success("您好，处理取消物流请求时发生异常，请联系管理人员。");
        }
    }

    /**
     * 从订单数据中提取物流信息
     *
     * @param orderData 订单数据
     * @param orderNumber 订单号(用于日志)
     * @return 物流信息对象，包含company和number字段；如果无法获取则返回null
     */
    private JSONObject extractExpressInfo(JSONObject orderData, String orderNumber) {
        try {
            JSONObject express = orderData.getJSONObject("express");
            if (express == null) {
                log.info("订单 {} 没有物流信息", orderNumber);
                return null;
            }

            // 检查物流公司
            String company = express.getString("company");
            if (StringUtils.isNullOrEmpty(company)) {
                log.info("订单 {} 没有物流公司编号", orderNumber);
                return null;
            }

            // 检查物流单号
            String expressNumber = express.getString("number");
            if (StringUtils.isNullOrEmpty(expressNumber)) {
                log.info("订单 {} 没有物流单号", orderNumber);
                return null;
            }

            // 创建返回对象
            JSONObject result = new JSONObject();
            result.put("company", company);
            result.put("number", expressNumber);
            return result;
        } catch (Exception e) {
            log.error("提取物流信息异常，订单号: {}，错误: {}", orderNumber, e.getMessage(), e);
            return null;
        }
    }
}