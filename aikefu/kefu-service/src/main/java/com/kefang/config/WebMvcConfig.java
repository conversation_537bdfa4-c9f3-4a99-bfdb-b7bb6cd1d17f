package com.kefang.config;

import com.kefang.interceptor.AuthInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Web MVC 配置类
 * 用于配置拦截器和白名单URL
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private AuthInterceptor authInterceptor;
    
    @Autowired
    private AuthConfig authConfig;

    /**
     * 默认接口白名单，这些路径不需要进行身份验证
     */
    private static final List<String> DEFAULT_WHITE_LIST = Arrays.asList(
            "/error",
            "/user/login",
            "/agent/login",
            "/jd/auth",
            "/connect/**",
            "/weixin/**",
            "/favicon.ico"
    );

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 合并默认白名单和配置中的白名单
        List<String> whiteList = new ArrayList<>(DEFAULT_WHITE_LIST);
        if (authConfig.getWhiteList() != null && !authConfig.getWhiteList().isEmpty()) {
            whiteList.addAll(authConfig.getWhiteList());
        }
        
        // 注册认证拦截器，并设置白名单
        registry.addInterceptor(authInterceptor)
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns(whiteList); // 排除白名单中的路径
    }
} 