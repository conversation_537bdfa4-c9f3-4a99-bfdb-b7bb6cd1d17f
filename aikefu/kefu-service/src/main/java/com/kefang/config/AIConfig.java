package com.kefang.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * AI配置类，用于配置AI服务的相关参数
 */
@Configuration
@ConfigurationProperties(prefix = "ai")
public class AIConfig {

    /**
     * AI服务的URL地址，默认值为本地地址
     * 这个字段可以被外部配置覆盖，例如在application.yml文件中配置ai.serviceUrl属性
     */
    private String serviceUrl = "http://127.0.0.1:8000";

    public String getServiceUrl() {
        return serviceUrl;
    }

    public void setServiceUrl(String serviceUrl) {
        this.serviceUrl = serviceUrl;
    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}