package com.kefang.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * OSS配置属性类
 * 用于管理阿里云OSS相关配置信息
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-08
 */
@Data
@Component
@ConfigurationProperties(prefix = "oss")
public class OssProperties {
    
    /**
     * OSS服务端点地址
     * 例如：http://oss-cn-beijing.aliyuncs.com
     */
    private String endpoint;
    
    /**
     * 访问密钥ID
     */
    private String accessKeyId;
    
    /**
     * 访问密钥Secret
     */
    private String accessKeySecret;
    
    /**
     * 存储桶名称
     */
    private String bucketName;
    
    /**
     * 文件访问域名
     * 例如：https://file.juranguanjia.com
     */
    private String fileHost;
    
    /**
     * 文件上传根目录
     * 默认为：kefu
     */
    private String rootPath = "kefu";
    
    /**
     * 允许上传的最大文件大小（字节）
     * 默认为：100MB
     */
    private Long maxFileSize = 100 * 1024 * 1024L;
    
    /**
     * 允许上传的文件类型
     * 默认支持常见的图片、文档、视频格式
     */
    private String[] allowedFileTypes = {
        "jpg", "jpeg", "png", "gif", "bmp", "webp",  // 图片格式
        "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt",  // 文档格式
        "mp4", "avi", "mov", "wmv", "flv", "mkv",  // 视频格式
        "mp3", "wav", "flac", "aac",  // 音频格式
        "zip", "rar", "7z", "tar", "gz"  // 压缩格式
    };
}
