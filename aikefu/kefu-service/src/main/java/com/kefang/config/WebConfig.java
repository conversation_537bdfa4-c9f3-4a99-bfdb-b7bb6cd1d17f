package com.kefang.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Web配置类
 */
@Configuration
public class WebConfig {
    
    /**
     * 配置RestTemplate Bean
     * @return RestTemplate实例
     */
    @Bean(name = "ssoRestTemplate")
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate(getClientHttpRequestFactory());
        
        // 配置消息转换器
        configureMessageConverters(restTemplate);
        
        return restTemplate;
    }
    
    /**
     * 配置请求工厂
     * @return 请求工厂
     */
    private ClientHttpRequestFactory getClientHttpRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(15000); // 连接超时时间，15秒
        factory.setReadTimeout(30000); // 读取超时时间，30秒
        return factory;
    }
    
    /**
     * 配置消息转换器
     * @param restTemplate RestTemplate实例
     */
    private void configureMessageConverters(RestTemplate restTemplate) {
        // 清除现有的消息转换器
        restTemplate.getMessageConverters().clear();
        
        // 添加String消息转换器，处理文本响应
        StringHttpMessageConverter stringConverter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
        stringConverter.setSupportedMediaTypes(Arrays.asList(
                MediaType.TEXT_PLAIN,
                MediaType.TEXT_HTML,
                MediaType.APPLICATION_JSON,
                MediaType.APPLICATION_OCTET_STREAM, // 处理二进制流
                MediaType.ALL  // 处理所有类型
        ));
        
        // 添加Jackson消息转换器，处理JSON响应
        MappingJackson2HttpMessageConverter jsonConverter = new MappingJackson2HttpMessageConverter();
        // 支持所有可能的媒体类型
        List<MediaType> supportedMediaTypes = new ArrayList<>();
        supportedMediaTypes.add(MediaType.APPLICATION_JSON);
        supportedMediaTypes.add(MediaType.APPLICATION_OCTET_STREAM);
        supportedMediaTypes.add(MediaType.TEXT_PLAIN);
        supportedMediaTypes.add(MediaType.TEXT_HTML);
        supportedMediaTypes.add(MediaType.ALL);
        jsonConverter.setSupportedMediaTypes(supportedMediaTypes);
        
        // 将转换器添加到RestTemplate
        restTemplate.getMessageConverters().add(stringConverter);
        restTemplate.getMessageConverters().add(jsonConverter);
    }
} 