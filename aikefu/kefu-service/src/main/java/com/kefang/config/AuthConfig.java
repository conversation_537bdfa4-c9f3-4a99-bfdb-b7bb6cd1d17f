package com.kefang.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * 身份验证配置类
 * 提供JWT相关配置和白名单配置
 */
@Configuration
@ConfigurationProperties(prefix = "auth")
public class AuthConfig {

    /**
     * JWT密钥
     */
    private String jwtSecret = "customerServiceSecretKey";

    /**
     * token有效期（毫秒）
     * 默认24小时
     */
    private long tokenExpiration = 86400000;

    /**
     * 刷新token有效期（毫秒）
     * 默认7天
     */
    private long refreshTokenExpiration = 604800000;

    /**
     * 额外白名单路径
     * 这些路径不需要进行身份验证
     */
    private List<String> whiteList = new ArrayList<>();

    public String getJwtSecret() {
        return jwtSecret;
    }

    public void setJwtSecret(String jwtSecret) {
        this.jwtSecret = jwtSecret;
    }

    public long getTokenExpiration() {
        return tokenExpiration;
    }

    public void setTokenExpiration(long tokenExpiration) {
        this.tokenExpiration = tokenExpiration;
    }

    public long getRefreshTokenExpiration() {
        return refreshTokenExpiration;
    }

    public void setRefreshTokenExpiration(long refreshTokenExpiration) {
        this.refreshTokenExpiration = refreshTokenExpiration;
    }

    public List<String> getWhiteList() {
        return whiteList;
    }

    public void setWhiteList(List<String> whiteList) {
        this.whiteList = whiteList;
    }
} 