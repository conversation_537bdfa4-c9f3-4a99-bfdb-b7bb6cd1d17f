package com.kefang.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PreDestroy;

/**
 * OSS配置类
 * 用于创建和管理阿里云OSS客户端Bean
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-20
 */
@Slf4j
@Configuration
public class OssConfig {
    
    @Autowired
    private OssProperties ossProperties;
    
    private OSS ossClient;
    
    /**
     * 创建OSS客户端Bean
     * 
     * @return OSS客户端实例
     */
    @Bean
    public OSS ossClient() {
        log.info("正在初始化OSS客户端...");
        log.info("OSS配置信息 - endpoint: {}, bucketName: {}", 
                ossProperties.getEndpoint(), ossProperties.getBucketName());
        
        // 创建OSS客户端
        ossClient = new OSSClientBuilder().build(
                ossProperties.getEndpoint(),
                ossProperties.getAccessKeyId(),
                ossProperties.getAccessKeySecret()
        );
        
        log.info("OSS客户端初始化成功");
        return ossClient;
    }
    
    /**
     * 应用关闭时销毁OSS客户端，释放资源
     */
    @PreDestroy
    public void destroy() {
        if (ossClient != null) {
            log.info("正在关闭OSS客户端...");
            ossClient.shutdown();
            log.info("OSS客户端已关闭");
        }
    }
}
