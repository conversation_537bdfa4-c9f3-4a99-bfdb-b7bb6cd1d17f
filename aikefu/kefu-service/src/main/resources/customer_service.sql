-- 创建数据库
CREATE DATABASE IF NOT EXISTS customer_service DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE customer_service;

CREATE TABLE `agent` (
                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '客服ID',
                         `agent_no` varchar(20) DEFAULT NULL COMMENT '工号',
                         `name` varchar(50) DEFAULT NULL COMMENT '姓名',
                         `password` varchar(50) DEFAULT NULL COMMENT '密码',
                         `status` tinyint DEFAULT '0' COMMENT '在线状态（0离线 1在线）',
                         `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
                         `agent_type` tinyint NOT NULL DEFAULT '1' COMMENT '客服类型：1-人工客服，2-AI客服',
                         PRIMARY KEY (`id`),
                         UNIQUE KEY `agent_no` (`agent_no`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='客服表';

CREATE TABLE `chat_message` (
                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '消息ID',
                                `session_id` bigint NOT NULL COMMENT '关联会话ID',
                                `sender_type` tinyint DEFAULT NULL COMMENT '发送者类型（0用户 1客服 2ai）',
                                `content` text COMMENT '消息内容',
                                `msg_type` tinyint DEFAULT '0' COMMENT '消息类型（0文字 1图片）',
                                `is_read` tinyint DEFAULT '0' COMMENT '是否已读',
                                `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
                                PRIMARY KEY (`id`),
                                KEY `session_id` (`session_id`),
                                CONSTRAINT `chat_message_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `chat_session` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=309 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='消息表';

CREATE TABLE `chat_session` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '会话ID',
  `user_id` bigint NOT NULL COMMENT '关联用户ID',
  `agent_id` bigint DEFAULT NULL COMMENT '关联客服ID',
  `status` tinyint DEFAULT '0' COMMENT '会话状态（ 0：排队1：人工会话中2：已关闭3：AI会话中）',
  `start_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `end_time` datetime DEFAULT NULL,
  `closed_by` tinyint DEFAULT NULL COMMENT '关闭方（0用户 1客服）',
  `current_agent_type` tinyint NOT NULL DEFAULT '1' COMMENT '当前服务类型：1-人工客服，2-AI客服',
  `transfer_requested` tinyint NOT NULL DEFAULT '0' COMMENT '是否请求转人工：0-否，1-是',
  `satisfaction_level` int DEFAULT NULL COMMENT '满意度评分(1-5星)',
  `feedback_content` varchar(500) DEFAULT NULL COMMENT '评价内容/反馈',
  `is_solved` tinyint DEFAULT '0' COMMENT '问题是否解决(0-未解决，1-已解决)',
  `evaluation_time` datetime DEFAULT NULL COMMENT '评价时间',
  `solution_description` varchar(1000) DEFAULT NULL COMMENT '客服描述的问题解决方案',
  `user_suggestion` varchar(500) DEFAULT NULL COMMENT '用户建议内容，用于改进服务',
  `last_active_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后会话时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `agent_id` (`agent_id`),
  KEY `idx_is_solved` (`is_solved`),
  KEY `idx_satisfaction_level` (`satisfaction_level`),
  CONSTRAINT `chat_session_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `chat_session_ibfk_2` FOREIGN KEY (`agent_id`) REFERENCES `agent` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=198 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='会话表';



CREATE TABLE `faq` (
                       `id` bigint NOT NULL AUTO_INCREMENT COMMENT '问题ID',
                       `question` varchar(200) DEFAULT NULL COMMENT '常见问题',
                       `answer` text COMMENT '问题解答',
                       `category` varchar(50) DEFAULT NULL COMMENT '问题分类',
                       `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
                       PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='问题汇总表';



CREATE TABLE `user` (
                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
                        `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
                        `password` varchar(50) DEFAULT NULL COMMENT '密码',
                        `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
                        `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
                        `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                        `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
                        `address` varchar(255) DEFAULT NULL COMMENT '地址',
                        `age` int DEFAULT NULL COMMENT '年龄',
                        `gender` varchar(10) DEFAULT NULL COMMENT '性别',
                        `vip_level` int DEFAULT '0' COMMENT 'VIP等级',
                        `datasource` varchar(50) DEFAULT NULL COMMENT '数据来源',
                        `extra1` varchar(255) DEFAULT NULL COMMENT '扩展字段1',
                        `extra2` varchar(255) DEFAULT NULL COMMENT '扩展字段2',
                        `extra3` varchar(255) DEFAULT NULL COMMENT '扩展字段3',
                        `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
                        `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
                        PRIMARY KEY (`id`),
                        UNIQUE KEY `phone` (`phone`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户表';



ALTER TABLE `agent`
    ADD COLUMN `user_id` VARCHAR(255) COMMENT '用户ID',
ADD COLUMN `user_name` VARCHAR(255) COMMENT '用户名',
ADD COLUMN `avatar` VARCHAR(255) COMMENT '头像URL',
ADD COLUMN `user_type` VARCHAR(50) COMMENT '用户类型';


ALTER TABLE `user`
    ADD COLUMN `user_id` VARCHAR(50) DEFAULT NULL COMMENT 'SSO登入认证userId',
    ADD COLUMN `is_merchant` VARCHAR(50) DEFAULT NULL COMMENT '是否商家 (1:是)',
    ADD COLUMN `is_technician` VARCHAR(10) DEFAULT NULL COMMENT '是否是工程师 (1:是)';

-- 给FAQ表添加更新时间字段
ALTER TABLE `faq`
    ADD COLUMN `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 初始化一些测试数据
INSERT INTO `user` (`phone`, `password`, `nickname`, `avatar`, `remark`, `email`, `gender`, `vip_level`) VALUES
('13800000001', '123456', '测试用户1', 'https://via.placeholder.com/100', '重要客户', '<EMAIL>', '男', 2),
('13800000002', '123456', '测试用户2', 'https://via.placeholder.com/100', '新用户', '<EMAIL>', '女', 0);

INSERT INTO `agent` (`agent_no`, `name`, `password`, `status`) VALUES
('KF001', '客服小王', '123456', 0),
('KF002', '客服小李', '123456', 0);

INSERT INTO `faq` (`question`, `answer`, `category`) VALUES
('如何修改密码？', '可以在个人中心-安全设置中修改密码', '账户安全'),
('如何联系客服？', '点击右下角的客服图标即可发起会话', '使用帮助'),
('忘记密码怎么办？', '可以通过手机号找回密码', '账户安全');


-- 为 chat_message 表添加 datasource 字段，类型为 varchar(50)，表示数据源
ALTER TABLE `chat_message`
    ADD COLUMN `datasource` VARCHAR(50) DEFAULT NULL COMMENT '数据源';

-- 为 chat_message 表添加 collection_name 字段，类型为 varchar(50)，表示查询知识库集合
ALTER TABLE `chat_message`
    ADD COLUMN `collection_name` VARCHAR(50) DEFAULT NULL COMMENT '查询知识库集合';





SET GLOBAL event_scheduler = ON;

SHOW VARIABLES LIKE 'event_scheduler';



DELIMITER $$

CREATE EVENT update_chat_session_status
ON SCHEDULE EVERY 10 MINUTE -- 每10分钟执行一次
DO
BEGIN
UPDATE chat_session
SET
    status = 3,               -- 更新为 AI会话中
    current_agent_type = 2,   -- 更新为 AI客服
    transfer_requested = 0    -- 取消转人工请求
WHERE
    status = 1                -- 当前状态为人工会话中
  AND last_active_time < NOW() - INTERVAL 1 HOUR; -- 最后活跃时间 超过1小时
END$$

DELIMITER ;


CREATE TRIGGER `update_chat_session_last_active_time` AFTER INSERT ON `chat_message` FOR EACH ROW BEGIN
    UPDATE `chat_session`
    SET `last_active_time` = NOW()
    WHERE `id` = NEW.session_id;
END;