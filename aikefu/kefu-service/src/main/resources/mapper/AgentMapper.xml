<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kefang.mapper.AgentMapper">
    
    <resultMap id="BaseResultMap" type="com.kefang.entity.Agent">
        <id column="id" property="id" />
        <result column="agent_no" property="agentNo" />
        <result column="name" property="name" />
        <result column="password" property="password" />
        <result column="status" property="status" />
        <result column="agent_type" property="agentType" />
        <result column="created_at" property="createdAt" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="avatar" property="avatar" />
        <result column="user_type" property="userType" />
        <result column="login_at" property="loginAt" />
    </resultMap>
    
    <sql id="Base_Column_List">
        id, agent_no, name, password, status, agent_type, created_at, user_id, user_name, avatar, user_type, login_at
    </sql>
    
    <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT <include refid="Base_Column_List" />
        FROM agent
        WHERE id = #{id}
    </select>
    
    <select id="selectByAgentNo" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT <include refid="Base_Column_List" />
        FROM agent
        WHERE agent_no = #{agentNo}
    </select>
    
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM agent
    </select>
    
    <select id="selectOnlineAgents" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM agent
        WHERE status = 1 and agent_type != 3
    </select>
    
    <insert id="insert" parameterType="com.kefang.entity.Agent" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO agent (agent_no, name, password, status, agent_type, user_id, user_name, avatar, user_type, login_at)
        VALUES (#{agentNo}, #{name}, #{password}, #{status}, #{agentType}, #{userId}, #{userName}, #{avatar}, #{userType}, #{loginAt})
    </insert>
    
    <update id="update" parameterType="com.kefang.entity.Agent">
        UPDATE agent
        <set>
            <if test="agentNo != null">agent_no = #{agentNo},</if>
            <if test="name != null">name = #{name},</if>
            <if test="password != null">password = #{password},</if>
            <if test="status != null">status = #{status},</if>
            <if test="agentType != null">agent_type = #{agentType},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="loginAt != null">login_at = #{loginAt},</if>
        </set>
        WHERE id = #{id}
    </update>
    
    <update id="updateStatus">
        UPDATE agent
        SET status = #{status}
        WHERE id = #{id}
    </update>
    
    <update id="updateLoginTime">
        UPDATE agent
        SET login_at = NOW()
        WHERE id = #{id}
    </update>

    <select id="findByAgentType" resultType="com.kefang.entity.Agent">
        SELECT * FROM agent 
        WHERE agent_type = #{agentType} AND status = 1 
        LIMIT 1
    </select>

    <select id="findAvailableHumanAgent" resultType="com.kefang.entity.Agent">
        SELECT * FROM agent 
        WHERE agent_type = 1 AND status = 1
        ORDER BY RAND()
        LIMIT 1
    </select>
</mapper> 