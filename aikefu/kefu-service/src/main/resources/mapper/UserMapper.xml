<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kefang.mapper.UserMapper">
    
    <resultMap id="BaseResultMap" type="com.kefang.entity.User">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="phone" property="phone" />
        <result column="password" property="password" />
        <result column="nickname" property="nickname" />
        <result column="avatar" property="avatar" />
        <result column="remark" property="remark" />
        <result column="email" property="email" />
        <result column="address" property="address" />
        <result column="age" property="age" />
        <result column="gender" property="gender" />
        <result column="vip_level" property="vipLevel" />
        <result column="datasource" property="datasource" />
        <result column="is_merchant" property="isMerchant" />
        <result column="is_technician" property="isTechnician" />
        <result column="channel" property="channel" />
        <result column="extra1" property="extra1" />
        <result column="extra2" property="extra2" />
        <result column="extra3" property="extra3" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>
    
    <sql id="Base_Column_List">
        id, user_id, phone, password, nickname, avatar, remark, email, address, age, gender, vip_level, datasource, is_merchant, is_technician, channel, extra1, extra2, extra3, created_at, updated_at
    </sql>
    
    <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE id = #{id}
    </select>
    
    <select id="selectByPhone" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE phone = #{phone}
    </select>
    
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
    </select>

    <insert id="insert" parameterType="com.kefang.entity.User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user (user_id, phone, password, nickname, avatar, remark, email, address, age, gender, vip_level, datasource, is_merchant, is_technician, channel, extra1, extra2, extra3)
        VALUES (#{userId}, #{phone}, #{password}, #{nickname}, #{avatar}, #{remark}, #{email}, #{address}, #{age}, #{gender}, #{vipLevel}, #{datasource}, #{isMerchant}, #{isTechnician}, #{channel}, #{extra1}, #{extra2}, #{extra3})
    </insert>
    
    <update id="update" parameterType="com.kefang.entity.User">
        UPDATE user
        <set>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="password != null">password = #{password},</if>
            <if test="nickname != null">nickname = #{nickname},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="email != null">email = #{email},</if>
            <if test="address != null">address = #{address},</if>
            <if test="age != null">age = #{age},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="vipLevel != null">vip_level = #{vipLevel},</if>
            <if test="datasource != null">datasource = #{datasource},</if>
            <if test="isMerchant != null">is_merchant = #{isMerchant},</if>
            <if test="isTechnician != null">is_technician = #{isTechnician},</if>
            <if test="channel != null">channel = #{channel},</if>
            <if test="extra1 != null">extra1 = #{extra1},</if>
            <if test="extra2 != null">extra2 = #{extra2},</if>
            <if test="extra3 != null">extra3 = #{extra3},</if>
            updated_at = NOW()
        </set>
        WHERE id = #{id}
    </update>
</mapper> 