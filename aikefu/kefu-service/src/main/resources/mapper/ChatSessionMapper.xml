<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kefang.mapper.ChatSessionMapper">
    
    <resultMap id="BaseResultMap" type="com.kefang.entity.ChatSession">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="agent_id" property="agentId" />
        <result column="status" property="status" />
        <result column="current_agent_type" property="currentAgentType" />
        <result column="transfer_requested" property="transferRequested" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="closed_by" property="closedBy" />
        <result column="satisfaction_level" property="satisfactionLevel" />
        <result column="feedback_content" property="feedbackContent" />
        <result column="is_solved" property="isSolved" />
        <result column="evaluation_time" property="evaluationTime" />
        <result column="solution_description" property="solutionDescription" />
        <result column="last_active_time" property="lastActiveTime" />
        <result column="datasource" property="datasource" />
        <result column="collection_name" property="collectionName" />
        <result column="scene" property="scene" />
        <result column="channel" property="channel" />
        <association property="user" javaType="com.kefang.entity.User">
            <id column="user_id" property="id" />
            <result column="user_user_id" property="userId" />
            <result column="user_phone" property="phone" />
            <result column="user_nickname" property="nickname" />
            <result column="user_avatar" property="avatar" />
            <result column="user_remark" property="remark" />
            <result column="user_email" property="email" />
            <result column="user_address" property="address" />
            <result column="user_age" property="age" />
            <result column="user_gender" property="gender" />
            <result column="user_vip_level" property="vipLevel" />
            <result column="user_is_merchant" property="isMerchant" />
            <result column="user_is_technician" property="isTechnician" />
        </association>
        <association property="agent" javaType="com.kefang.entity.Agent">
            <id column="agent_id" property="id" />
            <result column="agent_no" property="agentNo" />
            <result column="agent_name" property="name" />
            <result column="agent_status" property="status" />
            <result column="agent_type" property="agentType" />
        </association>
    </resultMap>
    
    <sql id="Base_Column_List">
        id, user_id, agent_id, status, current_agent_type, transfer_requested, start_time, end_time, closed_by,
        satisfaction_level, feedback_content, is_solved, evaluation_time, solution_description, last_active_time,
        datasource, collection_name, scene, channel
    </sql>
    
    <sql id="Join_Column_List">
        s.id, s.user_id, s.agent_id, s.status, s.current_agent_type, s.transfer_requested, s.start_time, s.end_time, s.closed_by,
        s.satisfaction_level, s.feedback_content, s.is_solved, s.evaluation_time, s.solution_description, s.last_active_time,
        s.datasource, s.collection_name, s.scene, s.channel,
        u.user_id as user_user_id, u.phone as user_phone, u.nickname as user_nickname, u.avatar as user_avatar,
        u.remark as user_remark, u.email as user_email, u.address as user_address, 
        u.age as user_age, u.gender as user_gender, u.vip_level as user_vip_level,
        u.is_merchant as user_is_merchant, u.is_technician as user_is_technician,
        a.agent_no, a.name as agent_name, a.status as agent_status, a.agent_type
    </sql>
    
    <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT <include refid="Join_Column_List" />
        FROM chat_session s
        LEFT JOIN user u ON s.user_id = u.id
        LEFT JOIN agent a ON s.agent_id = a.id
        WHERE s.id = #{id}
    </select>
    
    <select id="selectByUserId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT <include refid="Join_Column_List" />
        FROM chat_session s
        LEFT JOIN user u ON s.user_id = u.id
        LEFT JOIN agent a ON s.agent_id = a.id
        WHERE s.user_id = #{userId}    AND s.status != 2
        ORDER BY s.last_active_time DESC
    </select>
    
    <select id="selectByAgentId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT <include refid="Join_Column_List" />
        FROM chat_session s
        LEFT JOIN user u ON s.user_id = u.id
        LEFT JOIN agent a ON s.agent_id = a.id
        WHERE s.agent_id = #{agentId} AND s.is_solved = 0
        ORDER BY s.last_active_time DESC
    </select>
    
    <select id="selectActiveByAgentId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT <include refid="Join_Column_List" />
        FROM chat_session s
        LEFT JOIN user u ON s.user_id = u.id
        LEFT JOIN agent a ON s.agent_id = a.id
        WHERE s.agent_id = #{agentId} AND s.status = 1
        ORDER BY s.last_active_time DESC
    </select>
    
    <select id="selectQueueSessions" resultMap="BaseResultMap">
        SELECT <include refid="Join_Column_List" />
        FROM chat_session s
        LEFT JOIN user u ON s.user_id = u.id
        LEFT JOIN agent a ON s.agent_id = a.id
        WHERE s.status = 0
        ORDER BY s.last_active_time DESC
    </select>
    
    <select id="selectSolvedSessions" resultMap="BaseResultMap">
        SELECT <include refid="Join_Column_List" />
        FROM chat_session s
        LEFT JOIN user u ON s.user_id = u.id
        LEFT JOIN agent a ON s.agent_id = a.id
        WHERE s.is_solved = 1
        ORDER BY s.evaluation_time DESC
    </select>
    
    <select id="selectBySatisfactionLevel" resultMap="BaseResultMap">
        SELECT <include refid="Join_Column_List" />
        FROM chat_session s
        LEFT JOIN user u ON s.user_id = u.id
        LEFT JOIN agent a ON s.agent_id = a.id
        WHERE s.satisfaction_level = #{satisfactionLevel}
        ORDER BY s.evaluation_time DESC
    </select>
    
    <select id="selectByFilters" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT <include refid="Join_Column_List" />
        FROM chat_session s
        LEFT JOIN user u ON s.user_id = u.id
        LEFT JOIN agent a ON s.agent_id = a.id
        <where>
            <!-- 基本条件：客服ID -->
            <!-- 动态生成 IN 条件 -->
            <if test="agentId != null and !agentId.isEmpty()">
                AND s.agent_id IN
                <foreach collection="agentId" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            
            <!-- 日期范围过滤 -->
            <if test="startDate != null">
                AND s.start_time &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND s.start_time &lt;= CONCAT(#{endDate}, ' 23:59:59')
            </if>
            
            <!-- 客服类型过滤 -->
            <if test="agentType != null">
                AND s.current_agent_type = #{agentType}
            </if>
            
            <!-- 会话状态过滤 -->
            <if test="status != null">
                AND s.status = #{status}
            </if>
            
            <!-- 问题解决状态过滤 -->
            <if test="isSolved != null">
                AND s.is_solved = #{isSolved}
            </if>
            
            <!-- 满意度等级过滤 -->
            <if test="satisfactionLevel != null">
                AND s.satisfaction_level = #{satisfactionLevel}
            </if>
            
            <!-- 数据源过滤 -->
            <if test="datasource != null and datasource != ''">
                AND s.datasource = #{datasource}
            </if>
            
            <!-- 知识库集合名称过滤 -->
            <if test="collectionName != null and collectionName != ''">
                AND s.collection_name = #{collectionName}
            </if>
            
            <!-- 对话应用场景过滤 -->
            <if test="scene != null and scene != ''">
                AND s.scene = #{scene}
            </if>
            
            <!-- 渠道来源过滤 -->
            <if test="channel != null and channel != ''">
                AND s.channel = #{channel}
            </if>
        </where>
        ORDER BY s.last_active_time DESC
        <if test="offset != null and size != null">
            LIMIT #{offset}, #{size}
        </if>
    </select>
    
    <select id="countByFilters" resultType="int" parameterType="java.util.Map">
        SELECT COUNT(*)
        FROM chat_session s
        <where>
            <if test="agentId != null and !agentId.isEmpty()">
                AND s.agent_id IN
                <foreach collection="agentId" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <!-- 日期范围过滤 -->
            <if test="startDate != null">
                AND s.start_time &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND s.start_time &lt;= CONCAT(#{endDate}, ' 23:59:59')
            </if>
            
            <!-- 客服类型过滤 -->
            <if test="agentType != null">
                AND s.current_agent_type = #{agentType}
            </if>
            
            <!-- 会话状态过滤 -->
            <if test="status != null">
                AND s.status = #{status}
            </if>
            
            <!-- 问题解决状态过滤 -->
            <if test="isSolved != null">
                AND s.is_solved = #{isSolved}
            </if>
            
            <!-- 满意度等级过滤 -->
            <if test="satisfactionLevel != null">
                AND s.satisfaction_level = #{satisfactionLevel}
            </if>
            
            <!-- 数据源过滤 -->
            <if test="datasource != null and datasource != ''">
                AND s.datasource = #{datasource}
            </if>
            
            <!-- 知识库集合名称过滤 -->
            <if test="collectionName != null and collectionName != ''">
                AND s.collection_name = #{collectionName}
            </if>
            
            <!-- 对话应用场景过滤 -->
            <if test="scene != null and scene != ''">
                AND s.scene = #{scene}
            </if>
            
            <!-- 渠道来源过滤 -->
            <if test="channel != null and channel != ''">
                AND s.channel = #{channel}
            </if>
        </where>
    </select>
    <select id="selectList" resultType="com.kefang.entity.ChatSession"></select>
    <select id="countByFiltersList" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM chat_session s
        <where>
            <!-- 会话状态过滤 -->
            s.status != 2

            <if test="agentId != null and !agentId.isEmpty()">
                AND s.agent_id IN
                <foreach collection="agentId" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <!-- 日期范围过滤 -->
            <if test="startDate != null">
                AND s.start_time &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND s.start_time &lt;= CONCAT(#{endDate}, ' 23:59:59')
            </if>

            <!-- 客服类型过滤 -->
            <if test="agentType != null">
                AND s.current_agent_type = #{agentType}
            </if>

            <!-- 问题解决状态过滤 -->
            <if test="isSolved != null">
                AND s.is_solved = #{isSolved}
            </if>

            <!-- 满意度等级过滤 -->
            <if test="satisfactionLevel != null">
                AND s.satisfaction_level = #{satisfactionLevel}
            </if>
        </where>


    </select>
    <select id="selectByFiltersList" resultType="com.kefang.entity.ChatSession">
        SELECT <include refid="Join_Column_List" />
        FROM chat_session s
        LEFT JOIN user u ON s.user_id = u.id
        LEFT JOIN agent a ON s.agent_id = a.id
        <where>
            <!-- 会话状态过滤 -->
            s.status != 2
            <!-- 基本条件：客服ID -->
            <!-- 动态生成 IN 条件 -->
            <if test="agentId != null and !agentId.isEmpty()">
                AND s.agent_id IN
                <foreach collection="agentId" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            <!-- 日期范围过滤 -->
            <if test="startDate != null">
                AND s.start_time &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND s.start_time &lt;= CONCAT(#{endDate}, ' 23:59:59')
            </if>

            <!-- 客服类型过滤 -->
            <if test="agentType != null">
                AND s.current_agent_type = #{agentType}
            </if>

            <!-- 问题解决状态过滤 -->
            <if test="isSolved != null">
                AND s.is_solved = #{isSolved}
            </if>

            <!-- 满意度等级过滤 -->
            <if test="satisfactionLevel != null">
                AND s.satisfaction_level = #{satisfactionLevel}
            </if>
        </where>
        ORDER BY s.last_active_time DESC
        <if test="offset != null and size != null">
            LIMIT #{offset}, #{size}
        </if>


    </select>

    <insert id="insert" parameterType="com.kefang.entity.ChatSession" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO chat_session (
            user_id,
            agent_id,
            status,
            current_agent_type,
            transfer_requested,
            start_time,
            channel,
            datasource,
            collection_name,
            scene
        ) VALUES (
                     #{userId},
                     #{agentId},
                     #{status},
                     #{currentAgentType},
                     #{transferRequested},
                     NOW(),
                     #{channel},
                     #{datasource},
                     #{collectionName},
                     #{scene}
                 )
    </insert>

    <update id="update" parameterType="com.kefang.entity.ChatSession">
        UPDATE chat_session
        <set>
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="currentAgentType != null">current_agent_type = #{currentAgentType},</if>
            <if test="transferRequested != null">transfer_requested = #{transferRequested},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="closedBy != null">closed_by = #{closedBy},</if>
            <if test="satisfactionLevel != null">satisfaction_level = #{satisfactionLevel},</if>
            <if test="feedbackContent != null">feedback_content = #{feedbackContent},</if>
            <if test="isSolved != null">is_solved = #{isSolved},</if>
            <if test="evaluationTime != null">evaluation_time = #{evaluationTime},</if>
            <if test="solutionDescription != null">solution_description = #{solutionDescription},</if>
            <if test="datasource != null">datasource = #{datasource},</if>
            <if test="collectionName != null">collection_name = #{collectionName},</if>
            <if test="scene != null">scene = #{scene},</if>
            <if test="channel != null">channel = #{channel},</if>
        </set>
        WHERE id = #{id}
    </update>
    
    <update id="closeSession">
        UPDATE chat_session
        SET status = 3, end_time = NOW(), closed_by = #{closedBy},current_agent_type = 2,agent_id = 6,transfer_requested = 0
        WHERE id = #{id}
    </update>
    
    <update id="assignAgent">
        UPDATE chat_session
        SET agent_id = #{agentId}, status = 1, current_agent_type = 1
        WHERE id = #{id}
    </update>
    
    <update id="addEvaluation">
        UPDATE chat_session
        SET satisfaction_level = #{satisfactionLevel},
            feedback_content = #{feedbackContent},
            is_solved = #{isSolved},
            user_suggestion = #{userSuggestion},
            evaluation_time = NOW()
        WHERE id = #{id}
    </update>
    
    <update id="addSolutionDescription">
        UPDATE chat_session
        SET solution_description = #{solutionDescription},
            is_solved = #{isSolved}
        WHERE id = #{id}
    </update>
    <update id="updateById" parameterType="com.kefang.entity.ChatSession">
        UPDATE chat_session
        SET agent_id = #{agentId},
            status = #{status}
        WHERE id = #{id}
    </update>
    <!-- 根据过滤条件查询会话 -->
    <select id="getSessionsByFilters" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT * FROM chat_session
        <where>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="agentId != null">
                AND agent_id = #{agentId}
            </if>
            <if test="isSolved != null">
                AND is_solved = #{isSolved}
            </if>
            <if test="satisfactionLevel != null">
                AND satisfaction_level = #{satisfactionLevel}
            </if>
        </where>
        ORDER BY last_active_time DESC
    </select>

    <!-- 获取消息发送者类型的统计数据 -->
    <select id="getMessageStatsBySenderType" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT
        sender_type as name,
        COUNT(*) as value
        FROM chat_message
        <where>
            <if test="startDate != null">
                AND DATE(created_at) >= #{startDate}
            </if>
            <if test="endDate != null">
                AND DATE(created_at) &lt;= #{endDate}
            </if>
        </where>
        GROUP BY sender_type
        ORDER BY value DESC
    </select>

    <!-- 获取消息数据来源的统计数据 -->
    <select id="getMessageStatsByDatasource" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT
        IFNULL(datasource, '未知来源') as name,
        COUNT(*) as value
        FROM chat_message
        <where>
            <if test="startDate != null">
                AND DATE(created_at) >= #{startDate}
            </if>
            <if test="endDate != null">
                AND DATE(created_at) &lt;= #{endDate}
            </if>
        </where>
        GROUP BY datasource
        ORDER BY value DESC
    </select>
    
    <!-- 获取数据源下拉选项 -->
    <select id="getDatasourceOptions" resultType="java.util.Map">
        SELECT 
            IFNULL(datasource, '未知') as name,
            COUNT(1) as count
        FROM chat_session
        GROUP BY datasource
        ORDER BY count DESC
    </select>
    
    <!-- 获取知识库集合下拉选项 -->
    <select id="getCollectionOptions" resultType="java.util.Map">
        SELECT 
            IFNULL(collection_name, '未知') as name,
            COUNT(1) as count
        FROM chat_session
        GROUP BY collection_name
        ORDER BY count DESC
    </select>
    
    <!-- 获取对话场景下拉选项 -->
    <select id="getSceneOptions" resultType="java.util.Map">
        SELECT 
            IFNULL(scene, '未知') as name,
            COUNT(1) as count
        FROM chat_session
        GROUP BY scene
        ORDER BY count DESC
    </select>
</mapper> 