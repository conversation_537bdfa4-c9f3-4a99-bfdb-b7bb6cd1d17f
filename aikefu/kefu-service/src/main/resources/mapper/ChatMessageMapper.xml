<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kefang.mapper.ChatMessageMapper">
    
    <resultMap id="BaseResultMap" type="com.kefang.entity.ChatMessage">
        <id column="id" property="id" />
        <result column="session_id" property="sessionId" />
        <result column="sender_id" property="senderId" />
        <result column="sender_type" property="senderType" />
        <result column="content" property="content" />
        <result column="msg_type" property="msgType" />
        <result column="is_read" property="isRead" />
        <result column="created_at" property="createdAt" />
        <result column="scene" property="scene" />
        <result column="datasource" property="datasource" />
        <result column="collection_name" property="collectionName" />
        <result column="channel" property="channel" />
    </resultMap>
    
    <sql id="Base_Column_List">
        id, session_id, sender_id, sender_type, content, msg_type, is_read, created_at, scene, datasource, collection_name, channel
    </sql>
    
    <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT <include refid="Base_Column_List" />
        FROM chat_message
        WHERE id = #{id}
    </select>
    
    <select id="selectBySessionId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT <include refid="Base_Column_List" />
        FROM chat_message
        WHERE session_id = #{sessionId}
        ORDER BY created_at ASC
    </select>
    
    <select id="selectPageBySessionId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM chat_message
        WHERE session_id = #{sessionId}
        ORDER BY created_at DESC
        LIMIT #{offset}, #{limit}
    </select>
    
    <select id="countUnreadMessages" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM chat_message
        WHERE session_id = #{sessionId} 
        AND sender_type = #{senderType}
        AND is_read = 0
    </select>
    
    <insert id="insert" parameterType="com.kefang.entity.ChatMessage" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO chat_message (session_id, sender_id, content, msg_type, sender_type, is_read, created_at, scene, datasource, collection_name, channel)
        VALUES (#{sessionId}, #{senderId}, #{content}, #{msgType}, #{senderType}, #{isRead}, NOW(), #{scene}, #{datasource}, #{collectionName}, #{channel})
    </insert>
    
    <update id="updateReadStatus">
        UPDATE chat_message
        SET is_read = 1
        WHERE session_id = #{sessionId} AND sender_type = #{senderType}
    </update>

    <!-- 查询会话最近一条消息 -->
    <select id="selectLatestBySessionId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT <include refid="Base_Column_List" />
        FROM chat_message
        WHERE session_id = #{sessionId}
        ORDER BY created_at DESC
        LIMIT 1
    </select>
    
    <!-- 按类型查询会话消息数量 -->
    <select id="countMessagesByType" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM chat_message
        WHERE session_id = #{sessionId}
        <if test="msgType != null">
            AND msg_type = #{msgType}
        </if>
    </select>
    
    <!-- 按时间范围查询会话消息 -->
    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM chat_message
        WHERE session_id = #{sessionId}
        <if test="startTime != null">
            AND created_at &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND created_at &lt;= #{endTime}
        </if>
        ORDER BY created_at ASC
    </select>
    
    <!-- 按条件查询消息（支持关键词搜索和类型过滤） -->
    <select id="selectMessagesByCondition" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM chat_message
        <where>
            <if test="sessionId != null">
                session_id = #{sessionId}
            </if>
            <if test="keyword != null and keyword != ''">
                AND content LIKE CONCAT('%', #{keyword}, '%')
            </if>
            <if test="msgType != null">
                AND msg_type = #{msgType}
            </if>
            <if test="senderType != null">
                AND sender_type = #{senderType}
            </if>
            <if test="startTime != null">
                AND created_at &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND created_at &lt;= #{endTime}
            </if>
            <if test="datasource != null and datasource != ''">
                AND datasource = #{datasource}
            </if>
            <if test="scene != null and scene != ''">
                AND scene = #{scene}
            </if>
            <if test="channel != null and channel != ''">
                AND channel = #{channel}
            </if>
        </where>
        ORDER BY created_at DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>
    
    <!-- 统计条件查询的消息总数 -->
    <select id="countMessagesByCondition" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM chat_message
        <where>
            <if test="sessionId != null">
                session_id = #{sessionId}
            </if>
            <if test="keyword != null and keyword != ''">
                AND content LIKE CONCAT('%', #{keyword}, '%')
            </if>
            <if test="msgType != null">
                AND msg_type = #{msgType}
            </if>
            <if test="senderType != null">
                AND sender_type = #{senderType}
            </if>
            <if test="startTime != null">
                AND created_at &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND created_at &lt;= #{endTime}
            </if>
            <if test="datasource != null and datasource != ''">
                AND datasource = #{datasource}
            </if>
            <if test="scene != null and scene != ''">
                AND scene = #{scene}
            </if>
            <if test="channel != null and channel != ''">
                AND channel = #{channel}
            </if>
        </where>
    </select>
</mapper> 