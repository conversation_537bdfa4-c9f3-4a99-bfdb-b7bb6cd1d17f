<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kefang.mapper.FaqMapper">
    
    <resultMap id="BaseResultMap" type="com.kefang.entity.Faq">
        <id column="id" property="id" />
        <result column="question" property="question" />
        <result column="answer" property="answer" />
        <result column="category" property="category" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>
    
    <sql id="Base_Column_List">
        id, question, answer, category, created_at, updated_at
    </sql>
    
    <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT <include refid="Base_Column_List" />
        FROM faq
        WHERE id = #{id}
    </select>
    
    <select id="selectByCategory" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT <include refid="Base_Column_List" />
        FROM faq
        WHERE category = #{category}
        ORDER BY updated_at DESC
    </select>
    
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM faq
        ORDER BY  updated_at DESC
    </select>
    
    <insert id="insert" parameterType="com.kefang.entity.Faq" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO faq (question, answer, category, created_at, updated_at)
        VALUES (#{question}, #{answer}, #{category}, NOW(), NOW())
    </insert>
    
    <update id="update" parameterType="com.kefang.entity.Faq">
        UPDATE faq
        <set>
            <if test="question != null">question = #{question},</if>
            <if test="answer != null">answer = #{answer},</if>
            <if test="category != null">category = #{category},</if>
            <!-- updated_at会自动更新，无需手动设置 -->
        </set>
        WHERE id = #{id}
    </update>
    
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM faq
        WHERE id = #{id}
    </delete>
</mapper> 