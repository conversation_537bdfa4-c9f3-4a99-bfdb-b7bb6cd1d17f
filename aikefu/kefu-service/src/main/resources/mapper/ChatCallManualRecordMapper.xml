<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- 定义一个Mapper，用于处理ChatCallManualRecord数据表 -->
<mapper namespace="com.kefang.mapper.ChatCallManualRecordMapper">

    <!-- 定义结果集映射，将数据库字段映射到ChatCallManualRecord类的属性 -->
    <resultMap id="BaseResultMap" type="com.kefang.entity.ChatCallManualRecord">
        <id column="id" property="id" />
        <result column="agent_id" property="agentId" />
        <result column="session_id" property="sessionId" />
        <result column="user_id" property="userId" />
        <result column="response_duration" property="responseDuration" />
        <result column="request_time" property="requestTime" />
        <result column="accept_time" property="acceptTime" />
        <result column="is_accepted" property="isAccepted" />
    </resultMap>

    <!-- 定义常用SQL片段，包含所有需要查询的列 -->
    <sql id="Base_Column_List">
        id, agent_id, user_id, response_duration, request_time, accept_time, is_accepted
    </sql>

    <!-- 根据主键查询记录 -->
    <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT <include refid="Base_Column_List" />
        FROM chat_call_manual_records
        WHERE id = #{id}
    </select>

    <!-- 根据用户ID查询记录，并按请求时间降序排序 -->
    <select id="selectByUserId" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT <include refid="Base_Column_List" />
        FROM chat_call_manual_records
        WHERE user_id = #{userId}
        ORDER BY request_time DESC
    </select>

    <!-- 根据客服ID查询记录，并按请求时间降序排序 -->
    <select id="selectByAgentId" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT <include refid="Base_Column_List" />
        FROM chat_call_manual_records
        WHERE agent_id = #{agentId}
        ORDER BY request_time DESC
    </select>

    <!-- 根据用户ID和客服ID查询呼叫记录查询记录，并按请求时间降序排序 -->
    <select id="selectByAgentIdAndUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM chat_call_manual_records
        WHERE agent_id = #{agentId} and  user_id = #{userId} and is_accepted = 0 and   session_id = #{sessionId}
        ORDER BY request_time DESC
    </select>
    <!-- 根据sessionId查询呼叫记录 -->
    <select id="selectBysessionId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT <include refid="Base_Column_List" />
        FROM chat_call_manual_records
        WHERE session_id = #{sessionId} and is_accepted = 0
        ORDER BY request_time DESC
    </select>
    <!-- 根据时间范围查询记录，并按请求时间降序排序 -->
    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM chat_call_manual_records
        WHERE request_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY request_time DESC
    </select>

    <!-- 查询所有未接受的记录，并按请求时间升序排序 -->
    <select id="selectUnaccepted" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM chat_call_manual_records
        WHERE is_accepted = 0
        ORDER BY request_time ASC
    </select>

    <!-- 查询所有记录，并按请求时间降序排序 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM chat_call_manual_records
        ORDER BY request_time DESC
    </select>

    <!-- 插入一条新记录 -->
    <insert id="insert" parameterType="com.kefang.entity.ChatCallManualRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO chat_call_manual_records (agent_id, user_id, response_duration, request_time, accept_time, is_accepted, session_id)
        VALUES (#{agentId}, #{userId}, #{responseDuration}, #{requestTime}, #{acceptTime}, #{isAccepted}, #{sessionId})
    </insert>

    <!-- 更新记录 -->
    <update id="update" parameterType="com.kefang.entity.ChatCallManualRecord">
        UPDATE chat_call_manual_records
        <set>
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="responseDuration != null">response_duration = #{responseDuration},</if>
            <if test="requestTime != null">request_time = #{requestTime},</if>
            <if test="acceptTime != null">accept_time = #{acceptTime},</if>
            <if test="isAccepted != null">is_accepted = #{isAccepted},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 更新记录的接受状态 -->
    <update id="updateAcceptStatus">
        UPDATE chat_call_manual_records
        SET accept_time = #{acceptTime},
            response_duration = #{responseDuration},
            is_accepted = 1
        WHERE id = #{id}
    </update>

    <!-- 统计指定时间范围内的记录数 -->
    <select id="countByTimeRange" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM chat_call_manual_records
        WHERE request_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <!-- 统计指定时间范围内已接受的记录数 -->
    <select id="countAcceptedByTimeRange" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM chat_call_manual_records
        WHERE request_time BETWEEN #{startTime} AND #{endTime}
        AND is_accepted = 1
    </select>

    <!-- 计算指定时间范围内已接受记录的平均响应时长 -->
    <select id="avgResponseDuration" resultType="java.lang.Double">
        SELECT AVG(response_duration)
        FROM chat_call_manual_records
        WHERE request_time BETWEEN #{startTime} AND #{endTime}
        AND is_accepted = 1
    </select>
</mapper>
