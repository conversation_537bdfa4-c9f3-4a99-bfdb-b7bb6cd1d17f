<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kefang.mapper.EvaluationMapper">
    
    <resultMap id="BaseResultMap" type="com.kefang.entity.Evaluation">
        <id column="id" property="id" />
        <result column="session_id" property="sessionId" />
        <result column="user_id" property="userId" />
        <result column="score" property="score" />
        <result column="comment" property="comment" />
        <result column="created_at" property="createdAt" />
        <association property="chatSession" javaType="com.kefang.entity.ChatSession">
            <id column="session_id" property="id" />
            <result column="session_status" property="status" />
            <result column="start_time" property="startTime" />
            <result column="end_time" property="endTime" />
            <result column="agent_id" property="agentId" />
        </association>
        <association property="user" javaType="com.kefang.entity.User">
            <id column="user_id" property="id" />
            <result column="nickname" property="nickname" />
            <result column="avatar" property="avatar" />
        </association>
    </resultMap>
    
    <sql id="Base_Column_List">
        id, session_id, user_id, score, comment, created_at
    </sql>
    
    <sql id="Join_Column_List">
        e.id, e.session_id, e.user_id, e.score, e.comment, e.created_at,
        s.status as session_status, s.start_time, s.end_time, s.agent_id,
        u.nickname, u.avatar
    </sql>
    
    <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT <include refid="Join_Column_List" />
        FROM evaluation e
        LEFT JOIN chat_session s ON e.session_id = s.id
        LEFT JOIN user u ON e.user_id = u.id
        WHERE e.id = #{id}
    </select>
    
    <select id="selectBySessionId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT <include refid="Join_Column_List" />
        FROM evaluation e
        LEFT JOIN chat_session s ON e.session_id = s.id
        LEFT JOIN user u ON e.user_id = u.id
        WHERE e.session_id = #{sessionId}
    </select>
    
    <select id="selectByUserId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT <include refid="Join_Column_List" />
        FROM evaluation e
        LEFT JOIN chat_session s ON e.session_id = s.id
        LEFT JOIN user u ON e.user_id = u.id
        WHERE e.user_id = #{userId}
        ORDER BY e.created_at DESC
    </select>
    
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Join_Column_List" />
        FROM evaluation e
        LEFT JOIN chat_session s ON e.session_id = s.id
        LEFT JOIN user u ON e.user_id = u.id
        ORDER BY e.created_at DESC
    </select>
    
    <insert id="insert" parameterType="com.kefang.entity.Evaluation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO evaluation (session_id, user_id, score, comment, created_at)
        VALUES (#{sessionId}, #{userId}, #{score}, #{comment}, NOW())
    </insert>
</mapper> 