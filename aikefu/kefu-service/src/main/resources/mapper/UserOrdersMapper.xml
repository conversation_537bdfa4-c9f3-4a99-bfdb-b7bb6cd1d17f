<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kefang.mapper.UserOrdersMapper">
    
    <resultMap id="BaseResultMap" type="com.kefang.entity.UserOrders">
        <id column="id" property="id" />
        <result column="phone" property="phone" />
        <result column="currentOrder" property="currentOrder" />
        <result column="orderList" property="orderList" />
        <result column="createdAt" property="createdAt" />
        <result column="updatedAt" property="updatedAt" />
    </resultMap>
    
    <sql id="Base_Column_List">
        id, phone, currentOrder, orderList, createdAt, updatedAt
    </sql>
    
    <select id="selectByPhone" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT <include refid="Base_Column_List" />
        FROM user_orders
        WHERE phone = #{phone}
    </select>
    <select id="selectByID" resultType="com.kefang.entity.UserOrders">
        SELECT <include refid="Base_Column_List" />
        FROM user_orders
        WHERE id = #{id}
    </select>

    <insert id="insert" parameterType="com.kefang.entity.UserOrders" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_orders (phone, currentOrder, orderList, createdAt, updatedAt)
        VALUES (#{phone}, #{currentOrder}, #{orderList}, NOW(), NOW())
    </insert>
    
    <update id="update" parameterType="com.kefang.entity.UserOrders">
        UPDATE user_orders
        <set>
            <if test="currentOrder != null">currentOrder = #{currentOrder},</if>
            <if test="orderList != null">orderList = #{orderList},</if>
            updatedAt = NOW()
        </set>
        WHERE phone = #{phone}
    </update>
</mapper> 