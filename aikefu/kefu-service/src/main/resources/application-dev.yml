server:
  port: 8080

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************************
    username: root
    password: 123456
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB      # 单个文件最大大小
      max-request-size: 150MB   # 整个请求最大大小
      enabled: true            # 启用multipart上传

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.kefang.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

# 日志配置
logging:
  level:
    root: INFO
    com.kefang: INFO
    com.kefang.mapper: INFO
    #com.kefang.mapper: DEBUG
    org.springframework: INFO
    org.mybatis: INFO
    org.apache.ibatis: INFO
    com.baomidou.mybatisplus: INFO
    com.zaxxer.hikari: INFO  # HikariCP 的日志级别
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{50} - %msg%n'

# AI服务配置
ai:
  service-url: http://127.0.0.1:8000

# 微信公众号配置
wechat:
  token: BsqXUehXW463cv2g6SesIyD0rME7aw
  appid: VRTy6Vuo6nA4GKx
  encodingaeskey: MK1aIUzUytce2qzqM1EkkTqFGZ9f6ztAv1zSKClMIgd

# SSO统一登录配置 测试
#sso:
#  api:
#    url: https://uat.juranguanjia.com/papi/sso/jms/api
#  user:
#    login-url: https://uat.juranguanjia.com/papi/sso/user/login
#  bearhome:
#    url: https://uat.juranguanjia.com/papi/sso/user/status
#hs:
#  app:
#    order:
#      base-url: https://uat.juranguanjia.com/papi
#
#login:
#  mobile: 13301103100
#  password: 13301103100


# SSO统一登录配置 线上
sso:
  api:
    url: https://api.bearhome.cn/papi/sso/jms/api
  user:
    login-url: https://api.bearhome.cn/papi/sso/user/login
  bearhome:
    url: https://api.bearhome.cn/papi/sso/user/status
hs:
  app:
    order:
      base-url: https://api.bearhome.cn/papi

xd:
  base-url:  https://api.bearhome.cn/hsapi/recovery/order/

# 阿里云OSS配置（保持向后兼容）
oss:
  # 线上地址
  # baseUrl: https://api.bearhome.cn/papi
  # OSS服务端点地址
  endpoint: http://oss-cn-beijing.aliyuncs.com
  # 访问密钥ID
  access-key-id: LTAIR4PwcCd6ZFAQ
  # 访问密钥Secret
  access-key-secret: zk4KPaeVNW9M252PSzIfneKhy29ox9
  # 存储桶名称
  bucket-name: juranfile
  # 文件访问域名
  file-host: https://file.juranguanjia.com
  # 文件上传根目录
  root-path: kefu
  # 允许上传的最大文件大小（字节，默认100MB）
  max-file-size: 104857600

login:
  mobile: 13300000000
  password: 123456
# 认证配置
auth:
  # JWT密钥
  jwt-secret: customerServiceSecretKey
  # token有效期（毫秒），默认24小时
  token-expiration: 86400000
  # 刷新token有效期（毫秒），默认7天
  refresh-token-expiration: 604800000
  # 接口白名单，不需要验证的路径
  white-list:
    - /api/user/refresh-token
    - /api/agent/refresh-token
    - /weixin/**
    - /connect/**
    - /connect/wechat/**
    - /api/user/validateToken
    - /api/agent/validateToken
    - /api/sso-auth/**
    - /api/user/auto-register
    - /statisticsTask/**