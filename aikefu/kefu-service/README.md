# 客服系统（Customer Service System）

基于Java+Spring Boot+Vue开发的实时客服聊天系统，支持用户与客服之间的实时消息交流、工单管理、服务评价等功能。

## 技术架构

### 后端
- Java 17
- Spring Boot 2.7.9
- WebSocket（实时通信）
- MyBatis（ORM框架）
- MySQL 8.0（数据库）

### 前端
- Vue 3（Composition API）
- Vuex（状态管理）
- Vue Router（路由）
- Element UI（组件库）
- WebSocket（实时通信）

## 功能模块

### 用户端功能
1. **用户注册/登录**
   - 手机号+密码注册
2. **在线客服聊天**
   - 实时发送/接收消息，支持图片
   - 消息状态（已读/未读）
3. **历史记录查询**
   - 分页加载聊天记录
4. **服务评价**
   - 对客服会话评分（1-5星）

### 客服端功能
1. **工单管理**
   - 查看待处理/进行中的用户会话
2. **实时聊天**
   - 接收用户消息，支持富文本（文字、图片）
3. **用户信息查看**
   - 用户基础信息、历史评价
4. **数据统计**
   - 会话量、用户满意度
5. **常见问题汇总**

## 项目结构

```
├── src
│   ├── main
│   │   ├── java
│   │   │   └── com.kefang.customer_service
│   │   │       ├── config          # 配置类
│   │   │       ├── controller      # 控制器
│   │   │       ├── dto             # 数据传输对象
│   │   │       ├── entity          # 实体类
│   │   │       ├── exception       # 异常处理
│   │   │       ├── mapper          # MyBatis映射接口
│   │   │       ├── service         # 服务接口
│   │   │       │   └── impl        # 服务实现
│   │   │       ├── util            # 工具类
│   │   │       ├── vo              # 视图对象
│   │   │       ├── websocket       # WebSocket处理
│   │   │       └── CustomerServiceApplication.java   # 应用入口
│   │   ├── resources
│   │   │   ├── mapper              # MyBatis XML映射文件
│   │   │   ├── application.yml     # 应用配置
│   │   │   └── customer_service.sql # 数据库脚本
```

## 数据库设计

系统包含以下数据表：
- `user` - 用户表
- `agent` - 客服表
- `chat_session` - 会话表
- `chat_message` - 消息表
- `evaluation` - 评价表
- `faq` - 常见问题表

详细设计见`src/main/resources/customer_service.sql`

## 运行说明

### 前置条件
- JDK 17+
- Maven 3.6+
- MySQL 8.0+

### 后端运行步骤
1. 创建数据库并导入数据
   ```sql
   mysql -u root -p < src/main/resources/customer_service.sql
   ```

2. 修改`application.yml`中的数据库连接信息

3. 编译打包运行
   ```shell
   mvn clean package
   java -jar target/customer-service-0.0.1-SNAPSHOT.jar
   ```

4. 访问接口
   - API Base URL: http://localhost:8080/api
   - WebSocket URL: ws://localhost:8080/ws/{role}/{id}

### 接口文档
系统主要包含以下接口模块：
- 用户模块 `/api/user/*`
- 客服模块 `/api/agent/*`
- 会话模块 `/api/session/*`
- 消息模块 `/api/message/*`
- 评价模块 `/api/evaluation/*`
- 常见问题模块 `/api/faq/*`

详细API文档可在系统运行后通过接口测试工具进行探索。

## WebSocket通信说明

系统使用WebSocket进行实时通信，连接URL格式为：`ws://domain:port/ws/{role}/{id}`

- `{role}`: 用户角色，取值 `user`或`agent`
- `{id}`: 用户ID或客服ID

消息格式：
```json
{
  "type": 1,           // 消息类型: 1-文本 2-图片 3-系统通知 4-上线 5-下线
  "from": 0,           // 消息来源: 0-用户 1-客服
  "senderId": 1001,    // 发送者ID
  "receiverId": 2001,  // 接收者ID
  "sessionId": 1,      // 会话ID
  "content": "消息内容", // 消息内容
  "timestamp": 1615482542000 // 时间戳
}
```

## 开发说明

本项目使用Maven构建，开发人员可通过以下步骤进行开发：

1. 克隆代码库
   ```shell
   git clone https://github.com/yourusername/customer-service.git
   ```

2. 导入IDE
   推荐使用IntelliJ IDEA或VS Code

3. 运行项目
   - 直接运行`CustomerServiceApplication.java`

