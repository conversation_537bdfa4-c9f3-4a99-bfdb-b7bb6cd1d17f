<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服系统</title>
</head>

<body>
<div id="app"></div>
<script type="module" src="/src/main.js"></script>

<!-- 微信 JS-SDK 如果不需要兼容小程序，则无需引用此 JS 文件。 -->
<script type="text/javascript" src="//appx/web-view.min.js"></script>
<!-- 支付宝 -->
<script type="text/javascript" src="//res.wx.qq.com/open/js/jweixin-1.4.0.js"></script>

<!-- uni 的 SDK，必须引用。 -->
<script type="text/javascript" src="//js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.1.js"></script>

<script type="text/javascript">
    // 待触发 `UniAppJSBridgeReady` 事件后，即可调用 uni 的 API。
    document.addEventListener('UniAppJSBridgeReady', function () {
    });
</script>

</body>


</html>