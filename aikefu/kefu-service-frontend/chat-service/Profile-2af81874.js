import{_ as W,b as $,x as G,r as f,k as S,u as H,o as K,c as i,d as V,e as B,g as a,w as l,f as d,A as D,j as u,t as Q,q as X,y as Y,W as Z,E as v}from"./index.js";const ee={class:"profile-container"},ae={class:"card-header"},le={key:1},oe={class:"profile-content"},se={class:"avatar-container"},re={class:"security-content"},te={class:"security-item"},ne={class:"dialog-footer"},de="/default-avatar.png",ie={__name:"Profile",setup(ue){const g=$(),m=G(()=>g.userInfo),w=f(!1),b=f({}),o=S({id:"",phone:"",nickname:"",avatar:"",gender:0,email:""}),N={nickname:[{required:!0,message:"请输入昵称",trigger:"blur"},{min:2,max:10,message:"昵称长度应为2-10个字符",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}]},n=S({oldPassword:"",newPassword:"",confirmPassword:""}),I={oldPassword:[{required:!0,message:"请输入当前密码",trigger:"blur"}],newPassword:[{required:!0,validator:(t,e,s)=>{var p;e===""?s(new Error("请输入新密码")):(n.confirmPassword!==""&&((p=x.value)==null||p.validateField("confirmPassword")),s())},trigger:"blur"},{min:6,max:20,message:"密码长度应为6-20位",trigger:"blur"}],confirmPassword:[{required:!0,validator:(t,e,s)=>{e===""?s(new Error("请再次输入新密码")):e!==n.newPassword?s(new Error("两次输入密码不一致")):s()},trigger:"blur"}]},k=f(null),x=f(null),U=f(!1),y=f(!1),h=f(!1),O=H();K(()=>{if(g.loadFromStorage(),!g.isLoggedIn||!g.isUser){O.push("/login");return}q()});const q=()=>{m.value&&(Object.assign(o,{id:m.value.id,phone:m.value.phone,nickname:m.value.nickname||"",avatar:m.value.avatar||"",gender:m.value.gender||0,email:m.value.email||""}),b.value=JSON.parse(JSON.stringify(o)))},M=()=>{w.value=!0},A=()=>{Y.confirm("确定要取消编辑吗？未保存的修改将会丢失","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Object.assign(o,b.value),w.value=!1}).catch(()=>{})},J=async()=>{k.value&&await k.value.validate(async t=>{if(t){U.value=!0;try{const e={nickname:o.nickname,gender:o.gender,email:o.email,avatar:o.avatar},s=await Z(o.id,e);s.code===200?(v.success("保存成功"),g.setUserInfo(s.data),b.value=JSON.parse(JSON.stringify(o)),w.value=!1):v.error(s.message||"保存失败")}catch(e){console.error("保存个人资料失败",e),v.error("保存失败，请稍后重试")}finally{U.value=!1}}})},R=()=>{n.oldPassword="",n.newPassword="",n.confirmPassword="",y.value=!0},T=async()=>{x.value&&await x.value.validate(async t=>{if(t){h.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),v.success("密码修改成功"),y.value=!1}catch(e){console.error("修改密码失败",e),v.error("修改密码失败，请稍后重试")}finally{h.value=!1}}})},j=t=>{if(!t||!t.raw)return;const e=t.raw.type.startsWith("image/"),s=t.raw.size/1024/1024<2;if(!e){v.error("只能上传图片文件");return}if(!s){v.error("图片大小不能超过 2MB");return}const p=new FormData;p.append("file",t.raw),p.append("userId",o.id);const P=new FileReader;P.readAsDataURL(t.raw),P.onload=()=>{o.avatar=P.result}};return(t,e)=>{const s=i("el-button"),p=i("el-avatar"),P=i("el-upload"),_=i("el-input"),c=i("el-form-item"),C=i("el-option"),z=i("el-select"),F=i("el-form"),E=i("el-card"),L=i("el-dialog");return V(),B("div",ee,[a(E,{class:"profile-card"},{header:l(()=>[d("div",ae,[e[12]||(e[12]=d("h3",null,"个人资料",-1)),w.value?(V(),B("div",le,[a(s,{type:"primary",onClick:J,loading:U.value},{default:l(()=>e[10]||(e[10]=[u("保存")])),_:1,__:[10]},8,["loading"]),a(s,{onClick:A},{default:l(()=>e[11]||(e[11]=[u("取消")])),_:1,__:[11]})])):(V(),D(s,{key:0,type:"primary",onClick:M},{default:l(()=>e[9]||(e[9]=[u("编辑")])),_:1,__:[9]}))])]),default:l(()=>[d("div",oe,[d("div",se,[a(p,{size:100,src:o.avatar||de},{default:l(()=>[u(Q(o.nickname?o.nickname.substring(0,1):"用"),1)]),_:1},8,["src"]),w.value?(V(),D(P,{key:0,class:"avatar-upload",action:"","auto-upload":!1,"show-file-list":!1,"on-change":j,accept:"image/*"},{default:l(()=>[a(s,{size:"small",type:"primary"},{default:l(()=>e[13]||(e[13]=[u("更换头像")])),_:1,__:[13]})]),_:1})):X("",!0)]),a(F,{ref_key:"profileForm",ref:k,model:o,rules:N,"label-width":"80px",disabled:!w.value},{default:l(()=>[a(c,{label:"手机号",prop:"phone"},{default:l(()=>[a(_,{modelValue:o.phone,"onUpdate:modelValue":e[0]||(e[0]=r=>o.phone=r),disabled:""},null,8,["modelValue"])]),_:1}),a(c,{label:"昵称",prop:"nickname"},{default:l(()=>[a(_,{modelValue:o.nickname,"onUpdate:modelValue":e[1]||(e[1]=r=>o.nickname=r),placeholder:"请输入昵称"},null,8,["modelValue"])]),_:1}),a(c,{label:"性别",prop:"gender"},{default:l(()=>[a(z,{modelValue:o.gender,"onUpdate:modelValue":e[2]||(e[2]=r=>o.gender=r),placeholder:"请选择性别",style:{width:"100%"}},{default:l(()=>[a(C,{label:"男",value:1}),a(C,{label:"女",value:2}),a(C,{label:"保密",value:0})]),_:1},8,["modelValue"])]),_:1}),a(c,{label:"邮箱",prop:"email"},{default:l(()=>[a(_,{modelValue:o.email,"onUpdate:modelValue":e[3]||(e[3]=r=>o.email=r),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","disabled"])])]),_:1}),a(E,{class:"security-card"},{header:l(()=>e[14]||(e[14]=[d("div",{class:"card-header"},[d("h3",null,"安全设置")],-1)])),default:l(()=>[d("div",re,[d("div",te,[e[16]||(e[16]=d("div",{class:"security-info"},[d("div",{class:"security-title"},"账户密码"),d("div",{class:"security-desc"},"用于保护账户信息安全")],-1)),a(s,{type:"primary",plain:"",onClick:R},{default:l(()=>e[15]||(e[15]=[u("修改密码")])),_:1,__:[15]})])])]),_:1}),a(L,{modelValue:y.value,"onUpdate:modelValue":e[8]||(e[8]=r=>y.value=r),title:"修改密码",width:"400px"},{footer:l(()=>[d("div",ne,[a(s,{onClick:e[7]||(e[7]=r=>y.value=!1)},{default:l(()=>e[17]||(e[17]=[u("取消")])),_:1,__:[17]}),a(s,{type:"primary",onClick:T,loading:h.value},{default:l(()=>e[18]||(e[18]=[u("确认修改")])),_:1,__:[18]},8,["loading"])])]),default:l(()=>[a(F,{ref:"passwordForm",model:n,rules:I,"label-width":"100px"},{default:l(()=>[a(c,{label:"当前密码",prop:"oldPassword"},{default:l(()=>[a(_,{modelValue:n.oldPassword,"onUpdate:modelValue":e[4]||(e[4]=r=>n.oldPassword=r),type:"password","show-password":"",placeholder:"请输入当前密码"},null,8,["modelValue"])]),_:1}),a(c,{label:"新密码",prop:"newPassword"},{default:l(()=>[a(_,{modelValue:n.newPassword,"onUpdate:modelValue":e[5]||(e[5]=r=>n.newPassword=r),type:"password","show-password":"",placeholder:"请输入新密码"},null,8,["modelValue"])]),_:1}),a(c,{label:"确认新密码",prop:"confirmPassword"},{default:l(()=>[a(_,{modelValue:n.confirmPassword,"onUpdate:modelValue":e[6]||(e[6]=r=>n.confirmPassword=r),type:"password","show-password":"",placeholder:"请再次输入新密码"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},fe=W(ie,[["__scopeId","data-v-35213b54"]]);export{fe as default};
