import{_ as Ie,r as h,x as X,c as w,d as c,e as f,g as i,w as l,f as d,A as P,j as m,q as C,B as Oe,E as p,u as Ve,b as He,v as Re,C as Be,o as ze,n as O,D as je,F as qe,G,t as U,H as Fe,I as Je,h as z,J as _e,K as Ke,L as Qe,y as Ye,M as ie,N as Ge,O as Xe,P as we,s as Ze,Q as et,R as tt}from"./index.js";import{h as he}from"./moment-a9aaa855.js";const st="/customer-service.svg";const nt={class:"evaluation-container"},ot={class:"evaluation-header"},at={key:0,class:"evaluation-body"},rt={class:"evaluation-item"},lt={class:"item-content"},it={class:"evaluation-item"},dt={class:"item-content"},ct={class:"evaluation-item"},ut={class:"item-content"},gt={class:"evaluation-item"},pt={class:"item-content"},vt={class:"evaluation-actions"},ft={key:1,class:"evaluation-body"},mt={__name:"SessionEvaluation",props:{sessionId:{type:[Number,String],required:!0}},emits:["evaluationComplete","skipEvaluation","startNewChat"],setup(pe,{emit:de}){const k=pe,t=de,x=h(5),b=h(1),A=h(""),D=h(""),V=h(!1),E=h(!1),Z=X(()=>x.value>0&&!E.value),ce=async()=>{if(Z.value){E.value=!0;try{const n=await Oe(k.sessionId,{satisfactionLevel:x.value,feedbackContent:A.value,userSuggestion:D.value,isSolved:b.value});n.code===200?(p.success("评价提交成功，感谢您的反馈"),V.value=!0,t("evaluationComplete")):p.error(n.message||"评价提交失败")}catch(n){console.error("提交评价失败",n),p.error("评价提交失败，请稍后重试")}finally{E.value=!1}}},L=()=>{t("skipEvaluation")},ee=()=>{t("startNewChat")};return(n,u)=>{const M=w("el-tag"),F=w("el-rate"),j=w("el-radio"),ue=w("el-radio-group"),te=w("el-input"),se=w("el-button"),le=w("el-result"),ge=w("el-card");return c(),f("div",nt,[i(ge,{class:"evaluation-card"},{header:l(()=>[d("div",ot,[u[5]||(u[5]=d("span",null,"会话评价",-1)),V.value?(c(),P(M,{key:0,type:"info"},{default:l(()=>u[4]||(u[4]=[m("已评价")])),_:1,__:[4]})):C("",!0)])]),default:l(()=>[V.value?(c(),f("div",ft,[i(le,{icon:"success",title:"感谢您的评价","sub-title":"您的反馈是我们持续改进的动力"},{extra:l(()=>[i(se,{type:"primary",onClick:ee},{default:l(()=>u[14]||(u[14]=[m("开始新会话")])),_:1,__:[14]})]),_:1})])):(c(),f("div",at,[d("div",rt,[u[6]||(u[6]=d("div",{class:"item-label"},"服务满意度",-1)),d("div",lt,[i(F,{modelValue:x.value,"onUpdate:modelValue":u[0]||(u[0]=N=>x.value=N),colors:["#99A9BF","#F7BA2A","#FF9900"],texts:["很不满意","不满意","一般","满意","非常满意"],"show-text":""},null,8,["modelValue"])])]),d("div",it,[u[9]||(u[9]=d("div",{class:"item-label"},"问题是否解决",-1)),d("div",dt,[i(ue,{modelValue:b.value,"onUpdate:modelValue":u[1]||(u[1]=N=>b.value=N)},{default:l(()=>[i(j,{label:1},{default:l(()=>u[7]||(u[7]=[m("已解决")])),_:1,__:[7]}),i(j,{label:0},{default:l(()=>u[8]||(u[8]=[m("未解决")])),_:1,__:[8]})]),_:1},8,["modelValue"])])]),d("div",ct,[u[10]||(u[10]=d("div",{class:"item-label"},"反馈意见",-1)),d("div",ut,[i(te,{modelValue:A.value,"onUpdate:modelValue":u[2]||(u[2]=N=>A.value=N),type:"textarea",rows:3,placeholder:"请输入您的反馈意见（选填）",resize:"none"},null,8,["modelValue"])])]),d("div",gt,[u[11]||(u[11]=d("div",{class:"item-label"},"您对我们的建议",-1)),d("div",pt,[i(te,{modelValue:D.value,"onUpdate:modelValue":u[3]||(u[3]=N=>D.value=N),type:"textarea",rows:3,placeholder:"请输入对我们服务的建议（选填）",resize:"none"},null,8,["modelValue"])])]),d("div",vt,[i(se,{type:"primary",onClick:ce,disabled:!Z.value},{default:l(()=>u[12]||(u[12]=[m("提交评价")])),_:1,__:[12]},8,["disabled"]),i(se,{onClick:L},{default:l(()=>u[13]||(u[13]=[m("暂不评价")])),_:1,__:[13]})])]))]),_:1})])}}},yt=Ie(mt,[["__scopeId","data-v-e6c8f520"]]);const _t={class:"chat-container"},wt={class:"chat-header"},ht={class:"chat-title"},It={key:0},Tt={key:1},kt={key:0,class:"chat-actions"},Ct={key:0,class:"welcome-container"},St={class:"welcome-text"},bt={key:0,class:"session-status"},At={key:1,class:"message-avatar"},xt={class:"message-content"},Dt={key:0,class:"message-sender"},Et={class:"message-time"},Mt={key:1,class:"message-image"},$t={class:"image-error"},Wt={class:"image-loading"},Ut={key:2,class:"message-system"},Pt={key:3,class:"message-text",style:{whiteSpace:"pre-wrap"}},Lt={key:2,class:"empty-message"},Nt={key:1,class:"chat-input"},Ot={key:0,class:"common-questions"},Vt={class:"question-title"},Ht={class:"question-list"},Rt=["onClick"],Bt={class:"input-actions"},zt={class:"input-textarea"},jt={key:0,class:"chat-closed-tip"},qt={class:"input-send"},Ft={__name:"Chat",setup(pe){const de=Ve(),k=He(),t=Re(),x=h(null),b=h(!1),A=h(""),D=h(!1),V=h(null),E=h(!1),Z=h(["熊洞智家是做什么的？","忘记密码怎么办？","如何查看订单状态？","如何申请退款？","会员等级有哪些权益？","如何联系客服？"]),ce=e=>{A.value=e,E.value=!1},L=X(()=>k.userInfo),ee=X(()=>t.hasActiveConversation),n=X(()=>t.currentConversation),u=X(()=>t.messages),M=h(!1),F=X(()=>n.value?!!(n.value.currentAgentType===2||n.value.agent&&n.value.agent.agentType===2||n.value.agentId&&t.aiAgentId&&n.value.agentId===t.aiAgentId):!1);Be(u,()=>{O(()=>{H()})},{deep:!0}),ze(async()=>{if(console.log("Chat组件挂载 - 开始初始化"),k.loadFromStorage(),!k.isLoggedIn||!k.isUser){console.warn("用户未登录或不是用户角色，跳转到登录页"),de.push("/login");return}const e=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);console.log("设备信息:",{isMobile:e,userAgent:navigator.userAgent,userId:k.userId,token:k.token?"存在":"不存在",platform:navigator.platform,windowWidth:window.innerWidth,windowHeight:window.innerHeight}),t.setInChatPage(!0),t.ws&&(console.log("关闭可能存在的旧WebSocket连接"),t.closeWebSocket()),window.addEventListener("chat-transfer-notification",me),console.log("初始化过程状态 - 开始:",{inChatPage:t.isInChatPage,connected:t.connected,hasWs:!!t.ws});try{console.log("开始初始化WebSocket连接"),t.initWebSocket();const a=()=>{if(console.log("WebSocket连接状态检查:",{connected:t.connected,readyState:t.ws?t.ws.readyState:"no-websocket",inChatPage:t.isInChatPage}),!t.connected&&t.ws&&t.ws.readyState===WebSocket.CONNECTING){console.log("WebSocket正在连接中，再等待3秒..."),setTimeout(a,3e3);return}t.connected||(console.log("首次连接失败，启动备用机制..."),t.initWebSocket(),setTimeout(()=>{t.connected||(console.log("再次连接失败，启动HTTP轮询作为备份"),t.startPolling())},5e3))};setTimeout(a,3e3)}catch(a){console.error("WebSocket初始化过程中出错:",a)}await ue(),O(()=>{H()});const s=()=>{O(()=>{H()})};window.addEventListener("chat-message-received",s),console.log("会话列表:",JSON.stringify(t.conversations.map(a=>({id:a.id,status:a.status,currentAgentType:a.currentAgentType})))),setTimeout(()=>{console.log("当前消息状态:",t.messages.map(a=>({id:a.id,content:a.content&&a.content.length>20?a.content.substring(0,20)+"...":a.content,from:a.from,senderType:a.senderType,msgType:a.msgType,display:Ae(a)})))},2e3),window.addEventListener("online",j),window.addEventListener("offline",j),console.log("Chat组件挂载 - 初始化完成")});const j=e=>{console.log("网络状态变化:",e.type),e.type==="online"?(console.log("网络恢复，尝试重新连接WebSocket"),t.closeWebSocket(),setTimeout(()=>{t.initWebSocket()},1e3)):e.type==="offline"&&(console.log("网络断开，标记WebSocket为断开状态"),t.connected=!1,t.stopHeartbeat())};je(()=>{console.log("用户聊天组件卸载，清理资源"),sessionUpdateInterval&&(clearInterval(sessionUpdateInterval),sessionUpdateInterval=null),window.removeEventListener("chat-message-received",handleNewMessage),window.removeEventListener("online",j),window.removeEventListener("offline",j),window.removeEventListener("chat-transfer-notification",me),t.setInChatPage(!1),t.stopPolling()});const ue=async()=>{if(!(!k.isLoggedIn||!k.isUser)){b.value=!0;try{await t.fetchConversations(),console.log("获取会话列表成功，会话数量:",t.conversations.length);const e=t.conversations.find(s=>[0,1,3].includes(s.status));console.log("未结束的会话:",e),e?(await t.setCurrentConversation(e),console.log("已设置当前会话:",e.id),console.log("开始加载历史消息, 会话ID:",e.id),await t.loadMessages(e.id,100),console.log("消息加载完成, 消息数量:",t.messages.length),p.info("检测到未结束的会话，已自动恢复")):(t.clearCurrentConversation(),console.log("没有未结束的会话，显示欢迎界面"))}catch(e){console.error("加载会话失败",e),p.error("加载会话失败")}finally{b.value=!1}}},te=async()=>{b.value=!0;try{const e=t.conversations.find(r=>r.status===1);if(e){await t.setCurrentConversation(e),p.info("您已有正在进行的会话");return}const s=parseInt(k.userId,10);console.log("准备创建会话，用户ID:",s,"用户信息:",k.userInfo);const a=await t.createNewConversation({userId:s,channel:"PC端",datasource:"PC端",collection_name:"information",scene:"PC端"});a.code===200?p.success("AI客服已为您接入，请问有什么可以帮您？"):p.error(a.message||"创建会话失败")}catch(e){console.error("创建会话失败",e),p.error("创建会话失败，请稍后重试")}finally{b.value=!1}};h([]);const se=()=>{const e=t.messages.filter(a=>{const r=a.from===0||a.senderType===0,y=a.senderType===2||a.from===1&&a.currentAgentType===2;return(r||y)&&!a.id.toString().startsWith("loading-")&&a.content.trim()!==""}).map(a=>({role:a.from===0||a.senderType===0?"user":"assistant",content:a.content})),s=10;return e.length>s*2?e.slice(-s*2):e},le=async()=>{if(!A.value.trim())return;if(!n.value){p.warning("请先开始会话");return}if(n.value.status===2){p.warning("会话已结束，无法发送消息");return}const e=A.value.trim();A.value="";try{console.log("发送消息:",e),console.log("当前会话状态:",{id:n.value.id,status:n.value.status,agentType:n.value.currentAgentType});const s=n.value.currentAgentType===2;console.log("是否为AI客服对话:",s),n.value.transferRequested===1&&!s&&console.log("会话已转为人工客服，使用正常消息发送流程");const a="local-"+Date.now();if(t.messages.push({id:a,sessionId:n.value.id,content:e,from:0,senderType:0,createdAt:new Date().toISOString()}),console.log("发送消息前的会话状态:",{id:n.value.id,agent:n.value.agent,status:n.value.status,currentAgentType:n.value.currentAgentType,messagesCount:t.messages.length}),await O(),H(),s)try{await t.sendTextMessage(e,null,!0);const r="loading-"+Date.now();t.messages.push({id:r,sessionId:n.value.id,content:"正在思考中...",from:1,senderType:2,createdAt:new Date().toISOString()}),await O(),H();const y="ai-"+Date.now();let g="";const S=t.messages.findIndex($=>$.id===r);if(S!==-1){const $={id:y,sessionId:n.value.id,content:g,from:1,senderType:2,createdAt:new Date().toISOString()};t.messages.splice(S,1,$),console.log("已将loadingMsg替换为aiMsg，ID:",y,"索引:",S),t.messages=[...t.messages]}else console.warn("未找到loadingMsg，ID:",r),t.messages.push({id:y,sessionId:n.value.id,content:g,from:1,senderType:2,createdAt:new Date().toISOString()}),console.log("已添加新的aiMsg，ID:",y);const R=t.messages.findIndex($=>$.id===y);R===-1?(console.error("严重错误：消息未能成功添加到数组中，请检查Vue的响应式更新"),t.messages.push({id:y,sessionId:n.value.id,content:g,from:1,senderType:2,createdAt:new Date().toISOString()}),console.log("已尝试再次添加消息")):console.log("确认消息已存在于数组中，索引:",R);const B="https://berhomellm.cpolar.cn/chat/stream";console.log("使用流式API:",B),console.log("环境变量:",{VITE_API_URL_LLM:"https://berhomellm.cpolar.cn",VITE_API_BASE_URL:"/api"});const I=se();console.log("对话历史:",I),console.log("对话历史数量:",I.length);const J={messages:e,collection_name:"information",stream:!0,history:I,scene:"PC端",channel:"PC端",datasource:"PC端"};console.log("请求数据:",J);const T=await fetch(B,{method:"POST",headers:{"Content-Type":"application/json",Accept:"text/event-stream"},body:JSON.stringify(J)});if(console.log("API响应状态:",T.status),console.log("API响应头:",Object.fromEntries(T.headers.entries())),!T.ok)throw new Error(`HTTP错误，状态: ${T.status}`);const q=T.body.getReader(),o=new TextDecoder;let _="",K=0;for(g="",console.log("开始处理流数据");;){const{done:$,value:oe}=await q.read();if($){console.log("数据流已关闭，总共处理块数:",K);break}const W=o.decode(oe,{stream:!0});_+=W,console.log(`收到新数据: ${W.length} 字节，当前缓冲区: ${_.length} 字节`);let Y,ae=0;for(;(Y=_.indexOf(`

`))>-1;){const re=_.substring(0,Y);_=_.substring(Y+2),ae++;let v="";if(re.startsWith("data:")){if(v=re.substring(5).trim(),!v||v==="data:"||v==="data: "){console.log("跳过空data前缀");continue}}else re.startsWith("大模型输出：")?v=re.substring(6).trim():v=re.trim();if(!v){console.log("跳过空内容块");continue}if((v==="\\n"||v===`
`||v==="<br>"||v==="<br/>")&&(console.log("检测到独立换行符"),v=`
`),v=v.replace(/^data:\s*/g,""),!v.trim()){console.log("清理前缀后内容为空，跳过");continue}g.length>0&&(v.endsWith(".")||v.endsWith("。")||v.endsWith("!")||v.endsWith("！"))&&(console.log("检测到句子结束，添加换行"),v=v+`
`),console.log(`处理消息块 ${K+1}: ${v.length} 字符，内容: "${v}"`),K++,(v.includes(`
`)||v.endsWith(".")||v.endsWith("。"))&&console.log("检测到换行或句子结束，保留格式"),g+=v,console.log(`当前累积内容(${g.length}字符): "${g.substring(0,50)}${g.length>50?"...":""}"`);const ye=t.messages.findIndex(Ne=>Ne.id===y);ye!==-1?(t.messages[ye].content=g,t.messages=[...t.messages],await O(),H()):(console.warn("找不到AI消息，ID:",y),t.messages.push({id:y,sessionId:n.value.id,content:g,from:1,senderType:2,createdAt:new Date().toISOString()}),t.messages=[...t.messages],await O(),H())}ae>0&&console.log(`本次处理了 ${ae} 个事件，缓冲区剩余 ${_.length} 字节`)}console.log("流式接收完成，最终内容长度:",g.length,"最终内容:",g),g=g.replace(/\\n/g,`
`),g=g.replace(/([.。!！?？])\s+([^\n])/g,`$1
$2`),console.log("流式接收完成，保存完整回复到数据库，内容长度:",g.length);const Q=[...t.messages];console.log("流式处理完成时的消息数量:",Q.length);try{const oe=await fetch("/api/message/send",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`${localStorage.getItem("token")||""}`},body:JSON.stringify({sessionId:n.value.id,content:g,msgType:0,senderType:2,isRead:0,scene:"默认应用场景",datasource:"默认数据源",collectionName:"information"})});if(!oe.ok)throw new Error(`API响应错误: ${oe.status}`);const W=await oe.json();if(W.code!==200)throw new Error(W.message||"API请求失败");if(console.log("AI回复保存成功",W),W.data&&W.data.id){const Y=t.messages.findIndex(ae=>ae.id===y);Y!==-1&&(t.messages[Y].id=W.data.id,console.log("已更新AI消息ID为数据库ID:",W.data.id))}t.messages.length===0&&Q.length>0&&(console.warn("警告：消息数组被清空，尝试恢复..."),t.messages=Q,console.log("已恢复消息数组，现有消息数量:",t.messages.length))}catch($){console.error("保存AI回复到数据库失败:",$)}}catch(r){console.error("流式获取AI回复失败:",r),t.messages.push({id:"err-"+Date.now(),sessionId:n.value.id,content:"抱歉，AI服务暂时无法回应，请稍后再试或转接人工客服",from:1,senderType:2,createdAt:new Date().toISOString()}),p.error("获取AI回复失败，请稍后重试")}else try{await t.sendTextMessage(e,null,!0)}catch(r){console.error("发送消息失败:",r),p.error("发送消息失败，请稍后重试")}}catch(s){console.error("发送消息失败",s),s.message&&s.message.includes("会话已关闭")?(p.warning("会话已结束，无法发送消息"),await t.refreshCurrentConversation()):p.error("发送消息失败，请稍后重试")}},ge=()=>{n.value&&Ye.confirm("确定要结束当前会话吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await t.closeConversation({conversationId:n.value.id,closedBy:"user"});e.code===200?(p.success("会话已结束"),V.value=n.value.id,await t.fetchConversations(),t.clearCurrentConversation(),D.value=!0):p.error(e.message||"结束会话失败")}catch(e){console.error("结束会话失败",e),p.error("结束会话失败，请稍后重试")}}).catch(()=>{})},N=()=>{p.success("感谢您的评价！"),setTimeout(()=>{D.value=!1,V.value=null},1500)},Te=()=>{D.value=!1,V.value=null,p.info("您已跳过评价")},ke=async e=>{var r;if(!e||!e.raw)return;if(!n.value){p.warning("请先开始会话");return}if(n.value.status===2){p.warning("会话已结束，无法发送消息");return}const s=e.raw.type.startsWith("image/"),a=e.raw.size/1024/1024<10;if(!s){p.error("只能上传图片文件");return}if(!a){p.error("图片大小不能超过 10MB");return}try{const y=URL.createObjectURL(e.raw),g="temp-"+Date.now();t.messages.push({id:g,sessionId:n.value.id,content:y,from:0,senderType:0,type:"image",msgType:1,createdAt:new Date().toISOString(),sending:!0}),await O(),H();const S=new FormData;S.append("file",e.raw),S.append("sessionId",n.value.id),S.append("senderType","0");const R=await fetch("https://jms.bearhome.cn/api/file/upload",{method:"POST",headers:{Authorization:`${localStorage.getItem("token")||""}`},body:S});if(!R.ok)throw new Error("图片上传失败，服务器响应: "+R.status);const B=await R.json();if(B.statusCode!==200)throw new Error(B.errorInfo||"图片上传失败");const I=B.data,J={sessionId:n.value.id,content:I,msgType:1,senderType:0,isRead:0},T=await ie(J),q=t.messages.findIndex(o=>o.id===g);q!==-1&&(t.messages[q]={...t.messages[q],id:((r=T==null?void 0:T.data)==null?void 0:r.id)||g,content:I,sending:!1},t.messages=[...t.messages]);try{const o={type:2,from:0,senderId:k.userId,receiverId:n.value.agentId,sessionId:n.value.id,content:I,msgType:1,timestamp:Date.now()};console.log("准备通过WebSocket发送图片消息:",o),console.log("WebSocket连接状态:",{connected:t.connected,readyState:t.ws?t.ws.readyState:"no-websocket"}),await t.sendMessage(o),console.log("WebSocket图片消息发送成功:",o);const _=t.messages.findIndex(K=>{var Q;return K.id===((Q=T==null?void 0:T.data)==null?void 0:Q.id)||g});_!==-1&&(console.log("再次确认消息URL已更新:",I),t.messages[_].content=I,t.messages[_].type="image",t.messages[_].msgType=1,t.messages=[...t.messages])}catch(o){console.error("WebSocket发送图片消息失败:",o)}console.log("图片上传成功，消息已发送"),p.success("图片发送成功")}catch(y){console.error("发送图片失败:",y),p.error("图片发送失败，请稍后重试");const g=t.messages.findIndex(S=>S.sending===!0);g!==-1&&t.messages.splice(g,1)}finally{e.raw&&URL.revokeObjectURL(URL.createObjectURL(e.raw))}},ve=e=>{if(!e)return"";const s=he(e),a=he();return s.isSame(a,"day")?s.format("HH:mm"):s.isSame(a.clone().subtract(1,"day"),"day")?`昨天 ${s.format("HH:mm")}`:s.isSame(a,"year")?s.format("MM-DD HH:mm"):s.format("YYYY-MM-DD HH:mm")},H=()=>{x.value&&O(()=>{x.value.scrollTop=x.value.scrollHeight,console.log("已滚动到底部",x.value.scrollHeight)})},Ce=async()=>{if(n.value){M.value=!0;try{const e="transfer-"+Date.now();t.messages.push({id:e,sessionId:n.value.id,content:"正在请求转接人工客服，请稍候...",from:1,senderType:2,createdAt:new Date().toISOString()});try{await ie({sessionId:n.value.id,content:"正在请求转接人工客服，请稍候...",msgType:0,senderType:2,isRead:0})}catch(r){console.error("保存转接请求消息失败:",r)}const a=await(await fetch(`/api/session/${n.value.id}/transfer-to-human`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`${localStorage.getItem("token")||""}`}})).json();if(console.log("转人工响应:",a),a.code===200){n.value.currentAgentType=1,n.value.transferRequested=1,M.value=!1;try{await ie({sessionId:n.value.id,content:systemMsg.content,msgType:0,senderType:1,isRead:0,from:1})}catch(r){console.error("保存系统消息失败:",r)}await t.refreshCurrentConversation(),await t.loadMessages(n.value.id),setTimeout(async()=>{try{const r=await Ge(n.value.id);if(console.log("获取人工客服信息:",r),r.code===200&&r.data.agent){n.value.agent=r.data.agent;const y={id:"agent-connected-"+Date.now(),sessionId:n.value.id,content:`人工客服 ${r.data.agent.name} 已接入，请开始咨询`,type:"system",from:1,senderType:1,createdAt:new Date().toISOString()};t.messages.push(y),await ie({sessionId:n.value.id,content:y.content,msgType:0,senderType:1,isRead:0})}}catch(r){console.error("获取人工客服信息失败:",r)}},2e3)}else t.messages.push({id:"system-"+Date.now(),sessionId:n.value.id,content:a.message||"转接人工客服失败，请稍后再试",type:"system",from:1,senderType:1,createdAt:new Date().toISOString()}),p.warning(a.message||"转接请求失败，请稍后再试"),M.value=!1}catch(e){console.error("请求转接人工客服失败",e),t.messages.push({id:"system-"+Date.now(),sessionId:n.value.id,content:"转接人工客服请求失败，请稍后重试",type:"system",from:1,senderType:1,createdAt:new Date().toISOString()}),p.error("请求转接人工客服失败，请稍后重试"),M.value=!1}}},Se=e=>{var s,a,r,y;return e.from===0||e.senderType===0||e.userId?e.from===0||e.senderType===0||e.userId?((s=L==null?void 0:L.nickname)==null?void 0:s.substring(0,1))||"我":((a=e.agentName)==null?void 0:a.substring(0,1))||"客":e.from===1||e.senderType===1||e.agentId?e.from===1||e.senderType===1||e.agentId?((r=e.agentName)==null?void 0:r.substring(0,1))||"客":((y=e.agentName)==null?void 0:y.substring(0,1))||"客":e.senderType===2||e.from===1&&e.currentAgentType===2?"AI":""},be=e=>e.type==="system"||e.msgType===2?"系统消息":e.senderType===2||e.from===2||e.from===1&&e.currentAgentType===2||e.senderId&&e.senderId===t.aiAgentId?"AI客服":e.from===0||e.senderType===0||e.userId?(L==null?void 0:L.nickname)||"我":e.from===1||e.senderType===1||e.agentId?e.agentName||"客服":"未知用户",Ae=e=>{if(!e.content)return!1;const s=document.querySelectorAll(".message-item");for(let a=0;a<s.length;a++)if(s[a].textContent.includes(e.content.substring(0,10)))return!0;return!1},ne=h(!1),xe=e=>{switch(e){case 0:return"warning";case 1:return"success";case 2:return"info";case 3:return"primary";default:return"info"}},De=e=>{switch(e){case 0:return"排队中";case 1:return"会话中";case 2:return"已结束";case 3:return"AI会话中";default:return"未知状态"}},Ee=async e=>{try{await t.setCurrentConversation(e),await t.loadMessages(e.id,100),ne.value=!1,p.success("已恢复会话")}catch(s){console.error("恢复会话失败:",s),p.error("恢复会话失败")}},Me=async e=>{try{await t.setCurrentConversation(e),await t.loadMessages(e.id,100),ne.value=!1}catch(s){console.error("加载会话历史失败:",s),p.error("加载会话历史失败")}},$e=e=>{e()},We=e=>{console.error("图片加载失败:",e.content),console.error("图片消息详情:",{id:e.id,type:e.type,msgType:e.msgType,from:e.from,senderType:e.senderType,isImageUrl:e.content&&(e.content.startsWith("http://")||e.content.startsWith("https://"))})},Ue=e=>{console.log("图片加载成功:",e.content),console.log("图片消息详情:",{id:e.id,type:e.type,msgType:e.msgType,from:e.from,senderType:e.senderType})},fe=e=>{if(!e)return"";if(e.startsWith("http://")||e.startsWith("https://"))return e},Pe=e=>{var s;return e.from===0||e.senderType===0||e.userId?((s=L.value)==null?void 0:s.avatar)||"":e.from===1||e.senderType===1||e.agentId?e.agentAvatar||"":e.senderType===2||e.from===1&&e.currentAgentType===2?"/ai-avatar.png":""},Le=()=>!0,me=e=>{console.log("收到转接人工客服通知:",e.detail),M.value=!0;const{content:s,agentId:a}=e.detail;p({message:s,type:"success",duration:5e3,showClose:!0}),n.value&&(n.value.currentAgentType=1,n.value.transferRequested=1,a&&(n.value.agentId=a),t.messages.push({id:"system-"+Date.now(),sessionId:n.value.id,content:s,type:"system",msgType:2,from:1,senderType:1,createdAt:new Date().toISOString()}),t.refreshCurrentConversation()),setTimeout(()=>{M.value=!1},5e3)};return(e,s)=>{const a=w("el-tag"),r=w("el-button"),y=w("el-avatar"),g=w("el-icon"),S=w("el-image"),R=w("el-upload"),B=w("el-input"),I=w("el-table-column"),J=w("el-table"),T=w("el-dialog"),q=qe("loading");return c(),f(G,null,[d("div",_t,[d("div",wt,[d("div",ht,[n.value?(c(),f("span",It,[F.value?(c(),P(a,{key:0,type:"success",size:"small",effect:"dark",class:"agent-tag"},{default:l(()=>s[5]||(s[5]=[m("AI客服")])),_:1,__:[5]})):(c(),P(a,{key:1,type:"primary",size:"small",effect:"dark",class:"agent-tag"},{default:l(()=>s[6]||(s[6]=[m("人工客服")])),_:1,__:[6]})),F.value?(c(),f(G,{key:2},[m(" 与AI智能客服对话中 ")],64)):n.value.agent&&n.value.agent.name?(c(),f(G,{key:3},[m(" 与客服 "+U(n.value.agent.name)+" 对话中 ",1)],64)):(c(),f(G,{key:4},[m(" 与客服对话中 ")],64))])):(c(),f("span",Tt,"在线客服"))]),n.value?(c(),f("div",kt,[F.value&&!M.value&&n.value.currentAgentType===2&&n.value.transferRequested!==1?(c(),P(r,{key:0,type:"primary",plain:"",size:"small",onClick:Ce},{default:l(()=>s[7]||(s[7]=[m("转接人工客服")])),_:1,__:[7]})):C("",!0),F.value&&M.value?(c(),P(r,{key:1,type:"warning",plain:"",size:"small",disabled:""},{default:l(()=>s[8]||(s[8]=[m("正在转接人工...")])),_:1,__:[8]})):C("",!0),i(r,{type:"danger",plain:"",size:"small",onClick:ge},{default:l(()=>s[9]||(s[9]=[m("结束会话")])),_:1,__:[9]})])):C("",!0)]),D.value?(c(),P(yt,{key:0,sessionId:V.value,onEvaluationComplete:N,onSkipEvaluation:Te,onStartNewChat:te},null,8,["sessionId"])):C("",!0),Fe((c(),f("div",{ref_key:"chatContent",ref:x,class:"chat-content"},[!ee.value&&!b.value?(c(),f("div",Ct,[s[14]||(s[14]=d("div",{class:"welcome-image"},[d("img",{src:st,alt:"客服系统"})],-1)),d("div",St,[s[12]||(s[12]=d("h2",null,"欢迎使用在线客服系统",-1)),s[13]||(s[13]=d("p",null,"我们的客服人员随时为您提供帮助和支持",-1)),z(t).conversations.length>0?(c(),f("div",bt,[d("p",null,"您有 "+U(z(t).conversations.filter(o=>[0,1,3].includes(o.status)).length)+" 个未结束的会话",1),i(r,{type:"text",onClick:s[0]||(s[0]=o=>ne.value=!0)},{default:l(()=>s[10]||(s[10]=[m("查看历史会话")])),_:1,__:[10]})])):C("",!0),i(r,{type:"primary",onClick:te},{default:l(()=>s[11]||(s[11]=[m("开始咨询")])),_:1,__:[11]})])])):u.value.length>0?(c(!0),f(G,{key:1},_e(u.value,(o,_)=>(c(),f("div",{key:o.id||_,class:Xe(["message-item",{"message-item-user":o.from===0||o.senderType===0||o.userId,"message-item-agent":(o.from===1||o.senderType===1||o.agentId)&&o.senderType!==2&&o.type!=="system"&&o.msgType!==2,"message-item-ai":o.senderType===2||o.from===1&&o.currentAgentType===2,"message-item-system":o.type==="system"||o.msgType===2}])},[C("",!0),o.type!=="system"&&o.msgType!==2?(c(),f("div",At,[i(y,{size:40,src:Pe(o),"fallback-src":"",onError:Le},{default:l(()=>[m(U(Se(o)),1)]),_:2},1032,["src"])])):C("",!0),d("div",xt,[o.type!=="system"&&o.msgType!==2?(c(),f("div",Dt,[m(U(be(o))+" ",1),d("span",Et,U(ve(o.createdAt||o.timestamp)),1)])):C("",!0),o.type==="image"||o.type===2||o.msgType===1?(c(),f("div",Mt,[i(S,{src:fe(o.content),"preview-src-list":[fe(o.content)],fit:"cover","initial-index":0,onError:()=>We(o),onLoad:()=>Ue(o)},{error:l(()=>[d("div",$t,[i(g,null,{default:l(()=>[i(z(we))]),_:1}),s[15]||(s[15]=d("span",null,"图片加载失败",-1))])]),placeholder:l(()=>[d("div",Wt,[i(g,{class:"is-loading"},{default:l(()=>[i(z(Ze))]),_:1}),s[16]||(s[16]=d("span",null,"加载中...",-1))])]),_:2},1032,["src","preview-src-list","onError","onLoad"])])):o.type==="system"||o.msgType===2?(c(),f("div",Ut,U(o.content),1)):(c(),f("div",Pt,U(o.content),1))])],2))),128)):ee.value&&!b.value?(c(),f("div",Lt,s[17]||(s[17]=[d("p",null,"暂无消息，开始发送第一条消息吧",-1)]))):C("",!0)])),[[q,b.value],[Je,!D.value]]),ee.value&&!D.value?(c(),f("div",Nt,[E.value&&Z.value.length>0?(c(),f("div",Ot,[d("div",Vt,[s[18]||(s[18]=d("span",null,"常见问题",-1)),i(r,{type:"text",size:"small",onClick:s[1]||(s[1]=o=>E.value=!1)},{default:l(()=>[i(g,null,{default:l(()=>[i(z(et))]),_:1})]),_:1})]),d("div",Ht,[(c(!0),f(G,null,_e(Z.value,(o,_)=>(c(),f("div",{key:_,class:"question-item",onClick:K=>ce(o)},U(o),9,Rt))),128))])])):C("",!0),d("div",Bt,[i(r,{type:"primary",plain:"",onClick:s[2]||(s[2]=o=>E.value=!E.value),title:E.value?"隐藏常见问题":"显示常见问题"},{default:l(()=>[i(g,null,{default:l(()=>[i(z(tt))]),_:1})]),_:1},8,["title"]),i(R,{action:"","auto-upload":!1,"show-file-list":!1,"on-change":ke,accept:"image/*"},{default:l(()=>[i(r,{type:"primary",plain:""},{default:l(()=>[i(g,null,{default:l(()=>[i(z(we))]),_:1})]),_:1})]),_:1})]),d("div",zt,[i(B,{modelValue:A.value,"onUpdate:modelValue":s[3]||(s[3]=o=>A.value=o),type:"textarea",rows:3,placeholder:"请输入消息...",resize:"none",disabled:!n.value||n.value.status===2,onKeydown:Ke(Qe(le,["exact","prevent"]),["enter"])},null,8,["modelValue","disabled","onKeydown"]),n.value&&n.value.status===2?(c(),f("div",jt," 会话已结束 ")):C("",!0)]),d("div",qt,[i(r,{type:"primary",onClick:le,disabled:!A.value.trim()||!n.value||n.value.status===2},{default:l(()=>s[19]||(s[19]=[m(" 发送 ")])),_:1,__:[19]},8,["disabled"])])])):C("",!0)]),i(T,{modelValue:ne.value,"onUpdate:modelValue":s[4]||(s[4]=o=>ne.value=o),title:"历史会话",width:"80%","before-close":$e},{default:l(()=>[i(J,{data:z(t).conversations,style:{width:"100%"}},{default:l(()=>[i(I,{prop:"id",label:"会话ID",width:"100"}),i(I,{label:"状态",width:"120"},{default:l(o=>[i(a,{type:xe(o.row.status)},{default:l(()=>[m(U(De(o.row.status)),1)]),_:2},1032,["type"])]),_:1}),i(I,{label:"客服类型",width:"120"},{default:l(o=>[o.row.currentAgentType===2?(c(),P(a,{key:0,type:"success"},{default:l(()=>s[20]||(s[20]=[m("AI客服")])),_:1,__:[20]})):(c(),P(a,{key:1,type:"primary"},{default:l(()=>s[21]||(s[21]=[m("人工客服")])),_:1,__:[21]}))]),_:1}),i(I,{prop:"createdAt",label:"创建时间",width:"180"},{default:l(o=>[m(U(ve(o.row.createdAt)),1)]),_:1}),i(I,{label:"操作"},{default:l(o=>[[0,1,3].includes(o.row.status)?(c(),P(r,{key:0,type:"primary",link:"",onClick:_=>Ee(o.row)},{default:l(()=>s[22]||(s[22]=[m("继续会话")])),_:2,__:[22]},1032,["onClick"])):(c(),P(r,{key:1,type:"info",link:"",onClick:_=>Me(o.row)},{default:l(()=>s[23]||(s[23]=[m("查看记录")])),_:2,__:[23]},1032,["onClick"]))]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"])],64)}}},Qt=Ie(Ft,[["__scopeId","data-v-dd0a6603"]]);export{Qt as default};
