import{_ as p}from"./ai-avatar-f145a15f.js";import{_ as f,u as S,a as v,b as m,r as u,o as y,E as h,c as k,d as n,e as r,f as s,q as i,t as x,g as _,w as O,h as w,s as I}from"./index.js";const L={class:"sso-login-container"},b={class:"sso-login-box card-shadow rounded"},q={class:"sso-login-content"},B={key:0},C={key:1,class:"error-message"},E={key:2,class:"loading-spinner"},N={__name:"SsoLogin",setup(V){const d=S(),l=v(),o=m(),a=u(!0),t=u("");return y(async()=>{const c=l.query.token;if(!c){t.value="未提供有效的SSO令牌",a.value=!1;return}try{await o.ssoLogin(c),o.userInfo&&o.userInfo.id&&await o.updateAgentStatus(o.userInfo.id,1),h({type:"success",message:"SSO登录成功"});const e=l.query.redirect||"/agent/dashboard";d.replace(e)}catch(e){console.error("SSO登录失败:",e),t.value=e.message||"SSO登录验证失败，请重试",a.value=!1}}),(c,e)=>{const g=k("el-icon");return n(),r("div",L,[s("div",b,[e[1]||(e[1]=s("div",{class:"logo-container"},[s("img",{src:p,alt:"熊小智",class:"logo-image"}),s("h2",{class:"title"},"熊小智客服")],-1)),s("div",q,[e[0]||(e[0]=s("h3",null,"SSO 快捷登录",-1)),!a.value&&!t.value?(n(),r("p",B,"系统正在进行SSO登录验证，请稍候...")):i("",!0),t.value?(n(),r("p",C,x(t.value),1)):i("",!0),a.value?(n(),r("div",E,[_(g,{class:"is-loading"},{default:O(()=>[_(w(I))]),_:1})])):i("",!0)])])])}}},A=f(N,[["__scopeId","data-v-82a4cfe8"]]);export{A as default};
