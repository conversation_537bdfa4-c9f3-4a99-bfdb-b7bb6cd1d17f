import{S as ge,_ as fe,b as pe,x as O,r as i,u as ye,o as he,T as ke,E as g,c as u,F as we,d as s,e as r,f as o,g as n,h as Ie,H as R,A as y,G as Q,J as X,w as l,U as Ve,j as c,t as d,O as Z,q as x,L as ee,V as Ce}from"./index.js";import{h as te}from"./moment-a9aaa855.js";function xe(D){return ge({url:"/evaluation/add",method:"post",data:D})}const Se={class:"history-container"},be={class:"history-header"},Me={class:"history-content"},He={key:1,class:"session-list"},Ae={class:"session-info"},De={class:"session-header"},Ne={class:"agent-info"},Ue={class:"agent-name"},ze={class:"session-time"},Fe={key:0},Be={class:"session-message"},Le={key:0},$e={key:1,class:"no-message"},Te={class:"session-actions"},Ee={class:"pagination-container"},Ye={class:"dialog-content"},Pe={key:0,class:"message-list"},We={class:"message-avatar"},je={class:"message-content"},qe={class:"message-sender"},Ge={class:"message-time"},Je={key:0,class:"message-text"},Ke={key:1,class:"message-image"},Oe={key:2,class:"message-system"},Re={class:"dialog-footer"},Qe={class:"dialog-footer"},Xe={__name:"History",setup(D){const _=pe(),S=O(()=>_.userInfo),w=i(""),I=i([]),N=O(()=>{if(!w.value)return I.value;const a=w.value.toLowerCase();return I.value.filter(e=>e.agentName&&e.agentName.toLowerCase().includes(a)||e.lastMessage&&e.lastMessage.toLowerCase().includes(a))}),U=i(1),ae=i(10),z=i(0),b=i(!1),M=i(!1),H=i(!1),h=i(!1),k=i(!1),f=i(null),V=i([]),v=i({sessionId:"",userId:"",agentId:"",score:5,content:""}),se=ye();he(async()=>{if(_.loadFromStorage(),!_.isLoggedIn||!_.isUser){se.push("/login");return}await F()});const F=async()=>{if(!(!_.isLoggedIn||!_.userId)){b.value=!0;try{const a=await ke(_.userId);a.code===200?(I.value=a.data,I.value.sort((e,m)=>new Date(m.lastActiveTime||0)-new Date(e.lastActiveTime||0)),z.value=a.data.length):g.error(a.message||"加载会话列表失败")}catch(a){console.error("加载会话列表失败",a),g.error("加载会话列表失败")}finally{b.value=!1}}},B=async a=>{f.value=a,h.value=!0,await oe(a.id)},oe=async a=>{M.value=!0,V.value=[];try{const e=await Ce(a);e.code===200?V.value=e.data:g.error(e.message||"加载消息失败")}catch(e){console.error("加载消息失败",e),g.error("加载消息失败")}finally{M.value=!1}},L=a=>{f.value=a,v.value={sessionId:a.id,userId:_.userId,agentId:a.agentId,score:5,content:""},k.value=!0},le=async()=>{if(!v.value.score){g.warning("请选择评分");return}H.value=!0;try{const a=await xe(v.value);a.code===200?(g.success("评价成功"),k.value=!1,await F(),h.value=!1):g.error(a.message||"提交评价失败")}catch(a){console.error("提交评价失败",a),g.error("提交评价失败")}finally{H.value=!1}},ne=a=>{U.value=a},re=a=>{switch(a){case 0:return"已结束";case 1:return"进行中";case 2:return"等待中";default:return"未知状态"}},ie=a=>{switch(a){case 0:return"status-closed";case 1:return"status-active";case 2:return"status-waiting";default:return""}},A=a=>{if(!a)return"";const e=te(a),m=te();return e.isSame(m,"day")?e.format("HH:mm:ss"):e.isSame(m.clone().subtract(1,"day"),"day")?`昨天 ${e.format("HH:mm")}`:e.isSame(m,"year")?e.format("MM-DD HH:mm"):e.format("YYYY-MM-DD HH:mm")},$=a=>{if(!a)return"";if(a.startsWith("http://")||a.startsWith("https://"))return a};return(a,e)=>{const m=u("el-input"),T=u("el-empty"),E=u("el-avatar"),p=u("el-button"),ue=u("el-tag"),de=u("el-card"),ce=u("el-pagination"),ve=u("el-image"),Y=u("el-dialog"),_e=u("el-rate"),P=u("el-form-item"),me=u("el-form"),W=we("loading");return s(),r("div",Se,[o("div",be,[e[8]||(e[8]=o("h2",null,"历史会话",-1)),n(m,{modelValue:w.value,"onUpdate:modelValue":e[0]||(e[0]=t=>w.value=t),placeholder:"搜索历史会话","prefix-icon":Ie(Ve),clearable:"",class:"search-input"},null,8,["modelValue","prefix-icon"])]),R((s(),r("div",Me,[N.value.length===0?(s(),y(T,{key:0,description:"暂无历史会话"})):(s(),r("div",He,[(s(!0),r(Q,null,X(N.value,t=>(s(),y(de,{key:t.id,class:"session-card",shadow:"hover",onClick:C=>B(t)},{default:l(()=>[o("div",Ae,[o("div",De,[o("div",Ne,[n(E,{size:40,src:t.agentAvatar},{default:l(()=>[c(d(t.agentName?t.agentName.substring(0,1):"客"),1)]),_:2},1032,["src"]),o("span",Ue,d(t.agentName||"系统客服"),1)]),o("div",{class:Z(["session-status",ie(t.status)])},d(re(t.status)),3)]),o("div",ze,[o("time",null,d(A(t.createdAt)),1),t.status===0?(s(),r("time",Fe,"结束于: "+d(A(t.closedAt)),1)):x("",!0)]),o("div",Be,[t.lastMessage?(s(),r("p",Le,d(t.lastMessage),1)):(s(),r("p",$e,"暂无消息记录"))]),o("div",Te,[n(p,{type:"primary",size:"small",plain:"",onClick:ee(C=>B(t),["stop"])},{default:l(()=>e[9]||(e[9]=[c(" 查看详情 ")])),_:2,__:[9]},1032,["onClick"]),t.status===0&&!t.evaluation?(s(),y(p,{key:0,type:"warning",size:"small",plain:"",onClick:ee(C=>L(t),["stop"])},{default:l(()=>e[10]||(e[10]=[c(" 去评价 ")])),_:2,__:[10]},1032,["onClick"])):t.evaluation?(s(),y(ue,{key:1,size:"small",type:"success"},{default:l(()=>[c(" 已评价: "+d(t.evaluation.score)+"分 ",1)]),_:2},1024)):x("",!0)])])]),_:2},1032,["onClick"]))),128))])),o("div",Ee,[n(ce,{background:"",layout:"prev, pager, next",total:z.value,"page-size":ae.value,"current-page":U.value,onCurrentChange:ne},null,8,["total","page-size","current-page"])])])),[[W,b.value]]),n(Y,{modelValue:h.value,"onUpdate:modelValue":e[3]||(e[3]=t=>h.value=t),title:"会话详情",width:"70%","destroy-on-close":""},{footer:l(()=>[o("div",Re,[n(p,{onClick:e[1]||(e[1]=t=>h.value=!1)},{default:l(()=>e[11]||(e[11]=[c("关闭")])),_:1,__:[11]}),f.value&&f.value.status===0&&!f.value.evaluation?(s(),y(p,{key:0,type:"primary",onClick:e[2]||(e[2]=t=>L(f.value))},{default:l(()=>e[12]||(e[12]=[c(" 评价服务 ")])),_:1,__:[12]})):x("",!0)])]),default:l(()=>[R((s(),r("div",Ye,[V.value.length>0?(s(),r("div",Pe,[(s(!0),r(Q,null,X(V.value,(t,C)=>{var j,q;return s(),r("div",{key:t.id||C,class:Z(["message-item",t.userId?"message-item-user":"message-item-agent"])},[o("div",We,[n(E,{size:40,src:t.userId?(j=S.value)==null?void 0:j.avatar:t.agentAvatar},{default:l(()=>{var G,J,K;return[c(d(t.userId?((J=(G=S.value)==null?void 0:G.nickname)==null?void 0:J.substring(0,1))||"用":((K=t.agentName)==null?void 0:K.substring(0,1))||"客"),1)]}),_:2},1032,["src"])]),o("div",je,[o("div",qe,[c(d(t.userId?((q=S.value)==null?void 0:q.nickname)||"我":t.agentName||"客服")+" ",1),o("span",Ge,d(A(t.createdAt)),1)]),t.type==="text"?(s(),r("div",Je,d(t.content),1)):t.type==="image"?(s(),r("div",Ke,[n(ve,{src:$(t.content),"preview-src-list":[$(t.content)],fit:"cover"},null,8,["src","preview-src-list"])])):t.type==="system"?(s(),r("div",Oe,d(t.content),1)):x("",!0)])],2)}),128))])):(s(),y(T,{key:1,description:"暂无消息记录"}))])),[[W,M.value]])]),_:1},8,["modelValue"]),n(Y,{modelValue:k.value,"onUpdate:modelValue":e[7]||(e[7]=t=>k.value=t),title:"服务评价",width:"400px","destroy-on-close":""},{footer:l(()=>[o("div",Qe,[n(p,{onClick:e[6]||(e[6]=t=>k.value=!1)},{default:l(()=>e[13]||(e[13]=[c("取消")])),_:1,__:[13]}),n(p,{type:"primary",onClick:le,loading:H.value},{default:l(()=>e[14]||(e[14]=[c("提交评价")])),_:1,__:[14]},8,["loading"])])]),default:l(()=>[n(me,{ref_key:"evaluateForm",ref:v,model:v.value,"label-width":"80px"},{default:l(()=>[n(P,{label:"服务评分"},{default:l(()=>[n(_e,{modelValue:v.value.score,"onUpdate:modelValue":e[4]||(e[4]=t=>v.value.score=t),colors:["#99A9BF","#F7BA2A","#FF9900"],max:5,"show-text":"",texts:["很差","一般","满意","很好","非常满意"]},null,8,["modelValue"])]),_:1}),n(P,{label:"评价内容"},{default:l(()=>[n(m,{modelValue:v.value.content,"onUpdate:modelValue":e[5]||(e[5]=t=>v.value.content=t),type:"textarea",rows:3,placeholder:"请输入您对本次服务的评价..."},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},tt=fe(Xe,[["__scopeId","data-v-60a0348f"]]);export{tt as default};
