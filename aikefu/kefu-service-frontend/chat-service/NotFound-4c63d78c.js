import{_ as a,u as c,b as d,c as u,d as _,e as i,f as s,g as l,w as p,j as f}from"./index.js";const m={class:"not-found-container"},g={class:"not-found-content"},v={__name:"NotFound",setup(x){const o=c(),t=d(),n=()=>{t.isUser?o.push("/user/chat"):t.isAgent?o.push("/agent/dashboard"):o.push("/login")};return(N,e)=>{const r=u("el-button");return _(),i("div",m,[s("div",g,[e[1]||(e[1]=s("h1",{class:"error-code"},"404",-1)),e[2]||(e[2]=s("div",{class:"error-message"},"页面不存在",-1)),e[3]||(e[3]=s("p",{class:"error-description"},"抱歉，您访问的页面不存在或已被删除。",-1)),l(r,{type:"primary",onClick:n},{default:p(()=>e[0]||(e[0]=[f("返回首页")])),_:1,__:[0]})])])}}},y=a(v,[["__scopeId","data-v-c91c92c1"]]);export{y as default};
