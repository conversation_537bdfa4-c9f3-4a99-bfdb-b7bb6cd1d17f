import{_ as Ke,r as u,k as xe,o as Ye,n as M,C as Ie,au as Ge,c,F as je,d as y,e as j,f as r,g as a,w as o,E as S,j as m,A as D,q as Je,G as me,J as pe,h as F,ap as Xe,av as Ze,$ as et,t as V,a1 as tt,aU as at,aV as ke,H as W,aW as ot,U as lt,L as st,aX as nt}from"./index.js";import"./index-100eba30.js";import{a as Qe,b as rt,g as ut,c as it}from"./wordCloud-f2415365.js";import{a as dt}from"./faq-65748221.js";import{i as J,L as ct}from"./installCanvasRenderer-44afd735.js";const ft={class:"question-analysis-container"},vt={class:"filter-panel"},_t={class:"stats-overview"},mt={class:"stat-flex"},pt={class:"stat-icon-container"},gt={class:"stat-content"},yt={class:"stat-value"},ht={class:"stat-flex"},bt={class:"stat-icon-container",style:{"background-color":"#67C23A"}},wt={class:"stat-content"},Ct={class:"stat-value"},St={class:"stat-flex"},xt={class:"stat-icon-container",style:{"background-color":"#E6A23C"}},kt={class:"stat-content"},Qt={class:"stat-value"},qt={class:"stat-flex"},Ft={class:"stat-icon-container",style:{"background-color":"#F56C6C"}},Vt={class:"stat-content"},Dt={class:"stat-value-row"},zt={class:"stat-value"},Lt={class:"stat-percent"},Tt={class:"analysis-display"},At={class:"card-header"},Et={class:"card-header"},Rt={class:"hot-questions-filter"},Ot={class:"question-content"},Ut={class:"question-count"},Mt={class:"dialog-footer"},Wt={__name:"QuestionAnalysis",setup($t){let x=null,k=null,Q=null,g=null;const X=u(null),Z=u(null),ee=u(null),L=u(null),_=u("keyword"),p=u("week"),h=u([]),b=u(""),w=u(""),te=u(!1),$=u(!1),ae=u(!1),oe=u(!1),R=u(!1),le=u(null),f=xe({question:"",answer:"",category:"",scene:"",datasource:""}),qe={question:[{required:!0,message:"请输入问题",trigger:"blur"}],answer:[{required:!0,message:"请输入答案",trigger:"blur"}],category:[{required:!0,message:"请选择问题分类",trigger:"change"}]},H=u([]),P=u([]),Fe=[{value:"order",label:"订单相关"},{value:"product",label:"商品相关"},{value:"delivery",label:"配送相关"},{value:"payment",label:"支付相关"},{value:"account",label:"账户相关"},{value:"other",label:"其他问题"}],d=xe({totalQuestions:0,questionTrend:0,uniqueQuestions:0,uniqueTrend:0,avgQuestionCount:0,avgTrend:0,mainSource:"",mainSourcePercent:0}),B=u([]),se=u([]),N=u({sceneStats:[],datasourceStats:[]}),ne=u([]),K=u(""),Ve=u("desc"),T=u([]),De=u(1),re=()=>{const l=new Date,e=new Date;p.value==="day"?e.setTime(e.getTime()-3600*1e3*24*6):p.value==="week"?e.setTime(e.getTime()-3600*1e3*24*28):p.value==="month"&&e.setMonth(e.getMonth()-5);const t=n=>{const v=n.getFullYear(),C=(n.getMonth()+1).toString().padStart(2,"0"),i=n.getDate().toString().padStart(2,"0");return`${v}-${C}-${i}`};return[t(e),t(l)]},ze=()=>{p.value!=="custom"&&(h.value=re(),ue())},Le=()=>{ue()},ue=()=>{de(),_.value==="trend"?Y():_.value==="category"||(_.value==="keyword"?I():_.value==="hotQuestions"&&ce())},ie=()=>{console.log("刷新数据，当前选择场景:",b.value,"数据源:",w.value),_.value==="trend"?Y():_.value==="category"?de():_.value==="keyword"?I():_.value==="hotQuestions"&&ce()},Te=()=>{p.value="week",h.value=re(),b.value="",w.value="",ue()},Y=async()=>{te.value=!0;const[l,e]=h.value;try{const t=await rt({timeUnit:p.value==="custom"?"day":p.value,scene:b.value,datasource:w.value,startDate:l,endDate:e});t.code===200?(se.value=t.data,M(()=>{Ae()})):S.error(t.message||"获取趋势数据失败")}catch(t){console.error("获取趋势数据异常",t),S.error("获取趋势数据异常")}finally{te.value=!1}},de=async()=>{$.value=!0;const[l,e]=h.value;try{const t=await Qe({scene:b.value,datasource:w.value,startDate:l,endDate:e});if(t.code===200){if(N.value=t.data,d.totalQuestions=t.data.totalQuestions||0,d.uniqueQuestions=t.data.uniqueQuestions||0,d.avgQuestionCount=parseFloat((d.totalQuestions/(d.uniqueQuestions||1)).toFixed(2)),t.data.datasourceStats&&t.data.datasourceStats.length>0){const n=t.data.datasourceStats[0],v=t.data.datasourceStats.reduce((C,i)=>C+i.count,0);d.mainSource=n.datasource,d.mainSourcePercent=Math.round(n.count/v*100)}ge(t.data),M(()=>{ye()})}else S.error(t.message||"获取分类统计失败")}catch(t){console.error("获取分类统计异常",t),S.error("获取分类统计异常")}finally{$.value=!1}},ge=l=>{if(l.sceneStats&&l.sceneStats.length>0){const e=l.sceneStats.filter(t=>t&&t.scene).map(t=>{const n=t.scene||"未知场景";return{value:n,label:`${n} (${t.count})`,count:t.count}});e.sort((t,n)=>n.count-t.count),H.value=e,console.log("场景选项已更新:",H.value)}else console.warn("未找到场景统计数据"),H.value=[];if(l.datasourceStats&&l.datasourceStats.length>0){const e=l.datasourceStats.filter(t=>t&&t.datasource).map(t=>{const n=t.datasource||"未知来源";return{value:n,label:`${n} (${t.count})`,count:t.count}});e.sort((t,n)=>n.count-t.count),P.value=e,console.log("数据源选项已更新:",P.value)}else console.warn("未找到数据源统计数据"),P.value=[]},I=async()=>{ae.value=!0;const[l,e]=h.value;console.log("加载关键词统计，场景:",b.value,"数据源:",w.value);try{const t=await ut({scene:b.value,datasource:w.value,startDate:l,endDate:e,limit:100});t.code===200?(ne.value=t.data,console.log("获取关键词数据:",ne.value),M(()=>{he()})):S.error(t.message||"获取关键词统计失败")}catch(t){console.error("获取关键词统计异常",t),S.error("获取关键词统计异常")}finally{ae.value=!1}},ce=async()=>{oe.value=!0;const[l,e]=h.value;try{const t=await it({scene:b.value,datasource:w.value,startDate:l,endDate:e,limit:50});t.code===200?B.value=t.data:S.error(t.message||"获取热门问题失败")}catch(t){console.error("获取热门问题异常",t),S.error("获取热门问题异常")}finally{oe.value=!1}},Ae=()=>{if(!X.value)return;x&&x.dispose(),x=J(X.value);const l=se.value.map(n=>p.value==="week"?n.start_date:p.value==="month"?n.month:n.date),e=se.value.map(n=>n.count),t={title:{text:"问题数量趋势",left:"center",textStyle:{fontWeight:"normal",fontSize:16}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},backgroundColor:"rgba(255, 255, 255, 0.9)",borderColor:"#eee",borderWidth:1,textStyle:{color:"#333"}},xAxis:{type:"category",data:l,axisLabel:{rotate:p.value==="day"?45:0,interval:p.value==="day"?"auto":0,color:"#666"},axisLine:{lineStyle:{color:"#ddd"}}},yAxis:{type:"value",name:"问题数",nameTextStyle:{color:"#666"},axisLabel:{color:"#666"},splitLine:{lineStyle:{color:"#eee"}}},series:[{name:"问题数量",type:"line",data:e,markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},smooth:!0,lineStyle:{width:3,color:"#409EFF"},areaStyle:{opacity:.3,color:new ct(0,0,0,1,[{offset:0,color:"#409EFF"},{offset:1,color:"rgba(64, 158, 255, 0.1)"}])},itemStyle:{color:"#409EFF"}}],grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0}};x.setOption(t),window.addEventListener("resize",()=>{x&&x.resize()})},ye=()=>{if(Z.value){k&&k.dispose(),k=J(Z.value);const l=N.value.sceneStats||[],e=["#409EFF","#67C23A","#E6A23C","#F56C6C","#909399","#9254de","#36cfc9","#ffec3d"],t={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)",backgroundColor:"rgba(255, 255, 255, 0.9)",borderColor:"#eee",borderWidth:1,textStyle:{color:"#333"}},legend:{orient:"vertical",left:"left",data:l.map(n=>n.scene||"未知"),textStyle:{color:"#666"}},series:[{name:"场景分布",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"16",fontWeight:"bold"}},labelLine:{show:!1},color:e,data:l.map(n=>({name:n.scene||"未知",value:n.count}))}]};k.setOption(t),window.addEventListener("resize",()=>{k&&k.resize()})}if(ee.value){Q&&Q.dispose(),Q=J(ee.value);const l=N.value.datasourceStats||[],e=["#67C23A","#E6A23C","#F56C6C","#409EFF","#909399","#9254de","#36cfc9","#ffec3d"],t={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)",backgroundColor:"rgba(255, 255, 255, 0.9)",borderColor:"#eee",borderWidth:1,textStyle:{color:"#333"}},legend:{orient:"vertical",left:"left",data:l.map(n=>n.datasource||"未知"),textStyle:{color:"#666"}},series:[{name:"数据源分布",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"16",fontWeight:"bold"}},labelLine:{show:!1},color:e,data:l.map(n=>({name:n.datasource||"未知",value:n.count}))}]};Q.setOption(t),window.addEventListener("resize",()=>{Q&&Q.resize()})}},he=()=>{if(!L.value){console.error("关键词图表DOM引用不存在");return}g&&g.dispose(),g=J(L.value);const l=ne.value||[];if(console.log("准备渲染词云，数据项数:",l.length),l.length===0){g.setOption({title:{text:"暂无关键词数据",left:"center",top:"center",textStyle:{fontSize:16,color:"#909399"}}});return}const e=l.filter(i=>i&&i.keyword&&i.count);if(e.length===0){console.error("关键词数据格式不正确:",l),g.setOption({title:{text:"数据格式不正确",left:"center",top:"center",textStyle:{fontSize:16,color:"#909399"}}});return}const t=e.map(i=>({name:i.keyword,value:typeof i.count=="number"?i.count:parseInt(i.count,10)||1}));console.log("处理后的词云数据:",t);const n=["#409EFF","#67C23A","#E6A23C","#F56C6C","#909399","#9254de","#36cfc9","#ffec3d","#ff4500","#00c1de"],v={tooltip:{show:!0,formatter:function(i){return i.name+" : "+i.value},backgroundColor:"rgba(255, 255, 255, 0.9)",borderColor:"#eee",borderWidth:1,textStyle:{color:"#333"}},series:[{type:"wordCloud",shape:"circle",left:"center",top:"center",width:"95%",height:"100%",sizeRange:[14,60],rotationRange:[-1,1],rotationStep:1,gridSize:20,drawOutOfBound:!1,layoutAnimation:!0,textStyle:{fontFamily:"sans-serif",fontWeight:"bold",color:function(){return n[Math.floor(Math.random()*n.length)]}},emphasis:{textStyle:{shadowBlur:12,shadowColor:"rgba(0, 0, 0, 0.3)"}},data:t}]};g.setOption(v),console.log("词云图配置已应用");const C=()=>{g&&g.resize()};return window.addEventListener("resize",C),()=>{window.removeEventListener("resize",C)}},Ee=()=>{_.value==="trend"?Y():_.value==="category"?de():_.value==="keyword"?(I(),setTimeout(()=>{L.value&&!g&&he()},100)):_.value==="hotQuestions"&&ce()},Re=l=>{f.question=l.content,f.answer="",f.category="other",f.scene=l.scene,f.datasource=l.datasource,R.value=!0},Oe=async()=>{le.value&&await le.value.validate(async l=>{if(l)try{const e=await dt({question:f.question,answer:f.answer,category:f.category,scene:f.scene,datasource:f.datasource});e.code===200?(nt({title:"成功",message:"已添加到常见问题",type:"success"}),R.value=!1):S.error(e.message||"添加失败")}catch(e){console.error("添加FAQ异常",e),S.error("添加FAQ异常")}})},be=()=>{K.value?T.value=B.value.filter(l=>l.content.toLowerCase().includes(K.value.toLowerCase())):T.value=[...B.value],we(),De.value=1},we=()=>{Ve.value==="desc"?T.value.sort((l,e)=>e.num-l.num):T.value.sort((l,e)=>l.num-e.num)},Ue=l=>l>=50?"danger":l>=20?"warning":l>=10?"success":"info";Ye(()=>{h.value=re(),Me(),I(),Y(),M(()=>{L.value&&(L.value.style.minHeight="400px")})});const Me=async()=>{const[l,e]=h.value;try{const t=await Qe({startDate:l,endDate:e});if(t.code===200){if(ge(t.data),d.totalQuestions=t.data.totalQuestions||0,d.uniqueQuestions=t.data.uniqueQuestions||0,d.avgQuestionCount=parseFloat((d.totalQuestions/(d.uniqueQuestions||1)).toFixed(2)),t.data.datasourceStats&&t.data.datasourceStats.length>0){const n=t.data.datasourceStats[0],v=t.data.datasourceStats.reduce((C,i)=>C+i.count,0);d.mainSource=n.datasource,d.mainSourcePercent=Math.round(n.count/v*100)}_.value==="category"&&(N.value=t.data,M(()=>{ye()}))}else console.error("获取初始分类统计失败:",t.message)}catch(t){console.error("获取初始分类统计异常",t)}},We=l=>{console.log("场景选择变化:",l),b.value=l,ie()},$e=l=>{console.log("数据源选择变化:",l),w.value=l,ie()},He=l=>{console.log("查看问题详情:",l)};return Ie(()=>B.value,l=>{l&&(T.value=[...l],we())},{immediate:!0}),Ge(()=>{x&&(x.dispose(),x=null),k&&(k.dispose(),k=null),Q&&(Q.dispose(),Q=null),g&&(g.dispose(),g=null),window.removeEventListener("resize",()=>{})}),(l,e)=>{const t=c("el-radio-button"),n=c("el-radio-group"),v=c("el-form-item"),C=c("el-date-picker"),i=c("el-option"),fe=c("el-select"),O=c("el-button"),Ce=c("el-form"),z=c("el-icon"),q=c("el-card"),A=c("el-col"),Se=c("el-row"),G=c("el-tab-pane"),ve=c("el-input"),E=c("el-table-column"),_e=c("el-tag"),Pe=c("el-table"),Be=c("el-tabs"),Ne=c("el-dialog"),U=je("loading");return y(),j("div",ft,[r("div",vt,[a(Ce,{inline:!0},{default:o(()=>[a(v,{label:"时间维度"},{default:o(()=>[a(n,{modelValue:p.value,"onUpdate:modelValue":e[0]||(e[0]=s=>p.value=s),onChange:ze,size:"large"},{default:o(()=>[a(t,{label:"day"},{default:o(()=>e[11]||(e[11]=[m("日")])),_:1,__:[11]}),a(t,{label:"week"},{default:o(()=>e[12]||(e[12]=[m("周")])),_:1,__:[12]}),a(t,{label:"month"},{default:o(()=>e[13]||(e[13]=[m("月")])),_:1,__:[13]}),a(t,{label:"custom"},{default:o(()=>e[14]||(e[14]=[m("自定义")])),_:1,__:[14]})]),_:1},8,["modelValue"])]),_:1}),p.value==="custom"?(y(),D(v,{key:0},{default:o(()=>[a(C,{modelValue:h.value,"onUpdate:modelValue":e[1]||(e[1]=s=>h.value=s),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",onChange:Le,size:"large"},null,8,["modelValue"])]),_:1})):Je("",!0),a(v,{label:"对话场景"},{default:o(()=>[a(fe,{modelValue:b.value,"onUpdate:modelValue":e[2]||(e[2]=s=>b.value=s),placeholder:"全部场景",clearable:"",onChange:We,filterable:"",size:"large",class:"filter-select"},{default:o(()=>[a(i,{label:"全部场景",value:""}),(y(!0),j(me,null,pe(H.value,s=>(y(),D(i,{key:s.value,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(v,{label:"数据来源"},{default:o(()=>[a(fe,{modelValue:w.value,"onUpdate:modelValue":e[3]||(e[3]=s=>w.value=s),placeholder:"全部来源",clearable:"",onChange:$e,filterable:"",size:"large",class:"filter-select"},{default:o(()=>[a(i,{label:"全部来源",value:""}),(y(!0),j(me,null,pe(P.value,s=>(y(),D(i,{key:s.value,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(v,null,{default:o(()=>[a(O,{type:"primary",onClick:ie,size:"large",icon:F(Xe)},{default:o(()=>e[15]||(e[15]=[m("查询")])),_:1,__:[15]},8,["icon"]),a(O,{onClick:Te,size:"large",icon:F(Ze)},{default:o(()=>e[16]||(e[16]=[m("重置")])),_:1,__:[16]},8,["icon"])]),_:1})]),_:1})]),r("div",_t,[a(Se,{gutter:20},{default:o(()=>[a(A,{xs:24,sm:12,md:6},{default:o(()=>[a(q,{shadow:"hover",class:"stat-card"},{default:o(()=>[r("div",mt,[r("div",pt,[a(z,{class:"stat-icon"},{default:o(()=>[a(F(et))]),_:1})]),r("div",gt,[r("div",yt,V(d.totalQuestions||0),1),e[17]||(e[17]=r("div",{class:"stat-title"},"问题总数",-1))])])]),_:1})]),_:1}),a(A,{xs:24,sm:12,md:6},{default:o(()=>[a(q,{shadow:"hover",class:"stat-card"},{default:o(()=>[r("div",ht,[r("div",bt,[a(z,{class:"stat-icon"},{default:o(()=>[a(F(tt))]),_:1})]),r("div",wt,[r("div",Ct,V(d.uniqueQuestions||0),1),e[18]||(e[18]=r("div",{class:"stat-title"},"独立问题数",-1))])])]),_:1})]),_:1}),a(A,{xs:24,sm:12,md:6},{default:o(()=>[a(q,{shadow:"hover",class:"stat-card"},{default:o(()=>[r("div",St,[r("div",xt,[a(z,{class:"stat-icon"},{default:o(()=>[a(F(at))]),_:1})]),r("div",kt,[r("div",Qt,V(d.avgQuestionCount||0),1),e[19]||(e[19]=r("div",{class:"stat-title"},"平均提问次数",-1))])])]),_:1})]),_:1}),a(A,{xs:24,sm:12,md:6},{default:o(()=>[a(q,{shadow:"hover",class:"stat-card"},{default:o(()=>[r("div",qt,[r("div",Ft,[a(z,{class:"stat-icon"},{default:o(()=>[a(F(ke))]),_:1})]),r("div",Vt,[r("div",Dt,[r("div",zt,[m(V(d.mainSource||"全部")+" ",1),r("div",Lt," 占比 "+V(d.mainSourcePercent||0)+"% ",1)]),e[20]||(e[20]=r("div",{class:"stat-title"},"主要问题来源",-1)),e[21]||(e[21]=r("div",{class:"stat-source-info"},null,-1))])])])]),_:1})]),_:1})]),_:1})]),r("div",Tt,[a(Be,{modelValue:_.value,"onUpdate:modelValue":e[5]||(e[5]=s=>_.value=s),onTabClick:Ee,type:"border-card",class:"modern-tabs"},{default:o(()=>[a(G,{label:"时间趋势分析",name:"trend"},{default:o(()=>[W((y(),D(q,{shadow:"never",class:"chart-card"},{default:o(()=>[r("div",{class:"chart-container",ref_key:"trendChartRef",ref:X},null,512)]),_:1})),[[U,te.value]])]),_:1}),a(G,{label:"分类统计分析",name:"category"},{default:o(()=>[a(Se,{gutter:20},{default:o(()=>[a(A,{xs:24,md:12},{default:o(()=>[W((y(),D(q,{shadow:"never",class:"chart-card"},{header:o(()=>[r("div",At,[r("span",null,[a(z,null,{default:o(()=>[a(F(ke))]),_:1}),e[22]||(e[22]=m(" 场景分布"))])])]),default:o(()=>[r("div",{class:"chart-container",ref_key:"sceneChartRef",ref:Z},null,512)]),_:1})),[[U,$.value]])]),_:1}),a(A,{xs:24,md:12},{default:o(()=>[W((y(),D(q,{shadow:"never",class:"chart-card"},{header:o(()=>[r("div",Et,[r("span",null,[a(z,null,{default:o(()=>[a(F(ot))]),_:1}),e[23]||(e[23]=m(" 数据源分布"))])])]),default:o(()=>[r("div",{class:"chart-container",ref_key:"datasourceChartRef",ref:ee},null,512)]),_:1})),[[U,$.value]])]),_:1})]),_:1})]),_:1}),a(G,{label:"关键词分析",name:"keyword"},{default:o(()=>[W((y(),D(q,{shadow:"never",class:"chart-card"},{default:o(()=>[r("div",{class:"chart-container wordcloud-container",ref_key:"keywordChartRef",ref:L},null,512)]),_:1})),[[U,ae.value]])]),_:1}),a(G,{label:"热门问题",name:"hotQuestions"},{default:o(()=>[W((y(),D(q,{shadow:"never",class:"chart-card"},{default:o(()=>[r("div",Rt,[a(ve,{modelValue:K.value,"onUpdate:modelValue":e[4]||(e[4]=s=>K.value=s),placeholder:"搜索问题关键词",class:"filter-input",clearable:"",onClear:be,onInput:be},{prefix:o(()=>[a(z,null,{default:o(()=>[a(F(lt))]),_:1})]),_:1},8,["modelValue"])]),a(Pe,{data:T.value,border:"",style:{width:"100%"},"header-cell-style":{background:"#f5f7fa",fontWeight:"bold"},"row-class-name":"question-table-row","row-style":{cursor:"pointer"},onRowClick:He,height:"450","max-height":"450",class:"modern-table"},{default:o(()=>[a(E,{type:"index",width:"50",align:"center"}),a(E,{prop:"content",label:"问题内容","min-width":"250","show-overflow-tooltip":""},{default:o(s=>[r("div",Ot,[r("span",null,V(s.row.content),1)])]),_:1}),a(E,{prop:"num",label:"提问次数",width:"120",sortable:"",align:"center"},{default:o(s=>[r("div",Ut,[a(_e,{type:Ue(s.row.num),effect:"plain",size:"large",style:{width:"100%","text-align":"center","font-weight":"bold"}},{default:o(()=>[m(V(s.row.num),1)]),_:2},1032,["type"])])]),_:1}),a(E,{prop:"scene",label:"场景",width:"150",align:"center"},{default:o(s=>[a(_e,{size:"small",effect:"plain"},{default:o(()=>[m(V(s.row.scene||"未知场景"),1)]),_:2},1024)]),_:1}),a(E,{prop:"datasource",label:"数据源",width:"150",align:"center"},{default:o(s=>[a(_e,{size:"small",type:"info",effect:"plain"},{default:o(()=>[m(V(s.row.datasource||"未知来源"),1)]),_:2},1024)]),_:1}),a(E,{label:"操作",width:"150",fixed:"right",align:"center"},{default:o(s=>[a(O,{type:"primary",size:"small",icon:"Plus",onClick:st(Ht=>Re(s.row),["stop"])},{default:o(()=>e[24]||(e[24]=[m(" 添加为FAQ ")])),_:2,__:[24]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})),[[U,oe.value]])]),_:1})]),_:1},8,["modelValue"])]),a(Ne,{modelValue:R.value,"onUpdate:modelValue":e[10]||(e[10]=s=>R.value=s),title:"添加到常见问题",width:"600px","destroy-on-close":"",class:"modern-dialog"},{footer:o(()=>[r("span",Mt,[a(O,{onClick:e[9]||(e[9]=s=>R.value=!1)},{default:o(()=>e[25]||(e[25]=[m("取消")])),_:1,__:[25]}),a(O,{type:"primary",onClick:Oe},{default:o(()=>e[26]||(e[26]=[m(" 确认添加 ")])),_:1,__:[26]})])]),default:o(()=>[a(Ce,{ref_key:"faqFormRef",ref:le,model:f,rules:qe,"label-width":"80px","status-icon":""},{default:o(()=>[a(v,{label:"问题",prop:"question"},{default:o(()=>[a(ve,{modelValue:f.question,"onUpdate:modelValue":e[6]||(e[6]=s=>f.question=s),type:"textarea",rows:"3"},null,8,["modelValue"])]),_:1}),a(v,{label:"答案",prop:"answer"},{default:o(()=>[a(ve,{modelValue:f.answer,"onUpdate:modelValue":e[7]||(e[7]=s=>f.answer=s),type:"textarea",rows:"5"},null,8,["modelValue"])]),_:1}),a(v,{label:"分类",prop:"category"},{default:o(()=>[a(fe,{modelValue:f.category,"onUpdate:modelValue":e[8]||(e[8]=s=>f.category=s),placeholder:"请选择问题分类",style:{width:"100%"}},{default:o(()=>[(y(),j(me,null,pe(Fe,s=>a(i,{key:s.value,label:s.label,value:s.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},It=Ke(Wt,[["__scopeId","data-v-1bab1458"]]);export{It as default};
