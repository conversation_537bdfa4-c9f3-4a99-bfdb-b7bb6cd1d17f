import{_ as b,u as h,b as E,r as f,k as C,c as n,d as R,e as U,f as m,g as r,w as s,h as p,p as q,i as B,l as F,m as N,j as c,E as g}from"./index.js";const S={class:"register-container"},T={class:"register-box card-shadow rounded"},j={class:"form-footer"},D={__name:"Register",setup(I){const w=h(),v=E(),_=f(!1),t=C({phone:"",nickname:"",password:"",confirmPassword:""}),V={phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确",trigger:"blur"}],nickname:[{required:!0,message:"请输入昵称",trigger:"blur"},{min:2,max:10,message:"昵称长度应为2-10个字符",trigger:"blur"}],password:[{required:!0,validator:(u,e,o)=>{var a;e===""?o(new Error("请输入密码")):(t.confirmPassword!==""&&((a=d.value)==null||a.validateField("confirmPassword")),o())},trigger:"blur"},{min:6,max:20,message:"密码长度应为6-20位",trigger:"blur"}],confirmPassword:[{required:!0,validator:(u,e,o)=>{e===""?o(new Error("请再次输入密码")):e!==t.password?o(new Error("两次输入密码不一致")):o()},trigger:"blur"}]},d=f(null),x=async()=>{d.value&&await d.value.validate(async u=>{if(u){_.value=!0;try{const{confirmPassword:e,...o}=t,a=await v.register(o);g({type:"success",message:"注册成功，即将跳转到登录页"}),setTimeout(()=>{w.push("/login")},1500)}catch(e){g.error(e.message||"注册失败，请稍后重试")}finally{_.value=!1}}})};return(u,e)=>{const o=n("el-icon"),a=n("el-input"),i=n("el-form-item"),y=n("el-button"),k=n("router-link"),P=n("el-form");return R(),U("div",S,[m("div",T,[e[7]||(e[7]=m("h2",{class:"title"},"用户注册",-1)),r(P,{ref_key:"registerForm",ref:d,model:t,rules:V,"label-width":"0","status-icon":""},{default:s(()=>[r(i,{prop:"phone"},{default:s(()=>[r(a,{modelValue:t.phone,"onUpdate:modelValue":e[0]||(e[0]=l=>t.phone=l),placeholder:"请输入手机号"},{prefix:s(()=>[r(o,null,{default:s(()=>[r(p(q))]),_:1})]),_:1},8,["modelValue"])]),_:1}),r(i,{prop:"nickname"},{default:s(()=>[r(a,{modelValue:t.nickname,"onUpdate:modelValue":e[1]||(e[1]=l=>t.nickname=l),placeholder:"请输入昵称"},{prefix:s(()=>[r(o,null,{default:s(()=>[r(p(B))]),_:1})]),_:1},8,["modelValue"])]),_:1}),r(i,{prop:"password"},{default:s(()=>[r(a,{modelValue:t.password,"onUpdate:modelValue":e[2]||(e[2]=l=>t.password=l),placeholder:"请设置密码",type:"password","show-password":""},{prefix:s(()=>[r(o,null,{default:s(()=>[r(p(F))]),_:1})]),_:1},8,["modelValue"])]),_:1}),r(i,{prop:"confirmPassword"},{default:s(()=>[r(a,{modelValue:t.confirmPassword,"onUpdate:modelValue":e[3]||(e[3]=l=>t.confirmPassword=l),placeholder:"请确认密码",type:"password","show-password":""},{prefix:s(()=>[r(o,null,{default:s(()=>[r(p(N))]),_:1})]),_:1},8,["modelValue"])]),_:1}),r(i,null,{default:s(()=>[r(y,{type:"primary",loading:_.value,class:"register-button",onClick:x},{default:s(()=>e[4]||(e[4]=[c("注册")])),_:1,__:[4]},8,["loading"])]),_:1}),m("div",j,[e[6]||(e[6]=m("span",null,"已有账号？",-1)),r(k,{to:"/login",class:"login-link"},{default:s(()=>e[5]||(e[5]=[c("立即登录")])),_:1,__:[5]})])]),_:1},8,["model"])])])}}},A=b(D,[["__scopeId","data-v-38772486"]]);export{A as default};
