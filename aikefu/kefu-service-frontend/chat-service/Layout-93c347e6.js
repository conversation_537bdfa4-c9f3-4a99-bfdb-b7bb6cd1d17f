import{_ as b,u as B,b as C,v as L,x as I,o as N,c as s,d as T,e as V,f as n,g as o,w as t,y as z,j as c,t as g,h as E,z as M}from"./index.js";const U={class:"user-layout"},j={class:"header"},A={class:"right-menu"},D={class:"avatar-container"},F={class:"nickname"},R={class:"main-content"},W="/default-avatar.png",q={__name:"Layout",setup(G){const a=B(),r=C(),u=L(),l=I(()=>r.userInfo);N(()=>{if(r.loadFromStorage(),!r.isLoggedIn){a.push("/login");return}if(!r.isUser){a.push("/login");return}u.initWebSocket()});const v=d=>{switch(d){case"profile":a.push("/user/profile");break;case"history":a.push("/user/history");break;case"logout":h();break}},h=()=>{z.confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{u.clearState(),r.logout(),a.push("/login")}).catch(()=>{})};return(d,e)=>{const w=s("el-avatar"),y=s("el-icon"),_=s("el-dropdown-item"),k=s("el-dropdown-menu"),x=s("el-dropdown"),S=s("router-view");return T(),V("div",U,[n("header",j,[e[3]||(e[3]=n("div",{class:"logo"},[n("h1",null,"客服系统")],-1)),n("div",A,[o(x,{trigger:"click",onCommand:v},{dropdown:t(()=>[o(k,null,{default:t(()=>[o(_,{command:"profile"},{default:t(()=>e[0]||(e[0]=[c("个人中心")])),_:1,__:[0]}),o(_,{command:"history"},{default:t(()=>e[1]||(e[1]=[c("历史会话")])),_:1,__:[1]}),o(_,{command:"logout",divided:""},{default:t(()=>e[2]||(e[2]=[c("退出登录")])),_:1,__:[2]})]),_:1})]),default:t(()=>{var i,m;return[n("div",D,[o(w,{size:30,src:((i=l.value)==null?void 0:i.avatar)||W},{default:t(()=>{var p,f;return[c(g(((f=(p=l.value)==null?void 0:p.nickname)==null?void 0:f.substring(0,1))||"用"),1)]}),_:1},8,["src"]),n("span",F,g(((m=l.value)==null?void 0:m.nickname)||"用户"),1),o(y,null,{default:t(()=>[o(E(M))]),_:1})])]}),_:1})])]),n("main",R,[o(S)])])}}},J=b(q,[["__scopeId","data-v-8713c8a0"]]);export{J as default};
