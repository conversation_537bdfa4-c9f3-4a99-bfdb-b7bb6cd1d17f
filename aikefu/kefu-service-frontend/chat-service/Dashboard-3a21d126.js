import{_ as Ve,u as Ae,r as d,x as ce,o as Re,D as Oe,c as w,F as qe,d as h,e as p,f as t,g as a,w as l,h as Y,a4 as Je,a5 as Qe,H as U,a6 as Xe,q as We,G as _e,J as fe,j as b,a7 as ue,t as r,O as ve,a8 as Ze,E as S,V as et,b as tt,v as st,k as at,y as lt,C as Te,n as be,a9 as ot,aa as nt,ab as $e,ac as rt,ad as it,ae as dt,af as ct,ag as ut,ah as vt,ai as _t,A as Le}from"./index.js";import{g as ft}from"./wordCloud-f2415365.js";import{h as k}from"./moment-a9aaa855.js";import{u as mt,g as Ce,i as de,L as Be,a as ht,b as gt,c as pt,d as yt,e as wt,f as bt,h as Ct,j as St}from"./installCanvasRenderer-44afd735.js";const Dt={class:"message-carousel-container"},Mt={class:"carousel-header"},Ft={class:"title"},kt={class:"message-scroll-container"},xt={class:"message-list"},Et={key:0,class:"empty-message"},It=["onClick"],Yt={class:"message-item-content"},zt={class:"user-avatar"},Tt={class:"message-info"},$t={class:"message-header"},Lt={class:"session-id"},Bt={class:"message-time"},Vt={class:"message-text"},At={class:"chat-detail-container"},Rt={key:0,class:"empty-detail"},Ot={key:1,class:"chat-messages"},qt={class:"message-sender"},Wt={class:"message-time"},Ht={class:"message-content"},Pt={class:"dialog-footer"},Ut={__name:"MessageCarousel",props:{displayCount:{type:Number,default:5},storageLimit:{type:Number,default:50}},setup(Se){const z=Se,F=Ae(),y=d([]),T=d(0),M=d(!1);let C=null,N=null;const V=d(!1),A=d([]),K=d(!1),R=d(null),E=ce(()=>{if(y.value.length===0)return[];if(y.value.length<=z.displayCount)return y.value;const c=T.value,o=c+z.displayCount<=y.value.length?c+z.displayCount:y.value.length;return y.value.slice(c,o)}),g=async()=>{M.value=!0;try{const c=await Ze();c.code===200&&c.data?y.value=c.data:console.error("加载最新消息失败:",c.message)}catch(c){console.error("加载最新消息失败:",c),S.error("获取最新消息失败，请刷新重试")}finally{M.value=!1}},me=()=>{C&&clearInterval(C),C=setInterval(()=>{y.value.length<=z.displayCount||(T.value=(T.value+1)%(y.value.length-z.displayCount+1),(T.value<0||T.value>=y.value.length)&&(T.value=0))},1800)},X=c=>k(c).format("YYYY-MM-DD HH:mm:ss"),O=c=>{const o=["#409EFF","#67C23A","#E6A23C","#F56C6C","#909399"],x=c%o.length;return{backgroundColor:o[x]}},$=async c=>{if(!c.session_id){S.warning("无法获取会话ID");return}R.value=c.session_id,V.value=!0,K.value=!0,A.value=[];try{const o=await et(c.session_id);o.code===200&&o.data?A.value=o.data:S.warning("获取会话消息失败")}catch(o){console.error("获取会话消息失败:",o),S.error("获取会话消息失败，请重试")}finally{K.value=!1}},j=c=>{switch(parseInt(c)){case 0:return"访客";case 1:return"客服";case 2:return"AI助手";default:return"未知"}},Z=c=>{c&&(V.value=!1,F.push(`/agent/chat?session=${c}`))},ee=()=>{F.push("/agent/chat")};return Re(async()=>{await g(),me(),N=setInterval(async()=>{await g()},6e4)}),Oe(()=>{C&&(clearInterval(C),C=null),N&&(clearInterval(N),N=null)}),(c,o)=>{const x=w("el-icon"),G=w("el-avatar"),J=w("el-button"),oe=w("el-dialog"),q=qe("loading");return h(),p("div",Dt,[t("div",Mt,[t("div",Ft,[a(x,null,{default:l(()=>[a(Y(Je))]),_:1}),o[3]||(o[3]=t("span",null,"最新消息",-1))]),t("div",{class:"more",onClick:ee},[o[4]||(o[4]=t("span",null,"查看更多",-1)),a(x,null,{default:l(()=>[a(Y(Qe))]),_:1})])]),U((h(),p("div",kt,[t("div",xt,[E.value.length===0?(h(),p("div",Et,[a(x,null,{default:l(()=>[a(Y(Xe))]),_:1}),o[5]||(o[5]=t("span",null,"暂无用户消息",-1))])):We("",!0),(h(!0),p(_e,null,fe(E.value,(m,W)=>(h(),p("div",{key:W,class:"message-item",onClick:L=>$(m)},[t("div",Yt,[t("div",zt,[a(G,{size:24,style:ue(O(W))},{default:l(()=>o[6]||(o[6]=[b("访")])),_:2,__:[6]},1032,["style"])]),t("div",Tt,[t("div",$t,[o[7]||(o[7]=t("span",{class:"user-name"},"访客",-1)),t("span",Lt,"会话ID: "+r(m.session_id),1),t("span",Bt,r(X(m.created_at)),1)]),t("div",Vt,r(m.content),1)])])],8,It))),128))])])),[[q,M.value]]),a(oe,{modelValue:V.value,"onUpdate:modelValue":o[2]||(o[2]=m=>V.value=m),title:"会话详情",width:"70%","destroy-on-close":""},{footer:l(()=>[t("span",Pt,[a(J,{onClick:o[0]||(o[0]=m=>V.value=!1)},{default:l(()=>o[8]||(o[8]=[b("关闭")])),_:1,__:[8]}),a(J,{type:"primary",onClick:o[1]||(o[1]=m=>Z(R.value))},{default:l(()=>o[9]||(o[9]=[b(" 进入会话 ")])),_:1,__:[9]})])]),default:l(()=>[U((h(),p("div",At,[A.value.length===0?(h(),p("div",Rt," 暂无会话记录 ")):(h(),p("div",Ot,[(h(!0),p(_e,null,fe(A.value,(m,W)=>(h(),p("div",{key:W,class:ve(["message-bubble",{"user-message":m.senderType===0,"agent-message":m.senderType===1,"ai-message":m.senderType===2}])},[t("div",qt,[b(r(j(m.senderType))+" ",1),t("span",Wt,r(X(m.createdAt)),1)]),t("div",Ht,r(m.content),1)],2))),128))]))])),[[q,K.value]])]),_:1},8,["modelValue"])])}}},Nt=Ve(Ut,[["__scopeId","data-v-a563a87b"]]);const Kt={class:"dashboard-container"},jt={class:"welcome-section"},Gt={class:"welcome-info"},Jt={class:"welcome-subtitle"},Qt={class:"agent-status"},Xt={class:"date-time"},Zt={class:"date"},es={class:"time"},ts={class:"fullscreen-btn"},ss={class:"data-cards"},as={class:"card-content"},ls={class:"card-icon chat"},os={class:"card-info"},ns={class:"card-data-row"},rs={class:"data-value-group"},is={class:"card-value"},ds={class:"card-content"},cs={class:"card-icon ai-resolved"},us={class:"card-info"},vs={class:"card-data-row"},_s={class:"data-value-group"},fs={class:"card-value"},ms={class:"card-content"},hs={class:"card-icon waiting"},gs={class:"card-info"},ps={class:"card-data-row"},ys={class:"data-value-group"},ws={class:"card-value"},bs={class:"card-actions"},Cs={class:"card-content"},Ss={class:"card-icon satisfaction"},Ds={class:"card-info"},Ms={class:"card-data-row"},Fs={class:"card-value"},ks={class:"card-content"},xs={class:"card-icon resolved"},Es={class:"card-info"},Is={class:"card-data-row"},Ys={class:"data-value-group"},zs={class:"card-value"},Ts={class:"charts-section"},$s={class:"chart-card chart-height"},Ls={class:"chart-header"},Bs={class:"chart-actions"},Vs={class:"table-container"},As={class:"data-bar"},Rs={class:"bar-value"},Os={class:"chart-card chart-height"},qs={class:"chart-header"},Ws={class:"chart-actions"},Hs={class:"charts-section stats-section"},Ps={class:"chart-card"},Us={class:"chart-header"},Ns={class:"pie-chart-container"},Ks={class:"pie-chart-legend"},js={class:"legend-label"},Gs={class:"legend-value"},Js={class:"chart-card"},Qs={class:"chart-scroll-container"},Xs={class:"chart-card"},Zs={class:"chart-header"},ea={class:"pie-chart-container"},ta={class:"pie-chart-legend"},sa={class:"legend-label"},aa={class:"legend-value"},la={__name:"Dashboard",setup(Se){mt([ht,gt,pt,yt,wt,bt,Ct,St]);const z=Ae(),F=tt();st();const y=d(!1),T=()=>{if(y.value)document.exitFullscreen?document.exitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitExitFullscreen?document.webkitExitFullscreen():document.msExitFullscreen&&document.msExitFullscreen(),setTimeout(()=>{const s=document.querySelector(".sidebar");s&&(s.style.display="")},100);else{const s=document.documentElement;s.requestFullscreen?s.requestFullscreen():s.mozRequestFullScreen?s.mozRequestFullScreen():s.webkitRequestFullscreen?s.webkitRequestFullscreen():s.msRequestFullscreen&&s.msRequestFullscreen();const e=document.querySelector(".sidebar");e&&(e.style.display="none")}y.value=!y.value},M=()=>{const s=!!(document.fullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||document.msFullscreenElement);y.value=s;const e=document.querySelector(".sidebar");e&&(s?e.style.display="none":setTimeout(()=>{e.style.display=""},100))},C=ce(()=>F.userInfo),N=ce(()=>{var e;const s=new Date().getHours();return(e=C.value)!=null&&e.name,s>=5&&s<9?"早上好，您辛苦了":s>=9&&s<12?"上午好，您辛苦了":s>=12&&s<14?"中午好，您辛苦了":s>=14&&s<18?"下午好，您辛苦了":s>=18&&s<22?"晚上好，您辛苦了":"夜深了，您辛苦了"}),V=ce(()=>{const s=new Date().getHours(),e=["今天也要元气满满哦！","辛苦了，记得适时休息~","您的微笑是最好的服务","工作顺利，加油！","感谢您的辛勤付出"];return s>=9&&s<12?"早起的鸟儿有虫吃，祝您工作顺利！":s>=12&&s<14?"记得按时用餐，注意休息哦~":s>=14&&s<17?"下午也要继续努力，加油！":s>=17&&s<19?"一天的工作即将结束，辛苦了！":s>=19&&s<23?"晚上好，夜班辛苦了！":s>=23||s<5?"夜深了，注意休息，劳逸结合！":e[Math.floor(Math.random()*e.length)]}),A=d(k().format("YYYY年MM月DD日")),K=d(k().format("HH:mm:ss"));let R=null,E=null;const g=at({todaySessionCount:0,todaySessionTrend:0,waitingSessionCount:0,averageSatisfaction:0,resolvedSessionCount:0,resolvedTrend:0,aiResolvedCount:0}),me=d([]),X=d(!1),O=d("day"),$=d("week"),j=d([]),Z=d([]),ee=d(!1),c=d(0),o=d("month"),x=d(!1),G=d([]),J=["#409EFF","#67C23A","#E6A23C"],oe=d(0),q=d([]),m=["#409EFF","#67C23A","#E6A23C","#F56C6C","#909399","#6B778C","#9254DE","#36CFC9"],W=d(0);let L=null,H=null,B=null;const te=d(null),he=d([]),ge=d(!1);let D=null;Re(async()=>{var s;if(F.loadFromStorage(),!F.isLoggedIn||!F.isAgent){z.push("/login");return}if(De(),R=setInterval(De,1e3),document.addEventListener("fullscreenchange",M),document.addEventListener("webkitfullscreenchange",M),document.addEventListener("mozfullscreenchange",M),document.addEventListener("MSFullscreenChange",M),((s=F.userInfo)==null?void 0:s.status)!==1)try{await lt.confirm("您当前处于离线状态，需要切换为在线状态才能处理会话，是否切换？","提示",{confirmButtonText:"切换为在线",cancelButtonText:"保持离线",type:"warning"}),await Me()}catch{S.info("您选择了保持离线状态，将无法接入新会话")}try{await Promise.all([pe(),ye(),Fe(),ne(o.value),re($.value)]),await Promise.all([we(O.value),Ie()]),E=setInterval(async()=>{F.isLoggedIn&&F.isAgent?await Promise.all([pe(),ye(),Fe(),ne(o.value),re($.value)]):(clearInterval(E),E=null)},6e4),Te(O,e=>{we(e)}),Te($,e=>{re(e)}),window.addEventListener("resize",ke),be(()=>{te.value&&(te.value.style.minHeight="330px")})}catch(e){console.error("初始化工作台失败:",e),S.error("加载数据失败，请刷新页面重试")}}),Oe(()=>{R&&(clearInterval(R),R=null),E&&(clearInterval(E),E=null),document.removeEventListener("fullscreenchange",M),document.removeEventListener("webkitfullscreenchange",M),document.removeEventListener("mozfullscreenchange",M),document.removeEventListener("MSFullscreenChange",M),window.removeEventListener("resize",ke),L&&(L.dispose(),L=null),H&&(H.dispose(),H=null),B&&(B.dispose(),B=null),D&&(D.dispose(),D=null)});const De=()=>{A.value=k().format("YYYY年MM月DD日"),K.value=k().format("HH:mm:ss")},pe=async()=>{try{const s=await ot();s.code===200?Object.assign(g,{...s.data,aiResolvedCount:s.data.aiResolvedCount||0}):S.error(s.message||"获取统计数据失败")}catch(s){console.error("加载统计数据失败",s)}},ye=async()=>{X.value=!0;try{const s=await nt();s.code===200?me.value=s.data.map(e=>({id:e.id,user_id:e.user_id,user_nickname:e.user_nickname,user_avatar:e.user_avatar||"",waiting_time:Math.floor((new Date-new Date(e.start_time))/1e3),last_message:e.last_message||"暂无消息",priority:He(e.vip_level)})):S.error(s.message||"获取待处理会话失败")}catch(s){console.error("加载待处理会话失败",s)}finally{X.value=!1}},He=s=>s>=2?3:s>=1?2:1,we=async s=>{O.value=s;try{const e=await $e(s);if(e.code===200){const _=e.data.map(i=>({time:i.timeLabel,count:i.sessionCount}));j.value=_.sort((i,n)=>i.count>0&&n.count===0?-1:i.count===0&&n.count>0?1:0)}else S.error(e.message||"获取对话统计数据失败")}catch(e){console.error("更新对话统计失败",e)}},Pe=()=>{z.push("/agent/chat")},Me=async()=>{var s;if((s=C.value)!=null&&s.id)try{const e=C.value.status===1?0:1;await F.updateAgentStatusBulk(C.value.id,e),e===1&&await Promise.all([pe(),ye()]),S.success(e===1?"已切换为在线状态":"已切换为离线状态")}catch(e){console.error("更新状态失败",e),S.error("切换状态失败")}},Fe=async()=>{ee.value=!0;try{const s=await $e("month");if(s.code===200){const _=s.data.map(f=>({time:f.timeLabel,count:f.sessionCount,day:parseInt(f.timeLabel.replace("日",""))})).sort((f,I)=>f.day-I.day);c.value=Math.max(..._.map(f=>f.count),1);const i=new Date,n=i.getFullYear(),u=i.getMonth(),P=new Date(n,u+1,0).getDate(),se=[];for(let f=1;f<=P;f++){const I=_.find(ie=>ie.day===f);I?se.push(I):se.push({time:f+"日",count:0,day:f})}Z.value=se,be(()=>{Ie()})}else S.error(s.message||"获取月度对话统计数据失败")}catch(s){console.error("加载月度对话统计失败",s)}finally{ee.value=!1}},ke=()=>{L&&L.resize(),H&&H.resize(),B&&B.resize(),D&&D.resize()},ne=async s=>{o.value=s,x.value=!0;try{let e=null,_=null;if(s==="month"){const n=new Date;e=new Date(n.getFullYear(),n.getMonth(),1).toISOString().split("T")[0],_=new Date(n.getFullYear(),n.getMonth()+1,0).toISOString().split("T")[0]}const i=await rt(e,_);i.code===200?(G.value=i.data.senderTypeStats||[],oe.value=G.value.reduce((n,u)=>n+u.value,0),q.value=i.data.datasourceStats||[],W.value=q.value.reduce((n,u)=>n+u.value,0),Ue(),Ne()):S.error(i.message||"获取消息统计数据失败")}catch(e){console.error("更新消息统计失败",e)}finally{x.value=!1}},Ue=()=>{const s=document.getElementById("sender-type-chart");if(!s)return;L=Ce(s)||de(s);const e={tooltip:{trigger:"item",formatter:"{b}: {c} ({d}%)"},series:[{type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1},emphasis:{label:{show:!0,fontSize:"14",fontWeight:"bold"}},labelLine:{show:!1},data:G.value.map((_,i)=>({value:_.value,name:xe(_.name),itemStyle:{color:J[i%J.length]}}))}]};e&&L.setOption(e)},Ne=()=>{const s=document.getElementById("datasource-chart");if(!s)return;H=Ce(s)||de(s);const e={tooltip:{trigger:"item",formatter:"{b}: {c} ({d}%)"},series:[{type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1},emphasis:{label:{show:!0,fontSize:"14",fontWeight:"bold"}},labelLine:{show:!1},data:q.value.map((_,i)=>({value:_.value,name:_.name||"未知来源",itemStyle:{color:m[i%m.length]}}))}]};e&&H.setOption(e)},xe=s=>{switch(parseInt(s)){case 0:return"用户";case 1:return"客服";case 2:return"AI";default:return"未知"}},Ee=(s,e)=>e?Math.round(s/e*100):0,Ie=()=>{const s=document.getElementById("monthly-bar-chart");if(!s)return;B=Ce(s)||de(s);const e=Z.value.map(u=>u.time),_=Z.value.map(u=>u.count),i=Math.max(e.length*50,s.offsetWidth);s.style.width=`${i}px`;const n={tooltip:{trigger:"axis",axisPointer:{type:"shadow"},textStyle:{fontSize:12}},grid:{top:"10%",left:"3%",right:"3%",bottom:"8%",containLabel:!0},xAxis:[{type:"category",data:e,axisLabel:{interval:0,fontSize:11,color:"#606266"},axisTick:{alignWithLabel:!0},axisLine:{lineStyle:{color:"#E4E7ED"}}}],yAxis:[{type:"value",axisLabel:{fontSize:11,color:"#606266"},splitLine:{lineStyle:{color:"#EBEEF5",type:"dashed"}}}],series:[{name:"对话数",type:"bar",barWidth:30,data:_,itemStyle:{color:new Be(0,0,0,1,[{offset:0,color:"#5B9DF5"},{offset:1,color:"#409EFF"}]),borderRadius:[4,4,0,0]},emphasis:{itemStyle:{color:new Be(0,0,0,1,[{offset:0,color:"#409EFF"},{offset:1,color:"#2B85E4"}])}},label:{show:!0,position:"top",fontSize:12,color:"#606266",formatter:function(u){return u.value>0?u.value:""}}}]};n&&B.setOption(n),B.resize({width:i})},re=async s=>{$.value=s,ge.value=!0;try{let e,_;const i=new Date;s==="day"?(e=k().format("YYYY-MM-DD"),_=k().format("YYYY-MM-DD")):s==="week"?(e=k().subtract(6,"days").format("YYYY-MM-DD"),_=k().format("YYYY-MM-DD")):s==="month"&&(e=k().startOf("month").format("YYYY-MM-DD"),_=k().format("YYYY-MM-DD"));const n=await ft({startDate:e,endDate:_,limit:100});n.code===200?(he.value=n.data,console.log("获取关键词数据:",he.value),be(()=>{Ke()})):S.error(n.message||"获取关键词统计失败")}catch(e){console.error("更新关键词统计失败",e)}finally{ge.value=!1}},Ke=()=>{if(!te.value){console.error("关键词图表DOM引用不存在");return}D&&D.dispose(),D=de(te.value);const s=he.value||[];if(console.log("准备渲染词云，数据项数:",s.length),s.length===0){D.setOption({title:{text:"暂无关键词数据",left:"center",top:"center",textStyle:{fontSize:16,color:"#909399"}}});return}const e=s.filter(u=>u&&u.keyword&&u.count);if(e.length===0){console.error("关键词数据格式不正确:",s),D.setOption({title:{text:"数据格式不正确",left:"center",top:"center",textStyle:{fontSize:16,color:"#909399"}}});return}const _=e.map(u=>({name:u.keyword,value:typeof u.count=="number"?u.count:parseInt(u.count,10)||1}));console.log("处理后的词云数据:",_);const i={tooltip:{show:!0,formatter:function(u){return u.name+" : "+u.value}},series:[{type:"wordCloud",shape:"circle",left:"center",top:"center",width:"100%",height:"100%",sizeRange:[14,50],rotationRange:[-1,1],rotationStep:1,gridSize:15,drawOutOfBound:!1,layoutAnimation:!0,textStyle:{fontFamily:"sans-serif",fontWeight:"bold",color:function(){return"rgb("+Math.round(Math.random()*155+100)+","+Math.round(Math.random()*155+100)+","+Math.round(Math.random()*155+100)+")"}},emphasis:{textStyle:{shadowBlur:12,shadowColor:"rgba(0, 0, 0, 0.3)"}},data:_}]};D.setOption(i),console.log("词云图配置已应用");const n=()=>{D&&D.resize()};return window.addEventListener("resize",n),()=>{window.removeEventListener("resize",n)}};return(s,e)=>{var ze;const _=w("el-tag"),i=w("el-button"),n=w("el-icon"),u=w("el-tooltip"),P=w("el-card"),se=w("el-rate"),f=w("el-radio-button"),I=w("el-radio-group"),ie=w("el-table-column"),je=w("el-table"),Ge=w("el-empty"),Q=w("el-col"),Ye=w("el-row"),ae=qe("loading");return h(),p("div",Kt,[t("div",jt,[t("div",Gt,[t("h2",null,r(N.value),1),t("p",Jt,r(V.value),1),t("div",Qt,[e[5]||(e[5]=t("span",{class:"status-label"},"当前状态：",-1)),a(_,{type:((ze=C.value)==null?void 0:ze.status)===1?"success":"info",effect:"plain"},{default:l(()=>{var v;return[b(r(((v=C.value)==null?void 0:v.status)===1?"在线":"离线"),1)]}),_:1},8,["type"]),a(i,{type:"primary",size:"small",class:"status-btn",onClick:Me},{default:l(()=>{var v;return[b(r(((v=C.value)==null?void 0:v.status)===1?"切换为离线":"切换为在线"),1)]}),_:1})])]),t("div",Xt,[t("div",Zt,r(A.value),1),t("div",es,r(K.value),1),t("div",ts,[a(u,{content:y.value?"退出全屏":"全屏展示",placement:"left"},{default:l(()=>[a(i,{circle:"",onClick:T},{default:l(()=>[a(n,{class:ve({"rotate-icon":y.value})},{default:l(()=>[a(Y(it))]),_:1},8,["class"])]),_:1})]),_:1},8,["content"])])])]),t("div",ss,[a(P,{class:"data-card"},{default:l(()=>[t("div",as,[t("div",ls,[a(n,null,{default:l(()=>[a(Y(dt))]),_:1})]),t("div",os,[e[6]||(e[6]=t("div",{class:"card-title"},"今日对话",-1)),t("div",ns,[t("div",rs,[t("div",is,r(g.todaySessionCount),1),t("div",{class:ve(["card-trend",{up:g.todaySessionTrend>0,down:g.todaySessionTrend<0}])},r(g.todaySessionTrend>0?"+":"")+r(g.todaySessionTrend)+"% ",3)])])])])]),_:1}),a(P,{class:"data-card"},{default:l(()=>[t("div",ds,[t("div",cs,[a(n,null,{default:l(()=>[a(Y(ct))]),_:1})]),t("div",us,[e[8]||(e[8]=t("div",{class:"card-title"},"AI已回复",-1)),t("div",vs,[t("div",_s,[t("div",fs,r(g.aiResolvedCount),1),e[7]||(e[7]=t("div",{class:"card-trend up"}," 智能解答 ",-1))])])])])]),_:1}),a(P,{class:"data-card"},{default:l(()=>[t("div",ms,[t("div",hs,[a(n,null,{default:l(()=>[a(Y(ut))]),_:1})]),t("div",gs,[e[10]||(e[10]=t("div",{class:"card-title"},"待处理会话",-1)),t("div",ps,[t("div",ys,[t("div",ws,r(g.waitingSessionCount),1),t("div",bs,[a(i,{type:"primary",size:"small",class:"mini-btn",onClick:Pe},{default:l(()=>e[9]||(e[9]=[b("处理")])),_:1,__:[9]})])])])])])]),_:1}),a(P,{class:"data-card"},{default:l(()=>[t("div",Cs,[t("div",Ss,[a(n,null,{default:l(()=>[a(Y(vt))]),_:1})]),t("div",Ds,[e[11]||(e[11]=t("div",{class:"card-title"},"满意度评分",-1)),t("div",Ms,[t("div",Fs,r(g.averageSatisfaction.toFixed(1)),1),a(se,{modelValue:g.averageSatisfaction,"onUpdate:modelValue":e[0]||(e[0]=v=>g.averageSatisfaction=v),disabled:"","allow-half":"",colors:["#99A9BF","#F7BA2A","#FF9900"],size:"small"},null,8,["modelValue"])])])])]),_:1}),a(P,{class:"data-card"},{default:l(()=>[t("div",ks,[t("div",xs,[a(n,null,{default:l(()=>[a(Y(_t))]),_:1})]),t("div",Es,[e[12]||(e[12]=t("div",{class:"card-title"},"已解决会话",-1)),t("div",Is,[t("div",Ys,[t("div",zs,r(g.resolvedSessionCount),1),t("div",{class:ve(["card-trend",{up:g.resolvedTrend>0,down:g.resolvedTrend<0}])},r(g.resolvedTrend>0?"+":"")+r(g.resolvedTrend)+"% ",3)])])])])]),_:1})]),t("div",Ts,[a(Ye,{gutter:20},{default:l(()=>[a(Q,{xs:24,sm:24,md:8},{default:l(()=>[t("div",$s,[t("div",Ls,[e[16]||(e[16]=t("h3",null,"对话统计",-1)),t("div",Bs,[a(I,{modelValue:O.value,"onUpdate:modelValue":e[1]||(e[1]=v=>O.value=v),size:"small",onChange:we},{default:l(()=>[a(f,{label:"day"},{default:l(()=>e[13]||(e[13]=[b("今日")])),_:1,__:[13]}),a(f,{label:"week"},{default:l(()=>e[14]||(e[14]=[b("本周")])),_:1,__:[14]}),a(f,{label:"month"},{default:l(()=>e[15]||(e[15]=[b("本月")])),_:1,__:[15]})]),_:1},8,["modelValue"])])]),t("div",Vs,[U((h(),Le(je,{data:j.value,style:{width:"100%"},border:"",stripe:"",height:"330"},{default:l(()=>[a(ie,{prop:"time",label:"时间段",width:"120",align:"center",fixed:"left"}),a(ie,{prop:"count",label:"对话数","min-width":"180"},{default:l(v=>[t("div",As,[t("div",{class:"bar",style:ue({width:Math.min(v.row.count*8,200)+"px",backgroundColor:"#409EFF"})},null,4),t("span",Rs,r(v.row.count),1)])]),_:1})]),_:1},8,["data"])),[[ae,!j.value.length]]),j.value.length?We("",!0):(h(),Le(Ge,{key:0,description:"暂无数据"}))])])]),_:1}),a(Q,{xs:24,sm:24,md:8},{default:l(()=>[t("div",Os,[t("div",qs,[e[20]||(e[20]=t("h3",null,"热门关键词",-1)),t("div",Ws,[a(I,{modelValue:$.value,"onUpdate:modelValue":e[2]||(e[2]=v=>$.value=v),size:"small",onChange:re},{default:l(()=>[a(f,{label:"day"},{default:l(()=>e[17]||(e[17]=[b("今日")])),_:1,__:[17]}),a(f,{label:"week"},{default:l(()=>e[18]||(e[18]=[b("本周")])),_:1,__:[18]}),a(f,{label:"month"},{default:l(()=>e[19]||(e[19]=[b("本月")])),_:1,__:[19]})]),_:1},8,["modelValue"])])]),U(t("div",{class:"wordcloud-container",ref_key:"keywordChartRef",ref:te},null,512),[[ae,ge.value]])])]),_:1}),a(Q,{xs:24,sm:24,md:8},{default:l(()=>[a(Nt,{"display-count":5,"storage-limit":50,class:"chart-height"})]),_:1})]),_:1})]),t("div",Hs,[a(Ye,{gutter:20},{default:l(()=>[a(Q,{xs:24,sm:24,md:8},{default:l(()=>[t("div",Ps,[t("div",Us,[e[23]||(e[23]=t("h3",null,"对话类型统计",-1)),a(I,{modelValue:o.value,"onUpdate:modelValue":e[3]||(e[3]=v=>o.value=v),size:"small",onChange:ne},{default:l(()=>[a(f,{label:"month"},{default:l(()=>e[21]||(e[21]=[b("本月")])),_:1,__:[21]}),a(f,{label:"all"},{default:l(()=>e[22]||(e[22]=[b("全部")])),_:1,__:[22]})]),_:1},8,["modelValue"])]),U((h(),p("div",Ns,[e[24]||(e[24]=t("div",{class:"pie-chart",id:"sender-type-chart"},null,-1)),t("div",Ks,[(h(!0),p(_e,null,fe(G.value,(v,le)=>(h(),p("div",{key:le,class:"legend-item"},[t("div",{class:"legend-color",style:ue({backgroundColor:J[le]})},null,4),t("div",js,r(xe(v.name)),1),t("div",Gs,r(v.value)+" ("+r(Ee(v.value,oe.value))+"%)",1)]))),128))])])),[[ae,x.value]])])]),_:1}),a(Q,{xs:24,sm:24,md:8},{default:l(()=>[t("div",Js,[e[26]||(e[26]=t("div",{class:"chart-header"},[t("h3",null,"本月对话数据统计"),t("div",{class:"chart-subtitle"},"（可左右滚动查看更多）")],-1)),U((h(),p("div",Qs,e[25]||(e[25]=[t("div",{class:"pie-chart",id:"monthly-bar-chart"},null,-1)]))),[[ae,ee.value]])])]),_:1}),a(Q,{xs:24,sm:24,md:8},{default:l(()=>[t("div",Xs,[t("div",Zs,[e[29]||(e[29]=t("h3",null,"数据来源统计",-1)),a(I,{modelValue:o.value,"onUpdate:modelValue":e[4]||(e[4]=v=>o.value=v),size:"small",onChange:ne},{default:l(()=>[a(f,{label:"month"},{default:l(()=>e[27]||(e[27]=[b("本月")])),_:1,__:[27]}),a(f,{label:"all"},{default:l(()=>e[28]||(e[28]=[b("全部")])),_:1,__:[28]})]),_:1},8,["modelValue"])]),U((h(),p("div",ea,[e[30]||(e[30]=t("div",{class:"pie-chart",id:"datasource-chart"},null,-1)),t("div",ta,[(h(!0),p(_e,null,fe(q.value,(v,le)=>(h(),p("div",{key:le,class:"legend-item"},[t("div",{class:"legend-color",style:ue({backgroundColor:m[le%m.length]})},null,4),t("div",sa,r(v.name||"未知来源"),1),t("div",aa,r(v.value)+" ("+r(Ee(v.value,W.value))+"%)",1)]))),128))])])),[[ae,x.value]])])]),_:1})]),_:1})])])}}},da=Ve(la,[["__scopeId","data-v-27963d01"]]);export{da as default};
