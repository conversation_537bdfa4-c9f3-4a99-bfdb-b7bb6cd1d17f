import{_ as Ae,r as p,o as je,E as u,c as w,F as Se,d as x,e as A,f as n,g as a,w as o,h as C,aS as $e,t as S,a0 as Ee,al as Oe,j as b,aT as Ne,a1 as Be,G as ee,J as te,A as R,H as Pe,q as I,L as Q,am as le,av as ae,K as qe,U as Ue,y as se}from"./index.js";const Je={recycle_knowledge:"小智回收知识库",flea_market_knowledge:"小智二手商城知识库",market_knowledge:"小智集市知识库",engineer_knowledge:"工程师知识库",information:"默认知识库"};function G(m){return m?Je[m]||`${m}知识库`:"未知知识库"}async function oe(m){try{const d=await fetch("https://berhomellm.cpolar.cn/documents/add",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(m)});if(!d.ok)throw new Error(`请求失败: ${d.status}`);return await d.json()}catch(d){throw console.error("添加文档失败:",d),d}}async function ne(m,d){try{const f=await fetch("https://berhomellm.cpolar.cn/documents/delete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({ids:m,collection_name:d})});if(!f.ok)throw new Error(`请求失败: ${f.status}`);return await f.json()}catch(f){throw console.error("删除文档失败:",f),f}}async function Ke(m,d){try{const f=await fetch("https://berhomellm.cpolar.cn/documents/update",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({documents:m,collection_name:d})});if(!f.ok)throw new Error(`请求失败: ${f.status}`);return await f.json()}catch(f){throw console.error("更新文档失败:",f),f}}async function Re(m=1,d=10,f){try{console.log("请求文档列表参数:",{page:m,pageSize:d,collectionName:f});const i=await fetch("https://berhomellm.cpolar.cn/documents/list",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({page:m,page_size:d,collection_name:f})});if(!i.ok)throw new Error(`请求失败: ${i.status}`);return await i.json()}catch(i){throw console.error("获取文档列表失败:",i),i}}async function Me(m,d=3,f){try{const i=await fetch("https://berhomellm.cpolar.cn/query",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query_text:m,n_results:d,collection_name:f})});if(!i.ok)throw new Error(`请求失败: ${i.status}`);return await i.json()}catch(i){throw console.error("查询文档失败:",i),i}}async function Le(m){try{const d=await fetch("https://berhomellm.cpolar.cn/collections/create",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({collection_name:m})});if(!d.ok)throw new Error(`请求失败: ${d.status}`);return await d.json()}catch(d){throw console.error("创建集合失败:",d),d}}const ze={class:"knowledge-base-container"},Fe={class:"top-stats"},Ie={class:"stats-content"},Qe={class:"stats-icon"},Ge={class:"stats-info"},He={class:"stats-count"},We={class:"stats-content"},Xe={class:"stats-icon"},Ye={class:"stats-info"},Ze={class:"stats-count"},et={class:"stats-content"},tt={class:"stats-icon"},lt={class:"stats-info"},at={class:"stats-content"},st={class:"stats-icon"},ot={class:"stats-info"},nt={class:"stats-content"},rt={class:"stats-icon"},dt={class:"stats-main"},it={class:"main-content"},ut={class:"document-content"},ct={class:"document-preview"},vt={key:0,class:"document-full"},ft={class:"action-buttons"},_t={class:"pagination-container"},pt={class:"right-panel"},mt={class:"card-header"},yt={class:"search-input-group"},ht={key:0,class:"query-results"},wt={class:"result-header"},gt={class:"result-actions"},bt={class:"similarity"},kt={class:"similarity-value"},xt={class:"result-content"},Ct={key:1,class:"no-results"},Vt={key:2,class:"query-placeholder"},Dt={__name:"KnowledgeBase",setup(m){const d=p([]),f=p([]),i=p(""),j=p([]),B=p(!1),M=p(!1),D=p(0),H=p(10),U=p(1),k=p(null),_=p({id:"",text:""}),$=p(!1),V=p(!1),E=p(""),P=p(""),h=p([]),W=p(!1),q=p(!1),O=p(""),re=s=>s?s.split(`
`)[0]:"",de=s=>{if(!s)return"";const e=s.split(`
`);return e.length<=1?"":e.slice(1).join(`
`).trim()},ie=s=>s?s.includes(`
`):!1;je(async()=>{B.value=!0;try{const s=await fetch("https://berhomellm.cpolar.cn/collections/list",{method:"POST",headers:{"Content-Type":"application/json"}});if(!s.ok)throw new Error(`请求失败: ${s.status}`);const e=await s.json();if(console.log("集合列表原始响应:",e),e&&Array.isArray(e.collections)?(f.value=[...e.collections],d.value=e.collections.map(l=>({name:l}))):e&&Array.isArray(e)&&(f.value=[...e],d.value=e.map(l=>({name:typeof l=="string"?l:String(l)}))),console.log("处理后的集合数据:",d.value),d.value.length>0){const l=d.value.find(t=>t.name==="recycle_knowledge");l?(i.value=l.name,console.log("选择的默认集合:",i.value)):(i.value=d.value[0].name,console.log("未找到recycle_knowledge集合，选择的默认集合:",i.value)),await g()}else u.warning("暂无可用的知识库集合，请联系管理员")}catch(s){console.error("初始化失败",s),u.error("获取知识库数据失败，请刷新重试")}finally{B.value=!1}});const ue=async()=>{console.log("切换到集合:",i.value),U.value=1,k.value=null,_.value={id:"",text:""},await g()},g=async()=>{var s,e;if(!i.value){console.warn("未选择集合，无法加载文档");return}B.value=!0;try{console.log("加载集合文档:",i.value);const l=await Re(U.value,H.value,i.value);if(console.log("获取文档列表响应:",l),l&&(l.results||(s=l.data)!=null&&s.results)){const t=l.results||((e=l.data)==null?void 0:e.results);if(t){const{ids:v,documents:c,pagination:y}=t;Array.isArray(v)&&Array.isArray(c)?(j.value=v.map((T,N)=>({id:T,document:c[N]})),y&&(D.value=y.total)):(j.value=[],D.value=0)}else j.value=[],D.value=0}else l&&Array.isArray(l)?(j.value=l.map(t=>({id:t.id||"",document:t.text||t.content||t})),D.value=l.length):l&&Array.isArray(l.data)?(j.value=l.data.map(t=>({id:t.id||"",document:t.text||t.content||t})),D.value=l.data.length):(j.value=[],D.value=0)}catch(l){console.error("加载文档失败",l),u.error("获取知识文档失败，请刷新重试")}finally{B.value=!1}},ce=s=>{U.value=s,g(),k.value=null,_.value={id:"",text:""}},ve=s=>{k.value=s},fe=s=>{k.value=s,_.value={id:s.id,text:s.document},V.value=!0},_e=s=>{_.value={id:s.id,text:s.document},V.value=!0},pe=s=>{se.confirm("确定要删除该知识文档吗？该操作不可恢复。","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{var e,l;try{const t=await ne([s.id],i.value);if(console.log("删除文档响应:",t),t&&(t.deleted||(e=t.data)!=null&&e.deleted)){const v=t.deleted||((l=t.data)==null?void 0:l.deleted);if(v&&v.length>0){u.success("删除成功"),await g(),k.value&&k.value.id===s.id&&(k.value=null,_.value={id:"",text:""});return}}t?(u.success("删除成功"),await g(),k.value&&k.value.id===s.id&&(k.value=null,_.value={id:"",text:""})):u.error("删除失败")}catch(t){console.error("删除失败",t),u.error("删除失败，请重试")}}).catch(()=>{})},me=async()=>{var s,e,l,t,v;if(!_.value.text.trim()){u.warning("知识内容不能为空");return}try{if(_.value.id){const c=[{id:_.value.id,text:_.value.text}],y=await Ke(c,i.value);if(console.log("更新文档响应:",y),y&&(y.updated||(s=y.data)!=null&&s.updated)){const T=y.updated||((e=y.data)==null?void 0:e.updated);if(T&&T.length>0){u.success("更新成功"),V.value=!1,await g(),X(_.value.id,_.value.text);return}}y?(u.success("更新成功"),V.value=!1,await g(),X(_.value.id,_.value.text)):u.error("更新失败")}else{const c=await oe([{text:_.value.text,collection_name:i.value}]);if(console.log("添加文档响应:",c),c&&(c.results||(l=c.data)!=null&&l.results)){const y=c.results||((t=c.data)==null?void 0:t.results);if(y&&((v=y[i.value])==null?void 0:v.length)>0){u.success("添加成功"),V.value=!1,await g(),k.value=null,_.value={id:"",text:""};return}}c?(u.success("添加成功"),V.value=!1,await g(),k.value=null,_.value={id:"",text:""}):u.error("添加失败")}}catch(c){console.error("保存失败",c),u.error("保存失败，请重试")}},X=(s,e)=>{h.value.length&&(h.value=h.value.map(l=>l.id===s?{...l,document:e}:l))},ye=()=>{E.value="",$.value=!0},he=async()=>{var s,e,l;if(!E.value.trim()){u.warning("知识内容不能为空");return}try{const t=await oe([{text:E.value,collection_name:i.value}]);if(console.log("添加新文档响应:",t),t&&(t.results||(s=t.data)!=null&&s.results)){const v=t.results||((e=t.data)==null?void 0:e.results);if(v&&((l=v[i.value])==null?void 0:l.length)>0){u.success("添加成功"),await g(),$.value=!1;return}}t?(u.success("添加成功"),await g(),$.value=!1):u.error("添加失败")}catch(t){console.error("添加失败",t),u.error("添加失败，请重试")}},Y=async()=>{var s,e;if(!P.value.trim()){u.warning("请输入测试问题");return}M.value=!0,W.value=!0;try{const l=await Me(P.value,3,i.value);if(console.log("检索测试响应:",l),l&&(l.results||(s=l.data)!=null&&s.results)){const t=l.results||((e=l.data)==null?void 0:e.results);if(t&&t.documents&&t.documents[0]){const v=t.documents[0],c=t.distances&&t.distances[0]?t.distances[0]:[],y=t.ids&&t.ids[0]?t.ids[0]:[];h.value=v.map((T,N)=>({document:T,distance:c[N]||null,id:y[N]||null}))}else h.value=[]}else l&&Array.isArray(l)?h.value=l.map(t=>({document:t.document||t.text||t,distance:t.distance||t.score||null,id:t.id||null})):l&&Array.isArray(l.data)?h.value=l.data.map(t=>({document:t.document||t.text||t,distance:t.distance||t.score||null,id:t.id||null})):h.value=[];h.value.length===0&&u.info("未找到匹配的知识文档")}catch(l){console.error("检索测试失败",l),u.error("检索测试失败，请重试"),h.value=[]}finally{M.value=!1}},we=s=>{se.confirm("确定要删除该知识文档吗？该操作不可恢复。","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{var e,l;try{const t=await ne([s.id],i.value);if(console.log("删除文档响应:",t),t&&(t.deleted||(e=t.data)!=null&&e.deleted)){const v=t.deleted||((l=t.data)==null?void 0:l.deleted);if(v&&v.length>0){u.success("删除成功"),h.value=h.value.filter(c=>c.id!==s.id),await g();return}}t?(u.success("删除成功"),h.value=h.value.filter(v=>v.id!==s.id),await g()):u.error("删除失败")}catch(t){console.error("删除失败",t),u.error("删除失败，请重试")}}).catch(()=>{})},ge=({row:s,rowIndex:e})=>"table-row",be=()=>{O.value="",q.value=!0},ke=async()=>{if(!O.value.trim()){u.warning("集合名称不能为空");return}try{const s=await Le(O.value);if(console.log("创建集合响应:",s),s&&s.name){s.status==="created"?u.success(`创建集合 "${G(s.name)}" 成功`):s.status==="already_exists"&&u.info(`集合 "${G(s.name)}" 已存在`),q.value=!1;try{const e=await fetch("https://berhomellm.cpolar.cn/collections/list",{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw new Error(`请求失败: ${e.status}`);const l=await e.json();console.log("刷新集合列表响应:",l),l&&Array.isArray(l.collections)?(f.value=[...l.collections],d.value=l.collections.map(t=>({name:t}))):l&&Array.isArray(l)&&(f.value=[...l],d.value=l.map(t=>({name:typeof t=="string"?t:String(t)}))),s.status==="created"&&(i.value=s.name,await g())}catch(e){console.error("刷新集合列表失败:",e),u.warning("集合创建成功，但刷新列表失败，请手动刷新页面")}}else u.error("创建集合失败")}catch(s){console.error("添加集合失败",s),u.error("添加集合失败，请重试")}};return(s,e)=>{const l=w("el-icon"),t=w("el-card"),v=w("el-col"),c=w("el-button"),y=w("el-option"),T=w("el-select"),N=w("el-row"),Z=w("el-table-column"),xe=w("el-table"),Ce=w("el-pagination"),J=w("el-input"),Ve=w("el-empty"),L=w("el-form-item"),z=w("el-form"),F=w("el-dialog"),De=Se("loading");return x(),A("div",ze,[n("div",Fe,[a(N,{gutter:24},{default:o(()=>[a(v,{span:4},{default:o(()=>[a(t,{shadow:"hover",class:"stats-card collection-total-card"},{default:o(()=>[n("div",Ie,[n("div",Qe,[a(l,null,{default:o(()=>[a(C($e))]),_:1})]),n("div",Ge,[n("div",He,S(d.value.length),1),e[11]||(e[11]=n("div",{class:"stats-label"},"知识库集合数",-1))])])]),_:1})]),_:1}),a(v,{span:4},{default:o(()=>[a(t,{shadow:"hover",class:"stats-card docs-total-card"},{default:o(()=>[n("div",We,[n("div",Xe,[a(l,null,{default:o(()=>[a(C(Ee))]),_:1})]),n("div",Ye,[n("div",Ze,S(D.value),1),e[12]||(e[12]=n("div",{class:"stats-label"},"知识库文档数",-1))])])]),_:1})]),_:1}),a(v,{span:6},{default:o(()=>[a(t,{shadow:"hover",class:"stats-card add-document-card"},{default:o(()=>[n("div",et,[n("div",tt,[a(l,null,{default:o(()=>[a(C(Oe))]),_:1})]),n("div",lt,[e[14]||(e[14]=n("div",{class:"stats-label"},"添加知识文档",-1)),a(c,{type:"primary",onClick:ye,class:"add-doc-btn"},{default:o(()=>e[13]||(e[13]=[b("添加文档")])),_:1,__:[13]})])])]),_:1})]),_:1}),a(v,{span:5},{default:o(()=>[a(t,{shadow:"hover",class:"stats-card add-collection-card"},{default:o(()=>[n("div",at,[n("div",st,[a(l,null,{default:o(()=>[a(C(Ne))]),_:1})]),n("div",ot,[e[16]||(e[16]=n("div",{class:"stats-label"},"添加知识库集合",-1)),a(c,{type:"primary",onClick:be,class:"add-doc-btn"},{default:o(()=>e[15]||(e[15]=[b("添加集合")])),_:1,__:[15]})])])]),_:1})]),_:1}),a(v,{span:5},{default:o(()=>[a(t,{shadow:"hover",class:"stats-card collection-select-card"},{default:o(()=>[n("div",nt,[n("div",rt,[a(l,null,{default:o(()=>[a(C(Be))]),_:1})]),n("div",dt,[e[17]||(e[17]=n("div",{class:"stats-label"},"当前知识库集合",-1)),a(T,{modelValue:i.value,"onUpdate:modelValue":e[0]||(e[0]=r=>i.value=r),placeholder:"请选择知识库集合",onChange:ue,class:"collection-select"},{default:o(()=>[(x(!0),A(ee,null,te(d.value,r=>(x(),R(y,{key:r.name,label:C(G)(r.name),value:r.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])])]),_:1})]),_:1})]),_:1})]),n("div",it,[a(t,{shadow:"hover",class:"document-list-card"},{header:o(()=>e[18]||(e[18]=[n("div",{class:"card-header"},[n("h3",null,"知识文档列表")],-1)])),default:o(()=>[Pe((x(),R(xe,{data:j.value,style:{width:"100%"},"max-height":"580",onRowClick:ve,"row-class-name":ge,"header-cell-style":{backgroundColor:"#f5f7fa",color:"#606266",fontWeight:"bold"}},{default:o(()=>[a(Z,{label:"文档内容"},{default:o(r=>[n("div",ut,[n("div",ct,S(re(r.row.document)),1),ie(r.row.document)?(x(),A("div",vt,S(de(r.row.document)),1)):I("",!0)])]),_:1}),a(Z,{label:"操作",width:"160",align:"center",fixed:"right"},{default:o(r=>[n("div",ft,[a(c,{class:"action-btn edit-btn",link:"",type:"primary",onClick:Q(K=>fe(r.row),["stop"])},{default:o(()=>[a(l,null,{default:o(()=>[a(C(le))]),_:1}),e[19]||(e[19]=b(" 编辑 "))]),_:2,__:[19]},1032,["onClick"]),a(c,{class:"action-btn delete-btn",link:"",type:"danger",onClick:Q(K=>pe(r.row),["stop"])},{default:o(()=>[a(l,null,{default:o(()=>[a(C(ae))]),_:1}),e[20]||(e[20]=b(" 删除 "))]),_:2,__:[20]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[De,B.value]]),n("div",_t,[a(Ce,{background:"",layout:"prev, pager, next",total:D.value,"page-size":H.value,"current-page":U.value,onCurrentChange:ce},null,8,["total","page-size","current-page"])])]),_:1}),n("div",pt,[a(t,{shadow:"hover",class:"search-test-card"},{header:o(()=>[n("div",mt,[e[22]||(e[22]=n("h3",null,"知识库检索",-1)),n("div",yt,[a(J,{modelValue:P.value,"onUpdate:modelValue":e[1]||(e[1]=r=>P.value=r),placeholder:"输入问题进行测试",onKeyup:qe(Y,["enter"])},{prefix:o(()=>[a(l,null,{default:o(()=>[a(C(Ue))]),_:1})]),_:1},8,["modelValue"]),a(c,{type:"primary",onClick:Y,disabled:!P.value,loading:M.value},{default:o(()=>e[21]||(e[21]=[b("查询")])),_:1,__:[21]},8,["disabled","loading"])])])]),default:o(()=>[h.value.length>0?(x(),A("div",ht,[(x(!0),A(ee,null,te(h.value,(r,K)=>(x(),A("div",{key:K,class:"result-item"},[n("div",wt,[n("h4",null,"匹配结果 #"+S(K+1),1),n("div",gt,[n("span",bt,[e[23]||(e[23]=b("相似度：")),n("span",kt,S(r.distance?(1-r.distance).toFixed(4):"未知"),1)]),r.id?(x(),R(c,{key:0,class:"action-btn edit-btn",link:"",type:"primary",onClick:Te=>_e(r)},{default:o(()=>[a(l,null,{default:o(()=>[a(C(le))]),_:1}),e[24]||(e[24]=b(" 编辑 "))]),_:2,__:[24]},1032,["onClick"])):I("",!0),r.id?(x(),R(c,{key:1,class:"action-btn delete-btn",link:"",type:"danger",onClick:Q(Te=>we(r),["stop"])},{default:o(()=>[a(l,null,{default:o(()=>[a(C(ae))]),_:1}),e[25]||(e[25]=b(" 删除 "))]),_:2,__:[25]},1032,["onClick"])):I("",!0)])]),n("div",xt,S(r.document),1)]))),128))])):W.value?(x(),A("div",Ct,[a(Ve,{description:"暂无匹配结果"})])):(x(),A("div",Vt,e[26]||(e[26]=[n("p",null,'在上方输入框中输入问题，点击"查询"按钮或按Enter键查看知识库匹配效果。',-1)])))]),_:1})])]),a(F,{modelValue:$.value,"onUpdate:modelValue":e[4]||(e[4]=r=>$.value=r),title:"添加知识文档",width:"600px","destroy-on-close":""},{footer:o(()=>[n("span",null,[a(c,{onClick:e[3]||(e[3]=r=>$.value=!1)},{default:o(()=>e[27]||(e[27]=[b("取消")])),_:1,__:[27]}),a(c,{type:"primary",onClick:he,disabled:!E.value},{default:o(()=>e[28]||(e[28]=[b("确认")])),_:1,__:[28]},8,["disabled"])])]),default:o(()=>[a(z,null,{default:o(()=>[a(L,{label:"知识内容"},{default:o(()=>[a(J,{modelValue:E.value,"onUpdate:modelValue":e[2]||(e[2]=r=>E.value=r),type:"textarea",rows:8,placeholder:"请输入知识文档内容"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),a(F,{modelValue:V.value,"onUpdate:modelValue":e[7]||(e[7]=r=>V.value=r),title:"编辑知识文档",width:"600px","destroy-on-close":""},{footer:o(()=>[n("span",null,[a(c,{onClick:e[6]||(e[6]=r=>V.value=!1)},{default:o(()=>e[29]||(e[29]=[b("取消")])),_:1,__:[29]}),a(c,{type:"primary",onClick:me,disabled:!_.value.text},{default:o(()=>e[30]||(e[30]=[b("保存")])),_:1,__:[30]},8,["disabled"])])]),default:o(()=>[a(z,{"label-position":"top"},{default:o(()=>[a(L,{label:"知识内容"},{default:o(()=>[a(J,{modelValue:_.value.text,"onUpdate:modelValue":e[5]||(e[5]=r=>_.value.text=r),type:"textarea",rows:10,placeholder:"请输入知识文档内容"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),a(F,{modelValue:q.value,"onUpdate:modelValue":e[10]||(e[10]=r=>q.value=r),title:"添加知识库集合",width:"500px","destroy-on-close":""},{footer:o(()=>[n("span",null,[a(c,{onClick:e[9]||(e[9]=r=>q.value=!1)},{default:o(()=>e[31]||(e[31]=[b("取消")])),_:1,__:[31]}),a(c,{type:"primary",onClick:ke,disabled:!O.value},{default:o(()=>e[32]||(e[32]=[b("确认")])),_:1,__:[32]},8,["disabled"])])]),default:o(()=>[a(z,null,{default:o(()=>[a(L,{label:"集合名称"},{default:o(()=>[a(J,{modelValue:O.value,"onUpdate:modelValue":e[8]||(e[8]=r=>O.value=r),placeholder:"请输入知识库集合名称，只能输入字母、数字、下划线"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}},At=Ae(Dt,[["__scopeId","data-v-c30d96c7"]]);export{At as default};
