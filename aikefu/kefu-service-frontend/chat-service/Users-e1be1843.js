import{_ as Te,u as Ve,k as K,r as g,x as he,o as De,D as Ue,n as Ce,aO as xe,E as m,c as i,F as Se,d as D,e as $,f as v,g as t,w as a,j as o,h as Q,al as ze,aP as Ne,K as Ie,H as Be,A as O,t as d,q as $e,T as Ae,W as Me,aQ as Pe,y as W,aR as Ye}from"./index.js";import{h as Le}from"./moment-a9aaa855.js";const Re={class:"users-container"},qe={class:"users-list"},Ee={class:"card-header"},Fe={class:"header-operations"},He={class:"user-info"},je={class:"user-detail"},Ke={class:"user-name"},Qe={class:"user-phone"},Oe={key:1},We={class:"pagination-container"},Ge={key:0,class:"user-detail-dialog"},Je={class:"dialog-footer"},Xe={__name:"Users",setup(Ze){const G=Ve(),n=K({pageNum:1,pageSize:10,userType:"",startDate:"",endDate:"",keyword:""}),C=g([]),x=g([]),S=g(0),z=g(!1),A=g("calc(100vh - 300px)"),N=g(!1),c=g(null),M=g([]),w=g(!1),I=g(null),r=K({id:"",username:"",nickname:"",phone:"",email:"",userType:"1",status:1}),J={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],nickname:[{required:!1,max:30,message:"最大长度为 30 个字符",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],userType:[{required:!0,message:"请选择用户类型",trigger:"change"}]},X=he(()=>r.id?"编辑用户":"新增用户");De(()=>{y(),window.addEventListener("resize",B),B()}),Ue(()=>{window.removeEventListener("resize",B)});const B=()=>{Ce(()=>{A.value="calc(100vh - 300px)"})},y=async()=>{z.value=!0;try{const s={pageNum:n.pageNum,pageSize:n.pageSize,userType:n.userType||"",startDate:n.startDate||"",endDate:n.endDate||"",keyword:n.keyword||""},e=await xe(s);if(e.code===200){const k=e.data||{records:[],total:0};x.value=(k.records||k.list||[]).map(u=>({...u,userType:u.vipLevel?u.vipLevel.toString():"1",registerTime:u.createdAt,lastActiveTime:u.updatedAt||u.createdAt,sessionCount:u.sessionCount||0,status:u.status===void 0?1:u.status})),S.value=k.total||0,console.log("用户列表数据:",x.value),console.log("总数:",S.value),console.log("当前页:",n.pageNum),console.log("每页大小:",n.pageSize)}else m.error(e.message||"获取用户列表失败")}catch(s){console.error("加载用户列表失败",s),m.error("加载用户列表失败")}finally{z.value=!1}},Z=async s=>{try{c.value=s,N.value=!0,await ee(s.id)}catch(e){console.error("获取用户详情失败",e),m.error("获取用户详情失败")}},ee=async s=>{try{const e=await Ae(s);e.code===200?M.value=e.data||[]:m.error(e.message||"获取用户会话记录失败")}catch(e){console.error("加载用户会话记录失败",e),m.error("加载用户会话记录失败")}},te=s=>{G.push({path:"/agent/history",query:{id:s.id}})},ae=s=>{var e;r.id=s.id,r.username=s.username,r.nickname=s.nickname,r.phone=s.phone,r.email=s.email,r.userType=(e=s.userType)==null?void 0:e.toString(),r.status=s.status,w.value=!0},le=()=>{r.id="",r.username="",r.nickname="",r.phone="",r.email="",r.userType="1",r.status=1,w.value=!0},se=async()=>{if(I.value)try{await I.value.validate();const s={...r,vipLevel:parseInt(r.userType)};if(r.id){const e=await Me(s);e.code===200?(m.success("修改成功"),y()):m.error(e.message||"修改失败")}else{const e=await Pe(s);e.code===200?(m.success("新增成功"),y()):m.error(e.message||"新增失败")}w.value=!1}catch(s){console.error("表单验证失败",s)}},oe=s=>{W.confirm(`确定要删除用户 "${s.nickname||s.username}" 吗？`,"删除用户",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await Ye(s.id);e.code===200?(m.success("删除成功"),y()):m.error(e.message||"删除失败")}catch(e){console.error("删除用户失败",e),m.error("删除用户失败")}}).catch(()=>{m.info("已取消删除")})},re=s=>{W.confirm(`确定要与用户 "${s.nickname||s.username}" 发起会话吗？`,"发起会话",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then(()=>{m.info("发起会话功能开发中")}).catch(()=>{})},P=()=>{n.pageNum=1,y()},ne=()=>{n.userType="",n.startDate="",n.endDate="",n.keyword="",C.value=[],n.pageNum=1,y()},ue=s=>{s?(n.startDate=s[0],n.endDate=s[1]):(n.startDate="",n.endDate="")},ie=s=>{n.pageSize=s,n.pageNum=1,y()},de=s=>{n.pageNum=s,y()},pe=()=>{m.info("导出功能开发中")},Y=s=>{switch(s){case"1":return"";case"2":return"success";case"3":return"warning";default:return""}},L=s=>{switch(s){case"1":return"普通用户";case"2":return"会员";case"3":return"VIP";default:return"未知"}},me=s=>{switch(parseInt(s)){case 0:return"info";case 1:return"success";case 2:return"warning";default:return"info"}},ce=s=>{switch(parseInt(s)){case 0:return"已结束";case 1:return"进行中";case 2:return"待处理";default:return"未知"}},R=s=>s?s.replace(/(\d{3})\d{4}(\d{4})/,"$1****$2"):"-",T=s=>s?Le(s).format("YYYY-MM-DD HH:mm:ss"):"-";return(s,e)=>{const k=i("el-icon"),u=i("el-button"),b=i("el-option"),q=i("el-select"),_=i("el-form-item"),_e=i("el-date-picker"),V=i("el-input"),E=i("el-form"),p=i("el-table-column"),fe=i("el-avatar"),h=i("el-tag"),F=i("el-table"),ge=i("el-pagination"),ve=i("el-card"),f=i("el-descriptions-item"),ye=i("el-descriptions"),be=i("el-divider"),H=i("el-dialog"),j=i("el-radio"),we=i("el-radio-group"),ke=Se("loading");return D(),$("div",Re,[v("div",qe,[t(ve,{shadow:"hover"},{header:a(()=>[v("div",Ee,[e[17]||(e[17]=v("span",null,"用户管理",-1)),e[18]||(e[18]=o()),v("div",Fe,[t(u,{type:"primary",size:"small",onClick:le},{default:a(()=>[t(k,null,{default:a(()=>[t(Q(ze))]),_:1}),e[14]||(e[14]=o(" 新增用户 "))]),_:1,__:[14]}),e[16]||(e[16]=o()),t(u,{type:"primary",size:"small",onClick:pe},{default:a(()=>[t(k,null,{default:a(()=>[t(Q(Ne))]),_:1}),e[15]||(e[15]=o(" 导出 "))]),_:1,__:[15]})])])]),default:a(()=>[e[32]||(e[32]=o()),t(E,{inline:!0,class:"search-form"},{default:a(()=>[t(_,{label:"用户类型"},{default:a(()=>[t(q,{modelValue:n.userType,"onUpdate:modelValue":e[0]||(e[0]=l=>n.userType=l),placeholder:"全部类型",clearable:"",style:{width:"200px"}},{default:a(()=>[t(b,{label:"全部",value:""}),e[19]||(e[19]=o()),t(b,{label:"普通用户",value:"1"}),e[20]||(e[20]=o()),t(b,{label:"会员",value:"2"}),e[21]||(e[21]=o()),t(b,{label:"VIP",value:"3"})]),_:1,__:[19,20,21]},8,["modelValue"])]),_:1}),e[25]||(e[25]=o()),t(_,{label:"注册时间"},{default:a(()=>[t(_e,{modelValue:C.value,"onUpdate:modelValue":e[1]||(e[1]=l=>C.value=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",onChange:ue,style:{width:"350px"}},null,8,["modelValue"])]),_:1}),e[26]||(e[26]=o()),t(_,{label:"搜索"},{default:a(()=>[t(V,{modelValue:n.keyword,"onUpdate:modelValue":e[2]||(e[2]=l=>n.keyword=l),placeholder:"用户名/ID/手机号",clearable:"",onKeyup:Ie(P,["enter"])},null,8,["modelValue"])]),_:1}),e[27]||(e[27]=o()),t(_,null,{default:a(()=>[t(u,{type:"primary",onClick:P},{default:a(()=>e[22]||(e[22]=[o("查询")])),_:1,__:[22]}),e[24]||(e[24]=o()),t(u,{onClick:ne},{default:a(()=>e[23]||(e[23]=[o("重置")])),_:1,__:[23]})]),_:1,__:[24]})]),_:1,__:[25,26,27]}),Be((D(),O(F,{data:x.value,style:{width:"100%"},border:"",stripe:"","header-cell-style":{background:"#f5f7fa"},height:"calc(100vh - 300px)","max-height":A.value},{default:a(()=>[t(p,{type:"selection",width:"50"}),t(p,{prop:"id",label:"用户ID",width:"100"}),t(p,{label:"用户信息",width:"180"},{default:a(l=>[v("div",He,[t(fe,{size:32,src:l.row.avatar},{default:a(()=>[o(d(l.row.nickname?l.row.nickname.substring(0,1):"用"),1)]),_:2},1032,["src"]),v("div",je,[v("div",Ke,d(l.row.nickname||"用户"+l.row.id),1),v("div",Qe,d(R(l.row.phone)),1)])])]),_:1}),t(p,{label:"用户类型",width:"100"},{default:a(l=>[t(h,{type:Y(l.row.userType)},{default:a(()=>[o(d(L(l.row.userType)),1)]),_:2},1032,["type"])]),_:1}),t(p,{prop:"datasource",label:"数据来源",width:"120"},{default:a(l=>[l.row.datasource?(D(),O(h,{key:0,size:"small",type:"info"},{default:a(()=>[o(d(l.row.datasource),1)]),_:2},1024)):(D(),$("span",Oe,"-"))]),_:1}),t(p,{prop:"email",label:"邮箱",width:"180","show-overflow-tooltip":""}),t(p,{label:"注册时间",prop:"registerTime",width:"180"},{default:a(l=>[o(d(T(l.row.registerTime)),1)]),_:1}),t(p,{label:"最后活跃",prop:"lastActiveTime",width:"180"},{default:a(l=>[o(d(T(l.row.lastActiveTime)),1)]),_:1}),t(p,{label:"会话次数",prop:"sessionCount",width:"100",align:"center"}),t(p,{label:"操作",width:"220",fixed:"right"},{default:a(l=>[t(u,{type:"primary",link:"",onClick:U=>Z(l.row)},{default:a(()=>e[28]||(e[28]=[o(" 详情 ")])),_:2,__:[28]},1032,["onClick"]),t(u,{type:"primary",link:"",onClick:U=>ae(l.row)},{default:a(()=>e[29]||(e[29]=[o(" 编辑 ")])),_:2,__:[29]},1032,["onClick"]),t(u,{type:"danger",link:"",onClick:U=>oe(l.row)},{default:a(()=>e[30]||(e[30]=[o(" 删除 ")])),_:2,__:[30]},1032,["onClick"]),t(u,{type:"success",link:"",onClick:U=>re(l.row)},{default:a(()=>e[31]||(e[31]=[o(" 发起会话 ")])),_:2,__:[31]},1032,["onClick"])]),_:1})]),_:1},8,["data","max-height"])),[[ke,z.value]]),v("div",We,[t(ge,{"current-page":n.pageNum,"onUpdate:currentPage":e[3]||(e[3]=l=>n.pageNum=l),"page-size":n.pageSize,"onUpdate:pageSize":e[4]||(e[4]=l=>n.pageSize=l),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:S.value,onSizeChange:ie,onCurrentChange:de,background:""},null,8,["current-page","page-size","total"])])]),_:1,__:[32]})]),t(H,{modelValue:N.value,"onUpdate:modelValue":e[5]||(e[5]=l=>N.value=l),title:"用户详情",width:"60%","destroy-on-close":""},{default:a(()=>[c.value?(D(),$("div",Ge,[t(ye,{column:2,border:""},{default:a(()=>[t(f,{label:"用户ID"},{default:a(()=>[o(d(c.value.id),1)]),_:1}),t(f,{label:"用户名"},{default:a(()=>[o(d(c.value.username),1)]),_:1}),t(f,{label:"昵称"},{default:a(()=>[o(d(c.value.nickname),1)]),_:1}),t(f,{label:"手机号"},{default:a(()=>[o(d(R(c.value.phone)),1)]),_:1}),t(f,{label:"邮箱"},{default:a(()=>[o(d(c.value.email),1)]),_:1}),t(f,{label:"用户类型"},{default:a(()=>[t(h,{type:Y(c.value.userType)},{default:a(()=>[o(d(L(c.value.userType)),1)]),_:1},8,["type"])]),_:1}),t(f,{label:"注册时间"},{default:a(()=>[o(d(T(c.value.registerTime)),1)]),_:1}),t(f,{label:"最后活跃"},{default:a(()=>[o(d(T(c.value.lastActiveTime)),1)]),_:1}),t(f,{label:"会话次数"},{default:a(()=>[o(d(c.value.sessionCount),1)]),_:1}),t(f,{label:"账户状态"},{default:a(()=>[t(h,{type:c.value.status===1?"success":"danger"},{default:a(()=>[o(d(c.value.status===1?"正常":"禁用"),1)]),_:1},8,["type"])]),_:1})]),_:1}),t(be,{"content-position":"center"},{default:a(()=>e[33]||(e[33]=[o("最近会话记录")])),_:1,__:[33]}),t(F,{data:M.value,style:{width:"100%"},border:"",size:"small","header-cell-style":{background:"#f5f7fa"}},{default:a(()=>[t(p,{prop:"id",label:"会话ID",width:"120"}),t(p,{label:"会话状态",width:"100"},{default:a(l=>[t(h,{type:me(l.row.status)},{default:a(()=>[o(d(ce(l.row.status)),1)]),_:2},1032,["type"])]),_:1}),t(p,{label:"创建时间",width:"160"},{default:a(l=>[o(d(T(l.row.createdAt)),1)]),_:1}),t(p,{label:"最后消息","show-overflow-tooltip":""},{default:a(l=>[o(d(l.row.lastMessage||"无消息"),1)]),_:1}),t(p,{label:"操作",width:"100"},{default:a(l=>[t(u,{type:"primary",link:"",onClick:U=>te(l.row)},{default:a(()=>e[34]||(e[34]=[o(" 详情 ")])),_:2,__:[34]},1032,["onClick"])]),_:1})]),_:1},8,["data"])])):$e("",!0)]),_:1},8,["modelValue"]),t(H,{modelValue:w.value,"onUpdate:modelValue":e[13]||(e[13]=l=>w.value=l),title:X.value,width:"50%","destroy-on-close":""},{footer:a(()=>[v("span",Je,[t(u,{onClick:e[12]||(e[12]=l=>w.value=!1)},{default:a(()=>e[37]||(e[37]=[o("取消")])),_:1,__:[37]}),t(u,{type:"primary",onClick:se},{default:a(()=>e[38]||(e[38]=[o("确定")])),_:1,__:[38]})])]),default:a(()=>[t(E,{ref_key:"userFormRef",ref:I,model:r,rules:J,"label-width":"80px"},{default:a(()=>[t(_,{label:"用户名",prop:"username"},{default:a(()=>[t(V,{modelValue:r.username,"onUpdate:modelValue":e[6]||(e[6]=l=>r.username=l),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1}),t(_,{label:"昵称",prop:"nickname"},{default:a(()=>[t(V,{modelValue:r.nickname,"onUpdate:modelValue":e[7]||(e[7]=l=>r.nickname=l),placeholder:"请输入昵称"},null,8,["modelValue"])]),_:1}),t(_,{label:"手机号",prop:"phone"},{default:a(()=>[t(V,{modelValue:r.phone,"onUpdate:modelValue":e[8]||(e[8]=l=>r.phone=l),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),t(_,{label:"邮箱",prop:"email"},{default:a(()=>[t(V,{modelValue:r.email,"onUpdate:modelValue":e[9]||(e[9]=l=>r.email=l),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1}),t(_,{label:"用户类型",prop:"userType"},{default:a(()=>[t(q,{modelValue:r.userType,"onUpdate:modelValue":e[10]||(e[10]=l=>r.userType=l),placeholder:"请选择用户类型"},{default:a(()=>[t(b,{label:"普通用户",value:"1"}),t(b,{label:"会员",value:"2"}),t(b,{label:"VIP",value:"3"})]),_:1},8,["modelValue"])]),_:1}),t(_,{label:"账户状态",prop:"status"},{default:a(()=>[t(we,{modelValue:r.status,"onUpdate:modelValue":e[11]||(e[11]=l=>r.status=l)},{default:a(()=>[t(j,{label:1},{default:a(()=>e[35]||(e[35]=[o("正常")])),_:1,__:[35]}),t(j,{label:0},{default:a(()=>e[36]||(e[36]=[o("禁用")])),_:1,__:[36]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},at=Te(Xe,[["__scopeId","data-v-18577200"]]);export{at as default};
