import{_ as Q,a as W,u as X,b as Z,r as c,k as ee,o as te,E as D,c as n,F as oe,d as y,e as ae,f as r,A as I,w as s,H as se,g as t,t as g,j as i}from"./index.js";import{h as le}from"./moment-a9aaa855.js";const ne={class:"evaluation-container"},re={class:"evaluation-content"},ie={class:"session-info"},ue={class:"info-item"},de={class:"value"},me={class:"info-item"},pe={class:"value"},ce={class:"history-evaluations"},_e={class:"pagination-container"},fe={__name:"Evaluation",setup(ge){const C=W(),T=X(),S=Z(),v=c(null),w=c(null),l=ee({sessionId:"",satisfaction:5,resolution:1,responseSpeed:5,professionalism:5,attitude:5,comment:""}),z={satisfaction:[{required:!0,message:"请选择满意度",trigger:"change"}],resolution:[{required:!0,message:"请选择问题解决度",trigger:"change"}],responseSpeed:[{required:!0,message:"请评价响应速度",trigger:"change"}],professionalism:[{required:!0,message:"请评价专业程度",trigger:"change"}],attitude:[{required:!0,message:"请评价服务态度",trigger:"change"}]},V=c(!1),x=c([]),_=c(1),f=c(10),U=c(0);te(()=>{if(S.loadFromStorage(),!S.isLoggedIn||!S.isUser){T.push("/login");return}const a=C.query.id;a&&E(a),b()});const E=async a=>{try{await new Promise(e=>setTimeout(e,500)),v.value={id:a,agentId:"A1001",agentName:"客服小王",createdAt:new Date(Date.now()-Math.random()*864e5).toISOString(),endedAt:new Date().toISOString(),status:0},l.sessionId=a}catch(e){console.error("获取会话信息失败",e),D.error("获取会话信息失败")}},b=async()=>{V.value=!0;try{await new Promise(d=>setTimeout(d,800));const a=[],e=25,F=Math.min(f.value,e-(_.value-1)*f.value);for(let d=0;d<F;d++){const h=(_.value-1)*f.value+d,m=new Date(Date.now()-h*864e5*2-Math.random()*864e5).toISOString(),u=new Date(Date.parse(m)-Math.random()*36e5).toISOString();a.push({id:`E${1e4+h}`,sessionId:`S${2e4+h}`,userId:"U1001",agentId:`A${1e3+Math.floor(Math.random()*10)}`,agentName:["客服小王","客服小李","客服小张"][Math.floor(Math.random()*3)],satisfaction:Math.floor(Math.random()*3)+3,resolution:Math.floor(Math.random()*3)+1,responseSpeed:Math.floor(Math.random()*2)+4,professionalism:Math.floor(Math.random()*2)+4,attitude:Math.floor(Math.random()*2)+4,comment:["服务很好，问题得到解决","客服很专业，解决了我的问题","回复速度很快，态度很好","非常满意，谢谢客服的帮助",""][Math.floor(Math.random()*5)],sessionTime:u,createdAt:m})}x.value=a,U.value=e}catch(a){console.error("加载历史评价失败",a),D.error("加载历史评价失败")}finally{V.value=!1}},N=async()=>{if(w.value)try{await w.value.validate(),await new Promise(a=>setTimeout(a,500)),D.success("评价提交成功"),k(),_.value=1,b(),v.value=null}catch(a){console.error("表单验证失败",a)}},k=()=>{w.value&&w.value.resetFields()},q=a=>{f.value=a,b()},R=a=>{_.value=a,b()},H=a=>{switch(a){case 1:return"success";case 2:return"warning";case 3:return"danger";default:return"info"}},P=a=>{switch(a){case 1:return"完全解决";case 2:return"部分解决";case 3:return"未解决";default:return"未知"}},M=a=>a?le(a).format("YYYY-MM-DD HH:mm:ss"):"-";return(a,e)=>{const F=n("el-empty"),d=n("el-card"),h=n("el-divider"),m=n("el-rate"),u=n("el-form-item"),A=n("el-radio"),O=n("el-radio-group"),Y=n("el-input"),B=n("el-button"),$=n("el-form"),p=n("el-table-column"),j=n("el-tag"),L=n("el-table"),G=n("el-pagination"),J=oe("loading");return y(),ae("div",ne,[e[17]||(e[17]=r("div",{class:"page-header"},[r("h2",null,"客服评价"),r("p",{class:"sub-title"},"您的反馈将帮助我们提升服务质量")],-1)),r("div",re,[v.value?(y(),I(d,{key:1,class:"evaluation-form-card"},{default:s(()=>[r("div",ie,[r("div",ue,[e[8]||(e[8]=r("span",{class:"label"},"会话时间：",-1)),r("span",de,g(M(v.value.createdAt)),1)]),r("div",me,[e[9]||(e[9]=r("span",{class:"label"},"客服名称：",-1)),r("span",pe,g(v.value.agentName||"客服人员"),1)])]),t(h,{"content-position":"center"},{default:s(()=>e[10]||(e[10]=[i("评价表单")])),_:1,__:[10]}),t($,{ref_key:"formRef",ref:w,model:l,rules:z,"label-width":"100px",class:"evaluation-form"},{default:s(()=>[t(u,{label:"服务满意度",prop:"satisfaction"},{default:s(()=>[t(m,{modelValue:l.satisfaction,"onUpdate:modelValue":e[0]||(e[0]=o=>l.satisfaction=o),colors:["#99A9BF","#F7BA2A","#FF9900"],texts:["很差","一般","满意","很满意","非常满意"],"show-text":""},null,8,["modelValue"])]),_:1}),t(u,{label:"问题解决度",prop:"resolution"},{default:s(()=>[t(O,{modelValue:l.resolution,"onUpdate:modelValue":e[1]||(e[1]=o=>l.resolution=o)},{default:s(()=>[t(A,{label:1},{default:s(()=>e[11]||(e[11]=[i("完全解决")])),_:1,__:[11]}),t(A,{label:2},{default:s(()=>e[12]||(e[12]=[i("部分解决")])),_:1,__:[12]}),t(A,{label:3},{default:s(()=>e[13]||(e[13]=[i("未解决")])),_:1,__:[13]})]),_:1},8,["modelValue"])]),_:1}),t(u,{label:"响应速度",prop:"responseSpeed"},{default:s(()=>[t(m,{modelValue:l.responseSpeed,"onUpdate:modelValue":e[2]||(e[2]=o=>l.responseSpeed=o),colors:["#99A9BF","#F7BA2A","#FF9900"]},null,8,["modelValue"])]),_:1}),t(u,{label:"专业程度",prop:"professionalism"},{default:s(()=>[t(m,{modelValue:l.professionalism,"onUpdate:modelValue":e[3]||(e[3]=o=>l.professionalism=o),colors:["#99A9BF","#F7BA2A","#FF9900"]},null,8,["modelValue"])]),_:1}),t(u,{label:"服务态度",prop:"attitude"},{default:s(()=>[t(m,{modelValue:l.attitude,"onUpdate:modelValue":e[4]||(e[4]=o=>l.attitude=o),colors:["#99A9BF","#F7BA2A","#FF9900"]},null,8,["modelValue"])]),_:1}),t(u,{label:"评价内容",prop:"comment"},{default:s(()=>[t(Y,{modelValue:l.comment,"onUpdate:modelValue":e[5]||(e[5]=o=>l.comment=o),type:"textarea",rows:4,placeholder:"请输入您的评价或建议（选填）"},null,8,["modelValue"])]),_:1}),t(u,null,{default:s(()=>[t(B,{type:"primary",onClick:N},{default:s(()=>e[14]||(e[14]=[i("提交评价")])),_:1,__:[14]}),t(B,{onClick:k},{default:s(()=>e[15]||(e[15]=[i("重置")])),_:1,__:[15]})]),_:1})]),_:1},8,["model"])]),_:1})):(y(),I(d,{key:0,class:"empty-state"},{default:s(()=>[t(F,{description:"暂无待评价的会话"})]),_:1}))]),r("div",ce,[e[16]||(e[16]=r("h3",null,"历史评价",-1)),se((y(),I(L,{data:x.value,style:{width:"100%"},border:"",stripe:""},{default:s(()=>[t(p,{prop:"sessionId",label:"会话ID",width:"100"}),t(p,{label:"会话时间",width:"180"},{default:s(o=>[i(g(M(o.row.sessionTime)),1)]),_:1}),t(p,{label:"客服",width:"120"},{default:s(o=>[i(g(o.row.agentName||"客服人员"),1)]),_:1}),t(p,{label:"满意度",width:"150"},{default:s(o=>[t(m,{modelValue:o.row.satisfaction,"onUpdate:modelValue":K=>o.row.satisfaction=K,disabled:"","text-color":"#ff9900"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),t(p,{label:"问题解决度",width:"120"},{default:s(o=>[t(j,{type:H(o.row.resolution)},{default:s(()=>[i(g(P(o.row.resolution)),1)]),_:2},1032,["type"])]),_:1}),t(p,{prop:"comment",label:"评价内容","show-overflow-tooltip":""}),t(p,{label:"评价时间",width:"180"},{default:s(o=>[i(g(M(o.row.createdAt)),1)]),_:1})]),_:1},8,["data"])),[[J,V.value]]),r("div",_e,[t(G,{"current-page":_.value,"onUpdate:currentPage":e[6]||(e[6]=o=>_.value=o),"page-size":f.value,"onUpdate:pageSize":e[7]||(e[7]=o=>f.value=o),"page-sizes":[5,10,20,50],layout:"total, sizes, prev, pager, next, jumper",total:U.value,onSizeChange:q,onCurrentChange:R},null,8,["current-page","page-size","total"])])])])}}},he=Q(fe,[["__scopeId","data-v-96e1c311"]]);export{he as default};
