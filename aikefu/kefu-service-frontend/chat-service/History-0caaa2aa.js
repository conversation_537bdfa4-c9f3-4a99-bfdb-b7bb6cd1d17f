import{_ as Me,b as Ce,r as c,x as Ae,o as Ue,n as Le,E as S,az as Se,aL as ze,aM as Fe,aN as Ne,c as p,F as He,d as n,e as i,f as u,g as a,w as t,G as z,J as E,A as k,t as o,q as b,j as r,H as ue,h as Oe,U as Be,O as Ee,V as Re}from"./index.js";import{h as _}from"./moment-a9aaa855.js";const $e={class:"history-container"},je={class:"history-list"},We={class:"select-option-label"},qe={key:0,class:"selected-filter-tag"},Ge={key:0,class:"selected-filter-tag"},Je={key:0,class:"selected-filter-tag"},Ke={key:0,class:"selected-filter-tag"},Pe={class:"select-option-label"},Qe={key:0,class:"selected-filter-tag"},Xe={class:"select-option-label"},Ze={key:0,class:"selected-filter-tag"},el={class:"truncate-text"},ll={key:1},tl={key:1},al={class:"pagination-container"},sl={class:"session-info"},ol={key:0,class:"satisfaction-info"},nl={key:0,class:"feedback-content"},ul={key:1},rl={class:"message-filter"},dl={class:"message-container"},il={class:"sender"},cl={key:0},pl={key:1},vl={key:2},_l={class:"time"},fl={key:0,class:"content"},ml={key:1,class:"content image-content"},bl={__name:"History",setup(yl){const R=Ce(),y=c(1),A=c(10),$=c(0),j=c(!1),F=c([]),W=c(!1),d=c(null),N=c([]),q=c(!1),K=c("calc(100vh - 320px)"),V=c([_().subtract(14,"days").format("YYYY-MM-DD"),_().format("YYYY-MM-DD")]),re=c([{text:"最近15天",value:[_().subtract(14,"days"),_()]},{text:"最近一周",value:[_().subtract(7,"days"),_()]},{text:"最近一个月",value:[_().subtract(1,"month"),_()]},{text:"最近三个月",value:[_().subtract(3,"months"),_()]}]),h=c(null),x=c(null),g=c(null),I=c(null),M=c(""),P=Ae(()=>N.value.length?N.value.filter(s=>!(I.value!==null&&s.senderType!==I.value||M.value&&!s.content.toLowerCase().includes(M.value.toLowerCase()))):[]),Y=c(null),Q=c([]),T=c(null),X=c([]),D=c(null),Z=c([]);Ue(()=>{U(),fe(),window.addEventListener("resize",ee),ee()});const ee=()=>{Le(()=>{K.value="calc(100vh - 320px)"})},U=async()=>{j.value=!0;try{if(!R.isLoggedIn||!R.userId){S.warning("请先登录");return}const s={agentId:R.userId};V.value&&V.value.length===2&&(s.startDate=V.value[0],s.endDate=V.value[1]),h.value!==null&&(s.agentType=h.value),x.value!==null&&(s.status=x.value),g.value!==null&&(s.satisfactionLevel=g.value),Y.value!==null&&(s.datasource=Y.value),T.value!==null&&(s.collectionName=T.value),D.value!==null&&(s.scene=D.value),s.page=y.value,s.size=A.value;const e=await Se(s);e.code===200?e.data&&e.data.records?(F.value=e.data.records||[],$.value=e.data.total||0,y.value=e.data.pageNum||1):(F.value=e.data||[],$.value=e.data.length>0?e.data[0].total||F.value.length:0):S.error(e.message||"获取会话列表失败")}catch(s){console.error("加载历史会话失败",s),S.error("加载历史会话失败")}finally{j.value=!1}},de=async s=>{d.value=s,W.value=!0,I.value=null,M.value="",await ie(s.id)},ie=async s=>{if(s){q.value=!0,N.value=[];try{const e=await Re(s);e.code===200?N.value=e.data||[]:S.error(e.message||"获取会话消息失败")}catch(e){console.error("加载会话消息失败",e),S.error("加载会话消息失败")}finally{q.value=!1}}},ce=s=>{A.value=s,U()},pe=s=>{y.value=s,U()},ve=()=>{y.value=1,U()},_e=()=>{V.value=[_().subtract(14,"days").format("YYYY-MM-DD"),_().format("YYYY-MM-DD")],h.value=null,x.value=null,g.value=null,Y.value=null,T.value=null,D.value=null,y.value=1,U()},le=()=>{console.log("Filtering messages:",I.value,M.value)},te=s=>{if(s==null)return"info";switch(parseInt(s)){case 0:return"warning";case 1:return"success";case 2:return"info";case 3:return"primary";default:return"info"}},G=s=>{if(s==null)return"未知状态";switch(parseInt(s)){case 0:return"排队中";case 1:return"人工会话中";case 2:return"已关闭";case 3:return"AI会话中";default:return"未知状态"}},L=s=>{if(!s)return"-";try{return _(s).format("YYYY-MM-DD HH:mm:ss")}catch{return"-"}},ae=s=>{if(!s)return"";if(s.startsWith("http://")||s.startsWith("https://"))return s},fe=async()=>{try{const s=await ze();s.code===200&&(Q.value=s.data||[]);const e=await Fe();e.code===200&&(X.value=e.data||[]);const H=await Ne();H.code===200&&(Z.value=H.data||[])}catch(s){console.error("加载过滤选项失败",s)}};return(s,e)=>{const H=p("el-date-picker"),w=p("el-form-item"),v=p("el-option"),C=p("el-select"),J=p("el-button"),me=p("el-form"),f=p("el-table-column"),O=p("el-tag"),be=p("el-tooltip"),se=p("el-rate"),ye=p("el-table"),ge=p("el-pagination"),we=p("el-card"),m=p("el-descriptions-item"),ke=p("el-descriptions"),B=p("el-radio-button"),Ve=p("el-radio-group"),he=p("el-icon"),xe=p("el-input"),Ye=p("el-divider"),Te=p("el-empty"),De=p("el-image"),Ie=p("el-dialog"),oe=He("loading");return n(),i("div",$e,[u("div",je,[a(we,{shadow:"hover"},{header:t(()=>e[13]||(e[13]=[u("div",{class:"card-header"},[u("span",null,"历史会话列表")],-1)])),default:t(()=>[a(me,{inline:!0,class:"search-form"},{default:t(()=>[a(w,{label:"日期范围"},{default:t(()=>[a(H,{modelValue:V.value,"onUpdate:modelValue":e[0]||(e[0]=l=>V.value=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",shortcuts:re.value,style:{width:"350px"}},null,8,["modelValue","shortcuts"])]),_:1}),a(w,{label:"对话场景"},{default:t(()=>[a(C,{modelValue:D.value,"onUpdate:modelValue":e[1]||(e[1]=l=>D.value=l),placeholder:"全部","popper-class":"custom-select-dropdown",clearable:"",style:{width:"200px"}},{default:t(()=>[a(v,{label:"全部",value:null},{default:t(()=>e[14]||(e[14]=[u("span",{class:"select-option-label"},"全部",-1)])),_:1,__:[14]}),(n(!0),i(z,null,E(Z.value,l=>(n(),k(v,{key:l.name,label:l.name,value:l.name},{default:t(()=>[u("span",We,o(l.name)+" ("+o(l.count)+")",1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"]),D.value!==null?(n(),i("div",qe,o(D.value),1)):b("",!0)]),_:1}),a(w,{label:"客服类型"},{default:t(()=>[a(C,{modelValue:h.value,"onUpdate:modelValue":e[2]||(e[2]=l=>h.value=l),placeholder:"全部","popper-class":"custom-select-dropdown",clearable:"",style:{width:"200px"}},{default:t(()=>[a(v,{label:"全部",value:null},{default:t(()=>e[15]||(e[15]=[u("span",{class:"select-option-label"},"全部",-1)])),_:1,__:[15]}),a(v,{label:"人工客服",value:1},{default:t(()=>e[16]||(e[16]=[u("span",{class:"select-option-label"},"人工客服",-1)])),_:1,__:[16]}),a(v,{label:"AI客服",value:2},{default:t(()=>e[17]||(e[17]=[u("span",{class:"select-option-label"},"AI客服",-1)])),_:1,__:[17]})]),_:1},8,["modelValue"]),h.value!==null?(n(),i("div",Ge,o(h.value===1?"人工客服":"AI客服"),1)):b("",!0)]),_:1}),a(w,{label:"会话状态"},{default:t(()=>[a(C,{modelValue:x.value,"onUpdate:modelValue":e[3]||(e[3]=l=>x.value=l),placeholder:"全部","popper-class":"custom-select-dropdown",clearable:"",style:{width:"200px"}},{default:t(()=>[a(v,{label:"全部",value:null},{default:t(()=>e[18]||(e[18]=[u("span",{class:"select-option-label"},"全部",-1)])),_:1,__:[18]}),a(v,{label:"排队中",value:0},{default:t(()=>e[19]||(e[19]=[u("span",{class:"select-option-label"},"排队中",-1)])),_:1,__:[19]}),a(v,{label:"人工会话中",value:1},{default:t(()=>e[20]||(e[20]=[u("span",{class:"select-option-label"},"人工会话中",-1)])),_:1,__:[20]}),a(v,{label:"已关闭",value:2},{default:t(()=>e[21]||(e[21]=[u("span",{class:"select-option-label"},"已关闭",-1)])),_:1,__:[21]}),a(v,{label:"AI会话中",value:3},{default:t(()=>e[22]||(e[22]=[u("span",{class:"select-option-label"},"AI会话中",-1)])),_:1,__:[22]})]),_:1},8,["modelValue"]),x.value!==null?(n(),i("div",Je,o(G(x.value)),1)):b("",!0)]),_:1}),a(w,{label:"评价等级"},{default:t(()=>[a(C,{modelValue:g.value,"onUpdate:modelValue":e[4]||(e[4]=l=>g.value=l),placeholder:"全部","popper-class":"custom-select-dropdown",clearable:"",style:{width:"200px"}},{default:t(()=>[a(v,{label:"全部",value:null},{default:t(()=>e[23]||(e[23]=[u("span",{class:"select-option-label"},"全部",-1)])),_:1,__:[23]}),a(v,{label:"5星",value:5},{default:t(()=>e[24]||(e[24]=[u("span",{class:"select-option-label"},"5星",-1)])),_:1,__:[24]}),a(v,{label:"4星",value:4},{default:t(()=>e[25]||(e[25]=[u("span",{class:"select-option-label"},"4星",-1)])),_:1,__:[25]}),a(v,{label:"3星",value:3},{default:t(()=>e[26]||(e[26]=[u("span",{class:"select-option-label"},"3星",-1)])),_:1,__:[26]}),a(v,{label:"2星",value:2},{default:t(()=>e[27]||(e[27]=[u("span",{class:"select-option-label"},"2星",-1)])),_:1,__:[27]}),a(v,{label:"1星",value:1},{default:t(()=>e[28]||(e[28]=[u("span",{class:"select-option-label"},"1星",-1)])),_:1,__:[28]}),a(v,{label:"未评价",value:0},{default:t(()=>e[29]||(e[29]=[u("span",{class:"select-option-label"},"未评价",-1)])),_:1,__:[29]})]),_:1},8,["modelValue"]),g.value!==null?(n(),i("div",Ke,o(g.value===0?"未评价":`${g.value}星`),1)):b("",!0)]),_:1}),a(w,{label:"知识库集合"},{default:t(()=>[a(C,{modelValue:T.value,"onUpdate:modelValue":e[5]||(e[5]=l=>T.value=l),placeholder:"全部","popper-class":"custom-select-dropdown",clearable:"",style:{width:"200px"}},{default:t(()=>[a(v,{label:"全部",value:null},{default:t(()=>e[30]||(e[30]=[u("span",{class:"select-option-label"},"全部",-1)])),_:1,__:[30]}),(n(!0),i(z,null,E(X.value,l=>(n(),k(v,{key:l.name,label:l.name,value:l.name},{default:t(()=>[u("span",Pe,o(l.name)+" ("+o(l.count)+")",1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"]),T.value!==null?(n(),i("div",Qe,o(T.value),1)):b("",!0)]),_:1}),a(w,{label:"数据源"},{default:t(()=>[a(C,{modelValue:Y.value,"onUpdate:modelValue":e[6]||(e[6]=l=>Y.value=l),placeholder:"全部","popper-class":"custom-select-dropdown",clearable:"",style:{width:"200px"}},{default:t(()=>[a(v,{label:"全部",value:null},{default:t(()=>e[31]||(e[31]=[u("span",{class:"select-option-label"},"全部",-1)])),_:1,__:[31]}),(n(!0),i(z,null,E(Q.value,l=>(n(),k(v,{key:l.name,label:l.name,value:l.name},{default:t(()=>[u("span",Xe,o(l.name)+" ("+o(l.count)+")",1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"]),Y.value!==null?(n(),i("div",Ze,o(Y.value),1)):b("",!0)]),_:1}),a(w,null,{default:t(()=>[a(J,{type:"primary",onClick:ve},{default:t(()=>e[32]||(e[32]=[r("查询")])),_:1,__:[32]}),a(J,{onClick:_e},{default:t(()=>e[33]||(e[33]=[r("重置")])),_:1,__:[33]})]),_:1})]),_:1}),ue((n(),k(ye,{data:F.value,style:{width:"100%"},border:"",height:"calc(100vh - 320px)","max-height":K.value},{default:t(()=>[a(f,{label:"ID",width:"80",fixed:""},{default:t(l=>[r(o((y.value-1)*A.value+l.$index+1),1)]),_:1}),a(f,{label:"数据源",width:"150"},{default:t(l=>[r(o(l.row.datasource||"未知"),1)]),_:1}),a(f,{label:"渠道来源",width:"150"},{default:t(l=>[r(o(l.row.channel||"未知"),1)]),_:1}),a(f,{label:"用户",width:"150",fixed:"","show-overflow-tooltip":""},{default:t(l=>[r(o(l.row.user&&l.row.user.nickname?l.row.user.nickname:"访客"+l.row.userId),1)]),_:1}),a(f,{label:"对话场景",width:"150"},{default:t(l=>[r(o(l.row.scene||"未知"),1)]),_:1}),a(f,{label:"客服类型",width:"120"},{default:t(l=>[a(O,{type:l.row.currentAgentType===1?"primary":"success"},{default:t(()=>[r(o(l.row.currentAgentType===1?"人工客服":"AI客服"),1)]),_:2},1032,["type"])]),_:1}),a(f,{label:"会话状态",width:"120"},{default:t(l=>[a(O,{type:te(l.row.status)},{default:t(()=>[r(o(G(l.row.status)),1)]),_:2},1032,["type"])]),_:1}),a(f,{label:"知识库集合",width:"180"},{default:t(l=>[l.row.collectionName?(n(),k(be,{key:0,content:l.row.collectionName,placement:"top","show-after":500},{default:t(()=>[u("div",el,o(l.row.collectionName||"-"),1)]),_:2},1032,["content"])):(n(),i("span",ll,"-"))]),_:1}),a(f,{label:"最后活跃时间",width:"160"},{default:t(l=>[r(o(L(l.row.lastActiveTime)||"-"),1)]),_:1}),a(f,{label:"创建时间",width:"160"},{default:t(l=>[r(o(L(l.row.startTime)),1)]),_:1}),a(f,{label:"评价",width:"120"},{default:t(l=>[l.row.satisfactionLevel?(n(),k(se,{key:0,modelValue:l.row.satisfactionLevel,"onUpdate:modelValue":ne=>l.row.satisfactionLevel=ne,disabled:"","text-color":"#ff9900","score-template":"{value}"},null,8,["modelValue","onUpdate:modelValue"])):(n(),i("span",tl,"未评价"))]),_:1}),a(f,{label:"操作",width:"80",fixed:"right"},{default:t(l=>[a(J,{type:"primary",link:"",onClick:ne=>de(l.row)},{default:t(()=>e[34]||(e[34]=[r(" 详情 ")])),_:2,__:[34]},1032,["onClick"])]),_:1})]),_:1},8,["data","max-height"])),[[oe,j.value]]),u("div",al,[a(ge,{"current-page":y.value,"onUpdate:currentPage":e[7]||(e[7]=l=>y.value=l),"page-size":A.value,"onUpdate:pageSize":e[8]||(e[8]=l=>A.value=l),"page-sizes":[10,20,50],layout:"total, sizes, prev, pager, next",total:$.value,onSizeChange:ce,onCurrentChange:pe},null,8,["current-page","page-size","total"])])]),_:1})]),a(Ie,{modelValue:W.value,"onUpdate:modelValue":e[12]||(e[12]=l=>W.value=l),title:"会话详情",width:"80%","destroy-on-close":"",top:"5vh"},{default:t(()=>[d.value?(n(),i(z,{key:0},[u("div",sl,[a(ke,{column:2,border:""},{default:t(()=>[a(m,{label:"会话ID"},{default:t(()=>[r(o(d.value.id),1)]),_:1}),a(m,{label:"数据源"},{default:t(()=>[r(o(d.value.datasource||"未知"),1)]),_:1}),a(m,{label:"用户"},{default:t(()=>[r(o(d.value.user&&d.value.user.nickname?d.value.user.nickname:"访客"+d.value.userId),1)]),_:1}),a(m,{label:"对话场景"},{default:t(()=>[r(o(d.value.scene||"未知"),1)]),_:1}),a(m,{label:"客服类型"},{default:t(()=>[a(O,{type:d.value.currentAgentType===1?"primary":"success"},{default:t(()=>[r(o(d.value.currentAgentType===1?"人工客服":"AI客服"),1)]),_:1},8,["type"])]),_:1}),a(m,{label:"会话状态"},{default:t(()=>[a(O,{type:te(d.value.status)},{default:t(()=>[r(o(G(d.value.status)),1)]),_:1},8,["type"])]),_:1}),a(m,{label:"解决方案"},{default:t(()=>[r(o(d.value.solutionDescription||"-"),1)]),_:1}),a(m,{label:"用户评价"},{default:t(()=>[d.value.satisfactionLevel?(n(),i("div",ol,[a(se,{modelValue:d.value.satisfactionLevel,"onUpdate:modelValue":e[9]||(e[9]=l=>d.value.satisfactionLevel=l),disabled:""},null,8,["modelValue"]),d.value.feedbackContent?(n(),i("span",nl,' "'+o(d.value.feedbackContent)+'" ',1)):b("",!0)])):(n(),i("span",ul,"未评价"))]),_:1}),a(m,{label:"评价反馈"},{default:t(()=>[r(o(d.value.feedbackContent||"-"),1)]),_:1}),a(m,{label:"知识库集合"},{default:t(()=>[r(o(d.value.collectionName||"-"),1)]),_:1}),a(m,{label:"最后活跃时间"},{default:t(()=>[r(o(L(d.value.lastActiveTime)||"-"),1)]),_:1}),a(m,{label:"创建时间"},{default:t(()=>[r(o(L(d.value.startTime)),1)]),_:1})]),_:1})]),u("div",rl,[a(Ve,{modelValue:I.value,"onUpdate:modelValue":e[10]||(e[10]=l=>I.value=l),size:"small",onChange:le},{default:t(()=>[a(B,{label:null},{default:t(()=>e[35]||(e[35]=[r("全部消息")])),_:1,__:[35]}),a(B,{label:0},{default:t(()=>e[36]||(e[36]=[r("用户消息")])),_:1,__:[36]}),a(B,{label:1},{default:t(()=>e[37]||(e[37]=[r("客服消息")])),_:1,__:[37]}),d.value.currentAgentType===2?(n(),k(B,{key:0,label:2},{default:t(()=>e[38]||(e[38]=[r("AI回复")])),_:1,__:[38]})):b("",!0)]),_:1},8,["modelValue"]),a(xe,{modelValue:M.value,"onUpdate:modelValue":e[11]||(e[11]=l=>M.value=l),placeholder:"搜索消息内容",clearable:"",onInput:le,style:{width:"200px","margin-left":"10px"}},{prefix:t(()=>[a(he,null,{default:t(()=>[a(Oe(Be))]),_:1})]),_:1},8,["modelValue"])]),a(Ye,null,{default:t(()=>e[39]||(e[39]=[r("聊天记录")])),_:1,__:[39]}),ue((n(),i("div",dl,[P.value.length?(n(!0),i(z,{key:1},E(P.value,l=>(n(),i("div",{key:l.id,class:Ee(["message-item",{"message-user":l.senderType===0,"message-agent":l.senderType===1,"message-ai":l.senderType===2}])},[u("p",null,[u("span",il,[l.senderType===0?(n(),i("span",cl,"用户")):l.senderType===1?(n(),i("span",pl,"客服")):l.senderType===2?(n(),i("span",vl,"AI客服")):b("",!0)]),u("span",_l,o(L(l.createdAt)),1)]),l.msgType===0?(n(),i("div",fl,o(l.content),1)):l.msgType===1?(n(),i("div",ml,[a(De,{src:ae(l.content),"preview-src-list":[ae(l.content)],fit:"cover"},null,8,["src","preview-src-list"])])):b("",!0)],2))),128)):(n(),k(Te,{key:0,description:"暂无聊天记录"}))])),[[oe,q.value]])],64)):b("",!0)]),_:1},8,["modelValue"])])}}},kl=Me(bl,[["__scopeId","data-v-3388a826"]]);export{kl as default};
