import{S as De,_ as He,u as Ge,k as be,r as m,x as we,o as Oe,D as Ke,n as K,aY as Je,E as D,c as b,F as Qe,d as p,e as y,f as a,g as s,w as n,K as Ze,j as u,H as ie,A as B,t as l,q as L,G as j,J,aZ as Xe,a3 as et,a_ as tt}from"./index.js";import{h as _}from"./moment-a9aaa855.js";import"./index-100eba30.js";import{i as at,L as Q}from"./installCanvasRenderer-44afd735.js";function st(q){return De({url:`/session/${q}`,method:"get"})}function ot(q){return De({url:`/message/session/${q}`,method:"get"})}const nt={class:"agent-container"},lt={class:"agent-list"},rt={class:"agent-info"},it={class:"agent-detail"},dt={class:"agent-name"},ut={class:"agent-id"},ct={class:"pagination-container"},pt={key:0,class:"agent-detail-dialog"},vt={class:"detail-header"},gt={class:"agent-profile"},mt={class:"agent-basic-info"},_t={class:"agent-name-row"},ft={class:"agent-name"},yt={class:"agent-info-row"},ht={class:"agent-id"},bt={class:"action-buttons"},wt={class:"date-filter-container"},Dt={class:"performance-dashboard"},Yt={class:"stat-cards-container"},Ct={class:"stat-card-modern"},Mt={class:"stat-card-inner"},kt={class:"stat-content"},St={class:"stat-value"},Tt={class:"stat-card-modern"},At={class:"stat-card-inner"},xt={class:"stat-content"},Vt={class:"stat-value"},Rt={class:"stat-card-modern"},Nt={class:"stat-card-inner"},zt={class:"stat-content"},It={class:"stat-value"},Ut={class:"stat-card-modern"},Lt={class:"stat-card-inner"},$t={class:"stat-content"},Et={class:"stat-value"},Ft={class:"trend-chart-container"},Bt={key:0,class:"no-chart-data"},jt={key:0,class:"session-id-container"},qt={key:0,class:"session-id-separator"},Pt={class:"recent-sessions-list"},Wt={class:"recent-sessions-title"},Ht={key:1},Gt={class:"status-display"},Ot={class:"avatar-container"},Kt={class:"dialog-footer"},Jt={key:0,class:"session-detail"},Qt={class:"session-info"},Zt={class:"chat-records"},Xt={class:"messages-container"},ea={class:"date-divider"},ta={key:0,class:"message-item system-message"},aa={class:"message-content"},sa={class:"system-tip"},oa={class:"message-time"},na={key:1,class:"message-item user-message"},la={class:"message-avatar"},ra={class:"avatar-circle user-avatar"},ia={class:"message-content-wrapper"},da={class:"message-sender"},ua={class:"message-bubble"},ca={class:"message-content"},pa={class:"message-footer"},va={class:"message-time"},ga={key:2,class:"message-item agent-message"},ma={class:"message-content-wrapper"},_a={class:"message-sender"},fa={class:"message-bubble"},ya={class:"message-content"},ha={class:"message-footer"},ba={class:"message-time"},wa={class:"message-avatar"},Da={class:"avatar-circle agent-avatar"},Ya={key:3,class:"message-item ai-agent-message"},Ca={class:"message-content-wrapper"},Ma={class:"message-sender"},ka={class:"message-bubble"},Sa={class:"message-content"},Ta={class:"message-footer"},Aa={class:"message-time"},xa={key:1,class:"no-messages"},Va={key:1,class:"no-data"},Ra={__name:"ServiceAgentManagement",setup(q){Ge();const c=be({pageNum:1,pageSize:10,agentType:"",status:"",startDate:_().subtract(6,"days").format("YYYY-MM-DD"),endDate:_().format("YYYY-MM-DD"),keyword:""}),P=m([_().subtract(6,"days").format("YYYY-MM-DD"),_().format("YYYY-MM-DD")]),de=[{text:"今天",value:()=>{const t=_().format("YYYY-MM-DD");return[t,t]}},{text:"昨天",value:()=>{const t=_().subtract(1,"days").format("YYYY-MM-DD");return[t,t]}},{text:"最近一周",value:()=>[_().subtract(6,"days").format("YYYY-MM-DD"),_().format("YYYY-MM-DD")]},{text:"最近一个月",value:()=>[_().subtract(29,"days").format("YYYY-MM-DD"),_().format("YYYY-MM-DD")]},{text:"本月",value:()=>[_().startOf("month").format("YYYY-MM-DD"),_().endOf("month").format("YYYY-MM-DD")]}],W=m([]),ue=m(0),Z=m(!1),ce=m("calc(100vh - 300px)"),H=m(!1),R=m(!0),g=m(null);m([]),m(!1);const N=m([_().subtract(7,"days").format("YYYY-MM-DD"),_().format("YYYY-MM-DD")]),C=m([]),X=m(!1),Ye=m(1);m(10),m(0),m("week");const $=m(!1),ee=m(null),i=be({id:"",agentNo:"",name:"",password:"",agent_type:1,status:1,avatar:"https://file.juranguanjia.com/upfile/2025/04-16/5c72f327057b483fa97b916671048fef.png"}),Ce={agentNo:[{required:!0,message:"请输入账号",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],name:[{required:!0,message:"请输入姓名",trigger:"blur"},{max:30,message:"最大长度为 30 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur",validator:(t,e,d)=>{!i.id&&!e?d(new Error("请输入密码")):d()}}],agent_type:[{required:!0,message:"请选择客服类型",trigger:"change"}]},Me=we(()=>i.id?"编辑客服":"新增客服");let M=null;const te=m(null);Oe(()=>{z(),window.addEventListener("resize",ae),ae()}),Ke(()=>{window.removeEventListener("resize",ae),M&&(M.dispose(),M=null)});const ae=()=>{K(()=>{ce.value="calc(100vh - 300px)"})},z=async()=>{Z.value=!0;try{const t={pageNum:c.pageNum||1,pageSize:c.pageSize||10,agentType:c.agentType||"",status:c.status||"",startDate:c.startDate||_().format("YYYY-MM-DD"),endDate:c.endDate||_().format("YYYY-MM-DD"),keyword:c.keyword||"",orderBy:"status",orderType:"desc",orderBy2:"agentType",orderType2:"asc"},e=await Je(t);if(e.code===200){const d=e.data||{records:[],total:0};W.value=(d.records||d.list||[]).filter(r=>(r.agent_type||r.agentType)!==2).map(r=>({...r,agent_type:r.agent_type||r.agentType||1,status:typeof r.status=="number"?r.status:0,todaySessionCount:r.todaySessionCount||0,totalSessionCount:r.totalSessionCount||0,avgResponseTime:r.avgResponseTime||0,avgChatDuration:r.avgChatDuration||0,todayAvgResponseTime:r.todayAvgResponseTime||0,todayAvgChatDuration:r.todayAvgChatDuration||0,todayResponseCount:r.todayResponseCount||0,totalResponseCount:r.totalResponseCount||0})),ue.value=d.total||0,console.log("客服列表数据:",W.value),console.log("API返回的总数:",d.total)}else D.error(e.message||"获取客服列表失败")}catch(t){console.error("加载客服列表失败",t),D.error("加载客服列表失败")}finally{Z.value=!1}},ke=t=>{if(g.value=t,H.value=!0,R.value=!1,C.value=[],Ye.value=1,t.agent_type===2){D.info("AI客服不提供详情数据"),H.value=!1,R.value=!0;return}pe()},pe=()=>{if(R.value||!g.value||!N.value||N.value.length!==2)return;X.value=!0;const t=N.value[0],e=N.value[1];Xe(g.value.id,t,e).then(d=>{if(!R.value&&d&&d.code===200){C.value=d.data||[];let r=null;C.value.length>0&&(r=C.value.find(w=>w.date===e),r||(r=C.value[C.value.length-1]));let f=0,E=0,S=0,Y=0,V=0,v=0;C.value.forEach(w=>{f+=w.sessionCount||0,E+=w.responseCount||0,w.sessionCount&&w.sessionCount>0&&(w.avgResponseTime&&(S+=w.avgResponseTime*w.sessionCount,V+=w.sessionCount),w.avgChatDuration&&(Y+=w.avgChatDuration*w.sessionCount,v+=w.sessionCount))});const I=V>0?Math.round(S/V):0,U=v>0?Math.round(Y/v):0;g.value={...g.value,todaySessionCount:r&&r.sessionCount||0,todayResponseCount:r&&r.responseCount||0,todayAvgResponseTime:r&&r.avgResponseTime||0,todayAvgChatDuration:r&&r.avgChatDuration||0,totalSessionCount:f,totalResponseCount:E,avgResponseTime:I,avgChatDuration:U},K(()=>{Se()})}}).catch(d=>{R.value||(console.error("加载客服数据失败",d),D.error("加载统计数据失败"))}).finally(()=>{R.value||(X.value=!1)})},Se=()=>{if(!te.value||C.value.length===0)return;M&&M.dispose(),M=at(te.value);const t=C.value.map(f=>f.date),e=C.value.map(f=>f.sessionCount),d=C.value.map(f=>f.responseCount),r={tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(f){let S=`<div style="margin: 0px 0 0; line-height:1;">${f[0].axisValue}</div>`;return f.forEach(Y=>{const V=Y.color,v=Y.seriesName,I=Y.value;S+=`<div style="margin: 10px 0 0; line-height:1;">
                      <span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${V};">
                      </span>
                      <span>${v}：</span>
                      <span style="float:right;margin-left:10px;font-weight:bold;font-size:14px;">${I}</span>
                      <div style="clear:both"></div>
                    </div>`}),S}},legend:{data:["会话数","回答数"],right:"5%"},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:[{type:"category",data:t,axisTick:{alignWithLabel:!0},axisLabel:{rotate:45,interval:"auto"}}],yAxis:[{type:"value",name:"数量",minInterval:1,splitLine:{lineStyle:{type:"dashed"}}}],series:[{name:"会话数",type:"bar",barWidth:"35%",barGap:"0%",itemStyle:{color:new Q(0,0,0,1,[{offset:0,color:"#409EFF"},{offset:1,color:"#79bbff"}]),borderRadius:[3,3,0,0]},emphasis:{itemStyle:{color:new Q(0,0,0,1,[{offset:0,color:"#3a8ee6"},{offset:1,color:"#409EFF"}])}},data:e},{name:"回答数",type:"bar",barWidth:"35%",itemStyle:{color:new Q(0,0,0,1,[{offset:0,color:"#67C23A"},{offset:1,color:"#95d475"}]),borderRadius:[3,3,0,0]},emphasis:{itemStyle:{color:new Q(0,0,0,1,[{offset:0,color:"#5daf34"},{offset:1,color:"#67C23A"}])}},data:d}]};M.setOption(r),window.addEventListener("resize",()=>{M&&M.resize()})},Te=()=>{pe()},se=m(!1),oe=m(!1),T=m(null),A=m([]),Ae=we(()=>{if(!A.value||A.value.length===0)return{};const t={};return A.value.forEach(e=>{if(!e.createdAt)return;const d=e.createdAt.split(" ")[0];t[d]||(t[d]=[]),t[d].push(e)}),Object.keys(t).forEach(e=>{t[e].sort((d,r)=>new Date(d.createdAt)-new Date(r.createdAt))}),t}),xe=t=>{const e=_().format("YYYY-MM-DD"),d=_().subtract(1,"days").format("YYYY-MM-DD");return t===e?"今天":t===d?"昨天":_(t).format("YYYY年MM月DD日")},G=t=>t?_(t).format("HH:mm:ss"):"-",Ve=t=>t.senderName?t.senderName.charAt(0):"用",Re=t=>t.senderName?t.senderName.charAt(0):"客",ve=async t=>{se.value=!0,oe.value=!0;try{const e=await st(t);e.code===200?(T.value=e.data,await Ne(t)):D.error(e.message||"获取会话详情失败")}catch(e){console.error("获取会话详情失败",e),D.error("获取会话详情失败，请稍后再试")}finally{oe.value=!1}},Ne=async t=>{try{const e=await ot(t);e.code===200?A.value=e.data||[]:(A.value=[],D.warning(e.message||"未能获取聊天记录"))}catch(e){console.error("获取聊天记录失败",e),A.value=[],D.warning("获取聊天记录失败，请稍后再试")}},ge=t=>{if(t.agent_type===2){D.info("AI客服不可编辑");return}i.id=t.id,i.agentNo=t.agentNo,i.name=t.name,i.agent_type=t.agent_type,i.status=t.status,i.avatar=t.avatar||"https://file.juranguanjia.com/upfile/2025/04-16/5c72f327057b483fa97b916671048fef.png",$.value=!0},ze=async()=>{if(ee.value)try{if(await ee.value.validate(),i.id)try{const t=W.value.find(r=>r.id===i.id),e=t?t.status:i.status,d=await et({id:i.id,agentNo:i.agentNo,name:i.name,agentType:i.agent_type,status:e,avatar:i.avatar});d.code===200?(D.success("客服信息更新成功"),z(),$.value=!1):D.error(d.message||"客服信息更新失败")}catch(t){console.error("更新客服信息失败",t),D.error("更新客服信息失败")}else try{const t=await tt({agentNo:i.agentNo,name:i.name,password:i.password,agentType:i.agent_type,status:i.status,avatar:i.avatar});t.code===200?(D.success("客服添加成功"),z(),$.value=!1):D.error(t.message||"客服添加失败")}catch(t){console.error("添加客服失败",t),D.error("添加客服失败")}}catch(t){console.error("表单验证失败",t)}},me=t=>{console.log("下拉框变更为:",t),K(()=>{O()})},O=()=>{c.pageNum=1,z()},Ie=t=>{if(t)c.startDate=t[0],c.endDate=t[1];else{const e=_().format("YYYY-MM-DD");c.startDate=e,c.endDate=e,P.value=[e,e]}K(()=>{O()})},Ue=()=>{c.agentType="",c.status="";const t=_().format("YYYY-MM-DD");c.startDate=t,c.endDate=t,P.value=[t,t],c.keyword="",c.pageNum=1,z()},Le=t=>{c.pageSize=t,c.pageNum=1,z()},$e=t=>{c.pageNum=t,z()},_e=t=>{switch(parseInt(t)){case 1:return"";case 2:return"success";case 3:return"warning";default:return"info"}},fe=t=>{switch(parseInt(t)){case 1:return"人工客服";case 3:return"系统管理员";default:return"未知类型"}},x=t=>{if(!t&&t!==0)return"-";const e=Math.floor(t/60),d=t%60;return`${e}:${d<10?"0":""}${d}`},Ee=t=>{if(!t||!t.raw)return;const e=t.raw.type.startsWith("image/"),d=t.raw.size/1024/1024<2;if(!e){D.error("只能上传图片文件");return}if(!d){D.error("图片大小不能超过 2MB");return}const r=new FileReader;r.readAsDataURL(t.raw),r.onload=()=>{i.avatar=r.result}},Fe=()=>{R.value=!0,g.value=null,C.value=[],N.value=[_().subtract(7,"days").format("YYYY-MM-DD"),_().format("YYYY-MM-DD")],M&&(M.dispose(),M=null)};return(t,e)=>{const d=b("el-option"),r=b("el-select"),f=b("el-form-item"),E=b("el-date-picker"),S=b("el-input"),Y=b("el-button"),V=b("el-form"),v=b("el-table-column"),I=b("el-avatar"),U=b("el-tag"),w=b("el-table"),Be=b("el-pagination"),ne=b("el-card"),ye=b("el-link"),je=b("el-popover"),le=b("el-dialog"),he=b("el-radio"),qe=b("el-radio-group"),Pe=b("el-upload"),F=b("el-descriptions-item"),We=b("el-descriptions"),re=Qe("loading");return p(),y("div",nt,[a("div",lt,[s(ne,{shadow:"hover"},{header:n(()=>e[16]||(e[16]=[a("div",{class:"card-header"},[a("span",null,"客服列表")],-1)])),default:n(()=>[s(V,{inline:!0,class:"search-form"},{default:n(()=>[s(f,{label:"客服类型"},{default:n(()=>[s(r,{modelValue:c.agentType,"onUpdate:modelValue":e[0]||(e[0]=o=>c.agentType=o),placeholder:"全部类型",clearable:"",style:{width:"200px"},onChange:me},{default:n(()=>[s(d,{label:"全部",value:""}),s(d,{label:"人工客服",value:"1"}),s(d,{label:"系统管理员",value:"3"})]),_:1},8,["modelValue"])]),_:1}),s(f,{label:"在线状态"},{default:n(()=>[s(r,{modelValue:c.status,"onUpdate:modelValue":e[1]||(e[1]=o=>c.status=o),placeholder:"全部状态",clearable:"",style:{width:"200px"},onChange:me},{default:n(()=>[s(d,{label:"全部",value:""}),s(d,{label:"在线",value:"1"}),s(d,{label:"离线",value:"0"})]),_:1},8,["modelValue"])]),_:1}),s(f,{label:"日期范围"},{default:n(()=>[s(E,{modelValue:P.value,"onUpdate:modelValue":e[2]||(e[2]=o=>P.value=o),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",clearable:!0,onChange:Ie,style:{width:"350px"},shortcuts:de},null,8,["modelValue"])]),_:1}),s(f,{label:"搜索"},{default:n(()=>[s(S,{modelValue:c.keyword,"onUpdate:modelValue":e[3]||(e[3]=o=>c.keyword=o),placeholder:"客服姓名/ID",clearable:"",onKeyup:Ze(O,["enter"])},null,8,["modelValue"])]),_:1}),s(f,null,{default:n(()=>[s(Y,{type:"primary",onClick:O},{default:n(()=>e[17]||(e[17]=[u("查询")])),_:1,__:[17]}),s(Y,{onClick:Ue},{default:n(()=>e[18]||(e[18]=[u("重置")])),_:1,__:[18]})]),_:1})]),_:1}),ie((p(),B(w,{data:W.value,style:{width:"100%"},border:"",stripe:"","header-cell-style":{background:"#f5f7fa"},height:"calc(100vh - 300px)","max-height":ce.value},{default:n(()=>[s(v,{type:"selection",width:"50"}),s(v,{label:"客服信息","min-width":"180"},{default:n(o=>[a("div",rt,[s(I,{size:32,src:o.row.avatar},{default:n(()=>[u(l(o.row.name?o.row.name.substring(0,1):"客"),1)]),_:2},1032,["src"]),a("div",it,[a("div",dt,l(o.row.name||"客服"+o.row.id),1),a("div",ut,"ID: "+l(o.row.id),1)])])]),_:1}),s(v,{label:"在线状态",width:"100"},{default:n(o=>[s(U,{type:o.row.status===1?"success":"info"},{default:n(()=>[u(l(o.row.status===1?"在线":"离线"),1)]),_:2},1032,["type"])]),_:1}),s(v,{label:"客服类型",width:"120"},{default:n(o=>[s(U,{type:_e(o.row.agent_type)},{default:n(()=>[u(l(fe(o.row.agent_type)),1)]),_:2},1032,["type"])]),_:1}),s(v,{label:"当日接入数",width:"120",align:"center",prop:"todaySessionCount"}),s(v,{label:"当日回答数",width:"120",align:"center",prop:"todayResponseCount"}),s(v,{label:"当日响应时间",width:"120",align:"center"},{default:n(o=>[u(l(x(o.row.todayAvgResponseTime)),1)]),_:1}),s(v,{label:"当日对话时间",width:"120",align:"center"},{default:n(o=>[u(l(x(o.row.todayAvgChatDuration)),1)]),_:1}),s(v,{label:"累计接入数",width:"120",align:"center",prop:"totalSessionCount"}),s(v,{label:"累计答数",width:"120",align:"center",prop:"totalResponseCount"}),s(v,{label:"平均响应",width:"120",align:"center"},{default:n(o=>[u(l(x(o.row.avgResponseTime)),1)]),_:1}),s(v,{label:"平均对话",width:"120",align:"center"},{default:n(o=>[u(l(x(o.row.avgChatDuration)),1)]),_:1}),s(v,{label:"操作",width:"120",fixed:"right"},{default:n(o=>[s(Y,{type:"primary",link:"",onClick:k=>ke(o.row)},{default:n(()=>e[19]||(e[19]=[u(" 详情 ")])),_:2,__:[19]},1032,["onClick"]),o.row.agent_type!==2?(p(),B(Y,{key:0,type:"primary",link:"",onClick:k=>ge(o.row)},{default:n(()=>e[20]||(e[20]=[u(" 编辑 ")])),_:2,__:[20]},1032,["onClick"])):L("",!0)]),_:1})]),_:1},8,["data","max-height"])),[[re,Z.value]]),a("div",ct,[s(Be,{"current-page":c.pageNum,"onUpdate:currentPage":e[4]||(e[4]=o=>c.pageNum=o),"page-size":c.pageSize,"onUpdate:pageSize":e[5]||(e[5]=o=>c.pageSize=o),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:ue.value,onSizeChange:Le,onCurrentChange:$e,background:""},null,8,["current-page","page-size","total"])])]),_:1})]),s(le,{modelValue:H.value,"onUpdate:modelValue":e[8]||(e[8]=o=>H.value=o),title:"客服详情",width:"70%","destroy-on-close":"",onClosed:Fe,top:"5vh",class:"agent-detail-dialog-container"},{default:n(()=>[g.value?(p(),y("div",pt,[a("div",vt,[a("div",gt,[s(I,{size:90,src:g.value.avatar,class:"profile-avatar"},{default:n(()=>[u(l(g.value.name?g.value.name.substring(0,1):"客"),1)]),_:1},8,["src"]),a("div",mt,[a("div",_t,[a("h2",ft,l(g.value.name),1),s(U,{class:"status-tag",type:g.value.status===1?"success":"info",size:"small",effect:g.value.status===1?"light":"plain"},{default:n(()=>[u(l(g.value.status===1?"在线":"离线"),1)]),_:1},8,["type","effect"])]),a("div",yt,[a("div",ht,"ID: "+l(g.value.id),1),s(U,{type:_e(g.value.agent_type),size:"small",effect:"plain"},{default:n(()=>[u(l(fe(g.value.agent_type)),1)]),_:1},8,["type"])])])]),a("div",bt,[g.value.agent_type!==2&&g.value.agent_type!==3?(p(),B(Y,{key:0,type:"primary",plain:"",onClick:e[6]||(e[6]=o=>ge(g.value))},{default:n(()=>e[21]||(e[21]=[u(" 编辑资料 ")])),_:1,__:[21]})):L("",!0)])]),a("div",wt,[e[22]||(e[22]=a("div",{class:"date-filter-label"},"统计日期：",-1)),s(E,{modelValue:N.value,"onUpdate:modelValue":e[7]||(e[7]=o=>N.value=o),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",onChange:Te,class:"stats-date-picker",shortcuts:de},null,8,["modelValue"])]),a("div",Dt,[a("div",Yt,[a("div",Ct,[a("div",Mt,[e[24]||(e[24]=a("div",{class:"stat-icon-wrapper blue"},[a("i",{class:"el-icon-chat-line-round"})],-1)),a("div",kt,[a("div",St,l(g.value.totalSessionCount),1),e[23]||(e[23]=a("div",{class:"stat-label"},"累计会话",-1))])])]),a("div",Tt,[a("div",At,[e[26]||(e[26]=a("div",{class:"stat-icon-wrapper green"},[a("i",{class:"el-icon-chat-dot-square"})],-1)),a("div",xt,[a("div",Vt,l(g.value.totalResponseCount),1),e[25]||(e[25]=a("div",{class:"stat-label"},"累计回答",-1))])])]),a("div",Rt,[a("div",Nt,[e[28]||(e[28]=a("div",{class:"stat-icon-wrapper orange"},[a("i",{class:"el-icon-alarm-clock"})],-1)),a("div",zt,[a("div",It,l(x(g.value.avgResponseTime)),1),e[27]||(e[27]=a("div",{class:"stat-label"},"平均响应",-1))])])]),a("div",Ut,[a("div",Lt,[e[30]||(e[30]=a("div",{class:"stat-icon-wrapper purple"},[a("i",{class:"el-icon-stopwatch"})],-1)),a("div",$t,[a("div",Et,l(x(g.value.avgChatDuration)),1),e[29]||(e[29]=a("div",{class:"stat-label"},"平均对话",-1))])])])])]),s(ne,{shadow:"hover",class:"trend-chart-card"},{default:n(()=>[a("div",Ft,[C.value.length===0?(p(),y("div",Bt," 暂无历史数据 ")):(p(),y("div",{key:1,id:"trend-chart",ref_key:"trendChartRef",ref:te,class:"echarts-container"},null,512))])]),_:1}),s(ne,{shadow:"hover",class:"stats-card"},{default:n(()=>[ie((p(),B(w,{data:C.value,style:{width:"100%"},border:"","header-cell-style":{background:"#f5f7fa"},"empty-text":"暂无统计数据"},{default:n(()=>[s(v,{prop:"date",label:"日期",width:"120"}),s(v,{prop:"sessionCount",label:"接入会话数",width:"120",align:"center"}),s(v,{prop:"responseCount",label:"回答数",width:"120",align:"center"}),s(v,{prop:"avgResponseTime",label:"平均响应时间",width:"120",align:"center"},{default:n(o=>[u(l(x(o.row.avgResponseTime)),1)]),_:1}),s(v,{prop:"avgChatDuration",label:"平均对话时间",width:"120",align:"center"},{default:n(o=>[u(l(x(o.row.avgChatDuration)),1)]),_:1}),s(v,{label:"会话记录","min-width":"150",align:"center"},{default:n(o=>[o.row.recentSessionIds&&o.row.recentSessionIds.length>0?(p(),y("div",jt,[(p(!0),y(j,null,J(o.row.recentSessionIds.slice(0,8),(k,h)=>(p(),y(j,{key:k},[s(ye,{type:"primary",onClick:Na=>ve(k),class:"session-id-link"},{default:n(()=>[u(l(k),1)]),_:2},1032,["onClick"]),h<Math.min(o.row.recentSessionIds.slice(0,8).length-1,7)?(p(),y("span",qt,",")):L("",!0)],64))),128)),o.row.recentSessionIds.length>8?(p(),B(je,{key:0,placement:"right",trigger:"click",width:250},{reference:n(()=>[s(Y,{type:"text",size:"small",class:"more-sessions-btn"},{default:n(()=>[u(" 查看更多 ("+l(o.row.recentSessionIds.length-8)+") ",1)]),_:2},1024)]),default:n(()=>[a("div",Pt,[a("p",Wt,l(o.row.date)+" 会话列表",1),(p(!0),y(j,null,J(o.row.recentSessionIds.slice(8),k=>(p(),y("div",{class:"session-item",key:k},[s(ye,{type:"primary",onClick:h=>ve(k)},{default:n(()=>[u(" 会话ID: "+l(k),1)]),_:2},1032,["onClick"])]))),128))])]),_:2},1024)):L("",!0)])):(p(),y("span",Ht,"无会话记录"))]),_:1})]),_:1},8,["data"])),[[re,X.value]])]),_:1})])):L("",!0)]),_:1},8,["modelValue"]),s(le,{modelValue:$.value,"onUpdate:modelValue":e[14]||(e[14]=o=>$.value=o),title:Me.value,width:"50%","destroy-on-close":""},{footer:n(()=>[a("span",Kt,[s(Y,{onClick:e[13]||(e[13]=o=>$.value=!1)},{default:n(()=>e[35]||(e[35]=[u("取消")])),_:1,__:[35]}),s(Y,{type:"primary",onClick:ze},{default:n(()=>e[36]||(e[36]=[u("确定")])),_:1,__:[36]})])]),default:n(()=>[s(V,{ref_key:"agentFormRef",ref:ee,model:i,rules:Ce,"label-width":"100px"},{default:n(()=>[s(f,{label:"账号/登录名",prop:"agentNo"},{default:n(()=>[s(S,{modelValue:i.agentNo,"onUpdate:modelValue":e[9]||(e[9]=o=>i.agentNo=o),placeholder:"请输入账号/登录名",disabled:!!i.id},null,8,["modelValue","disabled"])]),_:1}),s(f,{label:"姓名",prop:"name"},{default:n(()=>[s(S,{modelValue:i.name,"onUpdate:modelValue":e[10]||(e[10]=o=>i.name=o),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1}),i.id?L("",!0):(p(),B(f,{key:0,label:"密码",prop:"password"},{default:n(()=>[s(S,{modelValue:i.password,"onUpdate:modelValue":e[11]||(e[11]=o=>i.password=o),placeholder:"请输入密码",type:"password","show-password":""},null,8,["modelValue"])]),_:1})),s(f,{label:"客服类型",prop:"agent_type"},{default:n(()=>[s(qe,{modelValue:i.agent_type,"onUpdate:modelValue":e[12]||(e[12]=o=>i.agent_type=o)},{default:n(()=>[s(he,{label:1},{default:n(()=>e[31]||(e[31]=[u("人工客服")])),_:1,__:[31]}),s(he,{label:3},{default:n(()=>e[32]||(e[32]=[u("系统管理员")])),_:1,__:[32]})]),_:1},8,["modelValue"])]),_:1}),s(f,{label:"在线状态",prop:"status"},{default:n(()=>[a("div",Gt,[s(U,{type:i.status===1?"success":"info",disabled:""},{default:n(()=>[u(l(i.status===1?"在线":"离线"),1)]),_:1},8,["type"]),e[33]||(e[33]=a("span",{class:"status-note"},null,-1))])]),_:1}),s(f,{label:"头像",prop:"avatar"},{default:n(()=>[a("div",Ot,[s(I,{size:80,src:i.avatar},{default:n(()=>[u(l(i.name?i.name.substring(0,1):"客"),1)]),_:1},8,["src"]),s(Pe,{class:"avatar-upload",action:"","auto-upload":!1,"show-file-list":!1,"on-change":Ee,accept:"image/*"},{default:n(()=>[s(Y,{size:"small",type:"primary"},{default:n(()=>e[34]||(e[34]=[u("更换头像")])),_:1,__:[34]})]),_:1})])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),s(le,{modelValue:se.value,"onUpdate:modelValue":e[15]||(e[15]=o=>se.value=o),title:"会话详情",width:"80%","destroy-on-close":""},{default:n(()=>[ie((p(),y("div",null,[T.value?(p(),y("div",Jt,[a("div",Qt,[s(We,{column:3,border:""},{default:n(()=>[s(F,{label:"用户昵称"},{default:n(()=>[u(l(T.value.user.nickname||"-"),1)]),_:1}),s(F,{label:"用户手机"},{default:n(()=>[u(l(T.value.user.phone||"-"),1)]),_:1}),s(F,{label:"最后活动时间"},{default:n(()=>[u(l(T.value.lastActiveTime||"-"),1)]),_:1}),s(F,{label:"数据源"},{default:n(()=>[u(l(T.value.datasource||"-"),1)]),_:1}),s(F,{label:"渠道"},{default:n(()=>[u(l(T.value.channel||"-"),1)]),_:1}),s(F,{label:"应用场景"},{default:n(()=>[u(l(T.value.scene||"-"),1)]),_:1})]),_:1})]),a("div",Zt,[e[39]||(e[39]=a("h3",null,"聊天记录",-1)),a("div",Xt,[A.value&&A.value.length>0?(p(!0),y(j,{key:0},J(Ae.value,(o,k)=>(p(),y("div",{class:"time-divider",key:k},[a("div",ea,[a("span",null,l(xe(k)),1)]),(p(!0),y(j,null,J(o,h=>(p(),y("div",{key:h.id,class:"message-wrapper"},[h.senderType===3?(p(),y("div",ta,[a("div",aa,[a("div",sa,l(h.content),1),a("div",oa,l(G(h.createdAt)),1)])])):h.senderType===1?(p(),y("div",na,[a("div",la,[a("div",ra,l(Ve(h)),1)]),a("div",ia,[a("div",da,l(h.senderName||"用户"),1),a("div",ua,[a("div",ca,l(h.content),1)]),a("div",pa,[a("span",va,l(G(h.createdAt)),1)])])])):h.senderType===0?(p(),y("div",ga,[a("div",ma,[a("div",_a,l(h.senderName||"客服"),1),a("div",fa,[a("div",ya,l(h.content),1)]),a("div",ha,[a("span",ba,l(G(h.createdAt)),1)])]),a("div",wa,[a("div",Da,l(Re(h)),1)])])):h.senderType===2?(p(),y("div",Ya,[a("div",Ca,[a("div",Ma,[e[37]||(e[37]=a("span",{class:"ai-label"},"AI",-1)),u(" "+l(h.senderName||"熊小智"),1)]),a("div",ka,[a("div",Sa,l(h.content),1)]),a("div",Ta,[a("span",Aa,l(G(h.createdAt)),1)])]),e[38]||(e[38]=a("div",{class:"message-avatar"},[a("div",{class:"avatar-circle ai-avatar"},"智")],-1))])):L("",!0)]))),128))]))),128)):(p(),y("div",xa,"暂无聊天记录"))])])])):(p(),y("div",Va,"暂无会话数据"))])),[[re,oe.value]])]),_:1},8,["modelValue"])])}}},$a=He(Ra,[["__scopeId","data-v-bd69f946"]]);export{$a as default};
