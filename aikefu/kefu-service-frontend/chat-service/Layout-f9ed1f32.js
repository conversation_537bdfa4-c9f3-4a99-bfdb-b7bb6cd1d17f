import{_ as ue,u as ie,a as re,b as fe,v as ce,x as k,r as P,k as me,o as pe,D as _e,c as i,d as r,e as m,f,g as t,w as a,q as _,O as E,y as ve,j as u,h as w,X as ge,Y as we,Z as be,$ as ye,a0 as ke,a1 as xe,G as D,i as M,a2 as Ve,t as h,A as U,z as Pe,J as he,a3 as Ce,E as L}from"./index.js";const Se={class:"agent-layout"},je={class:"sidebar-header"},Te={key:0,class:"logo-inner"},Ie={key:1,class:"logo-mini"},Ne={class:"sidebar-content"},ze={key:0,class:"menu-divider"},Ee={key:1,class:"menu-section-header"},Ue={key:2,class:"menu-divider"},Le={key:3,class:"menu-section-header"},qe={key:0,class:"menu-divider"},Be={key:1,class:"menu-section-header"},Fe={class:"sidebar-footer"},Re={key:0,class:"connection-status"},Ae={class:"agent-info"},De={key:0,class:"agent-detail"},Me={class:"agent-name"},$e={class:"avatar-selector"},Oe={class:"avatar-options"},We=["onClick"],Ge={class:"dialog-footer"},Je={__name:"Layout",setup(Xe){const C=ie(),$=re(),g=fe(),y=ce(),c=k(()=>g.userInfo),O=k(()=>{var s;return((s=c.value)==null?void 0:s.agentType)===3}),d=P(!1);k(()=>{var s;return((s=c.value)==null?void 0:s.status)===1});const W=k(()=>$.path);k(()=>y.connected?"status-connected":y.isPolling?"status-polling":"status-disconnected"),k(()=>y.connected?"已连接":y.isPolling?"轮询中":"未连接");const x=P(!1),T=P(!1),q=P(null),v=P(!1),l=me({id:"",name:"",agentNo:"",avatar:"",agentType:null,newPassword:"",confirmPassword:""}),G=["https://file.juranguanjia.com/upfile/2025/04-18/8288c7361d1a40a280122bdd93519f59.png","https://file.juranguanjia.com/upfile/2025/04-18/d70ffb09ffe24e1591ae6bdcb3f3f25e.png","https://file.juranguanjia.com/upfile/2025/04-18/7c4d83fcb68444e9842c1205df145418.png","https://file.juranguanjia.com/upfile/2025/04-18/197095ad9fa34129bf94ec65ca223ec8.png","https://file.juranguanjia.com/upfile/2025/04-18/5368534b64a74b83b68acb8194c87cd7.png","https://file.juranguanjia.com/upfile/2025/04-18/ce8a318cbaeb4b06b8f47f0ac8a34906.png","https://file.juranguanjia.com/upfile/2025/04-18/1dc20d13ae0c465ab68e32154941fad9.png"],J={name:[{required:!0,message:"请输入姓名",trigger:"blur"},{min:2,max:20,message:"姓名长度在2到20个字符之间",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur",validator:(s,e,n)=>{v.value&&!e?n(new Error("请输入新密码")):n()}},{min:6,max:20,message:"密码长度在6到20个字符之间",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认新密码",trigger:"blur",validator:(s,e,n)=>{v.value&&!e?n(new Error("请确认新密码")):v.value&&e!==l.newPassword?n(new Error("两次输入的密码不一致")):n()}}]},X=()=>{v.value=!1,l.newPassword="",l.confirmPassword="",c.value&&(l.id=c.value.id,l.name=c.value.name||"",l.agentNo=c.value.agentNo||"",l.avatar=c.value.avatar||"",l.agentType=c.value.agentType||1),x.value=!0},Y=()=>{var s;(s=q.value)==null||s.validate(async e=>{if(e)try{T.value=!0;const n={id:l.id,name:l.name,avatar:l.avatar,agentType:l.agentType};v.value&&l.newPassword&&(n.newPassword=l.newPassword);const p=await Ce(n);p.code===200?(L.success("个人信息修改成功"),x.value=!1,g.setUserInfo(p.data)):L.error(p.msg||"个人信息修改失败")}catch(n){console.error("更新个人信息失败",n),L.error("网络错误，请稍后重试")}finally{T.value=!1}})};pe(()=>{g.loadFromStorage();const s=localStorage.getItem("sidebar_collapsed");if(s!==null&&(d.value=s==="1"),!g.isLoggedIn){C.push("/login");return}if(!g.isAgent){C.push("/login");return}y.initWebSocket();const e=()=>{window.innerWidth<768&&(d.value=!0,localStorage.setItem("sidebar_collapsed","1"))};e(),window.addEventListener("resize",e),_e(()=>{window.removeEventListener("resize",e)})});const Z=()=>{d.value=!d.value,localStorage.setItem("sidebar_collapsed",d.value?"1":"0")},H=s=>{C.push(s)},K=s=>{s==="logout"?Q():s==="profile"&&X()},Q=()=>{ve.confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{var s;(s=g.userInfo)!=null&&s.id&&g.updateAgentStatus(g.userInfo.id,0),y.clearState(),g.logout(),C.push("/login")}).catch(()=>{})};return(s,e)=>{const n=i("el-icon"),p=i("el-menu-item"),ee=i("el-menu"),I=i("el-avatar"),te=i("el-tag"),B=i("el-dropdown-item"),ae=i("el-dropdown-menu"),le=i("el-dropdown"),oe=i("router-view"),b=i("el-form-item"),S=i("el-input"),F=i("el-option"),se=i("el-select"),N=i("el-button"),ne=i("el-form"),de=i("el-dialog");return r(),m("div",Se,[f("div",{class:E(["sidebar",{collapsed:d.value}])},[f("div",je,[f("div",{class:"logo-container",onClick:Z},[d.value?(r(),m("div",Ie,e[9]||(e[9]=[f("span",null,"熊",-1)]))):(r(),m("div",Te,e[8]||(e[8]=[f("h1",{class:"logo-text"},[f("span",null,"熊小智客服")],-1)])))])]),f("div",Ne,[t(ee,{"default-active":W.value,class:"sidebar-menu",collapse:d.value,"collapse-transition":!1,onSelect:H},{default:a(()=>[t(p,{index:"/agent/dashboard"},{title:a(()=>e[10]||(e[10]=[u("工作台")])),default:a(()=>[t(n,null,{default:a(()=>[t(w(ge))]),_:1})]),_:1}),t(p,{index:"/agent/chat"},{title:a(()=>e[11]||(e[11]=[u("会话服务")])),default:a(()=>[t(n,null,{default:a(()=>[t(w(we))]),_:1})]),_:1}),t(p,{index:"/agent/history"},{title:a(()=>e[12]||(e[12]=[u("历史会话")])),default:a(()=>[t(n,null,{default:a(()=>[t(w(be))]),_:1})]),_:1}),d.value?_("",!0):(r(),m("div",ze)),d.value?_("",!0):(r(),m("div",Ee,"内容分析")),t(p,{index:"/agent/question-analysis"},{title:a(()=>e[13]||(e[13]=[u("问题分析")])),default:a(()=>[t(n,null,{default:a(()=>[t(w(ye))]),_:1})]),_:1}),t(p,{index:"/agent/faq"},{title:a(()=>e[14]||(e[14]=[u("常见问题")])),default:a(()=>[t(n,null,{default:a(()=>[t(w(ke))]),_:1})]),_:1}),d.value?_("",!0):(r(),m("div",Ue)),d.value?_("",!0):(r(),m("div",Le,"知识管理")),t(p,{index:"/agent/knowledge-base"},{title:a(()=>e[15]||(e[15]=[u("知识库管理")])),default:a(()=>[t(n,null,{default:a(()=>[t(w(xe))]),_:1})]),_:1}),O.value?(r(),m(D,{key:4},[d.value?_("",!0):(r(),m("div",qe)),d.value?_("",!0):(r(),m("div",Be,"系统管理")),t(p,{index:"/agent/service-agents"},{title:a(()=>e[16]||(e[16]=[u("客服管理")])),default:a(()=>[t(n,null,{default:a(()=>[t(w(M))]),_:1})]),_:1}),t(p,{index:"/agent/users"},{title:a(()=>e[17]||(e[17]=[u("用户管理")])),default:a(()=>[t(n,null,{default:a(()=>[t(w(M))]),_:1})]),_:1})],64)):_("",!0)]),_:1},8,["default-active","collapse"])]),f("div",Fe,[d.value?_("",!0):(r(),m("div",Re,[t(Ve,{mode:"agent"})])),t(le,{trigger:"click",onCommand:K},{dropdown:a(()=>[t(ae,null,{default:a(()=>[t(B,{command:"profile"},{default:a(()=>e[18]||(e[18]=[u("个人信息修改")])),_:1,__:[18]}),t(B,{command:"logout",divided:""},{default:a(()=>e[19]||(e[19]=[u("退出登录")])),_:1,__:[19]})]),_:1})]),default:a(()=>{var o,j,z,R;return[f("div",Ae,[t(I,{size:30,src:(o=c.value)==null?void 0:o.avatar},{default:a(()=>{var V,A;return[u(h(((A=(V=c.value)==null?void 0:V.name)==null?void 0:A.substring(0,1))||"客"),1)]}),_:1},8,["src"]),d.value?_("",!0):(r(),m("div",De,[f("div",Me,[u(h(((j=c.value)==null?void 0:j.name)||((z=c.value)==null?void 0:z.agentNo)||"客服")+" ",1),t(te,{size:"small",type:((R=c.value)==null?void 0:R.status)===1?"success":"info"},{default:a(()=>{var V;return[u(h(((V=c.value)==null?void 0:V.status)===1?"在线":"离线"),1)]}),_:1},8,["type"])])])),d.value?_("",!0):(r(),U(n,{key:1,class:"dropdown-icon"},{default:a(()=>[t(w(Pe))]),_:1}))])]}),_:1})])],2),f("div",{class:E(["main-content",{expanded:d.value}])},[t(oe)],2),t(de,{modelValue:x.value,"onUpdate:modelValue":e[7]||(e[7]=o=>x.value=o),title:"个人信息修改",width:"500px","close-on-click-modal":!1},{footer:a(()=>[f("div",Ge,[t(N,{onClick:e[6]||(e[6]=o=>x.value=!1)},{default:a(()=>e[20]||(e[20]=[u("取消")])),_:1,__:[20]}),t(N,{type:"primary",onClick:Y,loading:T.value},{default:a(()=>e[21]||(e[21]=[u(" 确认修改 ")])),_:1,__:[21]},8,["loading"])])]),default:a(()=>[t(ne,{ref_key:"profileFormRef",ref:q,model:l,rules:J,"label-width":"80px","label-position":"right"},{default:a(()=>[t(b,{label:"头像"},{default:a(()=>[t(I,{size:80,src:l.avatar},{default:a(()=>{var o;return[u(h(((o=l.name)==null?void 0:o.substring(0,1))||"客"),1)]}),_:1},8,["src"]),f("div",$e,[f("div",Oe,[(r(),m(D,null,he(G,(o,j)=>f("div",{key:j,class:E(["avatar-option",{selected:l.avatar===o}]),onClick:z=>l.avatar=o},[t(I,{size:40,src:o},null,8,["src"])],10,We)),64))])])]),_:1}),t(b,{label:"姓名",prop:"name"},{default:a(()=>[t(S,{modelValue:l.name,"onUpdate:modelValue":e[0]||(e[0]=o=>l.name=o),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1}),t(b,{label:"工号",disabled:""},{default:a(()=>[t(S,{modelValue:l.agentNo,"onUpdate:modelValue":e[1]||(e[1]=o=>l.agentNo=o),disabled:""},null,8,["modelValue"])]),_:1}),t(b,{label:"客服类型",prop:"agentType"},{default:a(()=>[t(se,{modelValue:l.agentType,"onUpdate:modelValue":e[2]||(e[2]=o=>l.agentType=o),placeholder:"请选择客服类型"},{default:a(()=>[t(F,{value:1,label:"客服"}),t(F,{value:3,label:"管理员（管理员无法接收用户消息）"})]),_:1},8,["modelValue"])]),_:1}),v.value?(r(),U(b,{key:0,label:"新密码",prop:"newPassword"},{default:a(()=>[t(S,{modelValue:l.newPassword,"onUpdate:modelValue":e[3]||(e[3]=o=>l.newPassword=o),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1})):_("",!0),v.value?(r(),U(b,{key:1,label:"确认密码",prop:"confirmPassword"},{default:a(()=>[t(S,{modelValue:l.confirmPassword,"onUpdate:modelValue":e[4]||(e[4]=o=>l.confirmPassword=o),type:"password",placeholder:"请确认新密码","show-password":""},null,8,["modelValue"])]),_:1})):_("",!0),t(b,null,{default:a(()=>[t(N,{type:"primary",link:"",onClick:e[5]||(e[5]=o=>v.value=!v.value)},{default:a(()=>[u(h(v.value?"取消修改密码":"修改密码"),1)]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Ze=ue(Je,[["__scopeId","data-v-010addc8"]]);export{Ze as default};
