import{_ as C}from"./ai-avatar-f145a15f.js";import{_ as E,u as S,a as B,b as D,r as m,o as T,c as p,d as A,e as j,f as u,g as o,w as l,h as f,i as z,l as N,j as y,p as P,E as _,n as G}from"./index.js";const H={class:"login-container"},J={class:"login-box card-shadow rounded"},K={class:"form-footer"},O={__name:"Login",setup(Q){const b=S(),V=B(),a=D(),h=m("agent"),g=m(!1),v=m(null),w=m(null),d=m({phone:"",password:""}),i=m({agentNo:"",password:""}),R={phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度应为6-20位",trigger:"blur"}]},q={agentNo:[{required:!0,message:"请输入工号",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度应为6-20位",trigger:"blur"}]};T(()=>{U()});const U=()=>{const t=document.querySelector(".particles-container");if(t)for(let e=0;e<50;e++){const s=document.createElement("div");s.classList.add("particle");const r=Math.random()*4+1;s.style.width=`${r}px`,s.style.height=`${r}px`,s.style.left=`${Math.random()*100}%`,s.style.top=`${Math.random()*100}%`,s.style.animationDelay=`${Math.random()*5}s`,t.appendChild(s)}},F=async()=>{if(v.value)try{await v.value.validate(),g.value=!0,console.log("开始用户登录...",d.value);const t={phone:d.value.phone,password:d.value.password},e=await a.userLogin(t);console.log("用户登录成功，返回数据:",e),console.log("当前用户状态:",a.isLoggedIn,a.role),_({type:"success",message:"登录成功"});const s=V.query.redirect||"/user/chat";console.log("准备跳转到:",s),b.push(s),console.log("路由跳转已执行")}catch(t){console.error("登录失败:",t),_.error(t.message||"登录失败，请稍后重试")}finally{g.value=!1}},I=async()=>{if(w.value)try{await w.value.validate(),g.value=!0,console.log("开始客服登录...",i.value);const t={agentNo:i.value.agentNo,password:i.value.password},e=await a.agentLogin(t);console.log("客服登录成功，返回数据:",e),console.log("当前用户状态:",a.isLoggedIn,a.role),a.userInfo&&a.userInfo.id&&await a.updateAgentStatus(a.userInfo.id,1),_({type:"success",message:"登录成功"}),G(()=>{const s=V.query.redirect||"/agent/dashboard";b.replace(s).then(()=>{console.log("导航成功，跳转到:",s)}).catch(r=>{console.error("导航失败:",r),window.location.href=s})})}catch(t){console.error("登录失败:",t),_.error(t.message||"登录失败，请稍后重试")}finally{g.value=!1}};return(t,e)=>{const s=p("el-icon"),r=p("el-input"),c=p("el-form-item"),x=p("el-button"),k=p("el-form"),L=p("el-tab-pane"),M=p("router-link"),$=p("el-tabs");return A(),j("div",H,[e[10]||(e[10]=u("div",{class:"geometric-lines"},null,-1)),e[11]||(e[11]=u("div",{class:"blob-shape"},null,-1)),e[12]||(e[12]=u("div",{class:"particles-container"},null,-1)),u("div",J,[e[9]||(e[9]=u("div",{class:"logo-container"},[u("img",{src:C,alt:"熊小智",class:"logo-image"}),u("h2",{class:"title"},"熊小智客服")],-1)),o($,{modelValue:h.value,"onUpdate:modelValue":e[4]||(e[4]=n=>h.value=n),class:"login-tabs"},{default:l(()=>[o(L,{label:"客服登录",name:"agent"},{default:l(()=>[o(k,{model:i.value,rules:q,ref_key:"agentFormRef",ref:w,"label-width":"0"},{default:l(()=>[o(c,{prop:"agentNo"},{default:l(()=>[o(r,{modelValue:i.value.agentNo,"onUpdate:modelValue":e[0]||(e[0]=n=>i.value.agentNo=n),placeholder:"请输入工号"},{prefix:l(()=>[o(s,null,{default:l(()=>[o(f(z))]),_:1})]),_:1},8,["modelValue"])]),_:1}),o(c,{prop:"password"},{default:l(()=>[o(r,{modelValue:i.value.password,"onUpdate:modelValue":e[1]||(e[1]=n=>i.value.password=n),placeholder:"请输入密码",type:"password","show-password":""},{prefix:l(()=>[o(s,null,{default:l(()=>[o(f(N))]),_:1})]),_:1},8,["modelValue"])]),_:1}),o(c,null,{default:l(()=>[o(x,{type:"primary",loading:g.value,class:"login-button",onClick:I},{default:l(()=>e[5]||(e[5]=[y("登录")])),_:1,__:[5]},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1}),o(L,{label:"用户登录",name:"user"},{default:l(()=>[o(k,{model:d.value,rules:R,ref_key:"userFormRef",ref:v,"label-width":"0"},{default:l(()=>[o(c,{prop:"phone"},{default:l(()=>[o(r,{modelValue:d.value.phone,"onUpdate:modelValue":e[2]||(e[2]=n=>d.value.phone=n),placeholder:"请输入手机号"},{prefix:l(()=>[o(s,null,{default:l(()=>[o(f(P))]),_:1})]),_:1},8,["modelValue"])]),_:1}),o(c,{prop:"password"},{default:l(()=>[o(r,{modelValue:d.value.password,"onUpdate:modelValue":e[3]||(e[3]=n=>d.value.password=n),placeholder:"请输入密码",type:"password","show-password":""},{prefix:l(()=>[o(s,null,{default:l(()=>[o(f(N))]),_:1})]),_:1},8,["modelValue"])]),_:1}),o(c,null,{default:l(()=>[o(x,{type:"primary",loading:g.value,class:"login-button",onClick:F},{default:l(()=>e[6]||(e[6]=[y("登录")])),_:1,__:[6]},8,["loading"])]),_:1}),u("div",K,[e[8]||(e[8]=u("span",null,"还没有账号？",-1)),o(M,{to:"/register",class:"register-link"},{default:l(()=>e[7]||(e[7]=[y("立即注册")])),_:1,__:[7]})])]),_:1},8,["model"])]),_:1})]),_:1},8,["modelValue"])])])}}},Y=E(O,[["__scopeId","data-v-3233ffd0"]]);export{Y as default};
