import{S as U}from"./index.js";import{k as kt,S as ct,C as gt,u as mt,j as Et,l as bt,h as Ft,m as At,n as Pt,Z as Rt,o as Wt,r as Ot,p as zt,q as Dt,s as Lt,t as qt,v as vt,w as Bt}from"./installCanvasRenderer-44afd735.js";function Ut(l){return U({url:"/question-analysis/category-stats",method:"get",params:l})}function $t(l){return U({url:"/question-analysis/hot-questions",method:"get",params:l})}function Kt(l){return U({url:"/question-analysis/question-trend",method:"get",params:l})}function jt(l){return U({url:"/question-analysis/keyword-stats",method:"get",params:l})}function ot(l,r){return r=r||{},kt(l,null,null,r.state!=="normal")}function Xt(l){var r=ct.extend(l);return ct.registerClass(r),r}function Yt(l){var r=gt.extend(l);return gt.registerClass(r),r}mt([Et,bt]);mt(Ft);Xt({type:"series.wordCloud",visualStyleAccessPath:"textStyle",visualStyleMapper:function(l){return{fill:l.get("color")}},visualDrawType:"fill",optionUpdated:function(){var l=this.option;l.gridSize=Math.max(Math.floor(l.gridSize),4)},getInitialData:function(l,r){var a=At(l.data,{coordDimensions:["value"]}),s=new Pt(a,this);return s.initData(l.data),s},defaultOption:{maskImage:null,shape:"circle",keepAspect:!1,left:"center",top:"center",width:"70%",height:"80%",sizeRange:[12,60],rotationRange:[-90,90],rotationStep:45,gridSize:8,drawOutOfBound:!1,shrinkToFit:!1,textStyle:{fontWeight:"normal"}}});Yt({type:"wordCloud",render:function(l,r,a){var s=this.group;s.removeAll();var t=l.getData(),y=l.get("gridSize");l.layoutInstance.ondraw=function(d,i,T,P){var D=t.getItemModel(T),X=D.getModel("textStyle"),I=new Rt({style:ot(X),scaleX:1/P.info.mu,scaleY:1/P.info.mu,x:(P.gx+P.info.gw/2)*y,y:(P.gy+P.info.gh/2)*y,rotation:P.rot});I.setStyle({x:P.info.fillTextOffsetX,y:P.info.fillTextOffsetY+i*.5,text:d,verticalAlign:"middle",fill:t.getItemVisual(T,"style").fill,fontSize:i}),s.add(I),t.setItemGraphicEl(T,I),I.ensureState("emphasis").style=ot(D.getModel(["emphasis","textStyle"]),{state:"emphasis"}),I.ensureState("blur").style=ot(D.getModel(["blur","textStyle"]),{state:"blur"}),Wt(I,D.get(["emphasis","focus"]),D.get(["emphasis","blurScope"])),I.stateTransition={duration:l.get("animation")?l.get(["stateAnimation","duration"]):0,easing:l.get(["stateAnimation","easing"])},I.__highDownDispatcher=!0},this._model=l},remove:function(){this.group.removeAll(),this._model.layoutInstance.dispose()},dispose:function(){this._model.layoutInstance.dispose()}});/*!
 * wordcloud2.js
 * http://timdream.org/wordcloud2.js/
 *
 * Copyright 2011 - 2019 Tim Guan-tin Chien and contributors.
 * Released under the MIT license
 */window.setImmediate||(window.setImmediate=function(){return window.msSetImmediate||window.webkitSetImmediate||window.mozSetImmediate||window.oSetImmediate||function(){if(!window.postMessage||!window.addEventListener)return null;var a=[void 0],s="zero-timeout-message",t=function(d){var i=a.length;return a.push(d),window.postMessage(s+i.toString(36),"*"),i};return window.addEventListener("message",function(d){if(!(typeof d.data!="string"||d.data.substr(0,s.length)!==s)){d.stopImmediatePropagation();var i=parseInt(d.data.substr(s.length),36);a[i]&&(a[i](),a[i]=void 0)}},!0),window.clearImmediate=function(d){a[d]&&(a[d]=void 0)},t}()||function(a){window.setTimeout(a,0)}}());window.clearImmediate||(window.clearImmediate=function(){return window.msClearImmediate||window.webkitClearImmediate||window.mozClearImmediate||window.oClearImmediate||function(a){window.clearTimeout(a)}}());var st=function(){var r=document.createElement("canvas");if(!r||!r.getContext)return!1;var a=r.getContext("2d");return!(!a||!a.getImageData||!a.fillText||!Array.prototype.some||!Array.prototype.push)}(),lt=function(){if(st){for(var r=document.createElement("canvas").getContext("2d"),a=20,s,t;a;){if(r.font=a.toString(10)+"px sans-serif",r.measureText("Ｗ").width===s&&r.measureText("m").width===t)return a+1;s=r.measureText("Ｗ").width,t=r.measureText("m").width,a--}return 0}}(),Gt=function(l){if(Array.isArray(l)){var r=l.slice();return r.splice(0,2),r}else return[]},Ht=function(r){for(var a,s,t=r.length;t;)a=Math.floor(Math.random()*t),s=r[--t],r[t]=r[a],r[a]=s;return r},Z={},$=function(r,a){if(!st)return;var s=Math.floor(Math.random()*Date.now());Array.isArray(r)||(r=[r]),r.forEach(function(c,e){if(typeof c=="string"){if(r[e]=document.getElementById(c),!r[e])throw new Error("The element id specified is not found.")}else if(!c.tagName&&!c.appendChild)throw new Error("You must pass valid HTML elements, or ID of the element.")});var t={list:[],fontFamily:'"Trebuchet MS", "Heiti TC", "微軟正黑體", "Arial Unicode MS", "Droid Fallback Sans", sans-serif',fontWeight:"normal",color:"random-dark",minSize:0,weightFactor:1,clearCanvas:!0,backgroundColor:"#fff",gridSize:8,drawOutOfBound:!1,shrinkToFit:!1,origin:null,drawMask:!1,maskColor:"rgba(255,0,0,0.3)",maskGapWidth:.3,layoutAnimation:!0,wait:0,abortThreshold:0,abort:function(){},minRotation:-Math.PI/2,maxRotation:Math.PI/2,rotationStep:.1,shuffle:!0,rotateRatio:.1,shape:"circle",ellipticity:.65,classes:null,hover:null,click:null};if(a)for(var y in a)y in t&&(t[y]=a[y]);if(typeof t.weightFactor!="function"){var d=t.weightFactor;t.weightFactor=function(e){return e*d}}if(typeof t.shape!="function")switch(t.shape){case"circle":default:t.shape="circle";break;case"cardioid":t.shape=function(e){return 1-Math.sin(e)};break;case"diamond":t.shape=function(e){var n=e%(2*Math.PI/4);return 1/(Math.cos(n)+Math.sin(n))};break;case"square":t.shape=function(e){return Math.min(1/Math.abs(Math.cos(e)),1/Math.abs(Math.sin(e)))};break;case"triangle-forward":t.shape=function(e){var n=e%(2*Math.PI/3);return 1/(Math.cos(n)+Math.sqrt(3)*Math.sin(n))};break;case"triangle":case"triangle-upright":t.shape=function(e){var n=(e+Math.PI*3/2)%(2*Math.PI/3);return 1/(Math.cos(n)+Math.sqrt(3)*Math.sin(n))};break;case"pentagon":t.shape=function(e){var n=(e+.955)%(2*Math.PI/5);return 1/(Math.cos(n)+.726543*Math.sin(n))};break;case"star":t.shape=function(e){var n=(e+.955)%(2*Math.PI/10);return(e+.955)%(2*Math.PI/5)-2*Math.PI/10>=0?1/(Math.cos(2*Math.PI/10-n)+3.07768*Math.sin(2*Math.PI/10-n)):1/(Math.cos(n)+3.07768*Math.sin(n))};break}t.gridSize=Math.max(Math.floor(t.gridSize),4);var i=t.gridSize,T=i-t.maskGapWidth,P=Math.abs(t.maxRotation-t.minRotation),D=Math.min(t.maxRotation,t.minRotation),X=t.rotationStep,I,C,b,q,F,O,Y;function ut(c,e){return"hsl("+(Math.random()*360).toFixed()+","+(Math.random()*30+70).toFixed()+"%,"+(Math.random()*(e-c)+c).toFixed()+"%)"}switch(t.color){case"random-dark":Y=function(){return ut(10,50)};break;case"random-light":Y=function(){return ut(50,90)};break;default:typeof t.color=="function"&&(Y=t.color);break}var _;typeof t.fontWeight=="function"&&(_=t.fontWeight);var K=null;typeof t.classes=="function"&&(K=t.classes);var j=!1,N=[],J,ft=function(e){var n=e.currentTarget,o=n.getBoundingClientRect(),f,u;e.touches?(f=e.touches[0].clientX,u=e.touches[0].clientY):(f=e.clientX,u=e.clientY);var h=f-o.left,S=u-o.top,v=Math.floor(h*(n.width/o.width||1)/i),m=Math.floor(S*(n.height/o.height||1)/i);return N[v]?N[v][m]:null},dt=function(e){var n=ft(e);if(J!==n){if(J=n,!n){t.hover(void 0,void 0,e);return}t.hover(n.item,n.dimension,e)}},tt=function(e){var n=ft(e);n&&(t.click(n.item,n.dimension,e),e.preventDefault())},et=[],wt=function(e){if(et[e])return et[e];var n=e*8,o=n,f=[];for(e===0&&f.push([q[0],q[1],0]);o--;){var u=1;t.shape!=="circle"&&(u=t.shape(o/n*2*Math.PI)),f.push([q[0]+e*u*Math.cos(-o/n*2*Math.PI),q[1]+e*u*Math.sin(-o/n*2*Math.PI)*t.ellipticity,o/n*2*Math.PI])}return et[e]=f,f},at=function(){return t.abortThreshold>0&&new Date().getTime()-O>t.abortThreshold},pt=function(){return t.rotateRatio===0||Math.random()>t.rotateRatio?0:P===0?D:D+Math.round(Math.random()*P/X)*X},yt=function(e,n,o,f){var u=t.weightFactor(n);if(u<=t.minSize)return!1;var h=1;u<lt&&(h=function(){for(var nt=2;nt*u<lt;)nt+=2;return nt}());var S;_?S=_(e,n,u,f):S=t.fontWeight;var v=document.createElement("canvas"),m=v.getContext("2d",{willReadFrequently:!0});m.font=S+" "+(u*h).toString(10)+"px "+t.fontFamily;var A=m.measureText(e).width/h,w=Math.max(u*h,m.measureText("m").width,m.measureText("Ｗ").width)/h,p=A+w*2,k=w*3,R=Math.ceil(p/i),W=Math.ceil(k/i);p=R*i,k=W*i;var M=-A/2,g=-w*.4,x=Math.ceil((p*Math.abs(Math.sin(o))+k*Math.abs(Math.cos(o)))/i),E=Math.ceil((p*Math.abs(Math.cos(o))+k*Math.abs(Math.sin(o)))/i),z=E*i,H=x*i;v.setAttribute("width",z),v.setAttribute("height",H),m.scale(1/h,1/h),m.translate(z*h/2,H*h/2),m.rotate(-o),m.font=S+" "+(u*h).toString(10)+"px "+t.fontFamily,m.fillStyle="#000",m.textBaseline="middle",m.fillText(e,M*h,(g+u*.5)*h);var Q=m.getImageData(0,0,z,H).data;if(at())return!1;for(var ht=[],G=E,B,rt,it,L=[x/2,E/2,x/2,E/2];G--;)for(B=x;B--;){it=i;t:for(;it--;)for(rt=i;rt--;)if(Q[((B*i+it)*z+(G*i+rt))*4+3]){ht.push([G,B]),G<L[3]&&(L[3]=G),G>L[1]&&(L[1]=G),B<L[0]&&(L[0]=B),B>L[2]&&(L[2]=B);break t}}return{mu:h,occupied:ht,bounds:L,gw:E,gh:x,fillTextOffsetX:M,fillTextOffsetY:g,fillTextWidth:A,fillTextHeight:w,fontSize:u}},xt=function(e,n,o,f,u){for(var h=u.length;h--;){var S=e+u[h][0],v=n+u[h][1];if(S>=C||v>=b||S<0||v<0){if(!t.drawOutOfBound)return!1;continue}if(!I[S][v])return!1}return!0},St=function(e,n,o,f,u,h,S,v,m,A){var w=o.fontSize,p;Y?p=Y(f,u,w,h,S,A):p=t.color;var k;_?k=_(f,u,w,A):k=t.fontWeight;var R;K?R=K(f,u,w,A):R=t.classes,r.forEach(function(W){if(W.getContext){var M=W.getContext("2d"),g=o.mu;M.save(),M.scale(1/g,1/g),M.font=k+" "+(w*g).toString(10)+"px "+t.fontFamily,M.fillStyle=p,M.translate((e+o.gw/2)*i*g,(n+o.gh/2)*i*g),v!==0&&M.rotate(-v),M.textBaseline="middle",M.fillText(f,o.fillTextOffsetX*g,(o.fillTextOffsetY+w*.5)*g),M.restore()}else{var x=document.createElement("span"),E="";E="rotate("+-v/Math.PI*180+"deg) ",o.mu!==1&&(E+="translateX(-"+o.fillTextWidth/4+"px) scale("+1/o.mu+")");var z={position:"absolute",display:"block",font:k+" "+w*o.mu+"px "+t.fontFamily,left:(e+o.gw/2)*i+o.fillTextOffsetX+"px",top:(n+o.gh/2)*i+o.fillTextOffsetY+"px",width:o.fillTextWidth+"px",height:o.fillTextHeight+"px",lineHeight:w+"px",whiteSpace:"nowrap",transform:E,webkitTransform:E,msTransform:E,transformOrigin:"50% 40%",webkitTransformOrigin:"50% 40%",msTransformOrigin:"50% 40%"};p&&(z.color=p),x.textContent=f;for(var H in z)x.style[H]=z[H];if(m)for(var Q in m)x.setAttribute(Q,m[Q]);R&&(x.className+=R),W.appendChild(x)}})},Tt=function(e,n,o,f,u){if(!(e>=C||n>=b||e<0||n<0)){if(I[e][n]=!1,o){var h=r[0].getContext("2d");h.fillRect(e*i,n*i,T,T)}j&&(N[e][n]={item:u,dimension:f})}},Mt=function(e,n,o,f,u,h){var S=u.occupied,v=t.drawMask,m;v&&(m=r[0].getContext("2d"),m.save(),m.fillStyle=t.maskColor);var A;if(j){var w=u.bounds;A={x:(e+w[3])*i,y:(n+w[0])*i,w:(w[1]-w[3]+1)*i,h:(w[2]-w[0]+1)*i}}for(var p=S.length;p--;){var k=e+S[p][0],R=n+S[p][1];k>=C||R>=b||k<0||R<0||Tt(k,R,v,A,h)}v&&m.restore()},It=function c(e,n){if(n>20)return null;var o,f,u;Array.isArray(e)?(o=e[0],f=e[1]):(o=e.word,f=e.weight,u=e.attributes);var h=pt(),S=Gt(e),v=yt(o,f,h,S);if(!v||at())return!1;if(!t.drawOutOfBound&&!t.shrinkToFit){var m=v.bounds;if(m[1]-m[3]+1>C||m[2]-m[0]+1>b)return!1}for(var A=F+1,w=function(W){var M=Math.floor(W[0]-v.gw/2),g=Math.floor(W[1]-v.gh/2),x=v.gw,E=v.gh;return xt(M,g,x,E,v.occupied)?(St(M,g,v,o,f,F-A,W[2],h,u,S),Mt(M,g,x,E,v,e),{gx:M,gy:g,rot:h,info:v}):!1};A--;){var p=wt(F-A);t.shuffle&&(p=[].concat(p),Ht(p));for(var k=0;k<p.length;k++){var R=w(p[k]);if(R)return R}}return t.shrinkToFit?(Array.isArray(e)?e[1]=e[1]*3/4:e.weight=e.weight*3/4,c(e,n+1)):null},V=function(e,n,o){if(n)return!r.some(function(f){var u=new CustomEvent(e,{detail:o||{}});return!f.dispatchEvent(u)},this);r.forEach(function(f){var u=new CustomEvent(e,{detail:o||{}});f.dispatchEvent(u)},this)},Ct=function(){var e=r[0];if(e.getContext)C=Math.ceil(e.width/i),b=Math.ceil(e.height/i);else{var n=e.getBoundingClientRect();C=Math.ceil(n.width/i),b=Math.ceil(n.height/i)}if(V("wordcloudstart",!0)){q=t.origin?[t.origin[0]/i,t.origin[1]/i]:[C/2,b/2],F=Math.floor(Math.sqrt(C*C+b*b)),I=[];var o,f,u;if(!e.getContext||t.clearCanvas)for(r.forEach(function(g){if(g.getContext){var x=g.getContext("2d");x.fillStyle=t.backgroundColor,x.clearRect(0,0,C*(i+1),b*(i+1)),x.fillRect(0,0,C*(i+1),b*(i+1))}else g.textContent="",g.style.backgroundColor=t.backgroundColor,g.style.position="relative"}),o=C;o--;)for(I[o]=[],f=b;f--;)I[o][f]=!0;else{var h=document.createElement("canvas").getContext("2d");h.fillStyle=t.backgroundColor,h.fillRect(0,0,1,1);var S=h.getImageData(0,0,1,1).data,v=e.getContext("2d").getImageData(0,0,C*i,b*i).data;o=C;for(var m,A;o--;)for(I[o]=[],f=b;f--;){A=i;t:for(;A--;)for(m=i;m--;)for(u=4;u--;)if(v[((f*i+A)*C*i+(o*i+m))*4+u]!==S[u]){I[o][f]=!1;break t}I[o][f]!==!1&&(I[o][f]=!0)}v=h=S=void 0}if(t.hover||t.click){for(j=!0,o=C+1;o--;)N[o]=[];t.hover&&e.addEventListener("mousemove",dt),t.click&&(e.addEventListener("click",tt),e.addEventListener("touchstart",tt),e.addEventListener("touchend",function(g){g.preventDefault()}),e.style.webkitTapHighlightColor="rgba(0, 0, 0, 0)"),e.addEventListener("wordcloudstart",function g(){e.removeEventListener("wordcloudstart",g),e.removeEventListener("mousemove",dt),e.removeEventListener("click",tt),J=void 0})}u=0;var w,p,k=!0;t.layoutAnimation?t.wait!==0?(w=window.setTimeout,p=window.clearTimeout):(w=window.setImmediate,p=window.clearImmediate):(w=function(g){g()},p=function(){k=!1});var R=function(x,E){r.forEach(function(z){z.addEventListener(x,E)},this)},W=function(x,E){r.forEach(function(z){z.removeEventListener(x,E)},this)},M=function g(){W("wordcloudstart",g),p(Z[s])};R("wordcloudstart",M),Z[s]=(t.layoutAnimation?w:setTimeout)(function g(){if(k){if(u>=t.list.length){p(Z[s]),V("wordcloudstop",!1),W("wordcloudstart",M),delete Z[s];return}O=new Date().getTime();var x=It(t.list[u],0),E=!V("wordclouddrawn",!0,{item:t.list[u],drawn:x});if(at()||E){p(Z[s]),t.abort(),V("wordcloudabort",!1),V("wordcloudstop",!1),W("wordcloudstart",M);return}u++,Z[s]=w(g,t.wait)}},t.wait)}};Ct()};$.isSupported=st;$.minFontSize=lt;if(!$.isSupported)throw new Error("Sorry your browser not support wordCloud");function Zt(l){for(var r=l.getContext("2d"),a=r.getImageData(0,0,l.width,l.height),s=r.createImageData(a),t=0,y=0,d=0;d<a.data.length;d+=4){var i=a.data[d+3];if(i>128){var T=a.data[d]+a.data[d+1]+a.data[d+2];t+=T,++y}}for(var P=t/y,d=0;d<a.data.length;d+=4){var T=a.data[d]+a.data[d+1]+a.data[d+2],i=a.data[d+3];i<128||T>P?(s.data[d]=0,s.data[d+1]=0,s.data[d+2]=0,s.data[d+3]=0):(s.data[d]=255,s.data[d+1]=255,s.data[d+2]=255,s.data[d+3]=255)}r.putImageData(s,0,0)}Ot(function(l,r){l.eachSeriesByType("wordCloud",function(a){var s=Dt(a.getBoxLayoutParams(),{width:r.getWidth(),height:r.getHeight()}),t=a.get("keepAspect"),y=a.get("maskImage"),d=y?y.width/y.height:1;t&&_t(s,d);var i=a.getData(),T=document.createElement("canvas");T.width=s.width,T.height=s.height;var P=T.getContext("2d");if(y)try{P.drawImage(y,0,0,T.width,T.height),Zt(T)}catch(F){console.error("Invalid mask image"),console.error(F.toString())}var D=a.get("sizeRange"),X=a.get("rotationRange"),I=i.getDataExtent("value"),C=Math.PI/180,b=a.get("gridSize");$(T,{list:i.mapArray("value",function(F,O){var Y=i.getItemModel(O);return[i.getName(O),Y.get("textStyle.fontSize",!0)||Lt(F,I,D),O]}).sort(function(F,O){return O[1]-F[1]}),fontFamily:a.get("textStyle.fontFamily")||a.get("emphasis.textStyle.fontFamily")||l.get("textStyle.fontFamily"),fontWeight:a.get("textStyle.fontWeight")||a.get("emphasis.textStyle.fontWeight")||l.get("textStyle.fontWeight"),gridSize:b,ellipticity:s.height/s.width,minRotation:X[0]*C,maxRotation:X[1]*C,clearCanvas:!y,rotateRatio:1,rotationStep:a.get("rotationStep")*C,drawOutOfBound:a.get("drawOutOfBound"),shrinkToFit:a.get("shrinkToFit"),layoutAnimation:a.get("layoutAnimation"),shuffle:!1,shape:a.get("shape")});function q(F){var O=F.detail.item;F.detail.drawn&&a.layoutInstance.ondraw&&(F.detail.drawn.gx+=s.x/b,F.detail.drawn.gy+=s.y/b,a.layoutInstance.ondraw(O[0],O[1],O[2],F.detail.drawn))}T.addEventListener("wordclouddrawn",q),a.layoutInstance&&a.layoutInstance.dispose(),a.layoutInstance={ondraw:null,dispose:function(){T.removeEventListener("wordclouddrawn",q),T.addEventListener("wordclouddrawn",function(F){F.preventDefault()})}}})});zt(function(l){var r=(l||{}).series;!qt(r)&&(r=r?[r]:[]);var a=["shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];vt(r,function(t){if(t&&t.type==="wordCloud"){var y=t.textStyle||{};s(y.normal),s(y.emphasis)}});function s(t){t&&vt(a,function(y){t.hasOwnProperty(y)&&(t["text"+Bt(y)]=t[y])})}});function _t(l,r){var a=l.width,s=l.height;a>s*r?(l.x+=(a-s*r)/2,l.width=s*r):(l.y+=(s-a/r)/2,l.height=a/r)}export{Ut as a,Kt as b,$t as c,jt as g};
