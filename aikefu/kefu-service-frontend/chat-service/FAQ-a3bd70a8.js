import{_ as de,k as B,r as w,x as U,o as ce,E as c,c as p,F as pe,d as C,e as A,f as b,g as l,w as o,h as D,al as ge,j as g,av as fe,G as L,J as M,K as me,H as _e,A as ve,t as q,y as E,n as ye}from"./index.js";import{h as we}from"./moment-a9aaa855.js";import{g as be,d as P,u as he,a as ke}from"./faq-65748221.js";const Ce={class:"faq-container"},qe={class:"faq-list"},Fe={class:"card-header"},xe={class:"header-operations"},Ve={class:"pagination-container"},ze={class:"dialog-footer"},Ae={__name:"FAQ",setup(Se){const s=B({pageNum:1,pageSize:10,category:"",keyword:""}),S=[{value:"订单相关",label:"订单相关"},{value:"商品相关",label:"商品相关"},{value:"配送相关",label:"配送相关"},{value:"支付相关",label:"支付相关"},{value:"账户相关",label:"账户相关"},{value:"其他问题",label:"其他问题"}],f=w([]),k=w(0),F=w(!1),h=w([]),v=w(!1),x=w(null),n=B({id:"",category:"",question:"",answer:"",sort:0}),R={category:[{required:!0,message:"请选择问题分类",trigger:"change"}],question:[{required:!0,message:"请输入问题",trigger:"blur"},{max:200,message:"最大长度为 200 个字符",trigger:"blur"}],answer:[{required:!0,message:"请输入答案",trigger:"blur"}]},Y=U(()=>h.value.length>0),j=U(()=>n.id?"编辑常见问题":"新增常见问题");ce(()=>{y()});const y=async()=>{F.value=!0;try{const t=await be();if(t.code===200){let u=t.data||[];if(s.category&&(u=u.filter(r=>r.category===s.category)),s.keyword){const r=s.keyword.toLowerCase();u=u.filter(d=>d.question&&d.question.toLowerCase().includes(r)||d.answer&&d.answer.toLowerCase().includes(r))}k.value=u.length,u.sort((r,d)=>(r.sort||0)-(d.sort||0));const i=(s.pageNum-1)*s.pageSize,m=i+s.pageSize;f.value=u.slice(i,m)}else c.error(t.message||"加载常见问题失败")}catch(t){console.error("加载FAQ列表失败",t),c.error("加载FAQ列表失败："+t.message||"未知错误")}finally{F.value=!1}},H=t=>{h.value=t},K=()=>{N(),v.value=!0},I=t=>{N(),Object.assign(n,t),v.value=!0},O=t=>{E.confirm(`确定要删除问题"${t.question}"吗？`,"删除问题",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await P(t.id);e.code===200?(c.success("删除成功"),f.value=f.value.filter(u=>u.id!==t.id),k.value--):c.error(e.message||"删除失败")}catch(e){console.error("删除失败",e),c.error("删除失败："+e.message||"未知错误")}}).catch(()=>{})},G=()=>{if(h.value.length===0){c.warning("请至少选择一条记录");return}E.confirm(`确定要删除已选中的 ${h.value.length} 条问题吗？`,"批量删除",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const t=h.value.map(r=>r.id),e=t.map(r=>P(r)),u=await Promise.allSettled(e),i=u.filter(r=>r.status==="fulfilled"&&r.value.code===200).length,m=t.length-i;i>0?(f.value=f.value.filter(r=>!t.includes(r.id)||u.find(d=>d.status==="fulfilled"&&d.value.code!==200&&d.value.id===r.id)),k.value-=i,m>0?c.warning(`已成功删除${i}条问题，${m}条删除失败`):c.success("批量删除成功")):c.error("批量删除失败")}catch(t){console.error("批量删除失败",t),c.error("批量删除失败："+t.message||"未知错误")}}).catch(()=>{})},J=t=>{c.success(`已复制问题"${t.question}"的回答到剪贴板`)},N=()=>{n.id="",n.category="",n.question="",n.answer="",n.sort=0},W=async()=>{if(x.value)try{if(await x.value.validate(),n.id){const t=await he(n.id,n);if(t.code===200){c.success("修改成功");const e=f.value.findIndex(u=>u.id===n.id);e!==-1&&(f.value[e]=t.data||{...f.value[e],...n})}else c.error(t.message||"修改失败")}else{const t=await ke(n);t.code===200?(c.success("新增成功"),y()):c.error(t.message||"新增失败")}v.value=!1}catch(t){console.error("表单验证或提交失败",t),c.error("提交失败："+t.message||"未知错误")}},X=t=>{console.log("分类变更为:",t),ye(()=>{V()})},V=()=>{s.pageNum=1,y()},Z=()=>{s.category="",s.keyword="",s.pageNum=1,y()},ee=t=>{s.pageSize=t,y()},te=t=>{s.pageNum=t,y()},le=t=>{switch(t){case"订单相关":return"";case"商品相关":return"success";case"配送相关":return"warning";case"支付相关":return"danger";case"账户相关":return"info";case"其他问题":return"";default:return""}},ae=t=>t||"未知",Q=t=>t?we(t).format("YYYY-MM-DD HH:mm:ss"):"-";return(t,e)=>{const u=p("el-icon"),i=p("el-button"),m=p("el-option"),r=p("el-select"),d=p("el-form-item"),z=p("el-input"),T=p("el-form"),_=p("el-table-column"),oe=p("el-tag"),se=p("el-table"),ne=p("el-pagination"),re=p("el-card"),ue=p("el-dialog"),ie=pe("loading");return C(),A("div",Ce,[b("div",qe,[l(re,{shadow:"hover"},{header:o(()=>[b("div",Fe,[e[11]||(e[11]=b("span",null,"问题列表",-1)),b("div",xe,[l(i,{type:"primary",size:"small",onClick:K},{default:o(()=>[l(u,null,{default:o(()=>[l(D(ge))]),_:1}),e[9]||(e[9]=g(" 新增问题 "))]),_:1,__:[9]}),l(i,{type:"danger",size:"small",onClick:G,disabled:!Y.value},{default:o(()=>[l(u,null,{default:o(()=>[l(D(fe))]),_:1}),e[10]||(e[10]=g(" 批量删除 "))]),_:1,__:[10]},8,["disabled"])])])]),default:o(()=>[l(T,{inline:!0,class:"search-form"},{default:o(()=>[l(d,{label:"问题分类"},{default:o(()=>[l(r,{modelValue:s.category,"onUpdate:modelValue":e[0]||(e[0]=a=>s.category=a),placeholder:"全部分类",clearable:"",onChange:X,style:{width:"200px"}},{default:o(()=>[l(m,{label:"全部",value:""}),(C(),A(L,null,M(S,a=>l(m,{key:a.value,label:a.label,value:a.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),l(d,{label:"关键词"},{default:o(()=>[l(z,{modelValue:s.keyword,"onUpdate:modelValue":e[1]||(e[1]=a=>s.keyword=a),placeholder:"问题/答案",clearable:"",onKeyup:me(V,["enter"])},null,8,["modelValue"])]),_:1}),l(d,null,{default:o(()=>[l(i,{type:"primary",onClick:V},{default:o(()=>e[12]||(e[12]=[g("查询")])),_:1,__:[12]}),l(i,{onClick:Z},{default:o(()=>e[13]||(e[13]=[g("重置")])),_:1,__:[13]})]),_:1})]),_:1}),_e((C(),ve(se,{data:f.value,style:{width:"100%"},border:"",stripe:"","header-cell-style":{background:"#f5f7fa"},onSelectionChange:H},{default:o(()=>[l(_,{type:"selection",width:"50"}),l(_,{label:"序号",width:"80"},{default:o(a=>[g(q((s.pageNum-1)*s.pageSize+a.$index+1),1)]),_:1}),l(_,{label:"问题分类",width:"120"},{default:o(a=>[l(oe,{type:le(a.row.category)},{default:o(()=>[g(q(ae(a.row.category)),1)]),_:2},1032,["type"])]),_:1}),l(_,{prop:"question",label:"问题","show-overflow-tooltip":""}),l(_,{prop:"answer",label:"答案","show-overflow-tooltip":""}),l(_,{prop:"createdAt",label:"创建时间",width:"180"},{default:o(a=>[g(q(Q(a.row.createdAt)),1)]),_:1}),l(_,{prop:"updatedAt",label:"更新时间",width:"180"},{default:o(a=>[g(q(Q(a.row.updatedAt)),1)]),_:1}),l(_,{label:"操作",width:"160",fixed:"right"},{default:o(a=>[l(i,{type:"primary",link:"",onClick:$=>I(a.row)},{default:o(()=>e[14]||(e[14]=[g(" 编辑 ")])),_:2,__:[14]},1032,["onClick"]),l(i,{type:"danger",link:"",onClick:$=>O(a.row)},{default:o(()=>e[15]||(e[15]=[g(" 删除 ")])),_:2,__:[15]},1032,["onClick"]),l(i,{type:"success",link:"",onClick:$=>J(a.row)},{default:o(()=>e[16]||(e[16]=[g(" 复制 ")])),_:2,__:[16]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ie,F.value]]),b("div",Ve,[l(ne,{"current-page":s.pageNum,"onUpdate:currentPage":e[2]||(e[2]=a=>s.pageNum=a),"page-size":s.pageSize,"onUpdate:pageSize":e[3]||(e[3]=a=>s.pageSize=a),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:k.value,onSizeChange:ee,onCurrentChange:te},null,8,["current-page","page-size","total"])])]),_:1})]),l(ue,{modelValue:v.value,"onUpdate:modelValue":e[8]||(e[8]=a=>v.value=a),title:j.value,width:"50%","destroy-on-close":""},{footer:o(()=>[b("span",ze,[l(i,{onClick:e[7]||(e[7]=a=>v.value=!1)},{default:o(()=>e[17]||(e[17]=[g("取消")])),_:1,__:[17]}),l(i,{type:"primary",onClick:W},{default:o(()=>e[18]||(e[18]=[g("确定")])),_:1,__:[18]})])]),default:o(()=>[l(T,{ref_key:"faqFormRef",ref:x,model:n,rules:R,"label-width":"100px"},{default:o(()=>[l(d,{label:"问题分类",prop:"category"},{default:o(()=>[l(r,{modelValue:n.category,"onUpdate:modelValue":e[4]||(e[4]=a=>n.category=a),placeholder:"请选择问题分类"},{default:o(()=>[(C(),A(L,null,M(S,a=>l(m,{key:a.value,label:a.label,value:a.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),l(d,{label:"问题",prop:"question"},{default:o(()=>[l(z,{modelValue:n.question,"onUpdate:modelValue":e[5]||(e[5]=a=>n.question=a),placeholder:"请输入问题",type:"textarea",rows:2},null,8,["modelValue"])]),_:1}),l(d,{label:"答案",prop:"answer"},{default:o(()=>[l(z,{modelValue:n.answer,"onUpdate:modelValue":e[6]||(e[6]=a=>n.answer=a),placeholder:"请输入答案",type:"textarea",rows:5},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},$e=de(Ae,[["__scopeId","data-v-b602e8da"]]);export{$e as default};
