import{_ as xe,r as _,k as ot,c as C,d as l,e as c,g as n,w as o,f as e,j as E,E as h,aj as Zt,x as me,C as Ye,o as rt,F as $t,t as u,h as L,ak as He,A as ae,q as g,p as es,G as ye,J as he,U as it,K as Ct,al as ct,O as ke,H as Qe,a5 as ts,am as Mt,a0 as dt,I as ss,Q as vt,an as At,ao as Dt,V as ut,ap as qt,aq as nt,L as Ee,ar as as,as as ns,at as ls,au as os,av as kt,P as xt,aw as rs,y as Me,ax as is,D as lt,a as cs,u as us,b as bt,v as ds,n as Ue,ay as St,az as vs,aA as ms,N as Tt,aB as fs,aC as ps,aD as _s,aE as Je,aF as ys,aG as gs,i as hs,aH as ws,aI as ks,s as It,aJ as bs,aK as Ss}from"./index.js";import{h as We}from"./moment-a9aaa855.js";import{g as Ot,u as Ts,a as Is}from"./faq-65748221.js";const $s={class:"solution-container"},Cs={class:"solution-body"},Ms={class:"form-actions"},As={__name:"SolutionDescription",props:{sessionId:{type:[Number,String],required:!0}},emits:["success","cancel"],setup(X,{emit:oe}){const v=X,R=oe,P=_(null),M=_(!1),H=ot({isSolved:1,description:""}),J=async()=>{if(!H.description.trim()){h.warning("请输入解决方案描述");return}M.value=!0;try{const Y=await Zt(v.sessionId,{solutionDescription:H.description,isSolved:H.isSolved});Y.code===200?(h.success("解决方案提交成功"),R("success",{description:H.description,isSolved:H.isSolved})):h.error(Y.message||"解决方案提交失败")}catch(Y){console.error("提交解决方案失败",Y),h.error("解决方案提交失败，请稍后重试")}finally{M.value=!1}};return(Y,z)=>{const K=C("el-radio"),F=C("el-radio-group"),j=C("el-form-item"),ne=C("el-input"),ue=C("el-button"),q=C("el-form"),x=C("el-card");return l(),c("div",$s,[n(x,{class:"solution-card"},{header:o(()=>z[3]||(z[3]=[e("div",{class:"solution-header"},[e("span",null,"添加问题解决方案")],-1)])),default:o(()=>[e("div",Cs,[n(q,{model:H,ref_key:"solutionForm",ref:P,"label-position":"top"},{default:o(()=>[n(j,{label:"问题是否解决",prop:"isSolved"},{default:o(()=>[n(F,{modelValue:H.isSolved,"onUpdate:modelValue":z[0]||(z[0]=U=>H.isSolved=U)},{default:o(()=>[n(K,{label:1},{default:o(()=>z[4]||(z[4]=[E("已解决")])),_:1,__:[4]}),n(K,{label:0},{default:o(()=>z[5]||(z[5]=[E("未解决")])),_:1,__:[5]})]),_:1},8,["modelValue"])]),_:1}),n(j,{label:"解决方案描述",prop:"description"},{default:o(()=>[n(ne,{modelValue:H.description,"onUpdate:modelValue":z[1]||(z[1]=U=>H.description=U),type:"textarea",rows:5,placeholder:"请输入问题解决方案描述...",maxlength:1e3,"show-word-limit":""},null,8,["modelValue"])]),_:1}),n(j,null,{default:o(()=>[e("div",Ms,[n(ue,{type:"primary",onClick:J,loading:M.value},{default:o(()=>z[6]||(z[6]=[E("提交解决方案")])),_:1,__:[6]},8,["loading"]),n(ue,{onClick:z[2]||(z[2]=U=>Y.$emit("cancel"))},{default:o(()=>z[7]||(z[7]=[E("取消")])),_:1,__:[7]})])]),_:1})]),_:1},8,["model"])])]),_:1})])}}},Ds=xe(As,[["__scopeId","data-v-de8f85f3"]]);const qs={key:0,class:"user-info-placeholder"},xs={key:1,class:"user-info-content"},Os={class:"user-basic-info"},Ns={class:"user-name-info"},zs={key:0,class:"phone-row"},Us={key:0,class:"user-orders-info"},Ls={class:"orders-info-container"},Rs={key:0,class:"current-order"},Ps={class:"current-order-card"},Vs={class:"item-header"},Bs={class:"item-content"},Fs={class:"item-row"},Es={class:"item-value"},Hs={class:"item-row"},Qs={class:"item-value"},Ws={class:"item-row"},Ks={class:"item-value price"},js={class:"item-row"},Js={class:"item-value"},Ys={class:"item-row"},Gs={class:"item-value"},Xs={class:"item-row"},Zs={class:"item-value"},ea={key:0,class:"item-row"},ta={class:"item-value"},sa={key:1,class:"current-order"},aa={class:"current-order-card flea-market-card"},na={class:"item-header"},la={class:"item-content"},oa={key:0,class:"item-row"},ra={class:"item-value"},ia={key:1,class:"item-row"},ca={class:"item-value"},ua={key:2,class:"item-row"},da={class:"item-value price"},va={key:3,class:"item-row"},ma={class:"item-value"},fa={key:2,class:"current-order"},pa={class:"current-order-card market-card"},_a={class:"item-header"},ya={class:"item-content"},ga={key:0,class:"item-row"},ha={class:"item-value"},wa={key:1,class:"item-row"},ka={class:"item-value"},ba={key:2,class:"item-row"},Sa={class:"item-value price"},Ta={key:3,class:"item-row"},Ia={class:"item-value"},$a={key:3,class:"current-order"},Ca={class:"current-order-card engineer-card"},Ma={class:"item-header"},Aa={class:"item-content"},Da={key:0,class:"item-row"},qa={class:"item-value"},xa={key:1,class:"item-row"},Oa={class:"item-value"},Na={key:2,class:"item-row"},za={class:"item-value"},Ua={key:3,class:"item-row"},La={class:"item-value"},Ra={key:4,class:"recycle-orders"},Pa={class:"recycle-carousel"},Va={class:"recycle-item-card"},Ba={class:"item-header"},Fa=["onClick"],Ea={class:"item-content"},Ha={class:"item-row"},Qa={class:"item-value"},Wa={class:"item-row"},Ka={class:"item-value"},ja={class:"item-row"},Ja={class:"item-value price"},Ya={class:"item-row"},Ga={class:"item-value"},Xa={class:"item-row"},Za={class:"item-value"},en={key:5,class:"recycle-orders"},tn={class:"recycle-carousel"},sn={class:"recycle-item-card flea-market-item-card"},an={class:"item-header"},nn=["onClick"],ln={class:"item-content"},on={key:0,class:"item-row"},rn={class:"item-value"},cn={key:1,class:"item-row"},un={class:"item-value"},dn={key:2,class:"item-row"},vn={class:"item-value price"},mn={key:3,class:"item-row"},fn={class:"item-value"},pn={key:6,class:"recycle-orders"},_n={class:"recycle-carousel"},yn={class:"recycle-item-card market-item-card"},gn={class:"item-header"},hn=["onClick"],wn={class:"item-content"},kn={key:0,class:"item-row"},bn={class:"item-value"},Sn={key:1,class:"item-row"},Tn={class:"item-value"},In={key:2,class:"item-row"},$n={class:"item-value price"},Cn={key:3,class:"item-row"},Mn={class:"item-value"},An={key:7,class:"recycle-orders"},Dn={class:"recycle-carousel"},qn={class:"recycle-item-card engineer-item-card"},xn={class:"item-header"},On=["onClick"],Nn={class:"item-content"},zn={key:0,class:"item-row"},Un={class:"item-value"},Ln={key:1,class:"item-row"},Rn={class:"item-value"},Pn={key:2,class:"item-row"},Vn={class:"item-value price"},Bn={key:3,class:"item-row"},Fn={class:"item-value"},En={key:2,class:"user-info-placeholder"},Hn={class:"faq-card"},Qn={class:"faq-compact-header"},Wn={class:"faq-search-compact"},Kn={class:"faq-tabs-compact"},jn={class:"faq-category-nav"},Jn=["onClick"],Yn={class:"faq-content-compact"},Gn={key:0,class:"empty-faq"},Xn={key:1,class:"faq-list"},Zn=["onClick"],el={class:"faq-item-title"},tl={class:"faq-item-content"},sl={class:"faq-item-actions"},al={key:2,class:"faq-pagination-compact"},nl={class:"dialog-footer"},ll={__name:"UserInfoPanel",props:{currentSession:{type:Object,default:null},visible:{type:Boolean,default:!1},isMobile:{type:Boolean,default:!1}},emits:["close","insert-to-reply"],setup(X,{emit:oe}){const v=X,R=oe,P=_(null),M=_(null),H=_(!1),J=_(null),Y=_(null),z=_(null),K=_(null),F=_(null),j=_([]),ne=_(0),ue=_(!1),q=_(null),x=ot({keyword:"",category:"",pageNum:1,pageSize:5}),U=_([{label:"订单",value:"订单相关"},{label:"商品",value:"商品相关"},{label:"配送",value:"配送相关"},{label:"支付",value:"支付相关"},{label:"账户",value:"账户相关"},{label:"其他",value:"其他问题"}]),D=_(""),G=_(!1),de=_(null),W=ot({id:"",category:"",question:"",answer:"",sort:0}),m={category:[{required:!0,message:"请选择问题分类",trigger:"change"}],question:[{required:!0,message:"请输入问题",trigger:"blur"},{max:200,message:"最大长度为 200 个字符",trigger:"blur"}],answer:[{required:!0,message:"请输入答案",trigger:"blur"}]},i=me(()=>W.id?"编辑常见问题":"新增常见问题"),$=me(()=>{if(!M.value||!M.value.currentOrder)return null;try{return JSON.parse(M.value.currentOrder).recycle||null}catch(d){return console.error("解析当前回收订单数据失败:",d),null}}),S=me(()=>{if(!M.value||!M.value.currentOrder)return null;try{return JSON.parse(M.value.currentOrder).flea_market||null}catch(d){return console.error("解析当前二手商城订单数据失败:",d),null}}),A=me(()=>{if(!M.value||!M.value.currentOrder)return null;try{return JSON.parse(M.value.currentOrder).market||null}catch(d){return console.error("解析当前集市订单数据失败:",d),null}}),B=me(()=>{if(!M.value||!M.value.currentOrder)return null;try{return JSON.parse(M.value.currentOrder).engineer||null}catch(d){return console.error("解析当前工程师订单数据失败:",d),null}}),ve=async d=>{try{P.value=null,M.value=null,H.value=!0;const a=await At(d);a.code===200?(Array.isArray(a.data)?(P.value=a.data[0],M.value=a.data[1]):P.value=a.data,ie()):(P.value=null,console.error("获取用户信息失败:",a.message))}catch(a){P.value=null,console.error("获取用户信息出错:",a)}finally{H.value=!1}},ie=()=>{if(J.value=null,Y.value=null,z.value=null,K.value=null,!M.value){console.log("没有用户订单详情数据");return}if(M.value.orderList)try{const d=JSON.parse(M.value.orderList);d.recycle&&(J.value=d.recycle),d.flea_market&&(Y.value=d.flea_market),d.market&&(z.value=d.market),d.engineer&&(K.value=d.engineer)}catch(d){console.error("解析订单列表数据失败:",d)}},T=()=>{if(v.currentSession&&v.currentSession.source)switch(v.currentSession.source){case 1:return"小智回收";case 2:return"二手商城";case 3:return"小智集市";case 4:return"工程师";default:return""}return""},se=d=>{if(navigator.clipboard)navigator.clipboard.writeText(d).then(()=>{h.success("已复制到剪贴板")}).catch(()=>{h.error("复制失败")});else{const a=document.createElement("textarea");a.value=d,document.body.appendChild(a),a.select();try{document.execCommand("copy"),h.success("已复制到剪贴板")}catch{h.error("复制失败")}document.body.removeChild(a)}},we=d=>{const a=[];return d.province&&a.push(d.province),d.city&&a.push(d.city),d.district&&a.push(d.district),d.address&&a.push(d.address),a.join(" ")},O=d=>({0:"待处理",1:"已接单",2:"处理中",3:"已完成",4:"已取消"})[d]||"未知状态",_e=()=>{R("close")};Ye(()=>v.currentSession,d=>{d&&d.userId?F.value!==d.userId&&(F.value=d.userId,ve(d.userId)):(F.value=null,P.value=null,M.value=null)},{immediate:!0});const ee=async()=>{try{ue.value=!0;const d=await Ot();if(d.code===200){let re=d.data||[];if(x.category&&(re=re.filter(ce=>ce.category===x.category)),x.keyword){const ce=x.keyword.toLowerCase();re=re.filter(ge=>ge.question&&ge.question.toLowerCase().includes(ce)||ge.answer&&ge.answer.toLowerCase().includes(ce))}ne.value=re.length,re.sort((ce,ge)=>(ce.sort||0)-(ge.sort||0));const $e=(x.pageNum-1)*x.pageSize,qe=$e+x.pageSize;j.value=re.slice($e,qe)}else console.error("加载常见问题失败:",d.message)}catch(d){console.error("获取FAQ列表出错:",d)}finally{ue.value=!1}},Oe=p(()=>{x.pageNum=1,ee()},300),le=d=>{D.value=d,x.category=d,x.pageNum=1,ee()},Te=d=>{q.value=q.value===d?null:d},Ie=d=>{if(R("insert-to-reply",d.answer),navigator.clipboard)navigator.clipboard.writeText(d.answer).then(()=>{h.success("已复制到输入框和剪贴板")}).catch(()=>{h.success("已复制到输入框，但复制到剪贴板失败")});else{const a=document.createElement("textarea");a.value=d.answer,document.body.appendChild(a),a.select();try{document.execCommand("copy"),h.success("已复制到输入框和剪贴板")}catch{h.success("已复制到输入框，但复制到剪贴板失败")}document.body.removeChild(a)}},mt=d=>{x.pageNum=d,ee()},Le=()=>{D.value="",x.keyword="",x.category="",x.pageNum=1,ee()},Ge=()=>{Ke(),G.value=!0},Xe=d=>{Ke(),Object.assign(W,d),G.value=!0},Ke=()=>{W.id="",W.category="",W.question="",W.answer="",W.sort=0,de.value&&de.value.resetFields()},Ze=async()=>{if(de.value)try{if(await de.value.validate(),W.id){const d=await Ts(W.id,W);if(d.code===200){h.success("修改成功");const a=j.value.findIndex(re=>re.id===W.id);a!==-1&&(j.value[a]=d.data||{...j.value[a],...W})}else h.error(d.message||"修改失败")}else{const d=await Is(W);d.code===200?(h.success("新增成功"),ee()):h.error(d.message||"新增失败")}G.value=!1}catch(d){console.error("表单验证或提交失败",d),h.error("提交失败："+(d.message||"未知错误"))}};function p(d,a){let re;return function(...qe){const ce=()=>{clearTimeout(re),d(...qe)};clearTimeout(re),re=setTimeout(ce,a)}}return rt(()=>{ee()}),(d,a)=>{const re=C("el-skeleton"),$e=C("el-avatar"),qe=C("el-tag"),ce=C("el-icon"),ge=C("el-carousel-item"),Ne=C("el-carousel"),be=C("el-empty"),Re=C("el-input"),Ce=C("el-button"),et=C("el-pagination"),Pe=C("el-option"),tt=C("el-select"),Ve=C("el-form-item"),Be=C("el-form"),st=C("el-dialog"),at=$t("loading");return X.currentSession?(l(),c("div",{key:0,class:ke(["user-panel",{"mobile-show":X.visible}])},[H.value?(l(),c("div",qs,[n(re,{rows:5,animated:""})])):P.value?(l(),c("div",xs,[e("div",Os,[n($e,{size:64,src:X.currentSession.userAvatar||L(He),shape:"circle"},{default:o(()=>[E(u(P.value.nickname?P.value.nickname.substring(0,1):"访"),1)]),_:1},8,["src"]),e("div",Ns,[e("h4",null,[E(u(P.value.nickname||P.value.phone||"访客"+P.value.id)+" ",1),T()?(l(),ae(qe,{key:0,size:"small",effect:"light",class:"datasource-tag"},{default:o(()=>[E(u(T()),1)]),_:1})):g("",!0)]),P.value.phone?(l(),c("p",zs,[e("span",{class:"phone-number",onClick:a[0]||(a[0]=y=>se(P.value.phone)),title:"点击复制手机号"},[n(ce,null,{default:o(()=>[n(L(es))]),_:1}),e("span",null,u(P.value.phone),1)])])):g("",!0)])]),M.value?(l(),c("div",Us,[e("div",Ls,[$.value?(l(),c("div",Rs,[e("div",Ps,[e("div",Vs,[a[12]||(a[12]=e("h5",null,"当前订单",-1)),e("span",{class:"order-code",onClick:a[1]||(a[1]=y=>se($.value.code)),title:"点击复制订单编号"},u($.value.code),1)]),e("div",Bs,[e("div",Fs,[a[13]||(a[13]=e("span",{class:"item-label"},"姓名:",-1)),e("span",Es,u($.value.name),1)]),e("div",Hs,[a[14]||(a[14]=e("span",{class:"item-label"},"号码:",-1)),e("span",Qs,u($.value.mobile),1)]),e("div",Ws,[a[15]||(a[15]=e("span",{class:"item-label"},"价格:",-1)),e("span",Ks,"¥"+u($.value.price),1)]),e("div",js,[a[16]||(a[16]=e("span",{class:"item-label"},"来源:",-1)),e("span",Js,u($.value.source_name),1)]),e("div",Ys,[a[17]||(a[17]=e("span",{class:"item-label"},"状态:",-1)),e("span",Gs,u($.value.status_name),1)]),e("div",Xs,[a[18]||(a[18]=e("span",{class:"item-label"},"地址:",-1)),e("span",Zs,u(we($.value)),1)]),$.value.detail?(l(),c("div",ea,[a[19]||(a[19]=e("span",{class:"item-label"},"备注:",-1)),e("span",ta,u($.value.detail),1)])):g("",!0)])])])):g("",!0),S.value?(l(),c("div",sa,[e("div",aa,[e("div",na,[a[20]||(a[20]=e("h5",null,"当前订单",-1)),e("span",{class:"order-code",onClick:a[2]||(a[2]=y=>se(S.value.code)),title:"点击复制订单编号"},u(S.value.code),1)]),e("div",la,[S.value.name?(l(),c("div",oa,[a[21]||(a[21]=e("span",{class:"item-label"},"姓名:",-1)),e("span",ra,u(S.value.name),1)])):g("",!0),S.value.mobile?(l(),c("div",ia,[a[22]||(a[22]=e("span",{class:"item-label"},"号码:",-1)),e("span",ca,u(S.value.mobile),1)])):g("",!0),S.value.price?(l(),c("div",ua,[a[23]||(a[23]=e("span",{class:"item-label"},"价格:",-1)),e("span",da,"¥"+u(S.value.price),1)])):g("",!0),S.value.status_name?(l(),c("div",va,[a[24]||(a[24]=e("span",{class:"item-label"},"状态:",-1)),e("span",ma,u(S.value.status_name),1)])):g("",!0)])])])):g("",!0),A.value?(l(),c("div",fa,[e("div",pa,[e("div",_a,[a[25]||(a[25]=e("h5",null,"当前订单",-1)),e("span",{class:"order-code",onClick:a[3]||(a[3]=y=>se(A.value.code)),title:"点击复制订单编号"},u(A.value.code),1)]),e("div",ya,[A.value.name?(l(),c("div",ga,[a[26]||(a[26]=e("span",{class:"item-label"},"姓名:",-1)),e("span",ha,u(A.value.name),1)])):g("",!0),A.value.mobile?(l(),c("div",wa,[a[27]||(a[27]=e("span",{class:"item-label"},"号码:",-1)),e("span",ka,u(A.value.mobile),1)])):g("",!0),A.value.price?(l(),c("div",ba,[a[28]||(a[28]=e("span",{class:"item-label"},"价格:",-1)),e("span",Sa,"¥"+u(A.value.price),1)])):g("",!0),A.value.status_name?(l(),c("div",Ta,[a[29]||(a[29]=e("span",{class:"item-label"},"状态:",-1)),e("span",Ia,u(A.value.status_name),1)])):g("",!0)])])])):g("",!0),B.value?(l(),c("div",$a,[e("div",Ca,[e("div",Ma,[a[30]||(a[30]=e("h5",null,"当前订单",-1)),e("span",{class:"order-code",onClick:a[4]||(a[4]=y=>se(B.value.code)),title:"点击复制订单编号"},u(B.value.code),1)]),e("div",Aa,[B.value.name?(l(),c("div",Da,[a[31]||(a[31]=e("span",{class:"item-label"},"姓名:",-1)),e("span",qa,u(B.value.name),1)])):g("",!0),B.value.mobile?(l(),c("div",xa,[a[32]||(a[32]=e("span",{class:"item-label"},"号码:",-1)),e("span",Oa,u(B.value.mobile),1)])):g("",!0),B.value.service_name?(l(),c("div",Na,[a[33]||(a[33]=e("span",{class:"item-label"},"服务:",-1)),e("span",za,u(B.value.service_name),1)])):g("",!0),B.value.status_name?(l(),c("div",Ua,[a[34]||(a[34]=e("span",{class:"item-label"},"状态:",-1)),e("span",La,u(B.value.status_name),1)])):g("",!0)])])])):g("",!0),J.value&&J.value.total>0?(l(),c("div",Ra,[e("h5",null,"小智回收订单列表 (共"+u(J.value.total)+"件)",1),e("div",Pa,[n(Ne,{interval:4e3,height:"150px","indicator-position":"none"},{default:o(()=>[(l(!0),c(ye,null,he(J.value.list,(y,fe)=>(l(),ae(ge,{key:fe},{default:o(()=>[e("div",Va,[e("div",Ba,[e("span",{class:"order-code",onClick:Ae=>se(y.code),title:"点击复制订单编号"},u(y.code),9,Fa)]),e("div",Ea,[e("div",Ha,[a[35]||(a[35]=e("span",{class:"item-label"},"商品名称:",-1)),e("span",Qa,u(y.recover_item.item_cates),1)]),e("div",Wa,[a[36]||(a[36]=e("span",{class:"item-label"},"订单状态:",-1)),e("span",Ka,u(O(y.status)),1)]),e("div",ja,[a[37]||(a[37]=e("span",{class:"item-label"},"预估价格:",-1)),e("span",Ja,"¥"+u(y.price),1)]),e("div",Ya,[a[38]||(a[38]=e("span",{class:"item-label"},"订单类型:",-1)),e("span",Ga,u(y.order_type),1)]),e("div",Xa,[a[39]||(a[39]=e("span",{class:"item-label"},"创建时间:",-1)),e("span",Za,u(y.create_time),1)])])])]),_:2},1024))),128))]),_:1})])])):g("",!0),Y.value&&Y.value.total>0?(l(),c("div",en,[e("h5",null,"二手商城订单列表 (共"+u(Y.value.total)+"件)",1),e("div",tn,[n(Ne,{interval:4e3,height:"150px","indicator-position":"none"},{default:o(()=>[(l(!0),c(ye,null,he(Y.value.list,(y,fe)=>(l(),ae(ge,{key:fe},{default:o(()=>[e("div",sn,[e("div",an,[e("span",{class:"order-code",onClick:Ae=>se(y.code),title:"点击复制订单编号"},u(y.code),9,nn)]),e("div",ln,[y.product_name?(l(),c("div",on,[a[40]||(a[40]=e("span",{class:"item-label"},"商品名称:",-1)),e("span",rn,u(y.product_name),1)])):g("",!0),y.status_name?(l(),c("div",cn,[a[41]||(a[41]=e("span",{class:"item-label"},"订单状态:",-1)),e("span",un,u(y.status_name),1)])):g("",!0),y.price?(l(),c("div",dn,[a[42]||(a[42]=e("span",{class:"item-label"},"价格:",-1)),e("span",vn,"¥"+u(y.price),1)])):g("",!0),y.create_time?(l(),c("div",mn,[a[43]||(a[43]=e("span",{class:"item-label"},"创建时间:",-1)),e("span",fn,u(y.create_time),1)])):g("",!0)])])]),_:2},1024))),128))]),_:1})])])):g("",!0),z.value&&z.value.total>0?(l(),c("div",pn,[e("h5",null,"小智集市订单列表 (共"+u(z.value.total)+"件)",1),e("div",_n,[n(Ne,{interval:4e3,height:"150px","indicator-position":"none"},{default:o(()=>[(l(!0),c(ye,null,he(z.value.list,(y,fe)=>(l(),ae(ge,{key:fe},{default:o(()=>[e("div",yn,[e("div",gn,[e("span",{class:"order-code",onClick:Ae=>se(y.code),title:"点击复制订单编号"},u(y.code),9,hn)]),e("div",wn,[y.product_name?(l(),c("div",kn,[a[44]||(a[44]=e("span",{class:"item-label"},"商品名称:",-1)),e("span",bn,u(y.product_name),1)])):g("",!0),y.status_name?(l(),c("div",Sn,[a[45]||(a[45]=e("span",{class:"item-label"},"订单状态:",-1)),e("span",Tn,u(y.status_name),1)])):g("",!0),y.price?(l(),c("div",In,[a[46]||(a[46]=e("span",{class:"item-label"},"价格:",-1)),e("span",$n,"¥"+u(y.price),1)])):g("",!0),y.create_time?(l(),c("div",Cn,[a[47]||(a[47]=e("span",{class:"item-label"},"创建时间:",-1)),e("span",Mn,u(y.create_time),1)])):g("",!0)])])]),_:2},1024))),128))]),_:1})])])):g("",!0),K.value&&K.value.total>0?(l(),c("div",An,[e("h5",null,"工程师订单列表 (共"+u(K.value.total)+"件)",1),e("div",Dn,[n(Ne,{interval:4e3,height:"150px","indicator-position":"none"},{default:o(()=>[(l(!0),c(ye,null,he(K.value.list,(y,fe)=>(l(),ae(ge,{key:fe},{default:o(()=>[e("div",qn,[e("div",xn,[e("span",{class:"order-code",onClick:Ae=>se(y.code),title:"点击复制订单编号"},u(y.code),9,On)]),e("div",Nn,[y.service_name?(l(),c("div",zn,[a[48]||(a[48]=e("span",{class:"item-label"},"服务类型:",-1)),e("span",Un,u(y.service_name),1)])):g("",!0),y.status_name?(l(),c("div",Ln,[a[49]||(a[49]=e("span",{class:"item-label"},"订单状态:",-1)),e("span",Rn,u(y.status_name),1)])):g("",!0),y.price?(l(),c("div",Pn,[a[50]||(a[50]=e("span",{class:"item-label"},"价格:",-1)),e("span",Vn,"¥"+u(y.price),1)])):g("",!0),y.create_time?(l(),c("div",Bn,[a[51]||(a[51]=e("span",{class:"item-label"},"创建时间:",-1)),e("span",Fn,u(y.create_time),1)])):g("",!0)])])]),_:2},1024))),128))]),_:1})])])):g("",!0)])])):g("",!0)])):(l(),c("div",En,[n(be,{description:"未能获取用户信息"})])),e("div",Hn,[e("div",Qn,[a[52]||(a[52]=e("div",{class:"faq-title"},[e("strong",null,"快捷回复")],-1)),e("div",Wn,[n(Re,{modelValue:x.keyword,"onUpdate:modelValue":a[5]||(a[5]=y=>x.keyword=y),placeholder:"搜索问题...",clearable:"",size:"small",onKeyup:Ct(L(Oe),["enter"]),onClear:Le},{prefix:o(()=>[n(ce,null,{default:o(()=>[n(L(it))]),_:1})]),suffix:o(()=>[x.keyword?(l(),ae(ce,{key:0,class:"search-button",onClick:L(Oe)},{default:o(()=>[n(L(it))]),_:1},8,["onClick"])):g("",!0)]),_:1},8,["modelValue","onKeyup"]),n(Ce,{type:"primary",size:"small",circle:"",onClick:Ge,title:"新增问题",class:"faq-add-btn"},{default:o(()=>[n(ce,null,{default:o(()=>[n(L(ct))]),_:1})]),_:1})])]),e("div",Kn,[e("div",jn,[e("div",{class:ke(["faq-category-item",{active:D.value===""}]),onClick:a[6]||(a[6]=y=>le(""))}," 全部 ",2),(l(!0),c(ye,null,he(U.value,y=>(l(),c("div",{key:y.value,class:ke(["faq-category-item",{active:D.value===y.value}]),onClick:fe=>le(y.value)},u(y.label),11,Jn))),128))])]),Qe((l(),c("div",Yn,[j.value.length===0?(l(),c("div",Gn,[n(be,{description:"暂无常见问题","image-size":48})])):(l(),c("div",Xn,[(l(!0),c(ye,null,he(j.value,(y,fe)=>(l(),c("div",{key:fe,class:"faq-item"},[e("div",{class:"faq-item-header",onClick:Ae=>Te(fe)},[e("div",el,[e("span",null,u(y.question),1)]),n(ce,{class:ke({"is-active":q.value===fe})},{default:o(()=>[n(L(ts))]),_:2},1032,["class"])],8,Zn),Qe(e("div",tl,[e("p",null,u(y.answer),1),e("div",sl,[n(Ce,{type:"warning",link:"",size:"small",onClick:Ae=>Xe(y)},{default:o(()=>[n(ce,null,{default:o(()=>[n(L(Mt))]),_:1}),a[53]||(a[53]=E(" 编辑问题 "))]),_:2,__:[53]},1032,["onClick"]),n(Ce,{type:"primary",link:"",size:"small",onClick:Ae=>Ie(y)},{default:o(()=>[n(ce,null,{default:o(()=>[n(L(dt))]),_:1}),a[54]||(a[54]=E(" 复制到输入框 "))]),_:2,__:[54]},1032,["onClick"])])],512),[[ss,q.value===fe]])]))),128))])),ne.value>x.pageSize?(l(),c("div",al,[n(et,{small:"",background:"",layout:"prev, pager, next",total:ne.value,"page-size":x.pageSize,"current-page":x.pageNum,onCurrentChange:mt},null,8,["total","page-size","current-page"])])):g("",!0)])),[[at,ue.value]])]),X.isMobile?(l(),c("div",{key:3,class:"mobile-close-btn",onClick:_e},[n(ce,null,{default:o(()=>[n(L(vt))]),_:1})])):g("",!0),n(st,{modelValue:G.value,"onUpdate:modelValue":a[11]||(a[11]=y=>G.value=y),title:i.value,width:"80%","destroy-on-close":"","append-to-body":""},{footer:o(()=>[e("span",nl,[n(Ce,{onClick:a[10]||(a[10]=y=>G.value=!1),size:"small"},{default:o(()=>a[55]||(a[55]=[E("取消")])),_:1,__:[55]}),n(Ce,{type:"primary",onClick:Ze,size:"small"},{default:o(()=>a[56]||(a[56]=[E("确定")])),_:1,__:[56]})])]),default:o(()=>[n(Be,{ref_key:"faqFormRef",ref:de,model:W,rules:m,"label-width":"80px",size:"small"},{default:o(()=>[n(Ve,{label:"问题分类",prop:"category"},{default:o(()=>[n(tt,{modelValue:W.category,"onUpdate:modelValue":a[7]||(a[7]=y=>W.category=y),placeholder:"请选择问题分类"},{default:o(()=>[(l(!0),c(ye,null,he(U.value,y=>(l(),ae(Pe,{key:y.value,label:y.label,value:y.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(Ve,{label:"问题",prop:"question"},{default:o(()=>[n(Re,{modelValue:W.question,"onUpdate:modelValue":a[8]||(a[8]=y=>W.question=y),placeholder:"请输入问题",type:"textarea",rows:2},null,8,["modelValue"])]),_:1}),n(Ve,{label:"答案",prop:"answer"},{default:o(()=>[n(Re,{modelValue:W.answer,"onUpdate:modelValue":a[9]||(a[9]=y=>W.answer=y),placeholder:"请输入答案",type:"textarea",rows:5},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])],2)):g("",!0)}}},ol=xe(ll,[["__scopeId","data-v-c17eb9c9"]]);const rl={class:"history-dialog-content"},il={key:0,class:"history-loading"},cl={key:1,class:"history-empty"},ul={key:2,class:"history-message-list"},dl={key:0,class:"history-load-more"},vl={class:"history-message-content"},ml={class:"history-message-sender"},fl={key:0,class:"history-sender-name"},pl={class:"history-message-time"},_l={class:"history-message-body"},yl={key:0,class:"history-message-text"},gl={key:1,class:"history-message-image"},hl={key:2,class:"history-message-text system-message"},wl={class:"system-icon"},kl={class:"dialog-footer"},bl={__name:"HistoryDialog",props:{visible:{type:Boolean,default:!1},sessionId:{type:[String,Number],default:null}},emits:["update:visible","close"],setup(X,{emit:oe}){const v=X,R=oe,P=_([]),M=_(!1),H=_(!1),J=_(1),Y=me({get:()=>v.visible,set:q=>R("update:visible",q)});Ye(()=>v.sessionId,async q=>{q&&v.visible&&await z()}),Ye(()=>v.visible,async q=>{q&&v.sessionId&&(J.value=1,P.value=[],await z())});const z=async()=>{if(v.sessionId){M.value=!0;try{const q=await ut(v.sessionId);if(q.code===200&&Array.isArray(q.data)){const U=q.data.map(D=>(D.from===void 0&&D.senderType!==void 0?D.from=D.senderType:D.from!==void 0&&D.senderType===void 0&&(D.senderType=D.from),D.senderType===3?D.type=3:D.type===void 0&&D.msgType!==void 0?D.type=D.msgType+1:D.type!==void 0&&D.msgType===void 0&&(D.msgType=D.type-1),D)).sort((D,G)=>new Date(D.createdAt).getTime()-new Date(G.createdAt).getTime());P.value=U,H.value=!1}else console.error("加载历史消息失败:",q),h.error(q.message||"加载历史消息失败")}catch(q){console.error("加载历史消息失败",q),h.error("加载历史消息失败")}finally{M.value=!1}}},K=async()=>{if(!(!v.sessionId||M.value)){M.value=!0;try{J.value++;const q=await ut(v.sessionId);q.code===200&&Array.isArray(q.data)&&(H.value=!1)}catch(q){console.error("加载更多历史消息失败",q),h.error("加载更多历史消息失败")}finally{M.value=!1}}},F=q=>q?We(q).format("MM-DD HH:mm"):"",j=q=>q?q.startsWith("http://")||q.startsWith("https://")?q:`/api${q.startsWith("/")?"":"/"}${q}`:"",ne=()=>{R("update:visible",!1),R("close")},ue=()=>{P.value=[],J.value=1,H.value=!1};return(q,x)=>{const U=C("el-skeleton"),D=C("el-empty"),G=C("el-button"),de=C("el-image"),W=C("el-icon"),m=C("el-dialog");return l(),ae(m,{modelValue:Y.value,"onUpdate:modelValue":x[0]||(x[0]=i=>Y.value=i),title:"历史聊天记录",width:"80%","destroy-on-close":"","append-to-body":"",onClosed:ue},{footer:o(()=>[e("span",kl,[n(G,{onClick:ne},{default:o(()=>x[2]||(x[2]=[E("关闭")])),_:1,__:[2]})])]),default:o(()=>[e("div",rl,[M.value?(l(),c("div",il,[n(U,{rows:5,animated:""})])):P.value.length===0?(l(),c("div",cl,[n(D,{description:"暂无聊天记录"})])):(l(),c("div",ul,[H.value?(l(),c("div",dl,[n(G,{type:"primary",link:"",size:"small",loading:M.value,onClick:K},{default:o(()=>x[1]||(x[1]=[E(" 加载更多历史记录 ")])),_:1,__:[1]},8,["loading"])])):g("",!0),(l(!0),c(ye,null,he(P.value,i=>(l(),c("div",{key:i.id,class:ke(["history-message-item",{"history-message-agent":(i.from===1||i.senderType===1)&&i.senderType!==2&&i.currentAgentType!==2,"history-message-user":i.from===0||i.senderType===0,"history-message-system":i.type===3||i.senderType===3,"history-message-ai":i.senderType===2||i.from===1&&i.currentAgentType===2}])},[e("div",vl,[e("div",ml,[i.from===0||i.senderType===0?g("",!0):(l(),c("span",fl,u(i.senderName||""),1)),e("span",pl,u(F(i.createdAt)),1)]),e("div",_l,[i.type===1||i.msgType===0?(l(),c("div",yl,u(i.content),1)):i.type===2||i.msgType===1?(l(),c("div",gl,[n(de,{src:j(i.content),"preview-src-list":[j(i.content)],fit:"cover","z-index":3e3,loading:"lazy"},null,8,["src","preview-src-list"])])):i.type===3||i.senderType===3?(l(),c("div",hl,[e("span",wl,[n(W,null,{default:o(()=>[n(L(Dt))]),_:1})]),e("span",null,u(i.content),1)])):g("",!0)])])],2))),128))]))])]),_:1},8,["modelValue"])}}},Sl=xe(bl,[["__scopeId","data-v-48d59a54"]]);const Tl={class:"session-panel"},Il={class:"panel-header"},$l={class:"header-actions"},Cl={class:"panel-search"},Ml={class:"tab-label"},Al={class:"session-list"},Dl={key:0,class:"empty-state"},ql=["onClick"],xl={class:"session-avatar"},Ol={class:"session-info"},Nl={class:"session-header"},zl={class:"user-nickname"},Ul={class:"session-time"},Ll={class:"session-content"},Rl={class:"last-message"},Pl={class:"session-meta"},Vl={key:1,class:"waiting-time"},Bl={class:"tab-label"},Fl={class:"session-list"},El={key:0,class:"empty-state"},Hl=["onClick"],Ql={class:"session-avatar"},Wl={class:"session-info"},Kl={class:"session-header"},jl={class:"user-nickname"},Jl={class:"session-time"},Yl={class:"session-content"},Gl={class:"last-message"},Xl={class:"session-meta"},Zl={key:1,class:"agent-name"},eo={class:"tab-label"},to={class:"session-list"},so={key:0,class:"empty-state"},ao=["onClick"],no={class:"session-avatar"},lo={class:"session-info"},oo={class:"session-header"},ro={class:"user-nickname"},io={class:"session-time"},co={class:"session-content"},uo={class:"last-message"},vo={class:"session-meta"},mo={__name:"SessionListPanel",props:{sessions:{type:Array,default:()=>[]},currentSession:{type:Object,default:null},searchKeyword:{type:String,default:""},loading:{type:Boolean,default:!1},isDesktop:{type:Boolean,default:!0},visible:{type:Boolean,default:!0}},emits:["select-session","update:search-keyword","refresh-sessions","close","view-history"],setup(X,{emit:oe}){const v=X,R=oe,P=_("waiting"),M=_("");Ye(()=>v.searchKeyword,m=>{M.value=m},{immediate:!0});const H=me(()=>{let m=v.sessions.filter(i=>i.status===0);if(m=m.map(i=>{if(!i.waitingTime||isNaN(Number(i.waitingTime))){const $=new Date(i.lastActiveTime||i.startTime||i.createTime||Date.now()),S=Math.floor((Date.now()-$.getTime())/1e3);return{...i,waitingTime:S>0?S:0}}return i}),v.searchKeyword){const i=v.searchKeyword.toLowerCase();m=m.filter($=>G($)&&G($).toLowerCase().includes(i)||$.lastMessage&&$.lastMessage.toLowerCase().includes(i)||$.userId.toString().includes(i))}return m.sort((i,$)=>new Date($.lastActiveTime||0)-new Date(i.lastActiveTime||0))}),J=me(()=>{let m=v.sessions.filter(i=>i.status===1);if(v.searchKeyword){const i=v.searchKeyword.toLowerCase();m=m.filter($=>G($)&&G($).toLowerCase().includes(i)||$.lastMessage&&$.lastMessage.toLowerCase().includes(i)||$.userId.toString().includes(i))}return m.sort((i,$)=>new Date($.lastActiveTime)-new Date(i.lastActiveTime))}),Y=me(()=>{let m=v.sessions.filter(i=>i.status===3);if(v.searchKeyword){const i=v.searchKeyword.toLowerCase();m=m.filter($=>G($)&&G($).toLowerCase().includes(i)||$.lastMessage&&$.lastMessage.toLowerCase().includes(i)||$.userId.toString().includes(i))}return m.sort((i,$)=>new Date($.lastActiveTime)-new Date(i.lastActiveTime))}),z=m=>{R("select-session",m)},K=()=>{R("update:search-keyword",M.value)},F=()=>{M.value="",R("update:search-keyword","")},j=()=>{R("refresh-sessions")},ne=()=>{R("close")},ue=m=>{P.value=m},q=m=>{if(!m)return"";const i=We(m),$=We();return $.diff(i,"minutes")<1?"刚刚":$.diff(i,"hours")<1?`${$.diff(i,"minutes")}分钟前`:$.diff(i,"days")<1?`${$.diff(i,"hours")}小时前`:$.diff(i,"days")<7?`${$.diff(i,"days")}天前`:i.format("MM-DD HH:mm")},x=m=>{if(!m||m<0)return"0秒";const i=Math.floor(m/3600),$=Math.floor(m%3600/60),S=m%60;return i>0?`${i}小时${$}分钟`:$>0?`${$}分钟${S}秒`:`${S}秒`},U=m=>m.userAvatar?m.userAvatar.startsWith("http")?m.userAvatar:`${{}.VUE_APP_BASE_URL||"/"}${m.userAvatar}`:"/src/assets/images/userAvatar.png",D=m=>{switch(m.status){case 0:return"waiting";case 1:return"active";case 3:return"ai";default:return"offline"}},G=m=>m.userNickname&&m.userNickname.trim()?m.userNickname.trim():m.user&&m.user.nickname&&m.user.nickname.trim()?m.user.nickname.trim():m.user&&m.user.phone&&m.user.phone.trim()?m.user.phone.trim():m.userId?`访客${m.userId}`:"未知用户",de=m=>m.dataSource&&m.dataSource.trim()?m.dataSource.trim():m.datasource&&m.datasource.trim()?m.datasource.trim():m.user&&m.user.dataSource?m.user.dataSource:null,W=m=>{R("view-history",m)};return(m,i)=>{const $=C("el-button"),S=C("el-icon"),A=C("el-input"),B=C("el-badge"),ve=C("el-avatar"),ie=C("el-tag"),T=C("el-tab-pane"),se=C("el-tabs"),we=$t("loading");return l(),c("div",Tl,[e("div",Il,[i[4]||(i[4]=e("h3",null,"会话列表",-1)),e("div",$l,[n($,{onClick:j,icon:L(qt),size:"small",type:"primary",text:""},{default:o(()=>i[2]||(i[2]=[E(" 刷新 ")])),_:1,__:[2]},8,["icon"]),X.isDesktop?g("",!0):(l(),ae($,{key:0,onClick:ne,icon:L(vt),size:"small",type:"danger",text:""},{default:o(()=>i[3]||(i[3]=[E(" 关闭 ")])),_:1,__:[3]},8,["icon"]))])]),e("div",Cl,[n(A,{modelValue:M.value,"onUpdate:modelValue":i[0]||(i[0]=O=>M.value=O),onInput:K,onClear:F,placeholder:"搜索用户昵称、消息内容或用户ID",clearable:"",size:"default"},{prefix:o(()=>[n(S,null,{default:o(()=>[n(L(it))]),_:1})]),_:1},8,["modelValue"])]),n(se,{modelValue:P.value,"onUpdate:modelValue":i[1]||(i[1]=O=>P.value=O),class:"session-tabs",onTabChange:ue},{default:o(()=>[n(T,{name:"waiting"},{label:o(()=>[e("span",Ml,[i[5]||(i[5]=E(" 待处理 ")),H.value.length>0?(l(),ae(B,{key:0,value:H.value.length,max:99,class:"tab-badge"},null,8,["value"])):g("",!0)])]),default:o(()=>[Qe((l(),c("div",Al,[H.value.length===0?(l(),c("div",Dl,[n(S,{class:"empty-icon"},{default:o(()=>[n(L(nt))]),_:1}),i[6]||(i[6]=e("p",null,"暂无待处理会话",-1))])):g("",!0),(l(!0),c(ye,null,he(H.value,O=>{var _e;return l(),c("div",{key:O.id,class:ke(["session-item",{active:((_e=X.currentSession)==null?void 0:_e.id)===O.id}]),onClick:ee=>z(O)},[e("div",xl,[n(ve,{size:45,src:U(O),alt:G(O)||"未知用户"},{default:o(()=>i[7]||(i[7]=[e("img",{src:He,alt:"默认头像"},null,-1)])),_:2,__:[7]},1032,["src","alt"]),e("div",{class:ke(["online-status",D(O)])},null,2)]),e("div",Ol,[e("div",Nl,[e("span",zl,u(G(O)||"未知用户"),1),e("span",Ul,u(q(O.lastActiveTime)),1)]),e("div",Ll,[e("span",Rl,u(O.lastMessage||"暂无消息"),1)]),e("div",Pl,[O.dataSource?(l(),ae(ie,{key:0,size:"small",type:"info"},{default:o(()=>[E(u(O.dataSource),1)]),_:2},1024)):g("",!0),O.waitingTime>0?(l(),c("span",Vl," 等待: "+u(x(O.waitingTime)),1)):g("",!0)])])],10,ql)}),128))])),[[we,X.loading]])]),_:1}),n(T,{name:"active"},{label:o(()=>[e("span",Bl,[i[8]||(i[8]=E(" 进行中 ")),J.value.length>0?(l(),ae(B,{key:0,value:J.value.length,max:99,class:"tab-badge"},null,8,["value"])):g("",!0)])]),default:o(()=>[Qe((l(),c("div",Fl,[J.value.length===0?(l(),c("div",El,[n(S,{class:"empty-icon"},{default:o(()=>[n(L(nt))]),_:1}),i[9]||(i[9]=e("p",null,"暂无进行中会话",-1))])):g("",!0),(l(!0),c(ye,null,he(J.value,O=>{var _e;return l(),c("div",{key:O.id,class:ke(["session-item",{active:((_e=X.currentSession)==null?void 0:_e.id)===O.id}]),onClick:ee=>z(O)},[e("div",Ql,[n(ve,{size:45,src:U(O),alt:G(O)||"未知用户"},{default:o(()=>i[10]||(i[10]=[e("img",{src:He,alt:"默认头像"},null,-1)])),_:2,__:[10]},1032,["src","alt"]),e("div",{class:ke(["online-status",D(O)])},null,2)]),e("div",Wl,[e("div",Kl,[e("span",jl,u(G(O)||"未知用户"),1),e("span",Jl,u(q(O.lastActiveTime)),1)]),e("div",Yl,[e("span",Gl,u(O.lastMessage||"暂无消息"),1)]),e("div",Xl,[O.dataSource?(l(),ae(ie,{key:0,size:"small",type:"success"},{default:o(()=>[E(u(O.dataSource),1)]),_:2},1024)):g("",!0),O.agentName?(l(),c("span",Zl," 客服: "+u(O.agentName),1)):g("",!0)])])],10,Hl)}),128))])),[[we,X.loading]])]),_:1}),n(T,{name:"ai"},{label:o(()=>[e("span",eo,[i[11]||(i[11]=E(" AI会话 ")),Y.value.length>0?(l(),ae(B,{key:0,value:Y.value.length,max:99,class:"tab-badge"},null,8,["value"])):g("",!0)])]),default:o(()=>[Qe((l(),c("div",to,[Y.value.length===0?(l(),c("div",so,[n(S,{class:"empty-icon"},{default:o(()=>[n(L(nt))]),_:1}),i[12]||(i[12]=e("p",null,"暂无AI会话",-1))])):g("",!0),(l(!0),c(ye,null,he(Y.value,O=>{var _e;return l(),c("div",{key:O.id,class:ke(["session-item",{active:((_e=X.currentSession)==null?void 0:_e.id)===O.id}]),onClick:ee=>z(O)},[e("div",no,[n(ve,{size:45,src:U(O),alt:G(O)||"未知用户"},{default:o(()=>i[13]||(i[13]=[e("img",{src:He,alt:"默认头像"},null,-1)])),_:2,__:[13]},1032,["src","alt"]),i[14]||(i[14]=e("div",{class:"online-status ai-status"},null,-1))]),e("div",lo,[e("div",oo,[e("span",ro,u(G(O)||"未知用户"),1),e("span",io,u(q(O.lastActiveTime)),1)]),e("div",co,[e("span",uo,u(O.lastMessage||"暂无消息"),1)]),e("div",vo,[de(O)?(l(),ae(ie,{key:0,size:"small",type:"primary"},{default:o(()=>[E(u(de(O)),1)]),_:2},1024)):g("",!0),n(ie,{size:"small",type:"primary"},{default:o(()=>i[15]||(i[15]=[E("AI服务")])),_:1,__:[15]}),n($,{type:"text",size:"small",icon:L(dt),onClick:Ee(ee=>W(O),["stop"]),title:"查看历史记录",class:"history-btn"},null,8,["icon","onClick"])])])],10,ao)}),128))])),[[we,X.loading]])]),_:1})]),_:1},8,["modelValue"])])}}},fo=xe(mo,[["__scopeId","data-v-919ff7b7"]]);const po={key:0,class:"chat-header"},_o={class:"user-info"},yo={class:"user-details"},go={class:"user-name"},ho={class:"header-actions"},wo={__name:"ChatHeaderToolbar",props:{currentSession:{type:Object,default:null},loading:{type:Boolean,default:!1},userDetail:{type:Object,default:null},messages:{type:Array,default:()=>[]}},emits:["accept-session","refresh-messages","end-chat","transfer-session","view-history"],setup(X,{emit:oe}){const v=X,R=oe,P=me(()=>!v.currentSession||v.currentSession.status!==2),M=me(()=>!v.currentSession||v.currentSession.status!==1),H=me(()=>!v.currentSession||v.currentSession.status!==1&&v.currentSession.status!==3),J=()=>v.currentSession?v.currentSession.userNickname||v.currentSession.user&&v.currentSession.user.nickname||v.currentSession.user&&v.currentSession.user.phone||"访客"+v.currentSession.userId:"未知用户",Y=()=>{if(!v.currentSession)return"";const U=v.currentSession.userAvatar;return U?U.startsWith("http")?U:`${{}.VUE_APP_BASE_URL||"/"}${U}`:"/src/assets/images/userAvatar.png"},z=()=>{if(!v.messages||v.messages.length===0)return null;for(let U=v.messages.length-1;U>=0;U--){const D=v.messages[U];if((D.senderType===0||D.from===0)&&D.datasource)return D.datasource}return v.currentSession&&v.currentSession.datasource?v.currentSession.datasource:null},K=()=>{if(!v.messages||v.messages.length===0)return null;for(let U=v.messages.length-1;U>=0;U--){const D=v.messages[U];if((D.senderType===0||D.from===0)&&D.channel)return D.channel}return v.currentSession&&v.currentSession.channel?v.currentSession.channel:null},F=()=>{if(!v.messages||v.messages.length===0)return null;for(let U=v.messages.length-1;U>=0;U--){const D=v.messages[U];if((D.senderType===0||D.from===0)&&D.scene)return D.scene}return v.currentSession&&v.currentSession.scene?v.currentSession.scene:null},j=()=>{v.currentSession&&R("accept-session",v.currentSession)},ne=()=>{R("refresh-messages")},ue=()=>{R("end-chat")},q=()=>{R("transfer-session")},x=()=>{v.currentSession&&R("view-history",v.currentSession)};return(U,D)=>{const G=C("el-avatar"),de=C("el-tag"),W=C("el-button"),m=C("el-tooltip"),i=C("el-button-group");return X.currentSession?(l(),c("div",po,[e("div",_o,[n(G,{size:32,src:Y(),shape:"square",class:"user-avatar"},{default:o(()=>[E(u(J().substring(0,1)),1)]),_:1},8,["src"]),e("div",yo,[e("span",go,u(J()),1)]),z()?(l(),ae(de,{key:0,size:"small",effect:"light",class:"status-tag datasource-tag"},{default:o(()=>[E(u(z()),1)]),_:1})):g("",!0),K()?(l(),ae(de,{key:1,size:"small",effect:"light",class:"status-tag channel-tag"},{default:o(()=>[E(u(K()),1)]),_:1})):g("",!0),F()?(l(),ae(de,{key:2,size:"small",effect:"light",class:"status-tag scene-tag"},{default:o(()=>[E(u(F()),1)]),_:1})):g("",!0)]),e("div",ho,[n(i,null,{default:o(()=>[n(m,{content:"接入会话",placement:"bottom",disabled:P.value},{default:o(()=>[n(W,{type:"primary",icon:L(as),size:"small",disabled:P.value,onClick:j},null,8,["icon","disabled"])]),_:1},8,["disabled"]),n(m,{content:"刷新消息",placement:"bottom"},{default:o(()=>[n(W,{type:"info",icon:L(qt),size:"small",loading:X.loading,onClick:ne},null,8,["icon","loading"])]),_:1}),n(m,{content:"结束会话",placement:"bottom",disabled:M.value},{default:o(()=>[n(W,{type:"danger",icon:L(ns),size:"small",disabled:M.value,onClick:ue},null,8,["icon","disabled"])]),_:1},8,["disabled"]),n(m,{content:"转接会话",placement:"bottom",disabled:H.value},{default:o(()=>[n(W,{type:"info",icon:L(ls),size:"small",disabled:H.value,onClick:q},null,8,["icon","disabled"])]),_:1},8,["disabled"]),n(m,{content:"查看历史记录",placement:"bottom",disabled:!X.currentSession},{default:o(()=>[n(W,{type:"info",icon:L(dt),size:"small",disabled:!X.currentSession,onClick:x},null,8,["icon","disabled"])]),_:1},8,["disabled"])]),_:1})])])):g("",!0)}}},ko=xe(wo,[["__scopeId","data-v-aae00c78"]]);const bo={class:"message-input-panel"},So={class:"quick-replies"},To={class:"reply-list"},Io={class:"quick-reply-actions"},$o={class:"message-input-container"},Co={class:"left-tools-wrapper"},Mo={class:"message-input-wrapper"},Ao={class:"send-btn-wrapper"},Do={key:0,class:"image-preview"},qo={class:"preview-header"},xo={class:"preview-content"},Oo={class:"dialog-footer"},No={__name:"MessageInputPanel",props:{currentSession:{type:Object,default:null},quickReplies:{type:Array,default:()=>[]},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1}},emits:["send-message","insert-reply","paste-image","update-quick-replies"],setup(X,{expose:oe,emit:v}){const R=X,P=v,M=_(""),H=_(null),J=_(""),Y=_(!1),z=_(!1),K=_("add"),F=_(null),j=_({content:""}),ne=me(()=>(M.value.trim()||H.value)&&!Y.value&&!R.disabled&&!R.loading),ue=S=>{try{if(!S||!S.raw){h.error("请选择有效的图片文件");return}const A=10*1024*1024;if(S.raw.size>A){h.error("图片文件大小不能超过10MB");return}if(!S.raw.type.startsWith("image/")){h.error("请选择图片文件");return}H.value=S.raw,J.value=URL.createObjectURL(S.raw),P("paste-image",S.raw)}catch(A){console.error("图片上传处理失败:",A),h.error("图片处理失败，请重试")}},q=S=>{M.value=S,P("insert-reply",S)},x=()=>{if(ne.value)try{Y.value=!0,P("send-message",{text:M.value.trim(),image:H.value,imageUrl:J.value}),M.value="",U()}catch(S){console.error("发送消息处理失败:",S),h.error("发送失败，请重试")}finally{Y.value=!1}},U=()=>{try{J.value&&URL.revokeObjectURL(J.value),H.value=null,J.value=""}catch(S){console.error("清理图片预览失败:",S)}},D=S=>{try{if(S.ctrlKey){const A=S.target,B=A.selectionStart,ve=A.selectionEnd;M.value=M.value.substring(0,B)+`
`+M.value.substring(ve),setTimeout(()=>{A.selectionStart=A.selectionEnd=B+1},0)}else x()}catch(A){console.error("键盘事件处理失败:",A)}},G=async S=>{var A;try{const B=(A=S.clipboardData)==null?void 0:A.items;if(!B)return;for(const ve of B)if(ve.type.startsWith("image/")){const ie=ve.getAsFile();if(ie){if(ie.size>10485760){h.error("粘贴的图片文件大小不能超过10MB");return}H.value=ie,J.value=URL.createObjectURL(ie),P("paste-image",ie)}break}}catch(B){console.error("粘贴处理失败:",B),h.error("粘贴失败，请重试")}};os(()=>{U()}),oe({insertText:S=>{S&&(M.value?M.value=M.value+" "+S:M.value=S)}});const W=()=>{if(R.quickReplies.length>=25){h.warning("快捷回复已达到最大数量限制(25个)");return}z.value=!0,K.value="add",j.value={content:""}},m=()=>{if(j.value.content.trim())try{const S=j.value.content.trim();if(K.value==="add"){if(R.quickReplies.includes(S)){h.warning("已存在相同内容的快捷回复");return}if(R.quickReplies.length>=25){h.warning("快捷回复已达到最大数量限制(25个)");return}const A=[...R.quickReplies,S];P("update-quick-replies",A),M.value=S,h.success("添加快捷回复成功")}else{const A=[...R.quickReplies];if(A.splice(F.value,1),A.includes(S)){h.warning("已存在相同内容的快捷回复");return}const B=[...R.quickReplies];B[F.value]=S,P("update-quick-replies",B),h.success("编辑快捷回复成功")}}catch(S){console.error("提交回复失败:",S),h.error("操作失败，请重试")}finally{z.value=!1,j.value.content="",F.value=null}},i=(S,A)=>{z.value=!0,K.value="edit",F.value=S,j.value={content:A}},$=S=>{Me.confirm("确定要删除这条快捷回复吗？","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const A=[...R.quickReplies];A.splice(S,1),P("update-quick-replies",A),h.success("删除成功")}).catch(()=>{})};return(S,A)=>{const B=C("el-button"),ve=C("el-tooltip"),ie=C("el-icon"),T=C("el-scrollbar"),se=C("el-upload"),we=C("el-input"),O=C("el-image"),_e=C("el-form-item"),ee=C("el-form"),Oe=C("el-dialog");return l(),c("div",bo,[e("div",So,[n(T,null,{default:o(()=>[e("div",To,[(l(!0),c(ye,null,he(X.quickReplies,(le,Te)=>(l(),c("div",{key:Te,class:"quick-reply-item"},[n(ve,{content:le,placement:"top",disabled:le.length<15},{default:o(()=>[n(B,{size:"small",onClick:Ie=>q(le),round:""},{default:o(()=>[E(u(le.length>15?le.substring(0,15)+"...":le),1)]),_:2},1032,["onClick"])]),_:2},1032,["content","disabled"]),e("div",Io,[n(B,{class:"action-btn",type:"primary",size:"small",circle:"",onClick:Ee(Ie=>i(Te,le),["stop"])},{default:o(()=>[n(ie,null,{default:o(()=>[n(L(Mt))]),_:1})]),_:2},1032,["onClick"]),n(B,{class:"action-btn",type:"danger",size:"small",circle:"",onClick:Ee(Ie=>$(Te),["stop"])},{default:o(()=>[n(ie,null,{default:o(()=>[n(L(kt))]),_:1})]),_:2},1032,["onClick"])])]))),128)),X.quickReplies.length<25?(l(),ae(B,{key:0,size:"small",type:"primary",circle:"",onClick:W},{default:o(()=>[n(ie,null,{default:o(()=>[n(L(ct))]),_:1})]),_:1})):(l(),ae(ve,{key:1,content:"已达到最大数量限制(25个)",placement:"top"},{default:o(()=>[n(B,{size:"small",type:"info",circle:"",disabled:""},{default:o(()=>[n(ie,null,{default:o(()=>[n(L(ct))]),_:1})]),_:1})]),_:1}))])]),_:1})]),e("div",$o,[e("div",Co,[n(ve,{content:"发送图片",placement:"top"},{default:o(()=>[n(se,{class:"image-upload",action:"","auto-upload":!1,"show-file-list":!1,"on-change":ue,accept:"image/*"},{default:o(()=>[n(B,{type:"primary",text:"",icon:L(xt),class:"tool-btn"},null,8,["icon"])]),_:1})]),_:1})]),e("div",Mo,[n(we,{modelValue:M.value,"onUpdate:modelValue":A[0]||(A[0]=le=>M.value=le),type:"textarea",rows:3,placeholder:"请输入消息，按Enter发送，Ctrl+Enter换行",resize:"none",disabled:X.disabled,onKeydown:Ct(Ee(D,["prevent"]),["enter"]),onPaste:G},null,8,["modelValue","disabled","onKeydown"])]),e("div",Ao,[n(B,{type:"primary",disabled:!ne.value,loading:Y.value,onClick:x,class:"send-btn",icon:L(rs)},{default:o(()=>A[4]||(A[4]=[E(" 发送 ")])),_:1,__:[4]},8,["disabled","loading","icon"])])]),H.value?(l(),c("div",Do,[e("div",qo,[A[5]||(A[5]=e("span",null,"已选择图片",-1)),n(B,{type:"danger",text:"",icon:L(kt),onClick:U},null,8,["icon"])]),e("div",xo,[n(O,{src:J.value,fit:"contain"},null,8,["src"])])])):g("",!0),n(Oe,{modelValue:z.value,"onUpdate:modelValue":A[3]||(A[3]=le=>z.value=le),title:K.value==="add"?"添加快捷回复":"编辑快捷回复",width:"30%","close-on-click-modal":!1},{footer:o(()=>[e("span",Oo,[n(B,{onClick:A[2]||(A[2]=le=>z.value=!1)},{default:o(()=>A[6]||(A[6]=[E("取消")])),_:1,__:[6]}),n(B,{type:"primary",onClick:m,disabled:!j.value.content.trim()},{default:o(()=>A[7]||(A[7]=[E(" 确认 ")])),_:1,__:[7]},8,["disabled"])])]),default:o(()=>[n(ee,{model:j.value,"label-width":"80px",onSubmit:Ee(m,["prevent"])},{default:o(()=>[n(_e,{label:"回复内容",required:""},{default:o(()=>[n(we,{modelValue:j.value.content,"onUpdate:modelValue":A[1]||(A[1]=le=>j.value.content=le),type:"textarea",rows:3,placeholder:"请输入回复内容",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},zo=xe(No,[["__scopeId","data-v-11bfb0ed"]]);const Uo={class:"chat-container"},Lo={class:"chat-main"},Ro={key:2,class:"no-session-selected"},Po={key:0,class:"loading-messages"},Vo={key:1,class:"empty-messages"},Bo={key:2,class:"message-list"},Fo={key:0,class:"load-more"},Eo={class:"date-label"},Ho={key:0,class:"message-avatar"},Qo={key:1,class:"message-avatar"},Wo={key:2,class:"message-avatar"},Ko={class:"message-content"},jo={class:"message-sender"},Jo={key:0,class:"sender-name"},Yo={class:"message-time"},Go={key:0,class:"message-text"},Xo={key:1,class:"message-image"},Zo={class:"image-error"},er={class:"image-loading"},tr={key:2,class:"message-text system-message"},sr={class:"system-icon"},ar={key:3,class:"message-status"},nr={key:2,class:"session-ended"},lr={key:3,class:"session-waiting"},or={__name:"Chat",setup(X){const oe=_(!0),v=_(!1),R=_(!1),P=()=>{oe.value=window.innerWidth>=768,oe.value?(v.value=!0,R.value=!0):(v.value=!1,R.value=!1)};is(()=>{P()}),rt(()=>{window.addEventListener("resize",P)}),lt(()=>{window.removeEventListener("resize",P)});const M=async()=>{faqLoading.value=!0;try{const t=await Ot();if(t.code===200){let r=t.data||[];if(faqQueryParams.category&&(r=r.filter(b=>b.category===faqQueryParams.category)),faqQueryParams.keyword){const b=faqQueryParams.keyword.toLowerCase();r=r.filter(w=>w.question&&w.question.toLowerCase().includes(b)||w.answer&&w.answer.toLowerCase().includes(b))}faqTotal.value=r.length,r.sort((b,w)=>(b.sort||0)-(w.sort||0));const k=(faqQueryParams.pageNum-1)*faqQueryParams.pageSize,N=k+faqQueryParams.pageSize;faqList.value=r.slice(k,N)}else console.error("加载常见问题失败:",t.message)}catch(t){console.error("加载FAQ列表失败",t)}finally{faqLoading.value=!1}},H=()=>{oe.value||(v.value=!v.value,v.value&&(R.value=!1))},J=()=>{oe.value?R.value=!R.value:(R.value=!R.value,R.value&&(v.value=!1))},Y=cs(),z=us(),K=bt(),F=ds(),j=_(null);_("waiting");const ne=_(""),ue=_("waiting"),q=_([]),x=me(()=>K.userInfo),U=_(null),D=_(null),G=_(null),de=_(null),W=_(null),m=_(null),i=_(null),$=_(null),S=_(null),A=_(null),B=_(!1),ve=_(!1),ie=_(null);_([]),_(!1),_(!1),_(1),_(20);const T=_([]),se=_(!1),we=_(!1),O=_(!1),_e=_(1);_(20);const ee=_(null);_("");const Oe=_(null),le=_(""),Te=_(!1),Ie=_(null),Le=_([...["您好，很高兴为您服务","请问还有其他问题吗？","感谢您的咨询，祝您生活愉快","请稍等，我查询一下信息","已为您处理，请核实"]]),Ge=()=>{try{const t=localStorage.getItem("agent_quick_replies");if(t){const s=JSON.parse(t);Array.isArray(s)&&s.length>0&&(Le.value=s)}}catch(t){console.error("加载快捷回复失败:",t)}},Xe=t=>{try{localStorage.setItem("agent_quick_replies",JSON.stringify(t))}catch(s){console.error("保存快捷回复失败:",s)}},Ke=t=>{Le.value=t,Xe(t)},Ze=me(()=>{const t=new Set;return T.value.forEach(s=>{if(s.createdAt){const r=new Date(s.createdAt);t.add(r.toDateString())}}),Array.from(t)});me(()=>{let t=q.value.filter(s=>s.status===0);if(t=t.map(s=>{if(!s.waitingTime||isNaN(Number(s.waitingTime))){const r=new Date(s.lastActiveTime||s.startTime||s.createTime||Date.now()),k=Math.floor((Date.now()-r.getTime())/1e3);return{...s,waitingTime:k>0?k:0}}return s}),ne.value){const s=ne.value.toLowerCase();t=t.filter(r=>r.userNickname&&r.userNickname.toLowerCase().includes(s)||r.lastMessage&&r.lastMessage.toLowerCase().includes(s)||r.userId.toString().includes(s))}return t.sort((s,r)=>new Date(r.lastActiveTime||0)-new Date(s.lastActiveTime||0))}),me(()=>{let t=q.value.filter(s=>s.status===1);if(ne.value){const s=ne.value.toLowerCase();t=t.filter(r=>r.userNickname&&r.userNickname.toLowerCase().includes(s)||r.lastMessage&&r.lastMessage.toLowerCase().includes(s)||r.userId.toString().includes(s))}return t.sort((s,r)=>new Date(r.lastActiveTime)-new Date(s.lastActiveTime))}),me(()=>{let t=q.value.filter(s=>s.status===3);if(ne.value){const s=ne.value.toLowerCase();t=t.filter(r=>r.userNickname&&r.userNickname.toLowerCase().includes(s)||r.lastMessage&&r.lastMessage.toLowerCase().includes(s)||r.userId.toString().includes(s))}return t.sort((s,r)=>new Date(r.lastActiveTime)-new Date(s.lastActiveTime))});const p=_(null),d=_(!1);rt(async()=>{var t;if(K.loadFromStorage(),Ge(),!K.isLoggedIn||!K.isAgent){z.push("/login");return}if(((t=K.userInfo)==null?void 0:t.status)!==1&&Me.confirm("您当前处于离线状态，需要切换为在线状态才能处理会话，是否切换？","提示",{confirmButtonText:"切换为在线",cancelButtonText:"保持离线",type:"warning"}).then(()=>{Ae(1)}).catch(()=>{h.info("您选择了保持离线状态，将无法接入新会话")}),F.setInChatPage(!0),F.connected||F.initWebSocket(),await be(),a=setInterval(async()=>{F.isInChatPage&&await be(!1)},1e4),window.addEventListener("chat-message-received",ce),window.addEventListener("chat-transfer-notification",ge),Y.query.id){const s=Y.query.id,r=q.value.find(k=>k.id===s);if(r)Pe(r),ue.value=r.status===1?"active":"waiting";else try{const k=await Re(s);k.code===200&&k.data?(q.value.push(k.data),Pe(k.data),ue.value=k.data.status===1?"active":"waiting"):(h.warning(k.message||"该会话不存在或已结束"),z.replace({path:"/agent/chat",query:{}}))}catch(k){console.error("加载指定会话失败",k),h.error("加载指定会话失败"),z.replace({path:"/agent/chat",query:{}})}}yt(),je=setInterval(()=>{yt()},3e4),M()}),lt(()=>{console.log("聊天组件卸载，清理资源"),a&&(clearInterval(a),a=null),re&&(clearInterval(re),re=null),window.removeEventListener("chat-message-received",ce),window.removeEventListener("chat-transfer-notification",ge),F.setInChatPage(!1),je&&(clearInterval(je),je=null)});let a=null,re=null,$e=null;const qe=t=>{const s=t.filter(r=>r.status===0);if(s.length>0){const r=s.map(b=>{if(!b.waitingTime||isNaN(Number(b.waitingTime))){const w=new Date(b.startTime||b.createTime||b.lastActiveTime||Date.now()),Q=Math.floor((Date.now()-w.getTime())/1e3);return{...b,waitingTime:Q>0?Q:0}}return b});if(s.map(b=>b.id).some(b=>!lastKnownPendingSessionIds.value.includes(b))&&window.showAgentRequestAlert&&r.length>0){const b=r[0];window.showAgentRequestAlert({sessionId:b.id,data:{userId:b.userId},content:b.lastMessage||"新的待处理会话"})}}},ce=async t=>{var b,w;if(console.log("Chat.vue检测到新消息，消息详情:",t.detail),!(t.detail.isNewMessage!==!1)){console.log("非新消息，不处理");return}const r=t.detail.id,k=Number(t.detail.sessionId),N=Number(t.detail.from);if(p.value&&Number(p.value.id)===k){console.log("新消息属于当前会话，添加到消息列表");let Q=!1;if(N===1){const I=T.value.findIndex(f=>f.id&&f.id.toString().startsWith("temp_")&&f.content===t.detail.content&&f.from===N&&Math.abs(new Date(f.createdAt||Date.now()).getTime()-Date.now())<1e4);I!==-1?(console.log("找到对应的临时消息，更新消息ID和状态:",I),T.value[I].id=r,T.value[I].status=1,T.value[I].createdAt=t.detail.createdAt||new Date().toISOString(),Q=!0,setTimeout(()=>{T.value[I]&&(T.value[I].status=2)},3e3)):Q=T.value.some(f=>f.id===r)}else Q=T.value.some(I=>I.id===r||I.content===t.detail.content&&I.from===N&&Math.abs(new Date(I.createdAt||Date.now()).getTime()-Date.now())<5e3);if(Q)console.log("消息已存在或已更新临时消息，不重复添加");else{console.log("消息不存在，添加新消息到列表:",t.detail);const I={id:r||`received_${Date.now()}`,sessionId:k,from:N,senderType:N,senderId:N===0?p.value.userId:(b=x.value)==null?void 0:b.id,receiverId:N===0?(w=x.value)==null?void 0:w.id:p.value.userId,content:t.detail.content,type:t.detail.type===2||t.detail.msgType===1?2:1,msgType:t.detail.type===2||t.detail.msgType===1?1:0,createdAt:t.detail.createdAt||new Date().toISOString(),status:1,_standardized:!0};if(T.value.push(I),I.type===2||I.msgType===1?p.value.lastMessage="[图片]":p.value.lastMessage=I.content,p.value.lastActiveTime=I.createdAt,await Ue(),ze(),N===0)try{console.log("更新用户消息已读状态"),await St(p.value.id,0)}catch(f){console.error("更新消息已读状态失败",f)}}}else console.log("新消息不属于当前会话或当前无选中会话"),$e||($e=setTimeout(async()=>{await be(!1),$e=null},1e3))},ge=t=>{console.log("收到转接通知:",t.detail),Ne(),be(!1)},Ne=()=>{try{const t=new Audio("/audio/notification.mp3");t.volume=.6,t.play().catch(s=>{console.warn("播放通知音效失败:",s);const r=new Audio("/audio/notification.mp3");r.volume=.6,r.play().catch(k=>console.error("备用音频播放失败:",k))})}catch(t){console.error("播放提示音失败:",t)}},be=async(t=!0)=>{try{t&&(se.value=!0);const s=await vs(K.userInfo.id);if(s.code===200){const r=new Map;q.value.forEach(I=>{I.lastMessage&&r.set(I.id,{lastMessage:I.lastMessage,lastActiveTime:I.lastActiveTime})});const{pendingSessions:k,inProgressSessions:N,aiReplySessions:b}=s.data;let w=[];k&&k.records&&(w=w.concat(k.records)),N&&N.records&&(w=w.concat(N.records)),b&&b.records&&(w=w.concat(b.records));const Q=w.filter(I=>!I.lastMessage||I.lastMessage==="暂无消息");if(Q.length>0)try{const I=Q.map(async te=>{try{const Z=await ms(te.id);if(Z.code===200&&Z.data){const pe=Z.data;return{sessionId:te.id,lastMessage:pe.msgType===1?"[图片]":pe.content,lastActiveTime:pe.createdAt}}}catch(Z){console.error(`获取会话 ${te.id} 的最后消息失败:`,Z)}return null}),f=await Promise.all(I);w=w.map(te=>{const Z=f.find(pe=>pe&&pe.sessionId===te.id);return Z?{...te,lastMessage:Z.lastMessage,lastActiveTime:Z.lastActiveTime}:te})}catch(I){console.error("获取最后消息失败:",I)}if(w=w.map(I=>{const f=r.get(I.id);return f&&(!I.lastMessage||I.lastMessage==="暂无消息")?{...I,lastMessage:f.lastMessage,lastActiveTime:f.lastActiveTime}:I}),q.value=w,qe(w),p.value){const I=p.value.id,f=q.value.find(te=>te.id===I);f&&(p.value.lastMessage&&(!f.lastMessage||f.lastMessage==="暂无消息")&&(f.lastMessage=p.value.lastMessage,f.lastActiveTime=p.value.lastActiveTime),p.value=f)}}}catch(s){console.error("加载会话列表失败",s)}finally{t&&(se.value=!1)}},Re=async t=>{try{try{const s=await Tt(t);if(s.code===200&&s.data)return s}catch(s){console.error("API调用失败，无法获取会话信息:",s)}return console.log("会话不存在或无法访问:",t),{code:404,message:"会话不存在或已结束"}}catch(s){return console.error("获取会话信息失败",s),{code:500,message:"获取会话信息失败"}}},Ce=()=>{j.value&&(clearInterval(j.value),j.value=null)},et=()=>{var t;Ce(),((t=p.value)==null?void 0:t.status)===3&&(j.value=setInterval(async()=>{await pt()},6e4))};lt(()=>{Ce()});const Pe=async t=>{var s;if(Ce(),t.id===((s=p.value)==null?void 0:s.id))if(t.status===0)console.log("相同会话但为待处理状态，需要显示接入弹框");else{console.log("已经选中该会话且不是待处理状态，不重新加载消息");return}console.log("选择新会话:",t.id,t.userNickname||t.userId),T.value=[],p.value=t,z.push({path:"/agent/chat",query:{id:t.id}},{replace:!0}),await tt(t.userId),t.status===0&&(t.userId||t.userNickname||t.user&&(t.user.nickname||t.user.phone)?Me.confirm(`是否接入会话 "${t.userNickname||"访客"+t.userId}"？`,"接入会话",{confirmButtonText:"接入",cancelButtonText:"稍后处理",type:"info"}).then(()=>{fe(t)}).catch(()=>{}):(console.warn("会话缺少有效的用户信息，不显示接入提示"),h.warning("会话信息不完整，请刷新会话列表")));try{const r=await Tt(t.id);if(r.code===200&&r.data){const k={...t,...r.data,unreadCount:t.unreadCount};p.value=k}}catch(r){console.error("获取完整会话信息失败:",r)}if(console.log("准备加载会话消息:",t.id),await Be(t.id,!0),t.status===3&&(console.log("启动AI会话自动刷新"),et()),t.unreadCount>0){t.unreadCount=0;try{await St(t.id,0)}catch(r){console.error("更新消息已读状态失败",r)}}},tt=async t=>{if(!t){U.value=null;return}B.value=!0;try{const s=await At(t);console.log("获取用户详细信息:",s),s.code===200&&s.data?Array.isArray(s.data)?(U.value=s.data[0],s.data.length>1&&(D.value=s.data[1],Ve())):U.value=s.data:(h.warning("获取用户详细信息失败"),U.value=null)}catch(s){console.error("获取用户详细信息失败:",s),h.warning("获取用户详细信息失败"),U.value=null}finally{B.value=!1}},Ve=()=>{try{if(G.value=null,de.value=null,W.value=null,m.value=null,i.value=null,$.value=null,S.value=null,A.value=null,!D.value)return;if(D.value.currentOrder)try{const t=JSON.parse(D.value.currentOrder);t.recycle&&(de.value=t.recycle),t.flea_market&&(m.value=t.flea_market),t.market&&($.value=t.market),t.engineer&&(A.value=t.engineer)}catch(t){console.error("解析当前订单数据失败:",t)}if(D.value.orderList)try{const t=JSON.parse(D.value.orderList);t.recycle&&(G.value=t.recycle),t.flea_market&&(W.value=t.flea_market),t.market&&(i.value=t.market),t.engineer&&(S.value=t.engineer)}catch(t){console.error("解析订单列表数据失败:",t)}}catch(t){console.error("解析订单数据失败:",t)}},Be=async(t,s=!1)=>{if(t){se.value=!0;try{console.log("开始加载会话消息, 会话ID:",t,"强制刷新:",s),s&&(console.log("强制刷新，先清空消息列表"),T.value=[]);const r=await ut(t);if(console.log("加载消息响应:",r),r.code===200&&Array.isArray(r.data)){if(console.log("API返回消息数量:",r.data.length),r.data.length===0&&!s){console.log("返回消息数量为0，疑似缓存问题，将在100ms后重试"),setTimeout(()=>Be(t,!0),100);return}const k=new Set;T.value.forEach(f=>{f.id&&k.add(f.id.toString())});const N=r.data.map(f=>(f.from===void 0&&f.senderType!==void 0?f.from=f.senderType:f.from!==void 0&&f.senderType===void 0&&(f.senderType=f.from),f.senderType===3?f.type=3:f.type===void 0&&f.msgType!==void 0?f.type=f.msgType+1:f.type!==void 0&&f.msgType===void 0&&(f.msgType=f.type-1),f._standardized=!0,f)),b=N.filter(f=>{if(!f.id)return!0;const te=f.id.toString();return k.has(te)?(console.log("过滤掉重复消息:",te),!1):(k.add(te),!0)});console.log("加载的消息总数:",N.length),console.log("去重后的消息数:",b.length);const w=T.value.filter(f=>f.status===0||f.id&&f.id.toString().startsWith("temp_"));w.length>0&&console.log("保留",w.length,"条临时消息");const Q=b.sort((f,te)=>new Date(f.createdAt).getTime()-new Date(te.createdAt).getTime());T.value=[...Q,...w],console.log("加载到的消息数量(去重后):",T.value.length);const I=T.value.filter(f=>f.from===0||f.senderType===0);if(console.log("用户发送的消息数量:",I.length),await Ue(),ze(),setTimeout(()=>{ze()},100),p.value&&T.value.length>0){const f=T.value[T.value.length-1];p.value.lastMessage=f.type===1||f.msgType===0?f.content:"[图片]",p.value.lastActiveTime=f.createdAt}}else console.error("加载消息失败:",r),h.error(r.message||"加载消息失败")}catch(r){console.error("加载会话消息失败",r),h.error("加载消息失败")}finally{se.value=!1}}},st=async()=>{var t,s,r,k,N;if(!(!((t=p.value)!=null&&t.id)||we.value)){we.value=!0;try{await new Promise(Z=>setTimeout(Z,800));const b=_e.value+1,w=[],Q=Math.floor(Math.random()*10)+5,I=Math.random()>.7,f=(s=ee.value)==null?void 0:s.querySelector(".message-item"),te=f==null?void 0:f.getBoundingClientRect().top;for(let Z=0;Z<Q;Z++){const pe=Math.random()>.5,Fe=!pe&&Math.random()<.2?"system":Math.random()<.2?"image":"text",V=new Date(new Date(((r=T.value[0])==null?void 0:r.createdAt)||Date.now()).getTime()-(Z+1)*6e4-Math.random()*5e4);let De="";Fe==="text"?pe?De=["您好，请问有人在吗？","我想咨询一下你们的产品","价格是多少？","有优惠活动吗？"][Math.floor(Math.random()*4)]:De=["您好，有什么可以帮到您的？","感谢您的咨询","正在为您查询","请问还有其他问题吗？"][Math.floor(Math.random()*4)]:Fe==="image"?De=`https://picsum.photos/300/200?random=${Math.floor(Math.random()*1e3)}`:Fe==="system"&&(De=["会话已创建","等待客服接入","系统消息：今日客服繁忙"][Math.floor(Math.random()*3)]),w.push({id:`msg_old_${p.value.id}_${Z}`,sessionId:p.value.id,senderId:pe?p.value.userId:((k=x.value)==null?void 0:k.id)||"agent",senderType:pe?"user":"agent",type:Fe,content:De,status:2,createdAt:V.toISOString()})}if(w.sort((Z,pe)=>new Date(Z.createdAt)-new Date(pe.createdAt)),T.value=[...w,...T.value],O.value=I,_e.value=b,await Ue(),te&&f){const Z=(N=ee.value)==null?void 0:N.querySelector(".message-item");if(Z){const Se=Z.getBoundingClientRect().top-te;ee.value.scrollTop+=Se}}}catch(b){console.error("加载更多消息失败",b),h.error("加载更多消息失败")}finally{we.value=!1}}},at=t=>T.value.filter(s=>s.createdAt?new Date(s.createdAt).toDateString()===t:!1),y=t=>{const s=new Date(t),r=new Date;if(s.toDateString()===r.toDateString())return"今天";const k=new Date(r);return k.setDate(k.getDate()-1),s.toDateString()===k.toDateString()?"昨天":Math.floor((r-s)/(1e3*60*60*24))<7?["星期日","星期一","星期二","星期三","星期四","星期五","星期六"][s.getDay()]:We(s).format("YYYY年MM月DD日")},fe=async t=>{try{const r=bt().agentId;if(!r){h.error("获取客服ID失败，请重新登录");return}const k=await fs(t.id,r);k.code===200?(h.success("成功接入会话"),t.status=1,ue.value="active",await be()):h.error(k.message||"接入会话失败")}catch(s){console.error("接入会话失败",s),h.error("接入会话失败")}},Ae=async t=>{var s;if((s=K.userInfo)!=null&&s.id)try{await K.updateAgentStatus(K.userInfo.id,t),h.success(t===1?"已切换为在线状态":"已切换为离线状态")}catch(r){console.error("更新状态失败",r),h.error("切换状态失败")}},Nt=t=>t?We(t).format("MM-DD HH:mm:ss"):"",zt=()=>{if(!p.value||p.value.status!==1){h.warning("没有可结束的会话");return}Me.confirm("是否要先添加解决方案再结束会话？","提示",{confirmButtonText:"添加解决方案",cancelButtonText:"直接结束会话",type:"info",distinguishCancelAndClose:!0,closeOnClickModal:!1}).then(()=>{d.value=!0}).catch(t=>{t==="cancel"&&ft()})},ft=()=>{!p.value||p.value.status!==1||Me.confirm("确定要结束当前会话吗？","结束会话",{confirmButtonText:"结束",cancelButtonText:"取消",type:"warning"}).then(async()=>{var t,s;try{const r=await ps(p.value.id,1);if(r.code===200){h.success("会话已结束"),p.value.status=0;try{await F.sendMessage({type:1,from:1,senderId:(t=x.value)==null?void 0:t.id,receiverId:p.value.userId,sessionId:p.value.id,content:"感谢您的咨询，您可继续和AI客服沟通",senderType:1,msgType:0,systemFlag:!0,timestamp:Date.now()}),console.log("已发送会话结束通知给用户")}catch(N){console.error("发送会话结束消息失败:",N)}const k={id:`msg_system_${Date.now()}`,sessionId:p.value.id,senderId:((s=x.value)==null?void 0:s.id)||"system",from:1,senderType:"system",type:"system",content:"感谢您的咨询，您可继续和AI客服沟通",status:1,createdAt:new Date().toISOString()};T.value.push(k),await be()}else h.error(r.message||"结束会话失败")}catch(r){console.error("结束会话失败",r),h.error("结束会话失败")}}).catch(()=>{})},Ut=()=>{!p.value||p.value.status!==1&&p.value.status!==3||Me.confirm("确定要转接当前会话吗？","转接会话",{confirmButtonText:"转接",cancelButtonText:"取消",type:"info"}).then(async()=>{try{const t=await _s();if(!t.data||!Array.isArray(t.data)||t.data.length===0){h.warning("当前没有在线客服可供选择");return}const s=t.data;Me.confirm(Je("div",{style:"width: 100%"},[Je("div",{style:"margin-bottom: 15px"},"请选择要转接的客服："),Je("select",{id:"agent-selector",style:"width: 100%; padding: 8px; border-radius: 4px; border: 1px solid #dcdfe6;"},s.map(r=>{var N;const k=r.id===((N=x.value)==null?void 0:N.id);return Je("option",{value:r.id},`${r.name||"未命名客服"}${k?" (当前客服)":""}${r.status===1?" (在线)":""}`)}))]),"选择转接客服",{confirmButtonText:"确认转接",cancelButtonText:"取消",type:"info",beforeClose:async(r,k,N)=>{var b;if(r==="confirm"){const w=document.getElementById("agent-selector"),Q=w?parseInt(w.value):null;if(!Q){h.error("请选择要转接的客服");return}if(Q===((b=x.value)==null?void 0:b.id)&&!await Me.confirm("您选择的是当前客服，确定要继续转接吗？","转接确认",{confirmButtonText:"继续转接",cancelButtonText:"取消",type:"warning"}).catch(()=>!1))return;k.confirmButtonLoading=!0;try{console.log("转接会话:",p.value.id,Q);const I=await ys(p.value.id,Q);if(I.code===200&&I.data){h.success("转接成功");const f=s.find(Z=>Z.id===Q),te={id:`system-${Date.now()}`,sessionId:p.value.id,content:`会话已转接给客服 ${f?f.name||"未命名客服":""}`,from:2,senderType:2,status:1,createdAt:new Date().toISOString()};T.value.push(te),await be()}else h.error(I.message||"转接失败")}catch(I){console.error("转接失败",I),h.error("转接失败")}finally{k.confirmButtonLoading=!1,N()}}else N()}}).catch(()=>{})}catch(t){console.error("获取在线客服列表失败",t),h.error("获取在线客服列表失败")}}).catch(()=>{})},ze=()=>{ee.value&&(console.log("执行滚动到底部"),ee.value.scrollTop=ee.value.scrollHeight,setTimeout(()=>{ee.value&&(ee.value.scrollTop=ee.value.scrollHeight+1e3)},50),setTimeout(()=>{ee.value&&(ee.value.scrollTop=ee.value.scrollHeight+1e3)},300))},Lt=()=>{le.value&&URL.revokeObjectURL(le.value),Oe.value=null,le.value=""},Rt=async t=>{var b;const{text:s,image:r,imageUrl:k}=t;if(!s.trim()&&!r||Te.value)return;if(!p.value||p.value.status!==1){h.warning("只能在进行中的会话中发送消息");return}Te.value=!0;const N=`temp_${Date.now()}`;try{const w=r?2:1,Q=w===1?s:"",I={id:N,sessionId:p.value.id,senderId:(b=x.value)==null?void 0:b.id,from:1,senderType:1,receiverId:p.value.userId,type:w,content:w===1?s:le.value||"[图片上传中...]",status:0,createdAt:new Date().toISOString()};T.value.push(I),await Ue(),ze(),w===1?await Pt(p.value.id,s,p.value.userId,N):w===2&&await Vt(p.value.id,r,p.value.userId,N),p.value.lastMessage=w===1?s:"[图片]",p.value.lastActiveTime=new Date().toISOString(),Lt()}catch(w){console.error("发送消息失败",w),h.error("发送消息失败");const Q=T.value.find(I=>I.id===N);Q&&(Q.status=-1)}finally{Te.value=!1}},Pt=async(t,s,r,k)=>{var N;try{const b={type:1,from:1,senderId:(N=x.value)==null?void 0:N.id,receiverId:r,sessionId:t,content:s,timestamp:Date.now(),scene:Ht(),datasource:Wt(),channel:Qt(),collectionName:Kt()};await F.sendMessage(b),console.log("发送WebSocket消息成功:",b);const w=T.value.find(Q=>Q.id===k);return w?w.status=1:console.warn("未找到要更新的临时消息:",k),{code:200}}catch(b){console.error("发送文本消息失败",b);const w=T.value.find(Q=>Q.id===k);throw w&&(w.status=-1),b}},Vt=async(t,s,r,k)=>{var N;try{const b=new FormData;b.append("file",s),b.append("sessionId",t),b.append("senderType","1");const w=await fetch("https://jms.bearhome.cn/api/file/upload",{method:"POST",headers:{Authorization:`Bearer ${K.token}`},body:b});if(!w.ok)throw new Error("图片上传失败，服务器响应: "+w.status);const Q=await w.json();if(Q.statusCode!==200)throw new Error(Q.errorInfo||"图片上传失败");const I=Q.data,f=T.value.find(Z=>Z.id===k);f&&(console.log("更新临时图片消息URL:",k,I),f.content=I,f.status=1);const te={type:2,from:1,senderId:(N=x.value)==null?void 0:N.id,receiverId:r,sessionId:t,content:I,timestamp:Date.now(),scene:"默认应用场景",datasource:"默认数据源",channel:"Pc",collectionName:"默认查询知识库集合"};return await F.sendMessage(te),console.log("发送WebSocket图片消息成功:",te),{code:200,data:{url:I}}}catch(b){console.error("发送图片消息失败",b);const w=T.value.find(Q=>Q.id===k);throw w&&(w.status=-1),h.error("图片发送失败: "+(b.message||"未知错误")),b}},Bt=async t=>{var N,b;d.value=!1,h.success("解决方案添加成功");const s=(t==null?void 0:t.description)||"客服已添加解决方案",k=((t==null?void 0:t.isSolved)!==void 0?t.isSolved:1)===1?`客服已将此问题标记为已解决，解决方案：${s}`:`客服已添加解决方案：${s}`;try{await F.sendMessage({type:1,from:1,senderId:(N=x.value)==null?void 0:N.id,receiverId:p.value.userId,sessionId:p.value.id,content:k,senderType:1,msgType:0,systemFlag:!0,timestamp:Date.now()});const w={id:`msg_solution_${Date.now()}`,sessionId:p.value.id,senderId:((b=x.value)==null?void 0:b.id)||"system",from:1,senderType:"system",type:"system",content:k,status:1,createdAt:new Date().toISOString()};T.value.push(w),p.value&&(p.value.lastMessage=k,p.value.lastActiveTime=w.createdAt),await Ue(),ze(),Me.confirm("解决方案已添加，是否要结束当前会话？","提示",{confirmButtonText:"结束会话",cancelButtonText:"继续会话",type:"info"}).then(()=>{ft()}).catch(()=>{})}catch(w){console.error("发送解决方案消息失败:",w),h.error("发送解决方案消息失败")}},pt=async()=>{if(!p.value){h.warning("请先选择一个会话");return}try{se.value=!0,setTimeout(async()=>{try{await Be(p.value.id,!0),await Ue(),ze()}catch(t){console.error("刷新消息失败:",t),h.error("刷新消息失败")}finally{se.value=!1}},100)}catch(t){console.error("刷新消息失败:",t),h.error("刷新消息失败"),se.value=!1}},_t=t=>{if(!t)return"";if(t.startsWith("blob:")||t.startsWith("http://")||t.startsWith("https://"))return t},Ft=t=>{},Et=t=>{console.log("图片加载成功:",t)},Ht=()=>{if(!T.value||T.value.length===0)return null;for(let t=T.value.length-1;t>=0;t--){const s=T.value[t];if((s.senderType===0||s.from===0)&&s.scene)return s.scene}return null},Qt=()=>{if(!T.value||T.value.length===0)return null;for(let t=T.value.length-1;t>=0;t--){const s=T.value[t];if((s.senderType===0||s.from===0)&&s.channel)return s.channel}return null},Wt=()=>{if(!T.value||T.value.length===0)return null;for(let t=T.value.length-1;t>=0;t--){const s=T.value[t];if((s.senderType===0||s.from===0)&&s.datasource)return s.datasource}return null},Kt=()=>{if(!T.value||T.value.length===0)return null;for(let t=T.value.length-1;t>=0;t--){const s=T.value[t];if((s.senderType===0||s.from===0)&&s.collectionName)return s.collectionName}return null},jt=_(!1);let je=null;const yt=()=>{const t={isWebSocketConnected:F.connected,isPolling:F.isPolling,wsReadyState:F.ws?F.ws.readyState:"no-websocket",reconnectAttempts:F.reconnectAttempts,currentMode:F.connected?"websocket":F.isPolling?"polling":"none"};console.log("当前连接状态:",t),jt.value=t.isPolling,!t.isWebSocketConnected&&!t.isPolling&&F.currentConversationId&&(console.log("没有活动连接，尝试重新建立WebSocket连接"),F.initWebSocket(),setTimeout(()=>{!F.connected&&F.currentConversationId&&(console.log("WebSocket连接失败，尝试使用最佳连接模式"),F.useOptimalConnectionMode())},5e3))},gt=async t=>{event.stopPropagation(),ie.value=t.id,ve.value=!0},ht=t=>{try{Ie.value&&Ie.value.insertText?Ie.value.insertText(t):console.warn("MessageInputPanel组件引用不可用")}catch(s){console.error("插入回复失败:",s)}},Jt=()=>{R.value=!1},Yt=t=>{ne.value=t},Gt=()=>{v.value=!1},Xt=t=>{console.log("粘贴图片:",t)};return(t,s)=>{var f,te,Z,pe;const r=C("el-icon"),k=C("el-button"),N=C("el-empty"),b=C("el-skeleton"),w=C("el-avatar"),Q=C("el-image"),I=C("el-alert");return l(),c("div",Uo,[n(fo,{sessions:q.value,"current-session":p.value,"search-keyword":ne.value,loading:se.value,"is-desktop":oe.value,visible:v.value,onSelectSession:Pe,"onUpdate:searchKeyword":Yt,onRefreshSessions:be,onClose:Gt,onViewHistory:gt},null,8,["sessions","current-session","search-keyword","loading","is-desktop","visible"]),e("div",Lo,[oe.value?g("",!0):(l(),c("div",{key:0,class:"mobile-menu-btn",onClick:H},[n(r,null,{default:o(()=>[n(L(gs))]),_:1})])),!oe.value&&p.value?(l(),c("div",{key:1,class:"mobile-user-btn",onClick:J},[n(r,null,{default:o(()=>[n(L(hs))]),_:1})])):g("",!0),p.value?(l(),c(ye,{key:3},[n(ko,{"current-session":p.value,loading:se.value,"user-detail":U.value,messages:T.value,onAcceptSession:fe,onRefreshMessages:pt,onEndChat:zt,onTransferSession:Ut,onViewHistory:gt},null,8,["current-session","loading","user-detail","messages"]),d.value?(l(),ae(Ds,{key:0,sessionId:(f=p.value)==null?void 0:f.id,onSuccess:Bt,onCancel:s[1]||(s[1]=Se=>d.value=!1)},null,8,["sessionId"])):g("",!0),e("div",{class:"message-container",ref_key:"messageContainer",ref:ee},[se.value?(l(),c("div",Po,[n(b,{rows:3,animated:""})])):T.value.length===0?(l(),c("div",Vo,[n(N,{description:"暂无消息记录"})])):(l(),c("div",Bo,[O.value?(l(),c("div",Fo,[n(k,{type:"primary",link:"",size:"small",loading:we.value,onClick:st},{default:o(()=>s[5]||(s[5]=[E(" 加载更多 ")])),_:1,__:[5]},8,["loading"])])):g("",!0),(l(!0),c(ye,null,he(Ze.value,(Se,Fe)=>(l(),c("div",{key:Se,class:"message-date-divider"},[e("div",Eo,[e("span",null,u(y(Se)),1)]),(l(!0),c(ye,null,he(at(Se),V=>{var De;return l(),c("div",{key:V.id,class:ke(["message-item",{"message-agent":(V.from===1||V.senderType===1)&&V.senderType!==2&&V.currentAgentType!==2,"message-user":V.from===0||V.senderType===0,"message-system":V.type===3||V.senderType===3,"message-ai":V.senderType===2||V.from===1&&V.currentAgentType===2}])},[V.from===0||V.senderType===0?(l(),c("div",Ho,[n(w,{size:40,src:p.value.userAvatar||L(He),shape:"circle"},{default:o(()=>[E(u(p.value.userNickname?p.value.userNickname.substring(0,1):"访"),1)]),_:1},8,["src"])])):V.senderType===2||V.from===1&&V.currentAgentType===2?(l(),c("div",Qo,[n(w,{size:40,src:L(ws),shape:"circle"},{default:o(()=>s[6]||(s[6]=[E(" AI ")])),_:1,__:[6]},8,["src"])])):V.from===1||V.senderType===1?(l(),c("div",Wo,[n(w,{size:40,src:((De=x.value)==null?void 0:De.avatar)||L(ks),shape:"circle"},{default:o(()=>{var wt;return[E(u((wt=x.value)!=null&&wt.name?x.value.name.substring(0,1):"客"),1)]}),_:1},8,["src"])])):g("",!0),e("div",Ko,[e("div",jo,[V.from===0||V.senderType===0?g("",!0):(l(),c("span",Jo,u(V.senderName||""),1)),e("span",Yo,u(Nt(V.createdAt)),1)]),e("div",{class:ke(["message-body",{"status-sending":V.status===0}])},[V.type===1||V.msgType===0?(l(),c("div",Go,u(V.content),1)):V.type===2||V.msgType===1?(l(),c("div",Xo,[n(Q,{src:_t(V.content),"preview-src-list":[_t(V.content)],fit:"cover","z-index":3e3,loading:"lazy",onError:()=>Ft(),onLoad:()=>Et(V)},{error:o(()=>[e("div",Zo,[n(r,null,{default:o(()=>[n(L(xt))]),_:1}),s[7]||(s[7]=e("span",null,"图片加载失败",-1))])]),placeholder:o(()=>[e("div",er,[n(r,{class:"is-loading"},{default:o(()=>[n(L(It))]),_:1}),s[8]||(s[8]=e("span",null,"加载中...",-1))])]),_:2},1032,["src","preview-src-list","onError","onLoad"])])):V.type===3||V.senderType===3?(l(),c("div",tr,[e("span",sr,[n(r,null,{default:o(()=>[n(L(Dt))]),_:1})]),e("span",null,u(V.content),1)])):g("",!0),V.from===1||V.senderType===1?(l(),c("div",ar,[V.status===0?(l(),ae(r,{key:0},{default:o(()=>[n(L(It))]),_:1})):V.status===1?(l(),ae(r,{key:1},{default:o(()=>[n(L(bs))]),_:1})):V.status===2?(l(),ae(r,{key:2},{default:o(()=>[n(L(Ss))]),_:1})):g("",!0)])):g("",!0)],2)])],2)}),128))]))),128))]))],512),((te=p.value)==null?void 0:te.status)===1?(l(),ae(zo,{key:1,ref_key:"messageInputRef",ref:Ie,"current-session":p.value,"quick-replies":Le.value,disabled:se.value,loading:Te.value,onSendMessage:Rt,onInsertReply:ht,onPasteImage:Xt,onUpdateQuickReplies:Ke},null,8,["current-session","quick-replies","disabled","loading"])):((Z=p.value)==null?void 0:Z.status)===0?(l(),c("div",nr,[n(I,{title:"未开始对话",type:"info",closable:!1,center:"","show-icon":""},{default:o(()=>s[9]||(s[9]=[E(" 未开始对话，无法发送新消息 ")])),_:1})])):((pe=p.value)==null?void 0:pe.status)===2?(l(),c("div",lr,[n(I,{title:"等待接入",type:"warning",closable:!1,center:"","show-icon":""},{default:o(()=>[s[11]||(s[11]=E(" 请先接入会话才能发送消息 ")),n(k,{type:"primary",size:"small",onClick:s[2]||(s[2]=Se=>fe(p.value)),round:""},{default:o(()=>s[10]||(s[10]=[E(" 接入会话 ")])),_:1,__:[10]})]),_:1})])):g("",!0)],64)):(l(),c("div",Ro,[n(N,{description:"请选择或接入一个会话"},{default:o(()=>[n(k,{type:"primary",onClick:s[0]||(s[0]=Se=>ue.value="waiting"),round:""},{default:o(()=>s[4]||(s[4]=[E("查看待处理会话")])),_:1,__:[4]})]),_:1})]))]),n(ol,{"current-session":p.value,visible:R.value,"is-mobile":!oe.value,onClose:Jt,onInsertToReply:ht},null,8,["current-session","visible","is-mobile"]),n(Sl,{visible:ve.value,"onUpdate:visible":s[3]||(s[3]=Se=>ve.value=Se),"session-id":ie.value},null,8,["visible","session-id"]),oe.value?g("",!0):(l(),c("div",{key:0,class:"mobile-close-btn",onClick:J},[n(r,null,{default:o(()=>[n(L(vt))]),_:1})]))])}}},ur=xe(or,[["__scopeId","data-v-fc6b60ee"]]);export{ur as default};
