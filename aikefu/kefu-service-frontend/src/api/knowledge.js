import request from '@/utils/request'

// 知识库集合名称映射
const collectionNameMapping = {
  'recycle_knowledge': '小智回收知识库',
  'flea_market_knowledge': '小智二手商城知识库',
  'market_knowledge': '小智集市知识库',
  'engineer_knowledge': '工程师知识库',
  'information': '默认知识库'
};

/**
 * 获取知识库集合的中文显示名称
 * @param {string} code 集合编码
 * @returns {string} 中文显示名称
 */
export function getCollectionDisplayName(code) {
  if (!code) return '未知知识库';
  return collectionNameMapping[code] || `${code}知识库`;
}

// 获取集合列表
export async function listCollections() {
  try {
    const response = await fetch('https://berhomellm.cpolar.cn/collections/list', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`请求失败: ${response.status}`);
    }
    
    const data = await response.json();
    console.log('集合列表原始响应:', data);
    
    // 处理响应格式
    if (data && Array.isArray(data.collections)) {
      // 如果collections是字符串数组，转换为对象数组
      if (data.collections.length > 0 && typeof data.collections[0] === 'string') {
        return {
          collections: data.collections.map(name => ({ 
            name,
            displayName: getCollectionDisplayName(name)
          }))
        };
      }
      return data;
    } else if (data && Array.isArray(data)) {
      // 如果直接返回数组，进行格式化
      if (data.length > 0 && typeof data[0] === 'string') {
        return {
          collections: data.map(name => ({ 
            name, 
            displayName: getCollectionDisplayName(name) 
          }))
        };
      }
      return { collections: data };
    }
    
    // 如果无法识别格式，构造一个默认的返回值
    return { collections: [] };
  } catch (error) {
    console.error('获取集合列表失败:', error);
    throw error;
  }
}

// 添加文档
export async function addDocuments(documents) {
  try {
    const response = await fetch('https://berhomellm.cpolar.cn/documents/add', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(documents)
    });
    
    if (!response.ok) {
      throw new Error(`请求失败: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('添加文档失败:', error);
    throw error;
  }
}

// 删除文档
export async function deleteDocuments(ids, collectionName) {
  try {
    const response = await fetch('https://berhomellm.cpolar.cn/documents/delete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ids,
        collection_name: collectionName
      })
    });
    
    if (!response.ok) {
      throw new Error(`请求失败: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('删除文档失败:', error);
    throw error;
  }
}

// 更新文档
export async function updateDocuments(documents, collectionName) {
  try {
    const response = await fetch('https://berhomellm.cpolar.cn/documents/update', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        documents,
        collection_name: collectionName
      })
    });
    
    if (!response.ok) {
      throw new Error(`请求失败: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('更新文档失败:', error);
    throw error;
  }
}

// 获取文档列表（分页）
export async function listDocuments(page = 1, pageSize = 10, collectionName) {
  try {
    console.log('请求文档列表参数:', { page, pageSize, collectionName });
    const response = await fetch('https://berhomellm.cpolar.cn/documents/list', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        page,
        page_size: pageSize,
        collection_name: collectionName
      })
    });
    
    if (!response.ok) {
      throw new Error(`请求失败: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('获取文档列表失败:', error);
    throw error;
  }
}

// 查询文档（测试检索效果）
export async function queryDocuments(queryText, nResults = 3, collectionName) {
  try {
    const response = await fetch('https://berhomellm.cpolar.cn/query', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query_text: queryText,
        n_results: nResults,
        collection_name: collectionName
      })
    });
    
    if (!response.ok) {
      throw new Error(`请求失败: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('查询文档失败:', error);
    throw error;
  }
}

// 创建知识库集合
export async function createCollection(collectionName) {
  try {
    const response = await fetch('https://berhomellm.cpolar.cn/collections/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        collection_name: collectionName
      })
    });
    
    if (!response.ok) {
      throw new Error(`请求失败: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('创建集合失败:', error);
    throw error;
  }
} 