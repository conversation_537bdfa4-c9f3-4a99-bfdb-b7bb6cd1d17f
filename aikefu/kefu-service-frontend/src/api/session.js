import request from '@/utils/request'

/**
 * 根据ID获取会话详情
 * @param {number} id - 会话ID
 * @returns {Promise}
 */
export function getSessionById(id) {
  return request({
    url: `/session/${id}`,
    method: 'get'
  })
}

/**
 * 根据会话ID获取消息列表
 * @param {number} sessionId - 会话ID
 * @returns {Promise}
 */
export function getMessagesBySessionId(sessionId) {
  return request({
    url: `/message/session/${sessionId}`,
    method: 'get'
  })
} 