import request from '@/utils/request'

// 添加常见问题
export function addFaq(data) {
  return request({
    url: '/faq/add',
    method: 'post',
    data
  })
}

// 更新常见问题
export function updateFaq(id, data) {
  return request({
    url: `/faq/${id}`,
    method: 'put',
    data
  })
}

// 删除常见问题
export function deleteFaq(id) {
  return request({
    url: `/faq/${id}`,
    method: 'delete'
  })
}

// 根据ID查询常见问题
export function getFaqById(id) {
  return request({
    url: `/faq/${id}`,
    method: 'get'
  })
}

// 根据分类查询常见问题列表
export function getFaqsByCategory(category) {
  return request({
    url: `/faq/category/${category}`,
    method: 'get'
  })
}

// 获取所有常见问题
export function getAllFaqs() {
  return request({
    url: '/faq/list',
    method: 'get'
  })
} 