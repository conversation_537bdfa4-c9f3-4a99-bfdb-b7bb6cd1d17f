import request from '@/utils/request'

// 创建会话
export function createSession(data) {
  console.log('API调用createSession，参数:', data)
  return request({
    url: '/session/create',
    method: 'post',
    params: { 
      userId: data.userId,
      assignHuman: data.assignHuman || false,
      channel: data.channel,
      datasource: data.datasource,
      collection_name: data.collection_name,
      scene: data.scene
    }
  }).catch(error => {
    console.error('创建会话API错误:', error)
    if (error.response) {
      console.error('状态码:', error.response.status)
      console.error('响应数据:', error.response.data)
    }
    throw error
  })
}

// 创建AI客服会话
export function createAISession(data) {
  console.log('API调用createAISession，参数:', data)
  return request({
    url: '/session/create-ai',
    method: 'post',
    params: { 
      userId: data.userId,
      channel: data.channel,
      datasource: data.datasource,
      collection_name: data.collection_name,
      scene: data.scene
    }
  }).catch(error => {
    console.error('创建AI会话API错误:', error)
    if (error.response) {
      console.error('状态码:', error.response.status)
      console.error('响应数据:', error.response.data)
    }
    throw error
  })
}

// 获取会话详情
export function getSessionById(id) {
  return request({
    url: `/session/${id}`,
    method: 'get'
  })
}

// 分配客服
export function assignAgent(id) {
  return request({
    url: `/session/${id}/assign`,
    method: 'put'
  })
}

/**
 * 手动分配客服
 * @param {number} id - 会话ID
 * @param {number} agentId - 客服ID
 * @returns {Promise} - API响应
 */
export function assignAgentManually(id, agentId) {
  return request({
    url: `/session/${id}/assign/${agentId}`,
    method: 'put'
  })
}

// 关闭会话
export function closeSession(id, closedBy) {
  return request({
    url: `/session/${id}/close`,
    method: 'put',
    params: { closedBy }
  })
}

// 获取用户的会话列表
export function getSessionsByUserId(userId) {
  return request({
    url: `/session/user/${userId}`,
    method: 'get'
  })
}

// 获取客服的会话列表
export function getSessionsByAgentId(params) {
  // 如果参数是数字或字符串类型，则视为agentId
  if (typeof params === 'string' || typeof params === 'number') {
    return request({
      url: `/session/agent/list/${params}`,
      method: 'get'
    })
  }
  
  // 否则视为参数对象，从中提取agentId和其他参数
  const { agentId, ...otherParams } = params
  
  return request({
    url: `/session/agent/${agentId}`,
    method: 'get',
    params: otherParams
  })
}

// 获取客服的活跃会话
export function getActiveSessionsByAgentId(agentId) {
  // 如果参数是数字或字符串类型，则视为agentId
  if (typeof agentId === 'string' || typeof agentId === 'number') {
    return request({
      url: `/session/agent/${agentId}/active`,
      method: 'get'
    })
  }
  
  // 否则视为参数对象，从中提取agentId和其他参数
  const { id, ...otherParams } = agentId
  
  return request({
    url: `/session/agent/${id}/active`,
    method: 'get',
    params: otherParams
  })
}

// 获取排队中的会话
export function getQueueSessions() {
  return request({
    url: '/session/queue',
    method: 'get'
  })
}

// 发送消息
export function sendMessage(data, isFile = false) {
  // 判断是否为文件上传
  if (isFile) {
    return request({
      url: '/message/upload',
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data' // 设置文件上传的Content-Type
      }
    })
  }
  
  // 普通文本消息
  return request({
    url: '/message/send',
    method: 'post',
    data
  })
}

// 获取会话的消息列表
export function getMessagesBySessionId(sessionId) {
  return request({
    url: `/message/session/${sessionId}`,
    method: 'get',
    params: {
      _t: Date.now() // 添加时间戳防止缓存
    }
  })
}

// 分页获取会话的消息
export function getMessagesByPage(sessionId, page = 1, size = 20) {
  return request({
    url: `/message/session/${sessionId}/page`,
    method: 'get',
    params: { page, size }
  })
}

// 获取会话最新一条消息
export function getLatestMessage(sessionId) {
  return request({
    url: `/message/session/${sessionId}/latest`,
    method: 'get'
  })
}

// 按类型统计消息数量
export function countMessagesByType(sessionId, msgType) {
  return request({
    url: `/message/session/${sessionId}/count`,
    method: 'get',
    params: { msgType }
  })
}

// 按时间范围查询消息
export function getMessagesByTimeRange(sessionId, startTime, endTime) {
  return request({
    url: `/message/session/${sessionId}/time-range`,
    method: 'get',
    params: { startTime, endTime }
  })
}

// 条件搜索消息
export function searchMessages(params) {
  return request({
    url: '/message/search',
    method: 'get',
    params
  })
}

// 更新消息已读状态
export function updateReadStatus(sessionId, senderType) {
  return request({
    url: `/message/session/${sessionId}/read`,
    method: 'put',
    params: { senderType }
  })
}

// 获取未读消息数量
export function countUnreadMessages(sessionId, senderType) {
  return request({
    url: `/message/session/${sessionId}/unread`,
    method: 'get',
    params: { senderType }
  })
}

// 接入会话
export function acceptChatSession(sessionId, agentId) {
  return request({
    url: `/session/${sessionId}/accept`,
    method: 'put',
    params: { agentId }
  })
}

// 用户提交会话评价
export function evaluateSession(sessionId, data) {
  return request({
    url: `/session/${sessionId}/evaluate`,
    method: 'post',
    params: data
  })
}

// 客服添加解决方案接口
export function addSolutionDescription(sessionId, data) {
  return request({
    url: `/session/${sessionId}/solution`,
    method: 'post',
    params: data
  })
}

// 获取已解决的会话列表
export function getSolvedSessions() {
  return request({
    url: '/session/solved',
    method: 'get'
  })
}

// 按满意度等级获取会话列表
export function getSessionsBySatisfactionLevel(level) {
  return request({
    url: `/session/satisfaction/${level}`,
    method: 'get'
  })
}

// 获取指定状态的会话列表
export function getSessionsByStatus(status, agentId) {
  return request({
    url: `/session/list/${status}`,
    method: 'get',
    params: { agentId }
  })
}

// 更新消息状态(已读、未读)
export function updateMessageStatus(data) {
  return request({
    url: '/chat/message/status',
    method: 'put',
    data
  })
}

// 获取数据源下拉选项
export function getDatasourceOptions() {
  return request({
    url: '/session/options/datasource',
    method: 'get'
  })
}

// 获取知识库集合下拉选项
export function getCollectionOptions() {
  return request({
    url: '/session/options/collection',
    method: 'get'
  })
}

// 获取对话场景下拉选项
export function getSceneOptions() {
  return request({
    url: '/session/options/scene',
    method: 'get'
  })
}


// 获取最近用户消息
export function getRecentUserMessages() {
  return request({
    url: '/message/recent-user-messages',
    method: 'get'
  })
} 