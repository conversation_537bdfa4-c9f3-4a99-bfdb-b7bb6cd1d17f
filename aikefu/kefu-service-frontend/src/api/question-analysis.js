import request from '@/utils/request'

/**
 * 获取分类统计数据
 * 
 * @param {Object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export function getCategoryStats(params) {
  return request({
    url: '/question-analysis/category-stats',
    method: 'get',
    params
  })
}

/**
 * 获取热门问题列表
 * 
 * @param {Object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export function getHotQuestions(params) {
  return request({
    url: '/question-analysis/hot-questions',
    method: 'get',
    params
  })
}

/**
 * 获取问题趋势数据
 * 
 * @param {Object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export function getQuestionTrend(params) {
  return request({
    url: '/question-analysis/question-trend',
    method: 'get',
    params
  })
}

/**
 * 获取关键词统计
 * 
 * @param {Object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export function getKeywordStats(params) {
  return request({
    url: '/question-analysis/keyword-stats',
    method: 'get',
    params
  })
}

/**
 * 将热门问题添加到FAQ
 * 
 * @param {Object} data 问题数据
 * @returns {Promise} 请求Promise
 */
export function addToFaq(data) {
  return request({
    url: '/faq/add',
    method: 'post',
    data
  })
} 