import request from '@/utils/request'

// 客服登录
export function login(data) {
  return request({
    url: '/agent/login',
    method: 'post',
    data
  })
}

// SSO登录
export function ssoLogin(data) {
  return request({
    url: '/sso-auth/login',
    method: 'post',
    data
  })
}

// 获取客服信息
export function getAgentInfo(id) {
  return request({
    url: `/agent/${id}`,
    method: 'get'
  })
}

// 更新客服状态（上线/下线）
export function updateAgentStatus(id, status) {
  return request({
    url: `/agent/${id}/status`,
    method: 'put',
    params: { status }
  })
}

/**
 * 获取所有在线客服
 * @returns {Promise} - 在线客服列表
 */
export function getOnlineAgents() {
  return request({
    url: '/agent/online',
    method: 'get'
  })
}

// 获取所有客服
export function getAllAgents() {
  return request({
    url: '/agent/list',
    method: 'get'
  })
}

// 获取仪表板统计数据
export function getDashboardStats() {
  return request({
    url: '/agent/dashboardStats',
    method: 'get'
  })
}

// 获取待处理会话列表
export function getWaitingSessions() {
  return request({
    url: '/agent/waitingSessions',
    method: 'get'
  })
}

// 获取最近的评价列表
export function getRecentEvaluations() {
  return request({
    url: '/agent/evaluations/recent',
    method: 'get'
  })
}

/**
 * 获取会话统计数据
 * @param {string} period 时间周期(day, week, month)
 */
export function getSessionStats(period) {
  return request({
    url: '/agent/sessionStats',
    method: 'get',
    params: { period }
  })
}

/**
 * 获取对话统计数据
 * @param {string} period 时间周期(day, week, month)
 */
export function getMessageStats(period) {
  return request({
    url: '/agent/messageStats',
    method: 'get',
    params: { period }
  })
}

/**
 * 获取满意度统计数据
 * @param {string} period 时间周期(day, week, month)
 */
export function getSatisfactionStats(period) {
  return request({
    url: '/agent/satisfactionStats',
    method: 'get',
    params: { period }
  })
}

/**
 * 接受会话
 * @param {number} sessionId - 会话ID
 * @returns {Promise}
 */
export function acceptSession(sessionId) {
  return request({
    url: `/agent/acceptSession/${sessionId}`,
    method: 'put'
  })
}

/**
 * 批量更新客服状态
 * @param {number} agentId - 客服ID
 * @param {number} status - 状态：0-离线，1-在线
 * @returns {Promise}
 */
export function updateAgentStatusBulk(agentId, status) {
  return request({
    url: '/agent/updateStatus',
    method: 'put',
    data: {
      agentId,
      status
    }
  })
}

// 获取消息类型和数据来源统计
export function getMessageTypeAndSourceStats(startDate, endDate) {
  return request({
    url: '/session/message-stats',
    method: 'get',
    params: { startDate, endDate }
  })
}

/**
 * 检查是否为系统管理员
 * @param {number} agentId - 客服ID
 * @returns {Promise}
 */
export function checkIsAdmin(agentId) {
  return request({
    url: '/agent/checkAdmin',
    method: 'get',
    params: { agentId }
  })
}

/**
 * 更新客服个人信息
 * @param {Object} data - 客服信息
 * @returns {Promise}
 */
export function updateAgentInfo(data) {
  return request({
    url: `/agent/${data.id}`,
    method: 'put',
    data
  })
}

/**
 * 获取客服统计数据（按日期）
 * @param {string} date - 查询日期，格式 YYYY-MM-DD
 * @returns {Promise}
 */
export function getAgentStatsByDate(date) {
  return request({
    url: '/agent-manage/stats',
    method: 'get',
    params: { date }
  })
}

/**
 * 获取客服分页列表（带统计数据）
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getAgentsByPage(params) {
  return request({
    url: '/agent-manage/page',
    method: 'get',
    params
  })
}

/**
 * 添加客服
 * @param {Object} data - 客服信息
 * @returns {Promise}
 */
export function addAgent(data) {
  return request({
    url: '/agent-manage/add',
    method: 'post',
    data
  })
}

/**
 * 删除客服
 * @param {number} id - 客服ID
 * @returns {Promise}
 */
export function deleteAgent(id) {
  return request({
    url: `/agent-manage/${id}`,
    method: 'delete'
  })
}

/**
 * 获取客服会话统计数据
 * @param {number} agentId - 客服ID
 * @param {string} startDate - 开始日期，格式 YYYY-MM-DD
 * @param {string} endDate - 结束日期，格式 YYYY-MM-DD
 * @param {string} period - 时间周期(day, week, month)，可选
 * @returns {Promise}
 */
export function getAgentSessionStats(agentId, startDate, endDate, period) {
  return request({
    url: `/agent-manage/${agentId}/session-stats`,
    method: 'get',
    params: { startDate, endDate, period }
  })
} 