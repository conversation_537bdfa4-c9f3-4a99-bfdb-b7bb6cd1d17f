import request from '@/utils/request'

// 添加评价
export function addEvaluation(data) {
  return request({
    url: '/evaluation/add',
    method: 'post',
    data
  })
}

// 根据ID查询评价
export function getEvaluationById(id) {
  return request({
    url: `/evaluation/${id}`,
    method: 'get'
  })
}

// 根据会话ID查询评价
export function getEvaluationBySessionId(sessionId) {
  return request({
    url: `/evaluation/session/${sessionId}`,
    method: 'get'
  })
}

// 根据用户ID查询评价列表
export function getEvaluationsByUserId(userId) {
  return request({
    url: `/evaluation/user/${userId}`,
    method: 'get'
  })
}

// 获取所有评价
export function getAllEvaluations() {
  return request({
    url: '/evaluation/list',
    method: 'get'
  })
} 