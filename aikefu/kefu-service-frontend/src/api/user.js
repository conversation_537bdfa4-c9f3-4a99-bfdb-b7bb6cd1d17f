import request from '@/utils/request'

// 用户注册
export function register(data) {
  return request({
    url: '/user/register',
    method: 'post',
    data
  })
}

// 用户登录
export function login(data) {
  return request({
    url: '/user/login',
    method: 'post',
    data
  })
}

// 获取用户列表
export function getUsers(params) {
  return request({
    url: '/user/list',
    method: 'get',
    params
  })
}

// 获取用户信息
export function getUserInfo(id) {
  return request({
    url: `/user/${id}`,
    method: 'get'
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/user/add',
    method: 'post',
    data
  })
}

// 更新用户信息
export function updateUser(data) {
  return request({
    url: `/user/${data.id}`,
    method: 'put',
    data
  })
}

// 删除用户
export function deleteUser(id) {
  return request({
    url: `/user/${id}`,
    method: 'delete'
  })
}

// 获取用户列表(分页)
export function getUsersByPage(params) {
  return request({
    url: '/user/page',
    method: 'get',
    params
  })
}

// 移动端自动注册
export function autoRegister(data) {
  return request({
    url: '/user/auto-register',
    method: 'post',
    data
  })
}
  // 用户订单信息
export function userOrderInfo(data) {
    return request({
      url: '/user/user-orderInfo',
      method: 'post',
      data
    })
}

