<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      title="待处理会话提醒"
      width="400px"
      :show-close="true"
      :center="true"
      :destroy-on-close="false"
      :modal="true"
      :close-on-click-modal="false"
      :append-to-body="true"
      :lock-scroll="true"
    >
      <div class="alert-content">
        <el-alert
          title="您有新的待处理会话！"
          type="warning"
          :closable="false"
          show-icon
        />
        <div class="session-list" v-if="waitingSessions.length > 0">
          <div v-for="(session, index) in waitingSessions" :key="session.id" class="session-item">
            <div class="session-info">
              <div class="user-name">
                {{ session.userNickname || '访客' + session.userId }}
              </div>
              <div class="last-message" :title="session.lastMessage || '暂无消息'">
                {{ session.lastMessage || '暂无消息' }}
              </div>
              <div class="waiting-time">
                <el-tag size="small" :type="getWaitingTimeType(session.waitingTime)" effect="light">
                  等待: {{ formatWaitingTime(session.waitingTime) }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="goToSessions">查看会话</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, onMounted, onUnmounted, computed } from 'vue'
import { useUserStore } from '@/store/user'
import { useChatStore } from '@/store/chat'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  waitingSessions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['close', 'view-sessions'])

const dialogVisible = ref(false)
const userStore = useUserStore()
const chatStore = useChatStore()

// 监听props.show的变化
watch(() => props.show, (newValue) => {
  console.log('WaitingSessionAlert - props.show变化:', newValue)
  dialogVisible.value = newValue
})

// 监听dialogVisible的变化
watch(dialogVisible, (newValue) => {
  console.log('WaitingSessionAlert - dialogVisible变化:', newValue)
  if (!newValue) {
    emit('close')
  }
})

// 跳转到会话列表
const goToSessions = () => {
  emit('view-sessions')
  dialogVisible.value = false
}

// 格式化等待时间
const formatWaitingTime = (seconds) => {
  // 确保seconds是数字类型
  const waitSeconds = Number(seconds)
  
  // 如果seconds不是有效数字，返回默认值
  if (isNaN(waitSeconds) || waitSeconds === undefined || waitSeconds === null) {
    console.warn('等待时间无效:', seconds)
    return '未知'
  }
  
  const minutes = Math.floor(waitSeconds / 60)
  const remainingSeconds = Math.floor(waitSeconds % 60)
  
  if (minutes === 0) {
    return `${remainingSeconds}秒`
  }
  
  return `${minutes}分${remainingSeconds}秒`
}

// 根据等待时间获取不同的标签类型
const getWaitingTimeType = (seconds) => {
  // 确保seconds是数字类型
  const waitSeconds = Number(seconds)
  
  // 如果seconds不是有效数字，返回默认值
  if (isNaN(waitSeconds) || waitSeconds === undefined || waitSeconds === null) {
    return 'info'
  }
  
  const minutes = Math.floor(waitSeconds / 60)
  
  if (minutes >= 5) return 'danger'
  if (minutes >= 2) return 'warning'
  return 'info'
}

// 本地维护的等待会话列表（仅当props没有提供会话列表时使用）
const waitingSessionsLocal = ref([])

// 计算属性：使用props的waitingSessions或本地维护的waitingSessionsLocal
const waitingSessions = computed(() => {
  return props.waitingSessions && props.waitingSessions.length > 0 
    ? props.waitingSessions 
    : waitingSessionsLocal.value
})

// 监听人工客服请求
const handleAgentRequest = (event) => {
  console.log('WaitingSessionAlert处理请求人工客服通知:', event.detail)
  
  // 确保是人工客服请求消息
  if (event.detail && event.detail.data && event.detail.data.transferType === 'requestAgent') {
    // 创建会话对象
    const session = {
      id: event.detail.sessionId,
      userId: event.detail.data.userId,
      lastMessage: event.detail.content,
      userNickname: `访客${event.detail.data.userId}`,
      waitingTime: 0 // 初始等待时间为0，可以根据实际情况设置
    }
    
    console.log('待处理的会话对象:', session)
    
    // 检查会话是否已存在于列表中
    const sessionExists = waitingSessionsLocal.value.some(s => s.id === session.id)
    if (!sessionExists) {
      waitingSessionsLocal.value.push(session)
    }
    
    // 显示弹窗
    console.log('要显示弹窗，当前dialogVisible值:', dialogVisible.value)
    dialogVisible.value = true
    console.log('设置dialogVisible为:', dialogVisible.value)
    
    // 播放提示音
    playNotificationSound()
  }
}

// 播放通知提示音
const playNotificationSound = () => {
  try {
    // 创建音频元素 - 更新音频文件路径为相对路径
    const audio = new Audio(import.meta.env.BASE_URL + 'audio/notification.mp3')
    audio.volume = 0.6
    audio.play().catch(e => {
      console.warn('播放通知音效失败:', e)
      // 尝试备用路径
      const backupAudio = new Audio('/audio/notification.mp3')
      backupAudio.volume = 0.6
      backupAudio.play().catch(err => console.error('备用音频播放失败:', err))
    })
  } catch (error) {
    console.error('播放提示音失败:', error)
  }
}

// 挂载组件时添加事件监听
onMounted(() => {
  console.log('WaitingSessionAlert组件已挂载')
  console.log('初始属性:', { 
    show: props.show, 
    waitingSessions: props.waitingSessions 
  })
  
  // 移除旧的监听器，避免重复
  window.removeEventListener('agent-request-notification', handleAgentRequest)
  window.removeEventListener('chat-transfer-notification', handleTransferRequest)
  window.removeEventListener('show-waiting-alert', handleShowWaitingAlert)
  
  // 添加新的监听器
  window.addEventListener('agent-request-notification', handleAgentRequest)
  window.addEventListener('chat-transfer-notification', handleTransferRequest)
  window.addEventListener('show-waiting-alert', handleShowWaitingAlert)
  
  // 创建全局函数，供其他组件直接调用
  window.showAgentRequestAlert = (eventDetail) => {
    console.log('直接调用全局函数显示弹窗:', eventDetail)
    if (eventDetail && eventDetail.data && eventDetail.data.userId) {
      // 创建会话对象
      const session = {
        id: eventDetail.sessionId,
        userId: eventDetail.data.userId,
        lastMessage: eventDetail.content,
        userNickname: `访客${eventDetail.data.userId}`,
        waitingTime: 0
      }
      
      // 添加到本地列表
      waitingSessionsLocal.value = [session]
      
      // 直接设置弹窗显示
      dialogVisible.value = true
      
      // 播放提示音
      playNotificationSound()
    }
  }
})

// 处理显示弹窗事件
const handleShowWaitingAlert = (event) => {
  console.log('WaitingSessionAlert收到show-waiting-alert事件:', event.detail)
  if (event.detail && event.detail.show) {
    if (event.detail.session) {
      waitingSessionsLocal.value = [event.detail.session]
    }
    // 显示弹窗
    dialogVisible.value = true
    // 播放提示音
    playNotificationSound()
  }
}

// 卸载组件时移除事件监听
onUnmounted(() => {
  console.log('WaitingSessionAlert组件卸载')
  window.removeEventListener('agent-request-notification', handleAgentRequest)
  window.removeEventListener('chat-transfer-notification', handleTransferRequest)
  window.removeEventListener('show-waiting-alert', handleShowWaitingAlert)
  
  // 删除全局函数
  delete window.showAgentRequestAlert
})

// 监听WebSocket转接请求
const handleTransferRequest = (event) => {
  console.log('WaitingSessionAlert收到转接请求通知:', event.detail)
  
  // 确保是当前客服的转接请求
  if (!event.detail.isTransferRequest || !userStore.isAgent) return
  
  // 关键改进：检查转接请求是否指定了特定客服，以及该客服是否为当前登录的客服
  // 如果请求中包含targetAgentId，则验证是否匹配当前客服ID
  if (event.detail.targetAgentId && event.detail.targetAgentId !== userStore.userInfo.id) {
    console.log('转接请求指定了其他客服，当前客服ID:', userStore.userInfo.id, '目标客服ID:', event.detail.targetAgentId)
    return; // 不是发给当前客服的请求，直接返回
  }
  
  // 创建会话对象
  const session = {
    id: event.detail.sessionId,
    userId: event.detail.userId,
    lastMessage: event.detail.content,
    userNickname: `访客${event.detail.userId}`,
    waitingTime: 0 // 初始等待时间为0，可以根据实际情况设置
  }
  
  // 检查会话是否已存在于列表中
  const sessionExists = waitingSessionsLocal.value.some(s => s.id === session.id)
  if (!sessionExists) {
    waitingSessionsLocal.value.push(session)
  }
  
  // 显示弹窗
  dialogVisible.value = true
  
  // 播放提示音
  playNotificationSound()
}
</script>

<style scoped>
.alert-content {
  padding: 10px 0;
}

.session-list {
  margin-top: 15px;
  max-height: 250px;
  overflow-y: auto;
}

.session-item {
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  align-items: center;
}

.session-item:last-child {
  border-bottom: none;
}

.session-info {
  flex: 1;
}

.user-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.last-message {
  font-size: 13px;
  color: #606266;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.waiting-time {
  display: flex;
  justify-content: flex-end;
}
</style> 