<template>
  <!-- 客服界面模式 -->
  <div v-if="mode === 'agent'" class="connection-status agent-mode" :class="[statusClass]">
    <div class="status-header" @click="toggleDetails">
      <div class="status-dot-wrapper">
        <div class="status-dot"></div>
      </div>
      <span class="status-text">{{ statusText }}</span>
      <el-icon class="toggle-icon" :class="{ 'is-expanded': showDetails }">
        <CaretBottom />
      </el-icon>
    </div>
    <transition name="slide-fade">
      <div v-if="showDetails" class="connection-details">
        <div class="detail-row">
          <div class="detail-label">连接方式</div>
          <div class="detail-value">{{ connectionTypeText }}</div>
        </div>
        <div class="detail-row">
          <div class="detail-label">最后更新</div>
          <div class="detail-value">{{ lastUpdateTime }}</div>
        </div>
        <div class="detail-row" v-if="reconnectAttempts > 0">
          <div class="detail-label">重连次数</div>
          <div class="detail-value">{{ reconnectAttempts }}</div>
        </div>
        <div class="detail-row" v-if="isPolling">
          <div class="detail-label">轮询间隔</div>
          <div class="detail-value">5秒</div>
        </div>
        <el-button 
          class="toggle-btn" 
          size="small" 
          :type="connected ? 'danger' : 'primary'"
          @click="toggleConnection"
        >
          {{ connected ? '断开连接' : '重新连接' }}
        </el-button>
      </div>
    </transition>
  </div>

  <!-- 移动端模式 -->
  <div v-else-if="mode === 'mobile'" class="mobile-connection-status" :class="statusClass">
    <div class="mobile-status-dot" @click="showDetails = !showDetails"></div>
    <span class="mobile-status-text" @click="showDetails = !showDetails">{{statusText}}</span>
    <div v-if="showDetails" class="mobile-connection-popup">
      <div class="popup-content">
        <p>连接方式: {{ connectionTypeText }}</p>
        <p>最后更新: {{ lastUpdateTime }}</p>
        <p v-if="reconnectAttempts > 0">重连次数: {{ reconnectAttempts }}</p>
        <button class="mobile-toggle-btn" @click="toggleConnection">
          {{ connected ? '断开连接' : '重新连接' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, defineProps } from 'vue'
import { useChatStore } from '@/store/chat'
import { CaretBottom } from '@element-plus/icons-vue'

const props = defineProps({
  mode: {
    type: String,
    default: 'agent', // 'agent' 或 'mobile'
    validator: (value) => ['agent', 'mobile'].includes(value)
  }
})

const chatStore = useChatStore()
const showDetails = ref(false)
const lastUpdateTime = ref(new Date().toLocaleTimeString())
const checkInterval = ref(null)

// 计算连接状态类和文本
const statusClass = computed(() => {
  if (chatStore.connected) return 'status-connected'
  if (chatStore.isPolling) return 'status-polling'
  return 'status-disconnected'
})

const statusText = computed(() => {
  if (chatStore.connected) return '已连接'
  if (chatStore.isPolling) return '轮询中'
  return 'WebSocket未连接'
})

const connectionTypeText = computed(() => {
  if (chatStore.connected) return 'WebSocket'
  if (chatStore.isPolling) return 'HTTP轮询'
  return '无连接'
})

// WebSocket readyState文本
const wsReadyState = computed(() => {
  if (!chatStore.ws) return null
  return chatStore.ws.readyState
})

const wsReadyStateText = computed(() => {
  if (wsReadyState.value === null) return '无WebSocket实例'
  const states = ['正在连接', '已连接', '正在关闭', '已关闭']
  return states[wsReadyState.value] || '未知状态'
})

// 重连尝试次数
const reconnectAttempts = computed(() => {
  return chatStore.reconnectAttempts || 0
})

// 是否在轮询中
const isPolling = computed(() => {
  return chatStore.isPolling
})

// WebSocket是否已连接
const connected = computed(() => {
  return chatStore.connected
})

// 切换连接状态
const toggleConnection = () => {
  if (chatStore.connected) {
    chatStore.closeWebSocket()
  } else {
    if (chatStore.isPolling) {
      chatStore.stopPolling()
    }
    chatStore.initWebSocket()
  }
}

// 切换详情显示
const toggleDetails = () => {
  showDetails.value = !showDetails.value
}

// 定期更新状态
const updateStatus = () => {
  lastUpdateTime.value = new Date().toLocaleTimeString()
}

onMounted(() => {
  // 每秒更新一次状态信息
  checkInterval.value = setInterval(updateStatus, 1000)
  
  // 注册事件监听
  window.addEventListener('chat-polling-started', updateStatus)
  window.addEventListener('chat-polling-stopped', updateStatus)
})

onUnmounted(() => {
  // 清除定时器和事件监听
  if (checkInterval.value) {
    clearInterval(checkInterval.value)
  }
  
  window.removeEventListener('chat-polling-started', updateStatus)
  window.removeEventListener('chat-polling-stopped', updateStatus)
})
</script>

<style scoped>
/* 客服界面样式 */
.connection-status.agent-mode {
  width: 85%;
  overflow: hidden;
  transition: all 0.3s ease;
  font-size: 12px;
  background-color: #1a2236;
  border-radius: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  border: 1px solid #2a385b;
}

.status-header {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 10px 14px;
  transition: background-color 0.2s;
  position: relative;
}

.status-header:hover {
  background-color: rgba(75, 139, 235, 0.1);
}

.status-dot-wrapper {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.status-text {
  color: #e9ecf3;
  flex: 1;
  font-weight: 500;
}

.toggle-icon {
  color: #8896b9;
  font-size: 12px;
  transition: transform 0.3s;
}

.toggle-icon.is-expanded {
  transform: rotate(180deg);
}

.status-connected .status-dot {
  background-color: #67c23a;
  box-shadow: 0 0 6px rgba(103, 194, 58, 0.6);
}

/* 只在代理模式下使用左边框 */
.agent-mode.status-connected {
  border-left: 3px solid #67c23a;
}

.status-polling .status-dot {
  background-color: #e6a23c;
  box-shadow: 0 0 6px rgba(230, 162, 60, 0.6);
}

/* 只在代理模式下使用左边框 */
.agent-mode.status-polling {
  border-left: 3px solid #e6a23c;
}

.status-disconnected .status-dot {
  background-color: #f56c6c;
  box-shadow: 0 0 6px rgba(245, 108, 108, 0.4);
}

/* 只在代理模式下使用左边框 */
.agent-mode.status-disconnected {
  border-left: 3px solid #f56c6c;
}

/* 连接详情区域 */
.connection-details {
  padding: 0 14px 14px;
  background-color: #141b2d;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.03);
}

.detail-row:last-of-type {
  border-bottom: none;
  margin-bottom: 5px;
}

.detail-label {
  color: #8896b9;
}

.detail-value {
  color: #e9ecf3;
  font-weight: 500;
}

.toggle-btn {
  width: 100%;
  margin-top: 8px;
  letter-spacing: 1px;
  font-weight: normal;
}

/* 自定义 element-plus 按钮样式 */
:deep(.el-button--danger) {
  --el-button-bg-color: #f56c6c;
  --el-button-border-color: #f56c6c;
  --el-button-hover-bg-color: #f78989;
  --el-button-hover-border-color: #f78989;
  --el-button-active-bg-color: #dd6161;
  --el-button-active-border-color: #dd6161;
}

:deep(.el-button--primary) {
  --el-button-bg-color: #409eff;
  --el-button-border-color: #409eff;
  --el-button-hover-bg-color: #66b1ff;
  --el-button-hover-border-color: #66b1ff;
  --el-button-active-bg-color: #3a8ee6;
  --el-button-active-border-color: #3a8ee6;
}

/* 过渡动画 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
  max-height: 200px;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  max-height: 0;
  opacity: 0;
  margin-top: -8px;
}

/* 移动端样式 */
.mobile-connection-status {
  display: inline-flex;
  position: relative;
  vertical-align: middle;
  margin-left: 8px;
  align-items: center;
}

.mobile-status-text {
  color: white;
  font-size: 12px;
  margin-left: 5px;
  cursor: pointer;
}

.mobile-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  cursor: pointer;
  transition: all 0.3s ease;
}

.status-connected .mobile-status-dot {
  background-color: #67c23a;
}

.status-polling .mobile-status-dot {
  background-color: #e6a23c;
}

.status-disconnected .mobile-status-dot {
  background-color: #999;
}

.mobile-connection-popup {
  position: absolute;
  top: 20px;
  right: 0;
  width: 200px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 1010;
  overflow: hidden;
}

.popup-content {
  padding: 10px 12px;
}

.popup-content p {
  margin: 5px 0;
  font-size: 12px;
  color: #606266;
}

.mobile-toggle-btn {
  margin-top: 10px;
  width: 100%;
  padding: 6px 0;
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
}
</style> 