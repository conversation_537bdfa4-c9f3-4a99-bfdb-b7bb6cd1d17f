<template>
  <div class="message-carousel-container">
    <div class="carousel-header">
      <div class="title">
        <el-icon><ChatLineRound /></el-icon>
        <span>最新消息</span>
      </div>
      <div class="more" @click="showAllMessages">
        <span>查看更多</span>
        <el-icon><ArrowRight /></el-icon>
      </div>
    </div>
    
    <div class="message-scroll-container" v-loading="loading">
      <div class="message-list">
        <div v-if="displayMessages.length === 0" class="empty-message">
          <el-icon><ChatLineSquare /></el-icon>
          <span>暂无用户消息</span>
        </div>
        
        <div 
          v-for="(message, index) in displayMessages" 
          :key="index" 
          class="message-item"
          @click="showChatDetail(message)"
        >
          <div class="message-item-content">
            <div class="user-avatar">
              <el-avatar 
                :size="24" 
                :style="getRandomAvatarStyle(index)"
              >访</el-avatar>
            </div>
            <div class="message-info">
              <div class="message-header">
                <span class="user-name">访客</span>
                <span class="session-id">会话ID: {{ message.session_id }}</span>
                <span class="message-time">{{ formatTime(message.created_at) }}</span>
              </div>
              <div class="message-text">{{ message.content }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 会话详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="会话详情"
      width="70%"
      destroy-on-close
    >
      <div v-loading="loadingDetails" class="chat-detail-container">
        <div v-if="chatMessages.length === 0" class="empty-detail">
          暂无会话记录
        </div>
        <div v-else class="chat-messages">
          <div 
            v-for="(msg, idx) in chatMessages" 
            :key="idx" 
            :class="['message-bubble', {'user-message': msg.senderType === 0, 'agent-message': msg.senderType === 1, 'ai-message': msg.senderType === 2}]"
          >
            <div class="message-sender">
              {{ getSenderName(msg.senderType) }}
              <span class="message-time">{{ formatTime(msg.createdAt) }}</span>
            </div>
            <div class="message-content">{{ msg.content }}</div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="navigateToChat(currentSessionId)">
            进入会话
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { 
  ChatLineRound, 
  ArrowRight, 
  ChatLineSquare 
} from '@element-plus/icons-vue'
import moment from 'moment'
import { getRecentUserMessages, getMessagesBySessionId } from '@/api/chat'
import { ElMessage } from 'element-plus'

const props = defineProps({
  displayCount: {
    type: Number,
    default: 5  // 一次展示5条数据
  },
  storageLimit: {
    type: Number,
    default: 50  // 最多存储50条数据
  }
})

const router = useRouter()
const messages = ref([])
const currentIndex = ref(0)
const loading = ref(false)
let scrollInterval = null
let refreshInterval = null

// 会话详情相关变量
const dialogVisible = ref(false)
const chatMessages = ref([])
const loadingDetails = ref(false)
const currentSessionId = ref(null)

// 计算当前应该显示的消息
const displayMessages = computed(() => {
  if (messages.value.length === 0) return []
  
  // 如果消息总数不足displayCount，直接全部显示
  if (messages.value.length <= props.displayCount) {
    return messages.value
  }
  
  // 计算开始索引和结束索引
  const startIndex = currentIndex.value
  const endIndex = (startIndex + props.displayCount) <= messages.value.length 
    ? (startIndex + props.displayCount) 
    : messages.value.length
  
  // 截取数组
  return messages.value.slice(startIndex, endIndex)
})

// 加载最新消息
const loadRecentMessages = async () => {
  loading.value = true
  try {
    const res = await getRecentUserMessages()
    if (res.code === 200 && res.data) {
      messages.value = res.data
    } else {
      console.error('加载最新消息失败:', res.message)
    }
  } catch (error) {
    console.error('加载最新消息失败:', error)
    ElMessage.error('获取最新消息失败，请刷新重试')
  } finally {
    loading.value = false
  }
}

// 启动自动滚动
const startScroll = () => {
  if (scrollInterval) {
    clearInterval(scrollInterval)
  }
  
  scrollInterval = setInterval(() => {
    // 当消息不足展示数量时不滚动
    if (messages.value.length <= props.displayCount) return
    
    // 更新当前索引，实现轮播效果
    currentIndex.value = (currentIndex.value + 1) % (messages.value.length - props.displayCount + 1)
    if (currentIndex.value < 0 || currentIndex.value >= messages.value.length) {
      currentIndex.value = 0 // 防止索引越界
    }
  }, 1800) // 每2秒滚动一条消息
}

// 格式化时间
const formatTime = (timestamp) => {
  return moment(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

// 获取随机头像样式
const getRandomAvatarStyle = (index) => {
  // 根据索引选择不同的背景颜色
  const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']
  const colorIndex = index % colors.length
  return {
    backgroundColor: colors[colorIndex]
  }
}

// 显示会话详情
const showChatDetail = async (message) => {
  if (!message.session_id) {
    ElMessage.warning('无法获取会话ID')
    return
  }
  
  currentSessionId.value = message.session_id
  dialogVisible.value = true
  loadingDetails.value = true
  chatMessages.value = []
  
  try {
    const res = await getMessagesBySessionId(message.session_id)
    if (res.code === 200 && res.data) {
      chatMessages.value = res.data
    } else {
      ElMessage.warning('获取会话消息失败')
    }
  } catch (error) {
    console.error('获取会话消息失败:', error)
    ElMessage.error('获取会话消息失败，请重试')
  } finally {
    loadingDetails.value = false
  }
}

// 获取发送者名称
const getSenderName = (senderType) => {
  switch (parseInt(senderType)) {
    case 0: return '访客'
    case 1: return '客服'
    case 2: return 'AI助手'
    default: return '未知'
  }
}

// 导航到聊天页面
const navigateToChat = (sessionId) => {
  if (sessionId) {
    dialogVisible.value = false
    router.push(`/agent/chat?session=${sessionId}`)
  }
}

// 查看所有消息
const showAllMessages = () => {
  router.push('/agent/chat')
}

onMounted(async () => {
  // 加载最新消息
  await loadRecentMessages()
  
  // 启动自动滚动
  startScroll()
  
  // 每分钟刷新一次消息列表
  refreshInterval = setInterval(async () => {
    await loadRecentMessages()
  }, 60000)
})

onUnmounted(() => {
  // 清除定时器
  if (scrollInterval) {
    clearInterval(scrollInterval)
    scrollInterval = null
  }
  
  if (refreshInterval) {
    clearInterval(refreshInterval)
    refreshInterval = null
  }
})
</script>

<style lang="scss" scoped>
.message-carousel-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.04);
  padding: 16px;
  margin-bottom: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .carousel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      
      .el-icon {
        color: #409EFF;
      }
    }
    
    .more {
      display: flex;
      align-items: center;
      gap: 4px;
      color: #409EFF;
      cursor: pointer;
      font-size: 13px;
      
      &:hover {
        color: #66b1ff;
      }
    }
  }
  
  .message-scroll-container {
    flex: 1;
    overflow: hidden;
    position: relative;
    border-radius: 8px;
    background-color: #f5f7fa;
  }
  
  .message-list {
    height: 100%;
    overflow-y: auto;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: rgba(144, 147, 153, 0.3);
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-track {
      background-color: transparent;
    }
  }
  
  .message-item {
    padding: 10px 16px;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.3s;
    border-bottom: 1px solid #ebeef5;
    animation: fadeIn 0.5s ease-in-out;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:hover {
      background-color: #ecf5ff;
      transform: translateX(5px);
    }
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .message-item-content {
    display: flex;
    gap: 10px;
  }
  
  .message-info {
    flex: 1;
    min-width: 0;
  }
  
  .message-header {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 5px;
    
    .user-name {
      font-weight: 600;
      font-size: 13px;
      color: #303133;
    }
    
    .session-id {
      font-size: 12px;
      color: #909399;
      background-color: #f0f2f5;
      padding: 2px 6px;
      border-radius: 10px;
    }
    
    .message-time {
      font-size: 12px;
      color: #909399;
      margin-left: auto;
    }
  }
  
  .message-text {
    font-size: 13px;
    color: #606266;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
  }
  
  .empty-message {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 8px;
    color: #909399;
    
    .el-icon {
      font-size: 24px;
    }
  }
}

// 会话详情样式
.chat-detail-container {
  height: 500px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  background-color: #f8f9fb;
  
  .empty-detail {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #909399;
  }
  
  .chat-messages {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .message-bubble {
    max-width: 80%;
    padding: 10px 14px;
    border-radius: 8px;
    position: relative;
    
    .message-sender {
      font-size: 12px;
      margin-bottom: 4px;
      font-weight: 500;
      display: flex;
      justify-content: space-between;
    }
    
    .message-content {
      font-size: 14px;
      line-height: 1.5;
      word-break: break-word;
    }
    
    .message-time {
      font-size: 12px;
      color: #909399;
      font-weight: normal;
    }
    
    &.user-message {
      align-self: flex-start;
      background-color: #f2f6fc;
      border: 1px solid #e4e7ed;
      
      .message-sender {
        color: #409EFF;
      }
    }
    
    &.agent-message {
      align-self: flex-end;
      background-color: #ecf5ff;
      border: 1px solid #d9ecff;
      
      .message-sender {
        color: #67C23A;
      }
    }
    
    &.ai-message {
      align-self: flex-end;
      background-color: #f0f9eb;
      border: 1px solid #e1f3d8;
      
      .message-sender {
        color: #E6A23C;
      }
    }
  }
}
</style> 