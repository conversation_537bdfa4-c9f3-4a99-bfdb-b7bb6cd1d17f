<template>
  <div>
    <client-only>
      <el-notification
        v-if="show"
        :title="title"
        :message="message"
        type="info"
        :duration="duration"
        @close="handleClose"
        position="bottom-right"
      >
        <template #default>
          <div class="notification-content">
            <div class="user-info">
              <span class="user-name">{{ userName }}</span>
              <span class="session-id">#{{ sessionId }}</span>
            </div>
            <div class="message-content">{{ messageContent }}</div>
          </div>
        </template>
      </el-notification>
    </client-only>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { ElNotification } from 'element-plus'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  userName: {
    type: String,
    default: '访客'
  },
  sessionId: {
    type: [String, Number],
    default: ''
  },
  messageContent: {
    type: String,
    default: ''
  },
  duration: {
    type: Number,
    default: 4500
  }
})

const emit = defineEmits(['close', 'update:show'])

const title = ref('新消息提醒')
const message = ref('')

const handleClose = () => {
  emit('close')
  emit('update:show', false)
}

// 监听 show 的变化，当为 true 时显示通知
watch(() => props.show, (newVal) => {
  if (newVal) {
    ElNotification({
      title: title.value,
      message: h('div', { class: 'notification-content' }, [
        h('div', { class: 'user-info' }, [
          h('span', { class: 'user-name' }, props.userName),
          h('span', { class: 'session-id' }, `#${props.sessionId}`)
        ]),
        h('div', { class: 'message-content' }, props.messageContent)
      ]),
      type: 'info',
      duration: props.duration,
      position: 'bottom-right',
      onClose: handleClose
    })
  }
})
</script>

<style scoped>
.notification-content {
  padding: 5px 0;
}

.user-info {
  margin-bottom: 5px;
}

.user-name {
  font-weight: bold;
  margin-right: 8px;
}

.session-id {
  color: #909399;
  font-size: 12px;
}

.message-content {
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}
</style> 