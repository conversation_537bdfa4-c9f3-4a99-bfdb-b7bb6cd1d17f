import axios from 'axios'
import { ElMessage } from 'element-plus'
import jwtDecode from 'jwt-decode'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api', // api的base_url
  timeout: 15000 // 请求超时时间
})

// 创建一个用于刷新token的axios实例，避免循环调用
const refreshTokenService = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000
})

// 是否正在刷新token
let isRefreshing = false
// 请求队列
let requestsQueue = []

// token是否将要过期（有效期小于10分钟）
const isTokenExpiringSoon = (token) => {
  try {
    const decoded = jwtDecode(token)
    // exp是秒级时间戳，现在时间也转为秒级
    const now = Math.floor(Date.now() / 1000)
    // 如果token有效期小于10分钟，认为即将过期
    return decoded.exp - now < 10 * 60
  } catch (error) {
    return true
  }
}

// 刷新token
const refreshToken = async () => {
  try {
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    const role = localStorage.getItem('role') || ''
    
    // 根据角色区分调用不同的刷新接口
    let response
    if (role === 'user') {
      response = await refreshTokenService.post('/user/refresh-token', { userId: userInfo.id })
    } else if (role === 'agent' || role === 'admin') {
      response = await refreshTokenService.post('/agent/refresh-token', { agentId: userInfo.id })
    } else {
      throw new Error('未知角色')
    }
    
    if (response.data.code === 200) {
      const newToken = response.data.data.token
      localStorage.setItem('token', newToken)
      return newToken
    } else {
      throw new Error('刷新token失败')
    }
  } catch (error) {
    console.error('刷新token失败:', error)
    throw error
  }
}

// 请求拦截器
service.interceptors.request.use(
  async config => {
    // 从localStorage中获取token
    let token = localStorage.getItem('token')
    
    if (token) {
      // 检查token是否将要过期
      if (isTokenExpiringSoon(token) && !config.url.includes('/refresh-token') && !config.url.includes('/validateToken')) {
        if (!isRefreshing) {
          isRefreshing = true
          try {
            // 刷新token
            token = await refreshToken()
            // 处理队列中的请求
            requestsQueue.forEach(cb => cb(token))
            requestsQueue = []
          } catch (error) {
            console.error('Token刷新失败，重定向到登录页')
            // 清除本地存储
            localStorage.removeItem('token')
            localStorage.removeItem('userInfo')
            localStorage.removeItem('role')
            // 重定向到登录页
            window.location.href = '/login'
            return Promise.reject(error)
          } finally {
            isRefreshing = false
          }
        } else {
          // 如果正在刷新，则将请求加入队列
          return new Promise((resolve) => {
            requestsQueue.push((newToken) => {
              config.headers['Authorization'] = newToken
              resolve(config)
            })
          })
        }
      }
      
      // 使用最新的token
      config.headers['Authorization'] = token
    }
    
    return config
  },
  error => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    
    // 如果响应状态码不是200，则报错
    if (res.code !== 200) {
      // 显示错误消息
      ElMessage.error(res.message || '请求失败')
      
      // token无效或过期
      if (res.code === 401) {
        // 清除本地存储
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
        localStorage.removeItem('role')
        
        // 重定向到登录页面
        setTimeout(() => {
          window.location.href = '/login'
        }, 1500)
      }
      
      return Promise.reject(new Error(res.message || '请求失败'))
    } else {
      return res
    }
  },
  error => {
    console.error('Response error:', error)
    
    // 401错误，未授权
    if (error.response && error.response.status === 401) {
      ElMessage.error('登录已过期，请重新登录')
      
      // 清除本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      localStorage.removeItem('role')
      
      // 重定向到登录页面
      setTimeout(() => {
        window.location.href = '/login'
      }, 1500)
    } else {
      // 显示错误消息
      ElMessage.error(error.message || '请求失败')
    }
    
    return Promise.reject(error)
  }
)

export default service