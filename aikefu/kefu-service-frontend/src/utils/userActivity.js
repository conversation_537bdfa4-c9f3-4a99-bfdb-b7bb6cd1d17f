import jwtDecode from 'jwt-decode'
import axios from 'axios'

// 活动检测的配置
const config = {
  // 活动检测间隔（毫秒）
  checkInterval: 5 * 60 * 1000, // 5分钟
  // 刷新token的阈值（秒）
  refreshThreshold: 30 * 60, // 30分钟
  // 用户不活跃的时间阈值（毫秒）
  inactivityThreshold: 60 * 60 * 1000, // 1小时
}

// 上次活动时间
let lastActivityTime = Date.now()
// 定时器ID
let checkIntervalId = null
// 刷新token的函数
let tokenRefreshCallback = null

// 创建一个用于刷新token的axios实例
const refreshTokenService = axios.create({
  baseURL: '/api',
  timeout: 10000
})

/**
 * 初始化用户活动监测
 * @param {Function} refreshCallback - 刷新token的回调函数
 */
export function initUserActivityMonitor(refreshCallback) {
  // 保存回调函数
  tokenRefreshCallback = refreshCallback
  
  // 注册用户活动事件
  registerActivityEvents()
  
  // 开始定期检查
  startActivityCheck()
  
  // 用户返回页面时重新检查
  window.addEventListener('focus', checkTokenOnFocus)
}

/**
 * 注册用户活动事件
 */
function registerActivityEvents() {
  // 鼠标移动、点击、按键、触摸、滚动都视为用户活动
  const events = ['mousemove', 'mousedown', 'keydown', 'touchstart', 'scroll']
  
  events.forEach(eventName => {
    document.addEventListener(eventName, updateActivityTimestamp, { passive: true })
  })
}

/**
 * 更新活动时间戳
 */
function updateActivityTimestamp() {
  lastActivityTime = Date.now()
}

/**
 * 开始定期检查活动状态
 */
function startActivityCheck() {
  // 先清除可能存在的旧定时器
  if (checkIntervalId) {
    clearInterval(checkIntervalId)
  }
  
  // 设置新的定期检查
  checkIntervalId = setInterval(() => {
    const currentTime = Date.now()
    const token = localStorage.getItem('token')
    
    // 如果没有token或者用户已登出，停止检查
    if (!token) {
      stopActivityCheck()
      return
    }
    
    try {
      // 检查token是否需要刷新
      const tokenData = jwtDecode(token)
      const tokenExpiry = tokenData.exp * 1000 // 转换为毫秒
      
      // 如果在用户活跃时，token快过期了，则刷新
      if (currentTime - lastActivityTime < config.inactivityThreshold) {
        const timeToExpiry = tokenExpiry - currentTime
        // 如果token将在配置的阈值内过期，刷新它
        if (timeToExpiry < config.refreshThreshold * 1000) {
          refreshToken()
        }
      }
    } catch (error) {
      console.error('解析token失败', error)
    }
  }, config.checkInterval)
}

/**
 * 停止活动检查
 */
function stopActivityCheck() {
  if (checkIntervalId) {
    clearInterval(checkIntervalId)
    checkIntervalId = null
  }
  
  // 移除事件监听
  window.removeEventListener('focus', checkTokenOnFocus)
}

/**
 * 当用户返回页面时检查token
 */
function checkTokenOnFocus() {
  const token = localStorage.getItem('token')
  if (!token) return
  
  try {
    const tokenData = jwtDecode(token)
    const now = Date.now()
    const tokenExpiry = tokenData.exp * 1000 // 转换为毫秒
    
    // 如果token已经过期或快要过期，刷新它
    if (tokenExpiry - now < config.refreshThreshold * 1000) {
      refreshToken()
    }
  } catch (error) {
    console.error('解析token失败', error)
  }
}

/**
 * 刷新token
 */
async function refreshToken() {
  try {
    // 如果有自定义刷新函数，使用它
    if (typeof tokenRefreshCallback === 'function') {
      await tokenRefreshCallback()
      return
    }
    
    // 否则使用内置的刷新逻辑
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    const role = localStorage.getItem('role') || ''
    
    // 根据角色区分调用不同的刷新接口
    let response
    if (role === 'user') {
      response = await refreshTokenService.post('/user/refresh-token', { userId: userInfo.id })
    } else if (role === 'agent') {
      response = await refreshTokenService.post('/agent/refresh-token', { agentId: userInfo.id })
    } else {
      throw new Error('未知角色')
    }
    
    if (response.data.code === 200) {
      const newToken = response.data.data.token
      localStorage.setItem('token', newToken)
      console.log('Token已刷新')
    }
  } catch (error) {
    console.error('刷新token失败:', error)
  }
}

export default {
  initUserActivityMonitor,
  updateActivityTimestamp,
  stopActivityCheck
}