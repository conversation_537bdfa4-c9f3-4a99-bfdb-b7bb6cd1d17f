import MobileChatH5 from '@/views/MobileChatH5.vue'
import { createRouter, createWebHistory } from 'vue-router'
import { useChatStore } from '@/store/chat'

// 懒加载路由组件
const Login = () => import('../views/Login.vue')
const Register = () => import('../views/Register.vue')
const SsoLogin = () => import('../views/SsoLogin.vue')
const UserLayout = () => import('../views/user/Layout.vue')
const UserChat = () => import('../views/user/Chat.vue')
const UserHistory = () => import('../views/user/History.vue')
const UserProfile = () => import('../views/user/Profile.vue')
const UserEvaluation = () => import('../views/user/Evaluation.vue')
const AgentLayout = () => import('../views/agent/Layout.vue')
const AgentDashboard = () => import('../views/agent/Dashboard.vue')
const AgentChat = () => import('../views/agent/Chat.vue')
const AgentHistory = () => import('../views/agent/History.vue')
const AgentUsers = () => import('../views/agent/Users.vue')
const AgentFAQ = () => import('../views/agent/FAQ.vue')
const AgentKnowledgeBase = () => import('../views/agent/KnowledgeBase.vue')
const QuestionAnalysis = () => import('../views/agent/QuestionAnalysis.vue')
const ServiceAgentManagement = () => import('../views/agent/ServiceAgentManagement.vue')
const NotFound = () => import('../views/NotFound.vue')

// 导入移动端聊天页面
import MobileChat from '@/views/MobileChat.vue'
import MobileEntry from '@/views/MobileEntry.vue'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { title: '登录' }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: { title: '注册' }
  },
  {
    path: '/sso-login',
    name: 'SsoLogin',
    component: SsoLogin,
    meta: { title: 'SSO登录' }
  },
  {
    path: '/user',
    component: UserLayout,
    redirect: '/user/chat',
    meta: { requiresAuth: true, userType: 'user' },
    children: [
      {
        path: 'chat',
        name: 'UserChat',
        component: UserChat,
        meta: { title: '熊小智客服' }
      },
      {
        path: 'history',
        name: 'UserHistory',
        component: UserHistory,
        meta: { title: '历史会话' }
      },
      {
        path: 'profile',
        name: 'UserProfile',
        component: UserProfile,
        meta: { title: '个人中心' }
      },
      {
        path: 'evaluation',
        name: 'UserEvaluation',
        component: UserEvaluation,
        meta: { title: '服务评价' }
      }
    ]
  },
  {
    path: '/agent',
    component: AgentLayout,
    redirect: '/agent/dashboard',
    meta: { requiresAuth: true, userType: 'agent' },
    children: [
      {
        path: 'dashboard',
        name: 'AgentDashboard',
        component: AgentDashboard,
        meta: { title: '工作台' }
      },
      {
        path: 'chat/:sessionId?',
        name: 'AgentChat',
        component: AgentChat,
        meta: { title: '会话服务' }
      },
      {
        path: 'history',
        name: 'AgentHistory',
        component: AgentHistory,
        meta: { title: '历史会话' }
      },
      {
        path: 'users',
        name: 'AgentUsers',
        component: AgentUsers,
        meta: { 
          title: '用户管理', 
          requiresAdmin: true // 需要管理员权限
        }
      },
      {
        path: 'service-agents',
        name: 'ServiceAgentManagement',
        component: ServiceAgentManagement,
        meta: { 
          title: '客服管理', 
          requiresAdmin: true // 需要管理员权限
        }
      },
      {
        path: 'faq',
        name: 'AgentFAQ',
        component: AgentFAQ,
        meta: { title: '常见问题' }
      },
      {
        path: 'question-analysis',
        name: 'QuestionAnalysis',
        component: QuestionAnalysis,
        meta: { title: '热门问题分析' }
      },
      {
        path: 'knowledge-base',
        name: 'AgentKnowledgeBase',
        component: AgentKnowledgeBase,
        meta: { title: '知识库管理' }
      }
    ]
  },
  {
    path: '/mobile',
    name: 'MobileEntry',
    component: MobileEntry,
    meta: { 
      title: '在线客服咨询',
      platform: 'mobile'
    }
  },
  {
    path: '/mobile-chat',
    name: 'MobileChat',
    component: MobileChat,
    meta: { 
      title: '熊小智客服',
      platform: 'mobile'
    }
  },
  {
    path: '/mobile-chat-h5',
    name: 'MobileChatH5',
    component: MobileChatH5,
    meta: {
      title: '熊小智客服',
      platform: 'mobile'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: { title: '页面不存在' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 检测是否为移动设备的函数
  const isMobileDevice = () => {
    return (
      navigator.userAgent.match(/Android/i) ||
      navigator.userAgent.match(/webOS/i) ||
      navigator.userAgent.match(/iPhone/i) ||
      navigator.userAgent.match(/iPad/i) ||
      navigator.userAgent.match(/iPod/i) ||
      navigator.userAgent.match(/BlackBerry/i) ||
      navigator.userAgent.match(/Windows Phone/i) ||
      (window.innerWidth <= 768)
    );
  };

  // 如果是访问聊天页面
  if (to.path === '/chat') {
    // 如果是移动设备但不是主动访问PC版
    if (isMobileDevice() && !to.query.forceDesktop) {
      // 重定向到移动版聊天页面，保留查询参数
      next({ 
        path: '/mobile-chat', 
        query: { ...to.query }
      });
      return;
    }
  }
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title;
  }
  
  // 检查是否需要登录权限
  if (to.matched.some(record => record.meta.requiresAuth)) {
    // 从localStorage获取用户信息
    const userInfo = localStorage.getItem('userInfo')
    const token = localStorage.getItem('token')
    const role = localStorage.getItem('role')
    
    // 检查是否已登录
    if (!userInfo || !token) {
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
    } else {
      // 检查用户类型是否匹配
      const user = JSON.parse(userInfo)
      const userType = to.meta.userType
      
      // 检查是否需要管理员权限
      if (to.meta.requiresAdmin && role !== 'admin') {
        // 对于需要管理员权限的页面，如果不是管理员，重定向到工作台
        next({ path: '/agent/dashboard' })
      }
      // 普通用户权限检查
      else if (userType === 'user' && !user.phone) {
        next({ path: '/login' })
      } 
      // 客服权限检查
      else if (userType === 'agent' && !user.agentNo) {
        next({ path: '/login' })
      } else {
        next()
      }
    }
  } else {
    next()
  }
})

// 全局导航后置钩子
router.afterEach((to, from) => {
  const chatStore = useChatStore()
  
  const enteringChatPage = to.path.startsWith('/agent/chat') || to.path.startsWith('/user/chat')
  
  if (enteringChatPage) {
    console.log('进入聊天页面，设置页面状态')
    chatStore.setInChatPage(true)
  } else {
    console.log('离开聊天页面，设置页面状态')
    chatStore.setInChatPage(false)
  }
})

export default router 