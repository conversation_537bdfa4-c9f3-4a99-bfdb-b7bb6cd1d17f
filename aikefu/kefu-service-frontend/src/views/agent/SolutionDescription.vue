<template>
  <div class="solution-container">
    <el-card class="solution-card">
      <template #header>
        <div class="solution-header">
          <span>添加问题解决方案</span>
        </div>
      </template>
      
      <div class="solution-body">
        <el-form :model="form" ref="solutionForm" label-position="top">
          <el-form-item label="问题是否解决" prop="isSolved">
            <el-radio-group v-model="form.isSolved">
              <el-radio :label="1">已解决</el-radio>
              <el-radio :label="0">未解决</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="解决方案描述" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="5"
              placeholder="请输入问题解决方案描述..."
              :maxlength="1000"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item>
            <div class="form-actions">
              <el-button type="primary" @click="submitSolution" :loading="submitting">提交解决方案</el-button>
              <el-button @click="$emit('cancel')">取消</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { addSolutionDescription } from '@/api/chat'

const props = defineProps({
  sessionId: {
    type: [Number, String],
    required: true
  }
})

const emit = defineEmits(['success', 'cancel'])

const solutionForm = ref(null)
const submitting = ref(false)

const form = reactive({
  isSolved: 1, // 默认已解决
  description: ''
})

// 提交解决方案
const submitSolution = async () => {
  if (!form.description.trim()) {
    ElMessage.warning('请输入解决方案描述')
    return
  }
  
  submitting.value = true
  
  try {
    const response = await addSolutionDescription(props.sessionId, {
      solutionDescription: form.description,
      isSolved: form.isSolved
    })
    
    if (response.code === 200) {
      ElMessage.success('解决方案提交成功')
      // 将解决方案数据传递给父组件
      emit('success', {
        description: form.description,
        isSolved: form.isSolved
      })
    } else {
      ElMessage.error(response.message || '解决方案提交失败')
    }
  } catch (error) {
    console.error('提交解决方案失败', error)
    ElMessage.error('解决方案提交失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.solution-container {
  max-width: 600px;
  margin: 20px auto;
}

.solution-card {
  border-radius: 8px;
}

.solution-header {
  font-size: 16px;
  font-weight: 500;
}

.solution-body {
  padding: 10px 0;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 20px;
}
</style> 