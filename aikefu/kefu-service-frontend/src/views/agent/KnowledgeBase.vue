<template>
  <div class="knowledge-base-container">
    <!-- 顶部信息区域 -->
    <div class="top-stats">
      <el-row :gutter="24">
        <el-col :span="4">
          <el-card shadow="hover" class="stats-card collection-total-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><Folder /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-count">{{ collections.length }}</div>
                <div class="stats-label">知识库集合数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card shadow="hover" class="stats-card docs-total-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-count">{{ total }}</div>
                <div class="stats-label">知识库文档数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stats-card add-document-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><Plus /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-label">添加知识文档</div>
                <el-button type="primary" @click="showAddDocumentDialog" class="add-doc-btn">添加文档</el-button>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card shadow="hover" class="stats-card add-collection-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><FolderAdd /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-label">添加知识库集合</div>
                <el-button type="primary" @click="showAddCollectionDialog" class="add-doc-btn">添加集合</el-button>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card shadow="hover" class="stats-card collection-select-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><Collection /></el-icon>
              </div>
              <div class="stats-main">
                <div class="stats-label">当前知识库集合</div>
                <el-select 
                  v-model="currentCollection" 
                  placeholder="请选择知识库集合"
                  @change="handleCollectionChange"
                  class="collection-select"
                >
                  <el-option
                    v-for="item in collections"
                    :key="item.name"
                    :label="getCollectionDisplayName(item.name)"
                    :value="item.name"
                  />
                </el-select>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="main-content">
      <!-- 左侧：知识文档列表 -->
      <el-card shadow="hover" class="document-list-card">
        <template #header>
          <div class="card-header">
            <h3>知识文档列表</h3>
          </div>
        </template>
        
        <el-table 
          v-loading="docsLoading"
          :data="documents"
          style="width: 100%"
          max-height="580"
          @row-click="handleRowClick"
          :row-class-name="tableRowClassName"
          :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266', fontWeight: 'bold' }"
        >
          <el-table-column label="文档内容">
            <template #default="scope">
              <div class="document-content">
                <div class="document-preview">{{ getPreviewText(scope.row.document) }}</div>
                <div class="document-full" v-if="hasDetailText(scope.row.document)">{{ getDetailText(scope.row.document) }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="160" align="center" fixed="right">
            <template #default="scope">
              <div class="action-buttons">
                <el-button class="action-btn edit-btn" link type="primary" @click.stop="handleEdit(scope.row)">
                  <el-icon><Edit /></el-icon> 编辑
                </el-button>
                <el-button class="action-btn delete-btn" link type="danger" @click.stop="handleDelete(scope.row)">
                  <el-icon><Delete /></el-icon> 删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container">
          <el-pagination
            background
            layout="prev, pager, next"
            :total="total"
            :page-size="pageSize"
            :current-page="currentPage"
            @current-change="handlePageChange"
          />
        </div>
      </el-card>

      <!-- 右侧：检索测试 -->
      <div class="right-panel">
        <!-- 检索测试区 -->
        <el-card shadow="hover" class="search-test-card">
          <template #header>
            <div class="card-header">
              <h3>知识库检索</h3>
              <div class="search-input-group">
                <el-input
                  v-model="queryText"
                  placeholder="输入问题进行测试"
                  @keyup.enter="testQuery"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                <el-button type="primary" @click="testQuery" :disabled="!queryText" :loading="queryLoading">查询</el-button>
              </div>
            </div>
          </template>
          
          <div v-if="queryResults.length > 0" class="query-results">
            <div v-for="(result, index) in queryResults" :key="index" class="result-item">
              <div class="result-header">
                <h4>匹配结果 #{{ index + 1 }}</h4>
                <div class="result-actions">
                  <span class="similarity">相似度：<span class="similarity-value">{{ result.distance ? (1 - result.distance).toFixed(4) : '未知' }}</span></span>
                  <el-button class="action-btn edit-btn" link type="primary" @click="handleEditResult(result)" v-if="result.id">
                    <el-icon><Edit /></el-icon> 编辑
                  </el-button>
                  <el-button class="action-btn delete-btn" link type="danger" @click.stop="handleDeleteResult(result)" v-if="result.id">
                    <el-icon><Delete /></el-icon> 删除
                  </el-button>
                </div>
              </div>
              <div class="result-content">{{ result.document }}</div>
            </div>
          </div>
          <div v-else-if="queryTested" class="no-results">
            <el-empty description="暂无匹配结果" />
          </div>
          <div v-else class="query-placeholder">
            <p>在上方输入框中输入问题，点击"查询"按钮或按Enter键查看知识库匹配效果。</p>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 添加知识文档对话框 -->
    <el-dialog 
      v-model="addDialogVisible" 
      title="添加知识文档" 
      width="600px"
      destroy-on-close
    >
      <el-form>
        <el-form-item label="知识内容">
          <el-input
            v-model="newDocument"
            type="textarea"
            :rows="8"
            placeholder="请输入知识文档内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span>
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="addDocument" :disabled="!newDocument">确认</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 编辑知识文档对话框 -->
    <el-dialog 
      v-model="editDialogVisible" 
      title="编辑知识文档" 
      width="600px"
      destroy-on-close
    >
      <el-form label-position="top">
        <el-form-item label="知识内容">
          <el-input
            v-model="editingDocument.text"
            type="textarea"
            :rows="10"
            placeholder="请输入知识文档内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span>
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveDocument" :disabled="!editingDocument.text">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加知识库集合对话框 -->
    <el-dialog 
      v-model="addCollectionDialogVisible" 
      title="添加知识库集合" 
      width="500px"
      destroy-on-close
    >
      <el-form>
        <el-form-item label="集合名称">
          <el-input
            v-model="newCollectionName"
            placeholder="请输入知识库集合名称，只能输入字母、数字、下划线"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span>
          <el-button @click="addCollectionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="addCollection" :disabled="!newCollectionName">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Folder, Document, Plus, Edit, Delete, Search, Collection, FolderAdd } from '@element-plus/icons-vue';
import { 
  listCollections, 
  addDocuments, 
  deleteDocuments, 
  updateDocuments, 
  listDocuments,
  queryDocuments,
  createCollection,
  getCollectionDisplayName
} from '@/api/knowledge';

// 状态变量
const collections = ref([]);
const collectionsDebug = ref([]); // 用于调试显示的原始集合名称
const currentCollection = ref('');
const documents = ref([]);
const docsLoading = ref(false); // 文档列表加载状态
const queryLoading = ref(false); // 查询加载状态
const total = ref(0);
const pageSize = ref(10);
const currentPage = ref(1);
const selectedDocument = ref(null);
const editingDocument = ref({ id: '', text: '' });
const addDialogVisible = ref(false);
const editDialogVisible = ref(false); // 新增编辑对话框可见状态
const newDocument = ref('');
const queryText = ref('');
const queryResults = ref([]);
const queryTested = ref(false);
const addCollectionDialogVisible = ref(false);
const newCollectionName = ref('');

// 获取预览文本，截取前30个字符
const getPreviewText = (text) => {
  if (!text) return '';
  // 分割文本为行
  const lines = text.split('\n');
  // 返回第一行
  return lines[0];
};

// 获取详细内容（第一行之后的所有内容）
const getDetailText = (text) => {
  if (!text) return '';
  const lines = text.split('\n');
  // 如果只有一行或没有内容，返回空字符串
  if (lines.length <= 1) return '';
  // 返回第二行开始的所有内容
  return lines.slice(1).join('\n').trim();
};

// 判断是否有详细内容（超过一行）
const hasDetailText = (text) => {
  if (!text) return false;
  return text.includes('\n');
};

// 初始化函数
onMounted(async () => {
  docsLoading.value = true;
  try {
    // 直接使用fetch调用API，避免中间层处理
    const response = await fetch('https://berhomellm.cpolar.cn/collections/list', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`请求失败: ${response.status}`);
    }
    
    const data = await response.json();
    console.log('集合列表原始响应:', data);
    
    // 确定数据结构并处理
    if (data && Array.isArray(data.collections)) {
      // 保存原始数据用于调试
      collectionsDebug.value = [...data.collections];
      
      // 转换为组件需要的格式
      collections.value = data.collections.map(name => ({
        name: name
      }));
    } 
    else if (data && Array.isArray(data)) {
      // 响应直接是数组
      collectionsDebug.value = [...data];
      collections.value = data.map(name => ({
        name: typeof name === 'string' ? name : String(name)
      }));
    }
    
    console.log('处理后的集合数据:', collections.value);
    
    if (collections.value.length > 0) {
      // 查找recycle_knowledge集合
      const recycleKnowledge = collections.value.find(collection => collection.name === 'recycle_knowledge');
      
      if (recycleKnowledge) {
        // 如果找到recycle_knowledge集合，设为默认
        currentCollection.value = recycleKnowledge.name;
        console.log('选择的默认集合:', currentCollection.value);
      } else {
        // 如果没有找到，则使用第一个集合
        currentCollection.value = collections.value[0].name;
        console.log('未找到recycle_knowledge集合，选择的默认集合:', currentCollection.value);
      }
      
      // 加载选定集合的文档
      await loadDocuments();
    } else {
      ElMessage.warning('暂无可用的知识库集合，请联系管理员');
    }
  } catch (error) {
    console.error('初始化失败', error);
    ElMessage.error('获取知识库数据失败，请刷新重试');
  } finally {
    docsLoading.value = false;
  }
});

// 切换集合
const handleCollectionChange = async () => {
  console.log('切换到集合:', currentCollection.value);
  // 重置页面状态
  currentPage.value = 1;
  selectedDocument.value = null;
  editingDocument.value = { id: '', text: '' };
  // 加载新集合的文档
  await loadDocuments();
};

// 加载文档列表
const loadDocuments = async () => {
  if (!currentCollection.value) {
    console.warn('未选择集合，无法加载文档');
    return;
  }
  
  docsLoading.value = true;
  try {
    console.log('加载集合文档:', currentCollection.value);
    const res = await listDocuments(currentPage.value, pageSize.value, currentCollection.value);
    console.log('获取文档列表响应:', res);
    
    // 尝试兼容不同的响应格式
    if (res && (res.results || res.data?.results)) {
      const resultsData = res.results || res.data?.results;
      
      if (resultsData) {
        // 转换格式
        const { ids, documents: docs, pagination } = resultsData;
        
        if (Array.isArray(ids) && Array.isArray(docs)) {
          documents.value = ids.map((id, index) => ({
            id,
            document: docs[index]
          }));
          
          // 更新分页信息
          if (pagination) {
            total.value = pagination.total;
          }
        } else {
          documents.value = [];
          total.value = 0;
        }
      } else {
        documents.value = [];
        total.value = 0;
      }
    } else {
      // 尝试解析其他可能的格式
      if (res && Array.isArray(res)) {
        documents.value = res.map(item => ({
          id: item.id || '',
          document: item.text || item.content || item
        }));
        total.value = res.length;
      } else if (res && Array.isArray(res.data)) {
        documents.value = res.data.map(item => ({
          id: item.id || '',
          document: item.text || item.content || item
        }));
        total.value = res.data.length;
      } else {
        documents.value = [];
        total.value = 0;
      }
    }
  } catch (error) {
    console.error('加载文档失败', error);
    ElMessage.error('获取知识文档失败，请刷新重试');
  } finally {
    docsLoading.value = false;
  }
};

// 分页处理
const handlePageChange = (page) => {
  currentPage.value = page;
  loadDocuments();
  // 清除选中状态
  selectedDocument.value = null;
  editingDocument.value = { id: '', text: '' };
};

// 点击行选择文档
const handleRowClick = (row) => {
  selectedDocument.value = row;
};

// 编辑文档
const handleEdit = (row) => {
  selectedDocument.value = row;
  editingDocument.value = {
    id: row.id,
    text: row.document
  };
  editDialogVisible.value = true; // 打开编辑对话框
};

// 编辑检索结果
const handleEditResult = (result) => {
  editingDocument.value = {
    id: result.id,
    text: result.document
  };
  editDialogVisible.value = true; // 打开编辑对话框
};

// 删除文档
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该知识文档吗？该操作不可恢复。', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteDocuments([row.id], currentCollection.value);
      console.log('删除文档响应:', res);
      
      // 处理不同响应格式
      if (res && (res.deleted || res.data?.deleted)) {
        const deleted = res.deleted || res.data?.deleted;
        if (deleted && deleted.length > 0) {
          ElMessage.success('删除成功');
          // 刷新列表
          await loadDocuments();
          // 清除选中状态
          if (selectedDocument.value && selectedDocument.value.id === row.id) {
            selectedDocument.value = null;
            editingDocument.value = { id: '', text: '' };
          }
          return;
        }
      }
      
      // 如果没有明确的成功标志，但也没有报错，假定成功
      if (res) {
        ElMessage.success('删除成功');
        await loadDocuments();
        // 清除选中状态
        if (selectedDocument.value && selectedDocument.value.id === row.id) {
          selectedDocument.value = null;
          editingDocument.value = { id: '', text: '' };
        }
      } else {
        ElMessage.error('删除失败');
      }
    } catch (error) {
      console.error('删除失败', error);
      ElMessage.error('删除失败，请重试');
    }
  }).catch(() => {});
};

// 保存修改的文档
const saveDocument = async () => {
  if (!editingDocument.value.text.trim()) {
    ElMessage.warning('知识内容不能为空');
    return;
  }
  
  try {
    // 判断是更新还是新增
    if (editingDocument.value.id) {
      // 更新
      const documents = [{
        id: editingDocument.value.id,
        text: editingDocument.value.text
      }];
      
      const res = await updateDocuments(documents, currentCollection.value);
      console.log('更新文档响应:', res);
      
      // 处理不同响应格式
      if (res && (res.updated || res.data?.updated)) {
        const updated = res.updated || res.data?.updated;
        if (updated && updated.length > 0) {
          ElMessage.success('更新成功');
          // 关闭编辑对话框
          editDialogVisible.value = false;
          // 刷新列表
          await loadDocuments();
          // 如果当前有搜索结果且包含更新的文档，更新搜索结果
          updateQueryResultsAfterEdit(editingDocument.value.id, editingDocument.value.text);
          return;
        }
      }
      
      // 如果没有明确的成功标志，但也没有报错，假定成功
      if (res) {
        ElMessage.success('更新成功');
        // 关闭编辑对话框
        editDialogVisible.value = false;
        await loadDocuments();
        // 如果当前有搜索结果且包含更新的文档，更新搜索结果
        updateQueryResultsAfterEdit(editingDocument.value.id, editingDocument.value.text);
      } else {
        ElMessage.error('更新失败');
      }
    } else {
      // 新增
      const res = await addDocuments([{
        text: editingDocument.value.text,
        collection_name: currentCollection.value
      }]);
      console.log('添加文档响应:', res);
      
      // 处理不同响应格式
      if (res && (res.results || res.data?.results)) {
        const results = res.results || res.data?.results;
        if (results && results[currentCollection.value]?.length > 0) {
          ElMessage.success('添加成功');
          // 关闭编辑对话框
          editDialogVisible.value = false;
          // 刷新列表
          await loadDocuments();
          // 清除编辑状态
          selectedDocument.value = null;
          editingDocument.value = { id: '', text: '' };
          return;
        }
      }
      
      // 如果没有明确的成功标志，但也没有报错，假定成功
      if (res) {
        ElMessage.success('添加成功');
        // 关闭编辑对话框
        editDialogVisible.value = false;
        await loadDocuments();
        // 清除编辑状态
        selectedDocument.value = null;
        editingDocument.value = { id: '', text: '' };
      } else {
        ElMessage.error('添加失败');
      }
    }
  } catch (error) {
    console.error('保存失败', error);
    ElMessage.error('保存失败，请重试');
  }
};

// 在编辑后更新检索结果
const updateQueryResultsAfterEdit = (id, newText) => {
  if (!queryResults.value.length) return;
  
  // 查找并更新匹配的结果
  queryResults.value = queryResults.value.map(result => {
    if (result.id === id) {
      return { ...result, document: newText };
    }
    return result;
  });
};

// 显示添加对话框
const showAddDocumentDialog = () => {
  newDocument.value = '';
  addDialogVisible.value = true;
};

// 添加新文档
const addDocument = async () => {
  if (!newDocument.value.trim()) {
    ElMessage.warning('知识内容不能为空');
    return;
  }
  
  try {
    const res = await addDocuments([{
      text: newDocument.value,
      collection_name: currentCollection.value
    }]);
    console.log('添加新文档响应:', res);
    
    // 处理不同响应格式
    if (res && (res.results || res.data?.results)) {
      const results = res.results || res.data?.results;
      if (results && results[currentCollection.value]?.length > 0) {
        ElMessage.success('添加成功');
        // 刷新列表
        await loadDocuments();
        // 关闭对话框
        addDialogVisible.value = false;
        return;
      }
    }
    
    // 如果没有明确的成功标志，但也没有报错，假定成功
    if (res) {
      ElMessage.success('添加成功');
      await loadDocuments();
      addDialogVisible.value = false;
    } else {
      ElMessage.error('添加失败');
    }
  } catch (error) {
    console.error('添加失败', error);
    ElMessage.error('添加失败，请重试');
  }
};

// 测试知识库检索
const testQuery = async () => {
  if (!queryText.value.trim()) {
    ElMessage.warning('请输入测试问题');
    return;
  }
  
  queryLoading.value = true;
  queryTested.value = true;
  
  try {
    const res = await queryDocuments(queryText.value, 3, currentCollection.value);
    console.log('检索测试响应:', res);
    
    // 尝试兼容不同的响应格式
    if (res && (res.results || res.data?.results)) {
      const resultsData = res.results || res.data?.results;
      
      if (resultsData && resultsData.documents && resultsData.documents[0]) {
        const documents = resultsData.documents[0];
        const distances = resultsData.distances && resultsData.distances[0] ? resultsData.distances[0] : [];
        const ids = resultsData.ids && resultsData.ids[0] ? resultsData.ids[0] : [];
        
        // 组合结果
        queryResults.value = documents.map((doc, index) => ({
          document: doc,
          distance: distances[index] || null,
          id: ids[index] || null
        }));
      } else {
        queryResults.value = [];
      }
    } else if (res && Array.isArray(res)) {
      // 可能是直接返回结果数组
      queryResults.value = res.map(item => ({
        document: item.document || item.text || item,
        distance: item.distance || item.score || null,
        id: item.id || null
      }));
    } else if (res && Array.isArray(res.data)) {
      // 可能是包装在data字段中的结果数组
      queryResults.value = res.data.map(item => ({
        document: item.document || item.text || item,
        distance: item.distance || item.score || null,
        id: item.id || null
      }));
    } else {
      queryResults.value = [];
    }
    
    if (queryResults.value.length === 0) {
      ElMessage.info('未找到匹配的知识文档');
    }
  } catch (error) {
    console.error('检索测试失败', error);
    ElMessage.error('检索测试失败，请重试');
    queryResults.value = [];
  } finally {
    queryLoading.value = false;
  }
};

// 删除检索结果文档
const handleDeleteResult = (result) => {
  ElMessageBox.confirm('确定要删除该知识文档吗？该操作不可恢复。', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteDocuments([result.id], currentCollection.value);
      console.log('删除文档响应:', res);
      
      // 处理不同响应格式
      if (res && (res.deleted || res.data?.deleted)) {
        const deleted = res.deleted || res.data?.deleted;
        if (deleted && deleted.length > 0) {
          ElMessage.success('删除成功');
          // 从检索结果中移除
          queryResults.value = queryResults.value.filter(item => item.id !== result.id);
          // 刷新文档列表
          await loadDocuments();
          return;
        }
      }
      
      // 如果没有明确的成功标志，但也没有报错，假定成功
      if (res) {
        ElMessage.success('删除成功');
        // 从检索结果中移除
        queryResults.value = queryResults.value.filter(item => item.id !== result.id);
        // 刷新文档列表
        await loadDocuments();
      } else {
        ElMessage.error('删除失败');
      }
    } catch (error) {
      console.error('删除失败', error);
      ElMessage.error('删除失败，请重试');
    }
  }).catch(() => {});
};

// 为表格添加行样式
const tableRowClassName = ({ row, rowIndex }) => {
  return 'table-row';
};

// 显示添加集合对话框
const showAddCollectionDialog = () => {
  newCollectionName.value = '';
  addCollectionDialogVisible.value = true;
};

// 添加新集合
const addCollection = async () => {
  if (!newCollectionName.value.trim()) {
    ElMessage.warning('集合名称不能为空');
    return;
  }
  
  try {
    // 调用创建集合API
    const res = await createCollection(newCollectionName.value);
    console.log('创建集合响应:', res);
    
    // 处理响应
    if (res && res.name) {
      if (res.status === 'created') {
        ElMessage.success(`创建集合 "${getCollectionDisplayName(res.name)}" 成功`);
      } else if (res.status === 'already_exists') {
        ElMessage.info(`集合 "${getCollectionDisplayName(res.name)}" 已存在`);
      }
      
      // 关闭对话框
      addCollectionDialogVisible.value = false;
      
      // 刷新集合列表
      try {
        // 直接使用fetch调用API，以保持与原始初始化方法一致
        const response = await fetch('https://berhomellm.cpolar.cn/collections/list', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        if (!response.ok) {
          throw new Error(`请求失败: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('刷新集合列表响应:', data);
        
        // 使用与初始化相同的处理方式
        if (data && Array.isArray(data.collections)) {
          collectionsDebug.value = [...data.collections];
          collections.value = data.collections.map(name => ({
            name: name
          }));
        } 
        else if (data && Array.isArray(data)) {
          collectionsDebug.value = [...data];
          collections.value = data.map(name => ({
            name: typeof name === 'string' ? name : String(name)
          }));
        }
        
        // 如果创建了新集合，选择它
        if (res.status === 'created') {
          currentCollection.value = res.name;
          // 加载新集合的文档
          await loadDocuments();
        }
      } catch (refreshError) {
        console.error('刷新集合列表失败:', refreshError);
        ElMessage.warning('集合创建成功，但刷新列表失败，请手动刷新页面');
      }
    } else {
      ElMessage.error('创建集合失败');
    }
  } catch (error) {
    console.error('添加集合失败', error);
    ElMessage.error('添加集合失败，请重试');
  }
};
</script>

<style lang="scss" scoped>
.knowledge-base-container {
  padding: 20px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  background-color: #f5f7fa;
  
  .top-stats {
    margin-bottom: 24px;
    
    .stats-card {
      height: 100%;
      border-radius: 8px;
      transition: all 0.3s ease;
      border: none;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
      }
      
      .stats-content {
        display: flex;
        align-items: center;
        padding: 8px;
        
        .stats-icon {
          font-size: 36px;
          margin-right: 20px;
          border-radius: 8px;
          width: 60px;
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .stats-info {
          flex: 1;
          
          .stats-count {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 6px;
            transition: all 0.3s ease;
          }
          
          .stats-label {
            color: #606266;
            font-size: 14px;
            margin-bottom: 10px;
            font-weight: 500;
          }
          
          .add-doc-btn {
            width: 100%;
            border-radius: 4px;
            height: 40px;
            font-weight: 500;
            transition: all 0.3s ease;
            
            &:hover {
              transform: scale(1.03);
            }
          }
        }
        
        .stats-main {
          width: 100%;
          
          .stats-label {
            margin-bottom: 10px;
            color: #606266;
            font-size: 14px;
            font-weight: 500;
          }
          
          .collection-select {
            width: 100%;
            
            :deep(.el-input__wrapper) {
              border-radius: 4px;
            }
          }
        }
      }
    }
    
    .collection-total-card {
      .stats-icon {
        background: linear-gradient(135deg, #4d7cff, #1a56ff);
        color: white;
      }
      
      .stats-count {
        color: #1a56ff;
      }
    }
    
    .docs-total-card {
      .stats-icon {
        background: linear-gradient(135deg, #36d1dc, #5b86e5);
        color: white;
      }
      
      .stats-count {
        color: #5b86e5;
      }
    }
    
    .collection-select-card {
      .stats-icon {
        background: linear-gradient(135deg, #ff9966, #ff5e62);
        color: white;
      }
    }
    
    .add-collection-card {
      .stats-icon {
        background: linear-gradient(135deg, #FF6B6B, #FF8E53);
        color: white;
      }
    }
    
    .add-document-card {
      .stats-icon {
        background: linear-gradient(135deg, #56ab2f, #a8e063);
        color: white;
      }
    }
  }
  
  .main-content {
    display: flex;
    gap: 24px;
    flex: 1;
    min-height: 0;
    
    .document-list-card {
      width: 40%;
      display: flex;
      flex-direction: column;
      max-height: calc(100vh - 240px);
      overflow-y: auto;
      border-radius: 8px;
      border: none;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 20px;
        
        h3 {
          margin: 0;
          font-size: 18px;
          color: #303133;
          font-weight: 600;
        }
      }
      
      .document-content {
        padding: 8px 0;
        
        .document-preview {
          font-weight: 600;
          margin-bottom: 8px;
          color: #303133;
          font-size: 15px;
        }
        
        .document-full {
          color: #606266;
          font-size: 13px;
          white-space: normal;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          padding-left: 12px;
          border-left: 2px solid #ebeef5;
          transition: all 0.3s ease;
        }
      }
      
      :deep(.table-row) {
        transition: all 0.3s ease;
        
        &:hover {
          background-color: #f0f7ff !important;
          
          .document-full {
            border-left-color: #409EFF;
          }
        }
        
        td {
          padding: 14px 12px;
        }
      }
      
      .action-buttons {
        display: flex;
        justify-content: center;
        gap: 8px;
        
        .action-btn {
          display: flex;
          align-items: center;
          padding: 4px 8px;
          border-radius: 4px;
          transition: all 0.2s ease;
          
          .el-icon {
            margin-right: 4px;
          }
          
          &:hover {
            background-color: rgba(64, 158, 255, 0.1);
          }
          
          &.delete-btn:hover {
            background-color: rgba(245, 108, 108, 0.1);
          }
        }
      }
      
      .pagination-container {
        margin-top: 24px;
        display: flex;
        justify-content: center;
        padding-bottom: 16px;
      }
    }
    
    .right-panel {
      width: 60%;
      display: flex;
      flex-direction: column;
      max-height: calc(100vh - 240px);
      overflow-y: auto;
      
      .search-test-card {
        flex: 1;
        overflow-y: auto;
        border-radius: 8px;
        border: none;
        
        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 20px;
          flex-wrap: wrap;
          gap: 12px;
          
          h3 {
            margin: 0;
            font-size: 18px;
            color: #303133;
            font-weight: 600;
          }
          
          .search-input-group {
            display: flex;
            gap: 12px;
            width: 350px;
            
            :deep(.el-input__wrapper) {
              border-radius: 4px;
            }
            
            .el-button {
              border-radius: 4px;
              font-weight: 500;
              height: 40px;
              min-width: 80px;
              transition: all 0.3s ease;
              
              &:hover:not(:disabled) {
                transform: scale(1.03);
              }
            }
          }
        }
        
        .query-results {
          max-height: calc(100vh - 340px);
          overflow-y: auto;
          padding: 0 20px 20px;
          
          .result-item {
            margin-bottom: 20px;
            border: 1px solid #ebeef5;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            animation: fadeIn 0.5s ease;
            
            &:hover {
              border-color: #c6e2ff;
              box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.08);
            }
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .result-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12px;
              padding-bottom: 8px;
              border-bottom: 1px dashed #ebeef5;
              
              h4 {
                margin: 0;
                font-size: 16px;
                color: #303133;
              }
              
              .result-actions {
                display: flex;
                align-items: center;
                gap: 12px;
                
                .similarity {
                  font-size: 14px;
                  color: #606266;
                  
                  .similarity-value {
                    color: #409EFF;
                    font-weight: 500;
                  }
                }
                
                .action-btn {
                  display: flex;
                  align-items: center;
                  padding: 4px 8px;
                  border-radius: 4px;
                  transition: all 0.2s ease;
                  
                  .el-icon {
                    margin-right: 4px;
                  }
                  
                  &:hover {
                    background-color: rgba(64, 158, 255, 0.1);
                  }
                  
                  &.delete-btn:hover {
                    background-color: rgba(245, 108, 108, 0.1);
                  }
                }
              }
            }
            
            .result-content {
              background-color: #f7f9fc;
              padding: 12px;
              border-radius: 6px;
              white-space: pre-wrap;
              line-height: 1.6;
              font-size: 14px;
              color: #303133;
            }
          }
        }
        
        .query-placeholder, .no-results {
          height: 240px;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #909399;
          font-size: 15px;
        }
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 