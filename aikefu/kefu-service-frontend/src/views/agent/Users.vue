<template>  <div class="users-container">        <!-- 用户列表 -->    <div class="users-list">      <el-card shadow="hover">        <template #header>          <div class="card-header">            <span>用户管理</span>            <div class="header-operations">              <el-button type="primary" size="small" @click="createUser">                <el-icon><Plus /></el-icon> 新增用户              </el-button>              <el-button type="primary" size="small" @click="exportUsers">                <el-icon><Download /></el-icon> 导出              </el-button>            </div>          </div>        </template>                <!-- 搜索筛选区 -->        <el-form :inline="true" class="search-form">          <el-form-item label="用户类型">            <el-select v-model="queryParams.userType" placeholder="全部类型" clearable style="width: 200px">              <el-option label="全部" value="" />              <el-option label="普通用户" value="1" />              <el-option label="会员" value="2" />              <el-option label="VIP" value="3" />            </el-select>          </el-form-item>                    <el-form-item label="注册时间">            <el-date-picker              v-model="dateRange"              type="daterange"              range-separator="至"              start-placeholder="开始日期"              end-placeholder="结束日期"              value-format="YYYY-MM-DD"              @change="handleDateRangeChange"              style="width: 350px"            />          </el-form-item>                    <el-form-item label="搜索">            <el-input              v-model="queryParams.keyword"              placeholder="用户名/ID/手机号"              clearable              @keyup.enter="handleSearch"            />          </el-form-item>                    <el-form-item>            <el-button type="primary" @click="handleSearch">查询</el-button>            <el-button @click="resetQuery">重置</el-button>          </el-form-item>        </el-form>
        
        <el-table
          v-loading="loading"
          :data="userList"
          style="width: 100%"
          border
          stripe
          :header-cell-style="{background:'#f5f7fa'}"
          height="calc(100vh - 300px)"
          :max-height="tableMaxHeight"
        >
          <el-table-column type="selection" width="50" />
          <el-table-column prop="id" label="用户ID" width="100" />
          <el-table-column label="用户信息" width="180">
            <template #default="scope">
              <div class="user-info">
                <el-avatar :size="32" :src="scope.row.avatar">
                  {{ scope.row.nickname ? scope.row.nickname.substring(0, 1) : '用' }}
                </el-avatar>
                <div class="user-detail">
                  <div class="user-name">{{ scope.row.nickname || '用户' + scope.row.id }}</div>
                  <div class="user-phone">{{ formatPhone(scope.row.phone) }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="用户类型" width="100">
            <template #default="scope">
              <el-tag :type="getUserTypeTag(scope.row.userType)">
                {{ getUserTypeText(scope.row.userType) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="datasource" label="数据来源" width="120">
            <template #default="scope">
              <el-tag size="small" type="info" v-if="scope.row.datasource">
                {{ scope.row.datasource }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="email" label="邮箱" width="180" show-overflow-tooltip />
          
          <el-table-column label="注册时间" prop="registerTime" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.registerTime) }}
            </template>
          </el-table-column>
          
          <el-table-column label="最后活跃" prop="lastActiveTime" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.lastActiveTime) }}
            </template>
          </el-table-column>
          
          <el-table-column label="会话次数" prop="sessionCount" width="100" align="center" />
          
          <el-table-column label="操作" width="220" fixed="right">
            <template #default="scope">
              <el-button 
                type="primary" 
                link
                @click="viewUserDetail(scope.row)"
              >
                详情
              </el-button>
              <el-button 
                type="primary" 
                link
                @click="editUser(scope.row)"
              >
                编辑
              </el-button>
              <el-button 
                type="danger" 
                link
                @click="handleDeleteUser(scope.row)"
              >
                删除
              </el-button>
              <el-button 
                type="success" 
                link
                @click="startChat(scope.row)"
              >
                发起会话
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            background
          />
        </div>
      </el-card>
    </div>
    
    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="用户详情"
      width="60%"
      destroy-on-close
    >
      <div v-if="currentUser" class="user-detail-dialog">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ currentUser.id }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ currentUser.username }}</el-descriptions-item>
          <el-descriptions-item label="昵称">{{ currentUser.nickname }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ formatPhone(currentUser.phone) }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ currentUser.email }}</el-descriptions-item>
          <el-descriptions-item label="用户类型">
            <el-tag :type="getUserTypeTag(currentUser.userType)">
              {{ getUserTypeText(currentUser.userType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="注册时间">{{ formatDateTime(currentUser.registerTime) }}</el-descriptions-item>
          <el-descriptions-item label="最后活跃">{{ formatDateTime(currentUser.lastActiveTime) }}</el-descriptions-item>
          <el-descriptions-item label="会话次数">{{ currentUser.sessionCount }}</el-descriptions-item>
          <el-descriptions-item label="账户状态">
            <el-tag :type="currentUser.status === 1 ? 'success' : 'danger'">
              {{ currentUser.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
        
        <el-divider content-position="center">最近会话记录</el-divider>
        
        <el-table
          :data="userSessions"
          style="width: 100%"
          border
          size="small"
          :header-cell-style="{background:'#f5f7fa'}"
        >
          <el-table-column prop="id" label="会话ID" width="120" />
          <el-table-column label="会话状态" width="100">
            <template #default="scope">
              <el-tag :type="getSessionStatusType(scope.row.status)">
                {{ getSessionStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="160">
            <template #default="scope">
              {{ formatDateTime(scope.row.createdAt) }}
            </template>
          </el-table-column>
          <el-table-column label="最后消息" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.lastMessage || '无消息' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button type="primary" link @click="viewSessionDetail(scope.row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    
    <!-- 用户编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="formTitle"
      width="50%"
      destroy-on-close
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userFormRules"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="userForm.nickname" placeholder="请输入昵称" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="userForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="用户类型" prop="userType">
          <el-select v-model="userForm.userType" placeholder="请选择用户类型">
            <el-option label="普通用户" value="1" />
            <el-option label="会员" value="2" />
            <el-option label="VIP" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="账户状态" prop="status">
          <el-radio-group v-model="userForm.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitUserForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { Plus, Download } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment'
import { getUsers, getUsersByPage, getUserInfo, updateUser, addUser, deleteUser } from '@/api/user'
import { getSessionsByUserId } from '@/api/chat'

const router = useRouter()

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  userType: '',
  startDate: '',
  endDate: '',
  keyword: ''
})

// 日期范围
const dateRange = ref([])

// 用户列表数据
const userList = ref([])
const total = ref(0)
const loading = ref(false)
const tableMaxHeight = ref('calc(100vh - 300px)')

// 详情对话框
const detailDialogVisible = ref(false)
const currentUser = ref(null)
const userSessions = ref([])

// 编辑对话框
const editDialogVisible = ref(false)
const userFormRef = ref(null)
const userForm = reactive({
  id: '',
  username: '',
  nickname: '',
  phone: '',
  email: '',
  userType: '1',
  status: 1
})
const userFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  nickname: [
    { required: false, max: 30, message: '最大长度为 30 个字符', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  userType: [
    { required: true, message: '请选择用户类型', trigger: 'change' }
  ]
}

// 表单标题
const formTitle = computed(() => {
  return userForm.id ? '编辑用户' : '新增用户'
})

// 生命周期钩子
onMounted(() => {
  // 加载用户列表
  loadUserList()
  
  // 窗口大小变化时调整表格高度
  window.addEventListener('resize', adjustTableHeight)
  adjustTableHeight()
})

onUnmounted(() => {
  // 清理事件监听
  window.removeEventListener('resize', adjustTableHeight)
})

// 调整表格高度
const adjustTableHeight = () => {
  // 设置合适的表格高度，减去其他元素的高度
  nextTick(() => {
    tableMaxHeight.value = `calc(100vh - 300px)`
  })
}

// 加载用户列表
const loadUserList = async () => {
  loading.value = true
  try {
    // 构建API请求参数
    const params = {
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize,
      userType: queryParams.userType || '',
      startDate: queryParams.startDate || '',
      endDate: queryParams.endDate || '',
      keyword: queryParams.keyword || ''
    }
    
    // 调用分页API
    const res = await getUsersByPage(params)
    if (res.code === 200) {
      // 处理API返回的数据
      const pageData = res.data || { records: [], total: 0 }
      
      // 转换数据格式
      userList.value = (pageData.records || pageData.list || []).map(user => ({
        ...user,
        userType: user.vipLevel ? user.vipLevel.toString() : '1', // 使用vipLevel作为用户类型
        registerTime: user.createdAt, // 字段名映射
        lastActiveTime: user.updatedAt || user.createdAt, // 如果没有更新时间，使用创建时间
        sessionCount: user.sessionCount || 0, // 默认会话次数
        status: user.status === undefined ? 1 : user.status // 默认状态为正常
      }))
      
      // 设置总数
      total.value = pageData.total || 0
      
      console.log('用户列表数据:', userList.value)
      console.log('总数:', total.value)
      console.log('当前页:', queryParams.pageNum)
      console.log('每页大小:', queryParams.pageSize)
    } else {
      ElMessage.error(res.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('加载用户列表失败', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 查看用户详情
const viewUserDetail = async (row) => {
  try {
    // 直接使用当前行的数据
    currentUser.value = row
    detailDialogVisible.value = true
    await loadUserSessions(row.id)
  } catch (error) {
    console.error('获取用户详情失败', error)
    ElMessage.error('获取用户详情失败')
  }
}

// 加载用户会话记录
const loadUserSessions = async (userId) => {
  try {
    // 调用真实API
    const res = await getSessionsByUserId(userId)
    if (res.code === 200) {
      userSessions.value = res.data || []
    } else {
      ElMessage.error(res.message || '获取用户会话记录失败')
    }
  } catch (error) {
    console.error('加载用户会话记录失败', error)
    ElMessage.error('加载用户会话记录失败')
  }
}

// 查看会话详情
const viewSessionDetail = (row) => {
  router.push({
    path: '/agent/history',
    query: { id: row.id }
  })
}

// 编辑用户
const editUser = (row) => {
  // 重置表单
  userForm.id = row.id
  userForm.username = row.username
  userForm.nickname = row.nickname
  userForm.phone = row.phone
  userForm.email = row.email
  userForm.userType = row.userType?.toString()
  userForm.status = row.status
  
  editDialogVisible.value = true
}

// 新增用户
const createUser = () => {
  // 重置表单
  userForm.id = ''
  userForm.username = ''
  userForm.nickname = ''
  userForm.phone = ''
  userForm.email = ''
  userForm.userType = '1'
  userForm.status = 1
  
  editDialogVisible.value = true
}

// 提交用户表单
const submitUserForm = async () => {
  if (!userFormRef.value) return
  
  try {
    await userFormRef.value.validate()
    
    // 适配后端字段
    const submitData = {
      ...userForm,
      vipLevel: parseInt(userForm.userType)
    }
    
    if (userForm.id) {
      // 编辑用户
      const res = await updateUser(submitData)
      if (res.code === 200) {
        ElMessage.success('修改成功')
        loadUserList() // 重新加载列表
      } else {
        ElMessage.error(res.message || '修改失败')
      }
    } else {
      // 新增用户
      const res = await addUser(submitData)
      if (res.code === 200) {
        ElMessage.success('新增成功')
        loadUserList() // 重新加载列表
      } else {
        ElMessage.error(res.message || '新增失败')
      }
    }
    
    editDialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 删除用户
const handleDeleteUser = (row) => {
  ElMessageBox.confirm(
    `确定要删除用户 "${row.nickname || row.username}" 吗？`,
    '删除用户',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      const res = await deleteUser(row.id)
      if (res.code === 200) {
        ElMessage.success('删除成功')
        loadUserList() // 重新加载列表
      } else {
        ElMessage.error(res.message || '删除失败')
      }
    } catch (error) {
      console.error('删除用户失败', error)
      ElMessage.error('删除用户失败')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 发起会话
const startChat = (row) => {
  ElMessageBox.confirm(
    `确定要与用户 "${row.nickname || row.username}" 发起会话吗？`,
    '发起会话',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(() => {
    // TODO: 实现发起会话功能
    // 可以创建一个新会话，然后跳转到聊天页面
    ElMessage.info('发起会话功能开发中')
  }).catch(() => {
    // 用户取消
  })
}

// 搜索
const handleSearch = () => {
  queryParams.pageNum = 1
  loadUserList()
}

// 重置查询条件
const resetQuery = () => {
  queryParams.userType = ''
  queryParams.startDate = ''
  queryParams.endDate = ''
  queryParams.keyword = ''
  dateRange.value = []
  queryParams.pageNum = 1
  loadUserList()
}

// 处理日期范围变化
const handleDateRangeChange = (val) => {
  if (val) {
    queryParams.startDate = val[0]
    queryParams.endDate = val[1]
  } else {
    queryParams.startDate = ''
    queryParams.endDate = ''
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  queryParams.pageSize = size
  queryParams.pageNum = 1 // 重置为第一页
  loadUserList()
}

// 处理当前页变化
const handleCurrentChange = (page) => {
  queryParams.pageNum = page
  loadUserList()
}

// 导出用户数据
const exportUsers = () => {
  // TODO: 实现导出功能
  ElMessage.info('导出功能开发中')
}

// 获取用户类型标签颜色
const getUserTypeTag = (type) => {
  switch (type) {
    case '1': return ''
    case '2': return 'success'
    case '3': return 'warning'
    default: return ''
  }
}

// 获取用户类型文本
const getUserTypeText = (type) => {
  switch (type) {
    case '1': return '普通用户'
    case '2': return '会员'
    case '3': return 'VIP'
    default: return '未知'
  }
}

// 获取会话状态类型
const getSessionStatusType = (status) => {
  switch (parseInt(status)) {
    case 0: return 'info'
    case 1: return 'success'
    case 2: return 'warning'
    default: return 'info'
  }
}

// 获取会话状态文本
const getSessionStatusText = (status) => {
  switch (parseInt(status)) {
    case 0: return '已结束'
    case 1: return '进行中'
    case 2: return '待处理'
    default: return '未知'
  }
}

// 格式化手机号 138****1234
const formatPhone = (phone) => {
  if (!phone) return '-'
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return moment(dateTime).format('YYYY-MM-DD HH:mm:ss')
}
</script>

<style lang="scss" scoped>
.users-container {
  padding: 20px;
  height: calc(100vh - 60px);
  overflow-y: auto;
  
    .search-form {    margin-bottom: 15px;        .el-form-item {      margin-bottom: 10px;    }    .el-select {      width: 200px;    }  }
  
  .users-list {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      
      .header-operations {
        display: flex;
        gap: 10px;
      }
    }
    
    .user-info {
      display: flex;
      align-items: center;
      
      .user-detail {
        margin-left: 10px;
        
        .user-name {
          font-size: 14px;
          color: var(--el-text-color-primary);
        }
        
        .user-phone {
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }
    
    .pagination-container {
      margin-top: 20px;
      padding: 10px 0;
      display: flex;
      justify-content: flex-end;
    }
  }
  
  .user-detail-dialog {
    .el-descriptions {
      margin-bottom: 20px;
    }
  }
}
</style>