<template>
  <div class="agent-container">
    <!-- 客服列表 -->
    <div class="agent-list">
      <el-card shadow="hover">
        <template #header>
          <div class="card-header">
            <span>客服列表</span>
          </div>
        </template>
        
        <!-- 搜索筛选区 -->
        <el-form :inline="true" class="search-form">
          <el-form-item label="客服类型">
            <el-select v-model="queryParams.agentType" placeholder="全部类型" clearable style="width: 200px" @change="handleSelectChange">
              <el-option label="全部" value="" />
              <el-option label="人工客服" value="1" />
              <!-- <el-option label="AI客服" value="2" /> -->
              <el-option label="系统管理员" value="3" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="在线状态">
            <el-select v-model="queryParams.status" placeholder="全部状态" clearable style="width: 200px" @change="handleSelectChange">
              <el-option label="全部" value="" />
              <el-option label="在线" value="1" />
              <el-option label="离线" value="0" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="日期范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :clearable="true"
              @change="handleDateRangeChange"
              style="width: 350px"
              :shortcuts="dateShortcuts"
            />
          </el-form-item>
          
          <el-form-item label="搜索">
            <el-input
              v-model="queryParams.keyword"
              placeholder="客服姓名/ID"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        
        <el-table
          v-loading="loading"
          :data="agentList"
          style="width: 100%"
          border
          stripe
          :header-cell-style="{background:'#f5f7fa'}"
          height="calc(100vh - 300px)"
          :max-height="tableMaxHeight"
        >
          <el-table-column type="selection" width="50" />
          <el-table-column label="客服信息" min-width="180">
            <template #default="scope">
              <div class="agent-info">
                <el-avatar :size="32" :src="scope.row.avatar">
                  {{ scope.row.name ? scope.row.name.substring(0, 1) : '客' }}
                </el-avatar>
                <div class="agent-detail">
                  <div class="agent-name">{{ scope.row.name || '客服' + scope.row.id }}</div>
                  <div class="agent-id">ID: {{ scope.row.id }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="在线状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
                {{ scope.row.status === 1 ? '在线' : '离线' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="客服类型" width="120">
            <template #default="scope">
              <el-tag :type="getAgentTypeTag(scope.row.agent_type)">
                {{ getAgentTypeText(scope.row.agent_type) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="当日接入数" width="120" align="center" prop="todaySessionCount" />
          <el-table-column label="当日回答数" width="120" align="center" prop="todayResponseCount" />
          <el-table-column label="当日响应时间" width="120" align="center">
            <template #default="scope">
              {{ formatDuration(scope.row.todayAvgResponseTime) }}
            </template>
          </el-table-column>
          <el-table-column label="当日对话时间" width="120" align="center">
            <template #default="scope">
              {{ formatDuration(scope.row.todayAvgChatDuration) }}
            </template>
          </el-table-column>
          
          <el-table-column label="累计接入数" width="120" align="center" prop="totalSessionCount" />
          <el-table-column label="累计答数" width="120" align="center" prop="totalResponseCount" />
          <el-table-column label="平均响应" width="120" align="center">
            <template #default="scope">
              {{ formatDuration(scope.row.avgResponseTime) }}
            </template>
          </el-table-column>
          <el-table-column label="平均对话" width="120" align="center">
            <template #default="scope">
              {{ formatDuration(scope.row.avgChatDuration) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button 
                type="primary" 
                link
                @click="viewAgentDetail(scope.row)"
              >
                详情
              </el-button>
              <el-button 
                v-if="scope.row.agent_type !== 2"
                type="primary" 
                link
                @click="editAgent(scope.row)"
              >
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            background
          />
        </div>
      </el-card>
    </div>
    
    <!-- 客服详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="客服详情"
      width="70%"
      destroy-on-close
      @closed="handleDetailDialogClosed"
      top="5vh"
      class="agent-detail-dialog-container"
    >
      <div v-if="currentAgent" class="agent-detail-dialog">
        <!-- 顶部信息区域 -->
        <div class="detail-header">
          <div class="agent-profile">
            <el-avatar :size="90" :src="currentAgent.avatar" class="profile-avatar">
                {{ currentAgent.name ? currentAgent.name.substring(0, 1) : '客' }}
              </el-avatar>
            <div class="agent-basic-info">
              <div class="agent-name-row">
                <h2 class="agent-name">{{ currentAgent.name }}</h2>
                <el-tag 
                  class="status-tag" 
                  :type="currentAgent.status === 1 ? 'success' : 'info'"
                  size="small"
                  :effect="currentAgent.status === 1 ? 'light' : 'plain'"
                >
                {{ currentAgent.status === 1 ? '在线' : '离线' }}
                </el-tag>
              </div>
              <div class="agent-info-row">
                <div class="agent-id">ID: {{ currentAgent.id }}</div>
                <el-tag 
                  :type="getAgentTypeTag(currentAgent.agent_type)" 
                  size="small"
                  effect="plain"
                >
                  {{ getAgentTypeText(currentAgent.agent_type) }}
                </el-tag>
              </div>
            </div>
          </div>
          <div class="action-buttons">
            <el-button 
              type="primary" 
              plain 
              v-if="currentAgent.agent_type !== 2 && currentAgent.agent_type !== 3"
              @click="editAgent(currentAgent)"
            >
              编辑资料
            </el-button>

          </div>
        </div>
        
        <!-- 日期选择器 - 移至顶部 -->
        <div class="date-filter-container">
          <div class="date-filter-label">统计日期：</div>
          <el-date-picker
            v-model="statsDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handleStatsDateChange"
            class="stats-date-picker"
            :shortcuts="dateShortcuts"
          />
        </div>
        
        <!-- 性能数据卡片和图表 -->
        <div class="performance-dashboard">
          <!-- 数据卡片区域 -->
          <div class="stat-cards-container">
            <!-- 累计会话卡片 -->
            <div class="stat-card-modern">
              <div class="stat-card-inner">
                <div class="stat-icon-wrapper blue">
                  <i class="el-icon-chat-line-round"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ currentAgent.totalSessionCount }}</div>
                  <div class="stat-label">累计会话</div>
                </div>
              </div>
            </div>
            
            <!-- 累计回答卡片 -->
            <div class="stat-card-modern">
              <div class="stat-card-inner">
                <div class="stat-icon-wrapper green">
                  <i class="el-icon-chat-dot-square"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ currentAgent.totalResponseCount }}</div>
                  <div class="stat-label">累计回答</div>
                </div>
              </div>
            </div>
            
            <!-- 平均响应卡片 -->
            <div class="stat-card-modern">
              <div class="stat-card-inner">
                <div class="stat-icon-wrapper orange">
                  <i class="el-icon-alarm-clock"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ formatDuration(currentAgent.avgResponseTime) }}</div>
                  <div class="stat-label">平均响应</div>
                </div>
              </div>
            </div>
            
            <!-- 平均对话卡片 -->
            <div class="stat-card-modern">
              <div class="stat-card-inner">
                <div class="stat-icon-wrapper purple">
                  <i class="el-icon-stopwatch"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ formatDuration(currentAgent.avgChatDuration) }}</div>
                  <div class="stat-label">平均对话</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 数据趋势图表 -->
        <el-card shadow="hover" class="trend-chart-card">
          <!-- <template #header>
            <div class="card-header with-options">
              <span>历史数据趋势</span>
            </div>
          </template> -->
          <div class="trend-chart-container">
            <div class="no-chart-data" v-if="sessionStats.length === 0">
              暂无历史数据
            </div>
            <!-- 使用ECharts渲染趋势图 -->
            <div id="trend-chart" ref="trendChartRef" class="echarts-container" v-else></div>
          </div>
        </el-card>
        
        <!-- 统计数据 -->
        <el-card shadow="hover" class="stats-card">
          <!-- <template #header>
            <div class="card-header">
              <span>统计数据</span>
            </div>
          </template> -->
          
          <el-table
            :data="sessionStats"
            style="width: 100%"
            border
            :header-cell-style="{background:'#f5f7fa'}"
            v-loading="statsLoading"
            empty-text="暂无统计数据"
          >
            <el-table-column prop="date" label="日期" width="120" />
            <el-table-column prop="sessionCount" label="接入会话数" width="120" align="center" />
            <el-table-column prop="responseCount" label="回答数" width="120" align="center" />
            <el-table-column prop="avgResponseTime" label="平均响应时间" width="120" align="center">
              <template #default="scope">
                {{ formatDuration(scope.row.avgResponseTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="avgChatDuration" label="平均对话时间" width="120" align="center">
              <template #default="scope">
                {{ formatDuration(scope.row.avgChatDuration) }}
              </template>
            </el-table-column>
            <el-table-column label="会话记录" min-width="150" align="center">
              <template #default="scope">
                <div v-if="scope.row.recentSessionIds && scope.row.recentSessionIds.length > 0" class="session-id-container">
                  <!-- 直接显示前八个会话ID -->
                  <template v-for="(sessionId, index) in scope.row.recentSessionIds.slice(0, 8)" :key="sessionId">
                    <el-link 
                      type="primary" 
                      @click="viewSessionDetail(sessionId)" 
                      class="session-id-link"
                    >
                      {{ sessionId }}
                    </el-link>
                    <span v-if="index < Math.min(scope.row.recentSessionIds.slice(0, 8).length - 1, 7)" class="session-id-separator">,</span>
                  </template>
                  
                  <!-- 如果会话ID超过8个，显示查看更多按钮 -->
                  <el-popover
                    placement="right"
                    trigger="click"
                    :width="250"
                    v-if="scope.row.recentSessionIds.length > 8"
                  >
                    <template #reference>
                      <el-button type="text" size="small" class="more-sessions-btn">
                        查看更多 ({{ scope.row.recentSessionIds.length - 8 }})
                      </el-button>
                    </template>
                    <template #default>
                      <div class="recent-sessions-list">
                        <p class="recent-sessions-title">{{ scope.row.date }} 会话列表</p>
                        <div class="session-item" v-for="sessionId in scope.row.recentSessionIds.slice(8)" :key="sessionId">
                          <el-link
                            type="primary"
                            @click="viewSessionDetail(sessionId)"
                          >
                            会话ID: {{ sessionId }}
                          </el-link>
                        </div>
                      </div>
                    </template>
                  </el-popover>
                </div>
                <span v-else>无会话记录</span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </el-dialog>
    
    <!-- 客服编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="formTitle"
      width="50%"
      destroy-on-close
    >
      <el-form
        ref="agentFormRef"
        :model="agentForm"
        :rules="agentFormRules"
        label-width="100px"
      >
        <el-form-item label="账号/登录名" prop="agentNo">
          <el-input v-model="agentForm.agentNo" placeholder="请输入账号/登录名" :disabled="!!agentForm.id" />
        </el-form-item>
        
        <el-form-item label="姓名" prop="name">
          <el-input v-model="agentForm.name" placeholder="请输入姓名" />
        </el-form-item>
        
        <el-form-item label="密码" prop="password" v-if="!agentForm.id">
          <el-input 
            v-model="agentForm.password" 
            placeholder="请输入密码" 
            type="password"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="客服类型" prop="agent_type">
          <el-radio-group v-model="agentForm.agent_type">
            <el-radio :label="1">人工客服</el-radio>
            <el-radio :label="3">系统管理员</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="在线状态" prop="status">
          <div class="status-display">
            <el-tag :type="agentForm.status === 1 ? 'success' : 'info'" disabled>
              {{ agentForm.status === 1 ? '在线' : '离线' }}
            </el-tag>
            <span class="status-note"></span>
          </div>
        </el-form-item>
        
        <el-form-item label="头像" prop="avatar">
          <div class="avatar-container">
            <el-avatar :size="80" :src="agentForm.avatar">
              {{ agentForm.name ? agentForm.name.substring(0, 1) : '客' }}
            </el-avatar>
            <el-upload
              class="avatar-upload"
              action=""
              :auto-upload="false"
              :show-file-list="false"
              :on-change="handleAvatarChange"
              accept="image/*"
            >
              <el-button size="small" type="primary">更换头像</el-button>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAgentForm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 会话详情对话框 -->
    <el-dialog
      v-model="sessionDialogVisible"
      title="会话详情"
      width="80%"
      destroy-on-close
    >
      <div v-loading="sessionLoading">
        <div v-if="currentSession" class="session-detail">
          <!-- 会话基本信息 -->
          <div class="session-info">
            <el-descriptions :column="3" border>
              <el-descriptions-item label="用户昵称">{{ currentSession.user.nickname || '-' }}</el-descriptions-item>
              <!-- <el-descriptions-item label="会话ID">{{ currentSession.id }}</el-descriptions-item>
              <el-descriptions-item label="用户ID">{{ currentSession.userId }}</el-descriptions-item> -->

              <el-descriptions-item label="用户手机">{{ currentSession.user.phone || '-' }}</el-descriptions-item>
              <el-descriptions-item label="最后活动时间">{{ currentSession.lastActiveTime || '-' }}</el-descriptions-item>
              <el-descriptions-item label="数据源">{{ currentSession.datasource || '-' }}</el-descriptions-item>
              <el-descriptions-item label="渠道">{{ currentSession.channel || '-' }}</el-descriptions-item>

              <el-descriptions-item label="应用场景">{{ currentSession.scene || '-' }}</el-descriptions-item>
            </el-descriptions>
          </div>
          
          <!-- 聊天记录 -->
          <div class="chat-records">
            <h3>聊天记录</h3>
            <div class="messages-container">
              <!-- 消息分组显示，按日期分组 -->
              <template v-if="currentSessionMessages && currentSessionMessages.length > 0">
                <!-- 时间分割线 -->
                <div class="time-divider" v-for="(dateGroup, date) in groupedMessages" :key="date">
                  <div class="date-divider">
                    <span>{{ formatDateForGroup(date) }}</span>
                  </div>
                  
                  <!-- 该时间组下的所有消息 -->
                  <div v-for="message in dateGroup" :key="message.id" class="message-wrapper">
                    <!-- 系统消息 -->
                    <div v-if="message.senderType === 3" class="message-item system-message">
                      <div class="message-content">
                        <div class="system-tip">{{ message.content }}</div>
                        <div class="message-time">{{ formatMessageTime(message.createdAt) }}</div>
                      </div>
                    </div>
                    
                    <!-- 用户消息 -->
                    <div v-else-if="message.senderType === 1" class="message-item user-message">
                      <div class="message-avatar">
                        <div class="avatar-circle user-avatar">{{ getUserInitial(message) }}</div>
                      </div>
                      <div class="message-content-wrapper">
                        <div class="message-sender">{{ message.senderName || '用户' }}</div>
                        <div class="message-bubble">
                          <div class="message-content">{{ message.content }}</div>
                        </div>
                        <div class="message-footer">
                          <span class="message-time">{{ formatMessageTime(message.createdAt) }}</span>
                          <!-- <span v-if="message.isRead === 1" class="read-status">已读</span>
                          <span v-else class="read-status unread">未读</span> -->
                        </div>
                      </div>
                    </div>
                    
                    <!-- 人工客服消息 -->
                    <div v-else-if="message.senderType === 0" class="message-item agent-message">
                      <div class="message-content-wrapper">
                        <div class="message-sender">{{ message.senderName || '客服' }}</div>
                        <div class="message-bubble">
                          <div class="message-content">{{ message.content }}</div>
                        </div>
                        <div class="message-footer">
                          <span class="message-time">{{ formatMessageTime(message.createdAt) }}</span>
                          <!-- <span v-if="message.isRead === 1" class="read-status">已读</span>
                          <span v-else class="read-status unread">未读</span> -->
                        </div>
                      </div>
                      <div class="message-avatar">
                        <div class="avatar-circle agent-avatar">{{ getAgentInitial(message) }}</div>
                      </div>
                    </div>
                    
                    <!-- AI客服消息 -->
                    <div v-else-if="message.senderType === 2" class="message-item ai-agent-message">
                      <div class="message-content-wrapper">
                        <div class="message-sender">
                          <span class="ai-label">AI</span>
                          {{ message.senderName || '熊小智' }}
                        </div>
                        <div class="message-bubble">
                          <div class="message-content">{{ message.content }}</div>
                        </div>
                        <div class="message-footer">
                          <span class="message-time">{{ formatMessageTime(message.createdAt) }}</span>
                        </div>
                      </div>
                      <div class="message-avatar">
                        <div class="avatar-circle ai-avatar">智</div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
              <div v-else class="no-messages">暂无聊天记录</div>
            </div>
          </div>
        </div>
        <div v-else class="no-data">暂无会话数据</div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, onUnmounted, watchEffect } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment'
import * as echarts from 'echarts'
import { 
  getAllAgents, 
  getAgentsByPage, 
  getAgentStatsByDate, 
  getAgentSessionStats,
  addAgent,
  updateAgentInfo,
  deleteAgent
} from '@/api/agent'
import { getSessionById } from '@/api/session'
import { getMessagesBySessionId } from '@/api/session'
import { useRouter } from 'vue-router'

const router = useRouter()

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  agentType: '',
  status: '',
  startDate: moment().subtract(6, 'days').format('YYYY-MM-DD'),
  endDate: moment().format('YYYY-MM-DD'),
  keyword: ''
})

// 日期范围
const dateRange = ref([moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')])

// 日期快捷选项
const dateShortcuts = [
  {
    text: '今天',
    value: () => {
      const today = moment().format('YYYY-MM-DD')
      return [today, today]
    }
  },
  {
    text: '昨天',
    value: () => {
      const yesterday = moment().subtract(1, 'days').format('YYYY-MM-DD')
      return [yesterday, yesterday]
    }
  },
  {
    text: '最近一周',
    value: () => {
      return [moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      return [moment().subtract(29, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
    }
  },
  {
    text: '本月',
    value: () => {
      return [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')]
    }
  }
]

// 客服列表数据
const agentList = ref([])
const total = ref(0)
const loading = ref(false)
const tableMaxHeight = ref('calc(100vh - 300px)')

// 详情对话框
const detailDialogVisible = ref(false)
const isDetailDialogClosed = ref(true)
const currentAgent = ref(null)
const agentSessions = ref([])
const sessionsLoading = ref(false)
const statsDateRange = ref([
  moment().subtract(7, 'days').format('YYYY-MM-DD'),
  moment().format('YYYY-MM-DD')
])
const sessionStats = ref([])
const statsLoading = ref(false)
// 会话记录分页
const sessionsPage = ref(1)
const sessionsPageSize = ref(10)
const sessionsTotal = ref(0)
// 图表周期
const chartPeriod = ref('week')

// 编辑对话框
const editDialogVisible = ref(false)
const agentFormRef = ref(null)
const agentForm = reactive({
  id: '',
  agentNo: '',
  name: '',
  password: '',
  agent_type: 1,
  status: 1,
  avatar: 'https://file.juranguanjia.com/upfile/2025/04-16/5c72f327057b483fa97b916671048fef.png' // 默认头像
})

// 表单验证规则
const agentFormRules = {
  agentNo: [
    { required: true, message: '请输入账号', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { max: 30, message: '最大长度为 30 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur', validator: (rule, value, callback) => {
      if (!agentForm.id && !value) {
        // 只在新增时验证密码
        callback(new Error('请输入密码'))
      } else {
        callback()
      }
    }}
  ],
  agent_type: [
    { required: true, message: '请选择客服类型', trigger: 'change' }
  ]
}

// 表单标题
const formTitle = computed(() => {
  return agentForm.id ? '编辑客服' : '新增客服'
})

// 图表实例
let trendChart = null
const trendChartRef = ref(null)

// 生命周期钩子
onMounted(() => {
  // 加载客服列表
  loadAgentList()
  
  // 窗口大小变化时调整表格高度
  window.addEventListener('resize', adjustTableHeight)
  adjustTableHeight()
})

onUnmounted(() => {
  // 清理事件监听
  window.removeEventListener('resize', adjustTableHeight)
  
  // 销毁图表实例
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }
})

// 调整表格高度
const adjustTableHeight = () => {
  // 设置合适的表格高度，减去其他元素的高度
  nextTick(() => {
    tableMaxHeight.value = `calc(100vh - 300px)`
  })
}

// 加载客服列表
const loadAgentList = async () => {
  loading.value = true
  
  try {
    // 构建查询参数
    const params = {
      pageNum: queryParams.pageNum || 1,
      pageSize: queryParams.pageSize || 10,
      agentType: queryParams.agentType || '',
      status: queryParams.status || '',
      startDate: queryParams.startDate || moment().format('YYYY-MM-DD'),
      endDate: queryParams.endDate || moment().format('YYYY-MM-DD'),
      keyword: queryParams.keyword || '',
      // 排序参数1：先按在线状态降序排列（在线客服靠前）
      orderBy: 'status',
      orderType: 'desc',
      // 排序参数2：再按客服类型排序（人工客服靠前）
      orderBy2: 'agentType',
      orderType2: 'asc'
    }
    
    // 调用分页API
    const res = await getAgentsByPage(params)
    if (res.code === 200) {
      // 处理API返回的数据
      const pageData = res.data || { records: [], total: 0 }
      
      // 转换数据格式，确保字段名与API返回一致，并过滤掉AI客服
      agentList.value = (pageData.records || pageData.list || [])
        .filter(agent => (agent.agent_type || agent.agentType) !== 2) // 过滤掉AI客服(agent_type=2)
        .map(agent => ({
          ...agent,
          // 确保agent_type字段有效 - 保持原字段名，因为后端可能返回的是agentType
          agent_type: agent.agent_type || agent.agentType || 1,
          // 使用status字段作为在线状态
          status: typeof agent.status === 'number' ? agent.status : 0,
          // 其他统计数据
          todaySessionCount: agent.todaySessionCount || 0,
          totalSessionCount: agent.totalSessionCount || 0,
          avgResponseTime: agent.avgResponseTime || 0, // 秒
          avgChatDuration: agent.avgChatDuration || 0, // 秒
          todayAvgResponseTime: agent.todayAvgResponseTime || 0, // 秒
          todayAvgChatDuration: agent.todayAvgChatDuration || 0, // 秒
          todayResponseCount: agent.todayResponseCount || 0,
          totalResponseCount: agent.totalResponseCount || 0
        }))
      
      // 设置总数 - 使用API返回的原始总数，而不是过滤后的列表长度
      total.value = pageData.total || 0
      
      console.log('客服列表数据:', agentList.value)
      console.log('API返回的总数:', pageData.total)
    } else {
      ElMessage.error(res.message || '获取客服列表失败')
    }
  } catch (error) {
    console.error('加载客服列表失败', error)
    ElMessage.error('加载客服列表失败')
  } finally {
    loading.value = false
  }
}

// 查看客服详情
const viewAgentDetail = (row) => {
  // 先设置当前客服和显示对话框
  currentAgent.value = row
  detailDialogVisible.value = true
  isDetailDialogClosed.value = false
  
  // 初始化数据为空数组，防止表格组件报错
  sessionStats.value = []
  sessionsPage.value = 1
  
  // 如果是AI客服，不加载详情
  if (row.agent_type === 2) {
    ElMessage.info('AI客服不提供详情数据')
    detailDialogVisible.value = false
    isDetailDialogClosed.value = true
    return
  }
  
  // 使用统一的数据加载函数
  loadAllAgentData()
}

// 统一加载所有客服数据
const loadAllAgentData = () => {
  if (isDetailDialogClosed.value || !currentAgent.value || !statsDateRange.value || statsDateRange.value.length !== 2) {
    return
  }
  
  // 显示加载状态
  statsLoading.value = true
  
  // 从日期选择器获取日期范围
  const startDate = statsDateRange.value[0]
  const endDate = statsDateRange.value[1]

  // 只获取统计数据，从中计算汇总指标
  getAgentSessionStats(currentAgent.value.id, startDate, endDate)
    .then(res => {
      if (!isDetailDialogClosed.value) {
        if (res && res.code === 200) {
          // 设置表格数据
          sessionStats.value = res.data || []
          
          // 计算选定日期的数据（取日期范围的最后一天数据作为"选定日期"的数据）
          let selectedDayStats = null
          if (sessionStats.value.length > 0) {
            // 尝试找到最后一天的数据（与endDate匹配）
            selectedDayStats = sessionStats.value.find(stat => stat.date === endDate)
            // 如果没找到，就取最后一条记录
            if (!selectedDayStats) {
              selectedDayStats = sessionStats.value[sessionStats.value.length - 1]
            }
          }
          
          // 计算选定范围的汇总数据
          let totalSessionCount = 0
          let totalResponseCount = 0
          let totalResponseTime = 0
          let totalChatDuration = 0
          let sessionWithResponseTimeCount = 0
          let sessionWithChatDurationCount = 0
          
          sessionStats.value.forEach(stat => {
            // 累加会话数和回答数
            totalSessionCount += stat.sessionCount || 0
            totalResponseCount += stat.responseCount || 0
            
            // 计算加权平均响应时间和对话时间
            if (stat.sessionCount && stat.sessionCount > 0) {
              if (stat.avgResponseTime) {
                totalResponseTime += stat.avgResponseTime * stat.sessionCount
                sessionWithResponseTimeCount += stat.sessionCount
              }
              
              if (stat.avgChatDuration) {
                totalChatDuration += stat.avgChatDuration * stat.sessionCount
                sessionWithChatDurationCount += stat.sessionCount
              }
            }
          })
          
          // 计算平均值
          const avgResponseTime = sessionWithResponseTimeCount > 0 
            ? Math.round(totalResponseTime / sessionWithResponseTimeCount) 
            : 0
          
          const avgChatDuration = sessionWithChatDurationCount > 0 
            ? Math.round(totalChatDuration / sessionWithChatDurationCount) 
            : 0
          
          // 更新currentAgent中的统计数据
          currentAgent.value = {
            ...currentAgent.value,
            // 选定日期数据
            todaySessionCount: selectedDayStats ? (selectedDayStats.sessionCount || 0) : 0,
            todayResponseCount: selectedDayStats ? (selectedDayStats.responseCount || 0) : 0,
            todayAvgResponseTime: selectedDayStats ? (selectedDayStats.avgResponseTime || 0) : 0,
            todayAvgChatDuration: selectedDayStats ? (selectedDayStats.avgChatDuration || 0) : 0,
            // 选定范围汇总数据
            totalSessionCount: totalSessionCount,
            totalResponseCount: totalResponseCount,
            avgResponseTime: avgResponseTime,
            avgChatDuration: avgChatDuration
          }
          
          // 初始化趋势图
          nextTick(() => {
            initTrendChart()
          })
        }
      }
    })
    .catch((error) => {
      if (!isDetailDialogClosed.value) {
        console.error('加载客服数据失败', error)
        ElMessage.error('加载统计数据失败')
      }
    })
    .finally(() => {
      if (!isDetailDialogClosed.value) {
        statsLoading.value = false
      }
    })
}

// 初始化历史数据趋势图
const initTrendChart = () => {
  if (!trendChartRef.value || sessionStats.value.length === 0) return
  
  // 销毁旧图表
  if (trendChart) {
    trendChart.dispose()
  }
  
  // 初始化图表
  trendChart = echarts.init(trendChartRef.value)
  
  // 准备数据
  const dates = sessionStats.value.map(item => item.date)
  const sessionCounts = sessionStats.value.map(item => item.sessionCount)
  const responseCounts = sessionStats.value.map(item => item.responseCount)
  
  // 配置图表选项
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        const date = params[0].axisValue
        let tooltip = `<div style="margin: 0px 0 0; line-height:1;">${date}</div>`
        
        params.forEach(param => {
          const color = param.color
          const seriesName = param.seriesName
          const value = param.value
          tooltip += `<div style="margin: 10px 0 0; line-height:1;">
                      <span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${color};">
                      </span>
                      <span>${seriesName}：</span>
                      <span style="float:right;margin-left:10px;font-weight:bold;font-size:14px;">${value}</span>
                      <div style="clear:both"></div>
                    </div>`
        })
        
        return tooltip
      }
    },
    legend: {
      data: ['会话数', '回答数'],
      right: '5%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: dates,
        axisTick: {
          alignWithLabel: true
        },
        axisLabel: {
          rotate: 45,
          interval: 'auto'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '数量',
        minInterval: 1,
        splitLine: {
          lineStyle: {
            type: 'dashed'
          }
        }
      }
    ],
    series: [
      {
        name: '会话数',
        type: 'bar',
        barWidth: '35%',
        barGap: '0%',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {offset: 0, color: '#409EFF'},
            {offset: 1, color: '#79bbff'}
          ]),
          borderRadius: [3, 3, 0, 0]
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {offset: 0, color: '#3a8ee6'},
              {offset: 1, color: '#409EFF'}
            ])
          }
        },
        data: sessionCounts
      },
      {
        name: '回答数',
        type: 'bar',
        barWidth: '35%',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {offset: 0, color: '#67C23A'},
            {offset: 1, color: '#95d475'}
          ]),
          borderRadius: [3, 3, 0, 0]
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {offset: 0, color: '#5daf34'},
              {offset: 1, color: '#67C23A'}
            ])
          }
        },
        data: responseCounts
      }
    ]
  }
  
  // 渲染图表
  trendChart.setOption(option)
  
  // 添加窗口大小调整的监听器
  window.addEventListener('resize', () => {
    trendChart && trendChart.resize()
  })
}

// 处理统计日期变化
const handleStatsDateChange = () => {
  // 调用统一的数据加载函数
  loadAllAgentData()
}

// 查看会话详情
const sessionDialogVisible = ref(false)
const sessionLoading = ref(false)
const currentSession = ref(null)
const currentSessionMessages = ref([])

// 按日期分组的消息
const groupedMessages = computed(() => {
  if (!currentSessionMessages.value || currentSessionMessages.value.length === 0) {
    return {}
  }
  
  // 将消息按日期分组
  const groups = {}
  
  currentSessionMessages.value.forEach(message => {
    if (!message.createdAt) return
    
    // 提取日期部分 (YYYY-MM-DD)
    const date = message.createdAt.split(' ')[0]
    
    if (!groups[date]) {
      groups[date] = []
    }
    
    groups[date].push(message)
  })
  
  // 对每个日期组内的消息按时间排序
  Object.keys(groups).forEach(date => {
    groups[date].sort((a, b) => {
      return new Date(a.createdAt) - new Date(b.createdAt)
    })
  })
  
  return groups
})

// 格式化日期组标题
const formatDateForGroup = (date) => {
  const today = moment().format('YYYY-MM-DD')
  const yesterday = moment().subtract(1, 'days').format('YYYY-MM-DD')
  
  if (date === today) {
    return '今天'
  } else if (date === yesterday) {
    return '昨天'
  } else {
    return moment(date).format('YYYY年MM月DD日')
  }
}

// 格式化消息时间（只显示小时和分钟）
const formatMessageTime = (dateTime) => {
  if (!dateTime) return '-'
  return moment(dateTime).format('HH:mm:ss')
}

// 获取用户头像的首字母
const getUserInitial = (message) => {
  if (!message.senderName) return '用'
  return message.senderName.charAt(0)
}

// 获取客服头像的首字母
const getAgentInitial = (message) => {
  if (!message.senderName) return '客'
  return message.senderName.charAt(0)
}

// 查看会话详情
const viewSessionDetail = async (sessionId) => {
  sessionDialogVisible.value = true
  sessionLoading.value = true
  
  try {
    // 获取会话详情
    const res = await getSessionById(sessionId)
    
    if (res.code === 200) {
      currentSession.value = res.data
      
      // 获取会话的聊天记录
      await getSessionMessages(sessionId)
    } else {
      ElMessage.error(res.message || '获取会话详情失败')
    }
  } catch (error) {
    console.error('获取会话详情失败', error)
    ElMessage.error('获取会话详情失败，请稍后再试')
  } finally {
    sessionLoading.value = false
  }
}

// 获取会话消息记录
const getSessionMessages = async (sessionId) => {
  try {
    const res = await getMessagesBySessionId(sessionId)
    
    if (res.code === 200) {
      currentSessionMessages.value = res.data || []
    } else {
      currentSessionMessages.value = []
      ElMessage.warning(res.message || '未能获取聊天记录')
    }
  } catch (error) {
    console.error('获取聊天记录失败', error)
    currentSessionMessages.value = []
    ElMessage.warning('获取聊天记录失败，请稍后再试')
  }
}

// 获取会话状态文本
const getStatusText = (status) => {
  switch (parseInt(status)) {
    case 0: return '已结束'
    case 1: return '进行中'
    case 2: return '待处理'
    default: return '未知'
  }
}

// 格式化时间
const formatTime = (dateTime) => {
  if (!dateTime) return '-'
  return moment(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

// 编辑客服
const editAgent = (row) => {
  // 如果是AI客服，不允许编辑
  if (row.agent_type === 2) {
    ElMessage.info('AI客服不可编辑')
    return
  }
  
  // 重置表单
  agentForm.id = row.id
  agentForm.agentNo = row.agentNo
  agentForm.name = row.name
  agentForm.agent_type = row.agent_type
  agentForm.status = row.status
  agentForm.avatar = row.avatar || 'https://file.juranguanjia.com/upfile/2025/04-16/5c72f327057b483fa97b916671048fef.png'
  
  editDialogVisible.value = true
}

// 新增客服
const createAgent = () => {
  // 重置表单
  agentForm.id = ''
  agentForm.agentNo = ''
  agentForm.name = ''
  agentForm.password = ''
  agentForm.agent_type = 1
  agentForm.status = 0 // 默认为离线状态
  agentForm.avatar = 'https://file.juranguanjia.com/upfile/2025/04-16/5c72f327057b483fa97b916671048fef.png'
  
  editDialogVisible.value = true
}

// 提交客服表单
const submitAgentForm = async () => {
  if (!agentFormRef.value) return
  
  try {
    await agentFormRef.value.validate()
    
    if (agentForm.id) {
      // 编辑客服
      try {
        // 获取当前的状态，确保不会被修改
        const currentAgent = agentList.value.find(a => a.id === agentForm.id)
        const currentStatus = currentAgent ? currentAgent.status : agentForm.status
        
        const res = await updateAgentInfo({
          id: agentForm.id,
          agentNo: agentForm.agentNo,
          name: agentForm.name,
          agentType: agentForm.agent_type,
          status: currentStatus, // 使用原始状态，保证不会被修改
          avatar: agentForm.avatar
        })
        
        if (res.code === 200) {
          ElMessage.success('客服信息更新成功')
          loadAgentList() // 重新加载列表
          editDialogVisible.value = false
        } else {
          ElMessage.error(res.message || '客服信息更新失败')
        }
      } catch (error) {
        console.error('更新客服信息失败', error)
        ElMessage.error('更新客服信息失败')
      }
    } else {
      // 新增客服
      try {
        const res = await addAgent({
          agentNo: agentForm.agentNo,
          name: agentForm.name,
          password: agentForm.password,
          agentType: agentForm.agent_type,
          status: agentForm.status,
          avatar: agentForm.avatar
        })
        
        if (res.code === 200) {
          ElMessage.success('客服添加成功')
          loadAgentList() // 重新加载列表
          editDialogVisible.value = false
        } else {
          ElMessage.error(res.message || '客服添加失败')
        }
      } catch (error) {
        console.error('添加客服失败', error)
        ElMessage.error('添加客服失败')
      }
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 删除客服
const handleDeleteAgent = (row) => {
  ElMessageBox.confirm(
    `确定要删除客服 "${row.name}" 吗？`,
    '删除客服',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      const res = await deleteAgent(row.id)
      if (res.code === 200) {
        ElMessage.success('删除成功')
        loadAgentList() // 重新加载列表
      } else {
        ElMessage.error(res.message || '删除失败')
      }
    } catch (error) {
      console.error('删除客服失败', error)
      ElMessage.error('删除客服失败')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 处理下拉框变化
const handleSelectChange = (val) => {
  console.log('下拉框变更为:', val)
  nextTick(() => {
    handleSearch()
  })
}

// 搜索
const handleSearch = () => {
  queryParams.pageNum = 1
  loadAgentList()
}

// 处理日期范围变化
const handleDateRangeChange = (val) => {
  if (val) {
    queryParams.startDate = val[0]
    queryParams.endDate = val[1]
  } else {
    const today = moment().format('YYYY-MM-DD')
    queryParams.startDate = today
    queryParams.endDate = today
    dateRange.value = [today, today]
  }
  nextTick(() => {
    handleSearch()
  })
}

// 重置查询条件
const resetQuery = () => {
  queryParams.agentType = ''
  queryParams.status = ''
  const today = moment().format('YYYY-MM-DD')
  queryParams.startDate = today
  queryParams.endDate = today
  dateRange.value = [today, today]
  queryParams.keyword = ''
  queryParams.pageNum = 1
  loadAgentList()
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  queryParams.pageSize = size
  queryParams.pageNum = 1 // 重置为第一页
  loadAgentList()
}

// 处理当前页变化
const handleCurrentChange = (page) => {
  queryParams.pageNum = page
  loadAgentList()
}

// 获取客服类型标签颜色
const getAgentTypeTag = (type) => {
  const agentType = parseInt(type)
  switch (agentType) {
    case 1: return ''
    case 2: return 'success'
    case 3: return 'warning'
    default: return 'info'
  }
}

// 获取客服类型文本
const getAgentTypeText = (type) => {
  const agentType = parseInt(type)
  switch (agentType) {
    case 1: return '人工客服'
    // case 2: return 'AI客服'
    case 3: return '系统管理员'
    default: return '未知类型'
  }
}

// 格式化时长为分钟:秒
const formatDuration = (seconds) => {
  if (!seconds && seconds !== 0) return '-'
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`
}

// 处理头像更改
const handleAvatarChange = (file) => {
  if (!file || !file.raw) return
  
  // 校验文件类型和大小
  const isImage = file.raw.type.startsWith('image/')
  const isLt2M = file.raw.size / 1024 / 1024 < 2
  
  if (!isImage) {
    ElMessage.error('只能上传图片文件')
    return
  }
  
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB')
    return
  }
  
  // 使用FileReader预览图片
  const reader = new FileReader()
  reader.readAsDataURL(file.raw)
  reader.onload = () => {
    agentForm.avatar = reader.result
  }
  
  // 注意：这里只是预览，实际上传会在提交表单时处理
  // TODO: 如果需要立即上传，可以在这里添加上传逻辑
  // const formData = new FormData()
  // formData.append('file', file.raw)
  // formData.append('agentId', agentForm.id)
  // 上传逻辑...
}

// 处理详情对话框关闭事件
const handleDetailDialogClosed = () => {
  // 标记对话框已关闭
  isDetailDialogClosed.value = true
  // 清理对话框相关数据
  currentAgent.value = null
  sessionStats.value = []
  statsDateRange.value = [
    moment().subtract(7, 'days').format('YYYY-MM-DD'),
    moment().format('YYYY-MM-DD')
  ]
  
  // 销毁图表实例
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }
}
</script>

<style lang="scss" scoped>
.agent-container {
  padding: 20px;
  height: calc(100vh - 60px);
  overflow-y: auto;
  
  .agent-list {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
    
    .search-form {
      margin-bottom: 15px;
      
      .el-form-item {
        margin-bottom: 10px;
      }

      .el-select {
        width: 200px;
      }
    }
    
    .agent-info {
      display: flex;
      align-items: center;
      
      .agent-detail {
        margin-left: 10px;
        
        .agent-name {
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-primary);
        }
        
        .agent-id {
          font-size: 12px;
          color: var(--el-text-color-secondary);
          margin-top: 2px;
        }
        
        .status-display {
          display: flex;
          align-items: center;
          
          .status-note {
            margin-left: 10px;
            font-size: 12px;
            color: var(--el-text-color-secondary);
            font-style: italic;
          }
        }
      }
    }
    
    .pagination-container {
      margin-top: 20px;
      padding: 10px 0;
      display: flex;
      justify-content: flex-end;
    }
  }
  
  // 客服详情对话框样式
  :deep(.agent-detail-dialog-container) {
    .el-dialog__body {
      padding: 20px;
    }
  }
  
  .agent-detail-dialog {
    // 基本信息区域
    .detail-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 25px;
      padding-bottom: 20px;
      border-bottom: 1px solid #ebeef5;
      
      .agent-profile {
        display: flex;
        align-items: center;
        
        .profile-avatar {
          flex-shrink: 0;
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
          border: 3px solid #fff;
        }
        
        .agent-basic-info {
          margin-left: 20px;
          
          .agent-name-row {
            display: flex;
            align-items: center;
            
            .agent-name {
              margin: 0;
              font-size: 24px;
              font-weight: 600;
              color: #303133;
            }
            
            .status-tag {
              margin-left: 12px;
            }
          }
          
          .agent-info-row {
            display: flex;
            align-items: center;
            margin: 8px 0;
            
            .agent-id {
              font-size: 14px;
              color: #606266;
              margin-right: 15px;
            }
          }
        }
      }
      
      .action-buttons {
        display: flex;
        gap: 10px;
      }
    }
    
    // 日期选择器 - 移至顶部
    .date-filter-container {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 20px;
      
      .date-filter-label {
        font-size: 14px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
    
    // 性能指标卡片区域
    .performance-dashboard {
      margin-bottom: 25px;
      
      .stat-cards-container {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;
        
        @media (max-width: 1200px) {
          grid-template-columns: repeat(2, 1fr);
        }
        
        @media (max-width: 768px) {
          grid-template-columns: 1fr;
        }
        
        .stat-card-modern {
          position: relative;
          background: #fff;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
          transition: all 0.3s ease;
          
          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
          }
          
          .stat-card-inner {
            display: flex;
            padding: 16px;
            align-items: center;
            
            .stat-icon-wrapper {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 50px;
              height: 50px;
              border-radius: 12px;
              margin-right: 16px;
              color: white;
              font-size: 22px;
              
              &.blue {
                background: linear-gradient(135deg, #2979ff, #4992f8);
              }
              
              &.green {
                background: linear-gradient(135deg, #4caf50, #52c234);
              }
              
              &.orange {
                background: linear-gradient(135deg, #ff9800, #f9a825);
              }
              
              &.purple {
                background: linear-gradient(135deg, #7b68ee, #6a5acd);
              }
            }
            
            .stat-content {
              flex: 1;
              
              .stat-value {
                font-size: 24px;
                font-weight: 600;
                color: #303133;
                margin-bottom: 4px;
                line-height: 1.2;
              }
              
              .stat-label {
                font-size: 14px;
                color: #909399;
                white-space: nowrap;
              }
            }
          }
        }
      }
    }
    
    // 趋势图表
    .trend-chart-card {
      margin-bottom: 25px;
      
      .card-header.with-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .trend-chart-container {
        height: 300px;
        
        .no-chart-data {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 200px;
          color: #909399;
          font-size: 14px;
        }
        
        .echarts-container {
          width: 100%;
          height: 100%;
        }
      }
    }
    
    // 统计数据
    .stats-card {
      margin-bottom: 25px;
      
      .card-header.with-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
  
  // 客服编辑对话框
  .avatar-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    
    .avatar-upload {
      margin-top: 10px;
    }
  }
  
  .status-display {
    display: flex;
    align-items: center;
    
    .status-note {
      margin-left: 10px;
      font-size: 12px;
      color: var(--el-text-color-secondary);
      font-style: italic;
    }
  }
}

// 会话记录相关样式
.recent-sessions-list {
  max-height: 300px;
  overflow-y: auto;
}

.recent-sessions-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #409EFF;
}

.session-item {
  padding: 5px 0;
  border-bottom: 1px solid #ebeef5;
}

.session-item:last-child {
  border-bottom: none;
}

/* 新增的会话ID相关样式 */
.session-id-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

.session-id-link {
  font-size: 13px;
}

.session-id-separator {
  color: #909399;
  margin: 0 2px;
}

.more-sessions-btn {
  margin-left: 5px;
  font-size: 12px;
}

.session-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chat-records {
  margin-top: 20px;
}

.messages-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 15px;
  background-color: #f9f9f9;
}

.no-messages {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: #909399;
  font-size: 14px;
}

.time-divider {
  margin-bottom: 15px;
}

.date-divider {
  display: flex;
  justify-content: center;
  margin: 10px 0;
  
  span {
    background-color: #f2f6fc;
    padding: 3px 10px;
    border-radius: 10px;
    font-size: 12px;
    color: #909399;
  }
}

.message-wrapper {
  margin-bottom: 15px;
}

.message-item {
  display: flex;
  position: relative;
  margin-bottom: 10px;
}

.system-message {
  justify-content: center;
  
  .message-content {
    max-width: 80%;
    
    .system-tip {
      background-color: rgba(144, 147, 153, 0.1);
      border-radius: 4px;
      padding: 5px 10px;
      font-size: 12px;
      color: #909399;
      text-align: center;
    }
    
    .message-time {
      font-size: 12px;
      color: #c0c4cc;
      margin-top: 4px;
      text-align: center;
    }
  }
}

.user-message {
  justify-content: flex-start;
  
  .message-avatar {
    margin-right: 10px;
  }
  
  .message-content-wrapper {
    max-width: 60%;
  }
  
  .message-bubble {
    background-color: #f2f6fc;
    border-radius: 10px 10px 10px 0;
    padding: 10px;
    position: relative;
    
    &:before {
      content: '';
      position: absolute;
      bottom: 0;
      left: -8px;
      width: 16px;
      height: 16px;
      background-color: #f2f6fc;
      clip-path: polygon(100% 0, 100% 100%, 0 100%);
    }
  }
}

.agent-message {
  justify-content: flex-end;
  
  .message-avatar {
    margin-left: 10px;
  }
  
  .message-content-wrapper {
    max-width: 60%;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }
  
  .message-bubble {
    background-color: #ecf5ff;
    border-radius: 10px 10px 0 10px;
    padding: 10px;
    position: relative;
    
    &:before {
      content: '';
      position: absolute;
      bottom: 0;
      right: -8px;
      width: 16px;
      height: 16px;
      background-color: #ecf5ff;
      clip-path: polygon(0 0, 0 100%, 100% 100%);
    }
  }
}

.ai-agent-message {
  justify-content: flex-end;
  
  .message-avatar {
    margin-left: 10px;
  }
  
  .message-content-wrapper {
    max-width: 60%;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }
  
  .message-bubble {
    background: linear-gradient(135deg, #b19dff 0%, #7b68ee 100%);
    color: #fff;
    border-radius: 10px 10px 0 10px;
    padding: 10px;
    position: relative;
    
    &:before {
      content: '';
      position: absolute;
      bottom: 0;
      right: -8px;
      width: 16px;
      height: 16px;
      background-color: #7b68ee;
      clip-path: polygon(0 0, 0 100%, 100% 100%);
    }
  }
}

.avatar-circle {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: 500;
  font-size: 14px;
}

.user-avatar {
  background: linear-gradient(135deg, #409EFF, #79bbff);
}

.agent-avatar {
  background: linear-gradient(135deg, #67C23A, #95d475);
}

.ai-avatar {
  background: linear-gradient(135deg, #7b68ee, #6a5acd);
}

.message-sender {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
  
  .ai-label {
    background-color: #7b68ee;
    color: white;
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 4px;
    margin-right: 4px;
  }
}

.message-content {
  word-break: break-word;
  font-size: 14px;
  line-height: 1.5;
}

.message-footer {
  display: flex;
  align-items: center;
  margin-top: 4px;
}

.message-time {
  font-size: 12px;
  color: #c0c4cc;
}

.read-status {
  margin-left: 5px;
  font-size: 12px;
  color: #909399;
  
  &.unread {
    color: #E6A23C;
  }
}

// 日期选择器宽度强制修改
:deep(.stats-date-picker) {
  width: 250px !important;
  
  .el-input__inner {
    width: 100%;
  }
}
</style> 