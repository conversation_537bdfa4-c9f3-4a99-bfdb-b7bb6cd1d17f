<template>
  <div class="history-container">
    <!-- 历史会话列表 -->
    <div class="history-list">
      <el-card shadow="hover">
        <template #header>
          <div class="card-header">
            <span>历史会话列表</span>
          </div>
        </template>
        
        <!-- 搜索筛选区 -->
        <el-form :inline="true" class="search-form">
          <el-form-item label="日期范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :shortcuts="dateShortcuts"
              style="width: 350px"
            />
          </el-form-item>


          <el-form-item label="对话场景">
            <el-select 
              v-model="sceneFilter" 
              placeholder="全部"
              popper-class="custom-select-dropdown"
              clearable
              style="width: 200px"
            >
              <el-option label="全部" :value="null">
                <span class="select-option-label">全部</span>
              </el-option>
              <el-option 
                v-for="item in sceneOptions" 
                :key="item.name" 
                :label="item.name" 
                :value="item.name"
              >
                <span class="select-option-label">{{ item.name }} ({{ item.count }})</span>
              </el-option>
            </el-select>
            <div v-if="sceneFilter !== null" class="selected-filter-tag">
              {{ sceneFilter }}
            </div>
          </el-form-item>

          <el-form-item label="客服类型">
            <el-select 
              v-model="agentTypeFilter" 
              placeholder="全部"
              popper-class="custom-select-dropdown"
              clearable
              style="width: 200px"
            >
              <el-option label="全部" :value="null">
                <span class="select-option-label">全部</span>
              </el-option>
              <el-option label="人工客服" :value="1">
                <span class="select-option-label">人工客服</span>
              </el-option>
              <el-option label="AI客服" :value="2">
                <span class="select-option-label">AI客服</span>
              </el-option>
            </el-select>
            <div v-if="agentTypeFilter !== null" class="selected-filter-tag">
              {{ agentTypeFilter === 1 ? '人工客服' : 'AI客服' }}
            </div>
          </el-form-item>
          <el-form-item label="会话状态">
            <el-select 
              v-model="statusFilter" 
              placeholder="全部"
              popper-class="custom-select-dropdown"
              clearable
              style="width: 200px"
            >
              <el-option label="全部" :value="null">
                <span class="select-option-label">全部</span>
              </el-option>
              <el-option label="排队中" :value="0">
                <span class="select-option-label">排队中</span>
              </el-option>
              <el-option label="人工会话中" :value="1">
                <span class="select-option-label">人工会话中</span>
              </el-option>
              <el-option label="已关闭" :value="2">
                <span class="select-option-label">已关闭</span>
              </el-option>
              <el-option label="AI会话中" :value="3">
                <span class="select-option-label">AI会话中</span>
              </el-option>
            </el-select>
            <div v-if="statusFilter !== null" class="selected-filter-tag">
              {{ getStatusText(statusFilter) }}
            </div>
          </el-form-item>
          <el-form-item label="评价等级">
            <el-select 
              v-model="satisfactionFilter" 
              placeholder="全部"
              popper-class="custom-select-dropdown"
              clearable
              style="width: 200px"
            >
              <el-option label="全部" :value="null">
                <span class="select-option-label">全部</span>
              </el-option>
              <el-option label="5星" :value="5">
                <span class="select-option-label">5星</span>
              </el-option>
              <el-option label="4星" :value="4">
                <span class="select-option-label">4星</span>
              </el-option>
              <el-option label="3星" :value="3">
                <span class="select-option-label">3星</span>
              </el-option>
              <el-option label="2星" :value="2">
                <span class="select-option-label">2星</span>
              </el-option>
              <el-option label="1星" :value="1">
                <span class="select-option-label">1星</span>
              </el-option>
              <el-option label="未评价" :value="0">
                <span class="select-option-label">未评价</span>
              </el-option>
            </el-select>
            <div v-if="satisfactionFilter !== null" class="selected-filter-tag">
              {{ satisfactionFilter === 0 ? '未评价' : `${satisfactionFilter}星` }}
            </div>
          </el-form-item>
          
          <!-- 新增知识库集合过滤 -->
          <el-form-item label="知识库集合">
            <el-select 
              v-model="collectionNameFilter" 
              placeholder="全部"
              popper-class="custom-select-dropdown"
              clearable
              style="width: 200px"
            >
              <el-option label="全部" :value="null">
                <span class="select-option-label">全部</span>
              </el-option>
              <el-option 
                v-for="item in collectionOptions" 
                :key="item.name" 
                :label="item.name" 
                :value="item.name"
              >
                <span class="select-option-label">{{ item.name }} ({{ item.count }})</span>
              </el-option>
            </el-select>
            <div v-if="collectionNameFilter !== null" class="selected-filter-tag">
              {{ collectionNameFilter }}
            </div>
          </el-form-item>
          
          <el-form-item label="数据源">
            <el-select 
              v-model="datasourceFilter" 
              placeholder="全部"
              popper-class="custom-select-dropdown"
              clearable
              style="width: 200px"
            >
              <el-option label="全部" :value="null">
                <span class="select-option-label">全部</span>
              </el-option>
              <el-option 
                v-for="item in datasourceOptions" 
                :key="item.name" 
                :label="item.name" 
                :value="item.name"
              >
                <span class="select-option-label">{{ item.name }} ({{ item.count }})</span>
              </el-option>
            </el-select>
            <div v-if="datasourceFilter !== null" class="selected-filter-tag">
              {{ datasourceFilter }}
            </div>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetFilters">重置</el-button>
          </el-form-item>
        </el-form>
        
        <!-- 基础表格 -->
        <el-table
          v-loading="loading"
          :data="historyList"
          style="width: 100%"
          border
          height="calc(100vh - 320px)"
          :max-height="tableMaxHeight"
        >
        <el-table-column label="ID" width="80" fixed>
          <template #default="scope">
            {{ (page - 1) * pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        
        <el-table-column label="数据源" width="150">
          <template #default="scope">
            {{ scope.row.datasource || '未知' }}
          </template>
        </el-table-column>

        <el-table-column label="渠道来源" width="150">
          <template #default="scope">
            {{ scope.row.channel || '未知' }}
          </template>
        </el-table-column>

        <el-table-column label="用户" width="150" fixed show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.user && scope.row.user.nickname ? scope.row.user.nickname : '访客' + scope.row.userId }}
          </template>
        </el-table-column>
        
        <el-table-column label="对话场景" width="150">
          <template #default="scope">
            {{ scope.row.scene || '未知' }}
          </template>
        </el-table-column>
        
        <el-table-column label="客服类型" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.currentAgentType === 1 ? 'primary' : 'success'">
              {{ scope.row.currentAgentType === 1 ? '人工客服' : 'AI客服' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="会话状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <!-- <el-table-column label="解决方案" min-width="150">
          <template #default="scope">
            <el-tooltip 
              v-if="scope.row.solutionDescription" 
              :content="scope.row.solutionDescription" 
              placement="top" 
              :show-after="500"
            >
              <div class="truncate-text">{{ scope.row.solutionDescription || '-' }}</div>
            </el-tooltip>
            <span v-else>-</span>
          </template>
        </el-table-column> -->
        

<!--         
        <el-table-column label="评价反馈" min-width="150">
          <template #default="scope">
            <el-tooltip 
              v-if="scope.row.feedbackContent" 
              :content="scope.row.feedbackContent" 
              placement="top"
              :show-after="500"
            >
              <div class="truncate-text">{{ scope.row.feedbackContent || '-' }}</div>
            </el-tooltip>
            <span v-else>-</span>
          </template>
        </el-table-column>
         -->
        <el-table-column label="知识库集合" width="180">
          <template #default="scope">
            <el-tooltip 
              v-if="scope.row.collectionName" 
              :content="scope.row.collectionName" 
              placement="top"
              :show-after="500"
            >
              <div class="truncate-text">{{ scope.row.collectionName || '-' }}</div>
            </el-tooltip>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="最后活跃时间" width="160">
          <template #default="scope">
            {{ formatDateTime(scope.row.lastActiveTime) || '-' }}
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" width="160">
          <template #default="scope">
            {{ formatDateTime(scope.row.startTime) }}
          </template>
        </el-table-column>

        <el-table-column label="评价" width="120">
          <template #default="scope">
            <template v-if="scope.row.satisfactionLevel">
              <el-rate
                v-model="scope.row.satisfactionLevel"
                disabled
                text-color="#ff9900"
                score-template="{value}"
              />
            </template>
            <span v-else>未评价</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="80" fixed="right">
          <template #default="scope">
            <el-button 
              type="primary" 
              link
              @click="viewDetail(scope.row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="page"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      </el-card>
    </div>
    
    <!-- 会话详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="会话详情"
      width="80%"
      destroy-on-close
      :top="'5vh'"
    >
      <template v-if="currentSession">
        <div class="session-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="会话ID">{{ currentSession.id }}</el-descriptions-item>
            
            <el-descriptions-item label="数据源">
              {{ currentSession.datasource || '未知' }}
            </el-descriptions-item>
            
            <el-descriptions-item label="用户">
              {{ currentSession.user && currentSession.user.nickname ? currentSession.user.nickname : '访客' + currentSession.userId }}
            </el-descriptions-item>
            
            <el-descriptions-item label="对话场景">
              {{ currentSession.scene || '未知' }}
            </el-descriptions-item>
            
            <el-descriptions-item label="客服类型">
              <el-tag :type="currentSession.currentAgentType === 1 ? 'primary' : 'success'">
                {{ currentSession.currentAgentType === 1 ? '人工客服' : 'AI客服' }}
              </el-tag>
            </el-descriptions-item>
            
            <el-descriptions-item label="会话状态">
              <el-tag :type="getStatusType(currentSession.status)">
                {{ getStatusText(currentSession.status) }}
              </el-tag>
            </el-descriptions-item>
            
            <el-descriptions-item label="解决方案">
              {{ currentSession.solutionDescription || '-' }}
            </el-descriptions-item>
            
            <el-descriptions-item label="用户评价">
              <template v-if="currentSession.satisfactionLevel">
                <div class="satisfaction-info">
                  <el-rate v-model="currentSession.satisfactionLevel" disabled />
                  <span v-if="currentSession.feedbackContent" class="feedback-content">
                    "{{ currentSession.feedbackContent }}"
                  </span>
                </div>
              </template>
              <span v-else>未评价</span>
            </el-descriptions-item>
            
            <el-descriptions-item label="评价反馈">
              {{ currentSession.feedbackContent || '-' }}
            </el-descriptions-item>
            
            <el-descriptions-item label="知识库集合">
              {{ currentSession.collectionName || '-' }}
            </el-descriptions-item>
            
            <el-descriptions-item label="最后活跃时间">
              {{ formatDateTime(currentSession.lastActiveTime) || '-' }}
            </el-descriptions-item>
            
            <el-descriptions-item label="创建时间">
              {{ formatDateTime(currentSession.startTime) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        
        <!-- 消息过滤选项 -->
        <div class="message-filter">
          <el-radio-group v-model="messageTypeFilter" size="small" @change="filterMessages">
            <el-radio-button :label="null">全部消息</el-radio-button>
            <el-radio-button :label="0">用户消息</el-radio-button>
            <el-radio-button :label="1">客服消息</el-radio-button>
            <el-radio-button :label="2" v-if="currentSession.currentAgentType === 2">AI回复</el-radio-button>
          </el-radio-group>
          
          <el-input
            v-model="messageKeyword"
            placeholder="搜索消息内容"
            clearable
            @input="filterMessages"
            style="width: 200px; margin-left: 10px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        
        <el-divider>聊天记录</el-divider>
        
        <div v-loading="detailLoading" class="message-container">
          <!-- 空数据显示 -->
          <el-empty v-if="!filteredMessages.length" description="暂无聊天记录" />
          
          <!-- 消息列表 -->
          <template v-else>
            <div v-for="msg in filteredMessages" :key="msg.id" class="message-item" :class="{ 'message-user': msg.senderType === 0, 'message-agent': msg.senderType === 1, 'message-ai': msg.senderType === 2 }">
              <p>
                <span class="sender">
                  <span v-if="msg.senderType === 0">用户</span>
                  <span v-else-if="msg.senderType === 1">客服</span>
                  <span v-else-if="msg.senderType === 2">AI客服</span>
                </span>
                <span class="time">{{ formatDateTime(msg.createdAt) }}</span>
              </p>
              <div class="content" v-if="msg.msgType === 0">
                {{ msg.content }}
              </div>
              <div class="content image-content" v-else-if="msg.msgType === 1">
                <el-image :src="getImageUrl(msg.content)" :preview-src-list="[getImageUrl(msg.content)]" fit="cover" />
              </div>
            </div>
          </template>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import moment from 'moment'
import { getSessionsByAgentId, getMessagesBySessionId, searchMessages, getDatasourceOptions, getCollectionOptions, getSceneOptions } from '@/api/chat'
import { useUserStore } from '@/store/user'

// 用户信息
const userStore = useUserStore()

// 分页参数
const page = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 状态数据
const loading = ref(false)
const historyList = ref([])
const dialogVisible = ref(false)
const currentSession = ref(null)
const sessionMessages = ref([])
const detailLoading = ref(false)
const tableMaxHeight = ref('calc(100vh - 320px)')

// 日期过滤相关
const dateRange = ref([
  moment().subtract(14, 'days').format('YYYY-MM-DD'),  // 15天前（包含今天）
  moment().format('YYYY-MM-DD')  // 今天
])

const dateShortcuts = ref([
  {
    text: '最近15天',
    value: [moment().subtract(14, 'days'), moment()]
  },
  {
    text: '最近一周',
    value: [moment().subtract(7, 'days'), moment()]
  },
  {
    text: '最近一个月',
    value: [moment().subtract(1, 'month'), moment()]
  },
  {
    text: '最近三个月',
    value: [moment().subtract(3, 'months'), moment()]
  }
])

// 筛选条件
const agentTypeFilter = ref(null)
const statusFilter = ref(null)
const satisfactionFilter = ref(null)

// 消息筛选
const messageTypeFilter = ref(null)
const messageKeyword = ref('')
const filteredMessages = computed(() => {
  if (!sessionMessages.value.length) return []
  
  return sessionMessages.value.filter(msg => {
    // 按发送者类型筛选
    if (messageTypeFilter.value !== null && msg.senderType !== messageTypeFilter.value) {
      return false
    }
    
    // 按关键词筛选
    if (messageKeyword.value && !msg.content.toLowerCase().includes(messageKeyword.value.toLowerCase())) {
      return false
    }
    
    return true
  })
})

// 新增数据源过滤
const datasourceFilter = ref(null)
const datasourceOptions = ref([])

// 新增知识库集合过滤
const collectionNameFilter = ref(null)
const collectionOptions = ref([])

// 新增对话场景过滤
const sceneFilter = ref(null)
const sceneOptions = ref([])

// 初始化数据
onMounted(() => {
  loadHistoryList()
  loadFilterOptions()
  
  // 窗口大小变化时调整表格高度
  window.addEventListener('resize', adjustTableHeight)
  adjustTableHeight()
})

// 调整表格高度
const adjustTableHeight = () => {
  // 设置合适的表格高度，减去其他元素的高度
  nextTick(() => {
    tableMaxHeight.value = `calc(100vh - 320px)`
  })
}

// 加载历史会话列表
const loadHistoryList = async () => {
  loading.value = true
  try {
    // 确保用户已登录
    if (!userStore.isLoggedIn || !userStore.userId) {
      ElMessage.warning('请先登录')
      return
    }
    
    // 构建请求参数
    const params = {
      agentId: userStore.userId
    }
    
    // 添加日期过滤
    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }
    
    // 添加客服类型过滤
    if (agentTypeFilter.value !== null) {
      params.agentType = agentTypeFilter.value
    }
    
    // 添加状态过滤
    if (statusFilter.value !== null) {
      params.status = statusFilter.value
    }
    
    // 添加评价等级过滤
    if (satisfactionFilter.value !== null) {
      params.satisfactionLevel = satisfactionFilter.value
    }
    
    // 添加数据源过滤
    if (datasourceFilter.value !== null) {
      params.datasource = datasourceFilter.value
    }
    
    // 添加知识库集合过滤
    if (collectionNameFilter.value !== null) {
      params.collectionName = collectionNameFilter.value
    }
    
    // 添加对话场景过滤
    if (sceneFilter.value !== null) {
      params.scene = sceneFilter.value
    }
    
    // 添加分页参数
    params.page = page.value
    params.size = pageSize.value
    
    // 调用API获取会话列表
    const res = await getSessionsByAgentId(params)
    if (res.code === 200) {
      // 适配新的分页数据格式
      if (res.data && res.data.records) {
        historyList.value = res.data.records || []
        total.value = res.data.total || 0
        page.value = res.data.pageNum || 1
      } else {
        // 兼容旧格式
        historyList.value = res.data || []
        total.value = res.data.length > 0 ? res.data[0].total || historyList.value.length : 0
      }
    } else {
      ElMessage.error(res.message || '获取会话列表失败')
    }
  } catch (error) {
    console.error('加载历史会话失败', error)
    ElMessage.error('加载历史会话失败')
  } finally {
    loading.value = false
  }
}

// 查看会话详情
const viewDetail = async (row) => {
  currentSession.value = row
  dialogVisible.value = true
  messageTypeFilter.value = null
  messageKeyword.value = ''
  await loadSessionMessages(row.id)
}

// 加载会话消息
const loadSessionMessages = async (sessionId) => {
  if (!sessionId) return
  
  detailLoading.value = true
  sessionMessages.value = []
  
  try {
    // 调用API获取会话消息
    const res = await getMessagesBySessionId(sessionId)
    if (res.code === 200) {
      sessionMessages.value = res.data || []
    } else {
      ElMessage.error(res.message || '获取会话消息失败')
    }
  } catch (error) {
    console.error('加载会话消息失败', error)
    ElMessage.error('加载会话消息失败')
  } finally {
    detailLoading.value = false
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  loadHistoryList()
}

// 处理当前页变化
const handleCurrentChange = (current) => {
  page.value = current
  loadHistoryList()
}

// 处理搜索和重置
const handleSearch = () => {
  page.value = 1 // 重置到第一页
  loadHistoryList()
}

const resetFilters = () => {
  dateRange.value = [
    moment().subtract(14, 'days').format('YYYY-MM-DD'),
    moment().format('YYYY-MM-DD')
  ]
  agentTypeFilter.value = null
  statusFilter.value = null
  satisfactionFilter.value = null
  datasourceFilter.value = null
  collectionNameFilter.value = null
  sceneFilter.value = null
  page.value = 1 // 重置到第一页
  loadHistoryList()
}

// 过滤消息
const filterMessages = () => {
  // 实际过滤在计算属性中完成
  console.log('Filtering messages:', messageTypeFilter.value, messageKeyword.value)
}

// 获取状态类型
const getStatusType = (status) => {
  if (status === undefined || status === null) return 'info'
  
  switch (parseInt(status)) {
    case 0: return 'warning'    // 排队中
    case 1: return 'success'    // 人工会话中
    case 2: return 'info'       // 已关闭
    case 3: return 'primary'    // AI会话中
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  if (status === undefined || status === null) return '未知状态'
  
  switch (parseInt(status)) {
    case 0: return '排队中'
    case 1: return '人工会话中'
    case 2: return '已关闭'
    case 3: return 'AI会话中'
    default: return '未知状态'
  }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  try {
    return moment(dateTime).format('YYYY-MM-DD HH:mm:ss')
  } catch (error) {
    return '-'
  }
}

// 添加图片URL处理函数
const getImageUrl = (url) => {
  if (!url) return ''
  // 如果已经是完整的URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }
}

// 加载下拉框选项
const loadFilterOptions = async () => {
  try {
    // 获取数据源选项
    const datasourceRes = await getDatasourceOptions()
    if (datasourceRes.code === 200) {
      datasourceOptions.value = datasourceRes.data || []
    }
    
    // 获取知识库集合选项
    const collectionRes = await getCollectionOptions()
    if (collectionRes.code === 200) {
      collectionOptions.value = collectionRes.data || []
    }
    
    // 获取对话场景选项
    const sceneRes = await getSceneOptions()
    if (sceneRes.code === 200) {
      sceneOptions.value = sceneRes.data || []
    }
  } catch (error) {
    console.error('加载过滤选项失败', error)
  }
}
</script>

<style scoped>
.history-container {
  padding: 20px;
  min-height: calc(100vh - 84px);
  background-color: #f5f7fa;
  width: 100%;
  overflow-x: hidden;
}

h2 {
  margin-bottom: 20px;
  color: #303133;
  font-weight: 600;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.search-form {
  margin-bottom: 15px;
  
  .el-form-item {
    margin-bottom: 10px;
  }
}

.search-form .el-form-item {
  margin-bottom: 10px;
  position: relative;
}

.search-form .el-select {
  width: 200px;
}

.select-option-label {
  font-size: 14px;
  padding: 0 4px;
}

.selected-filter-tag {
  position: absolute;
  top: -8px;
  right: 0;
  font-size: 12px;
  background-color: #409eff;
  color: #fff;
  padding: 2px 8px;
  border-radius: 10px;
  z-index: 10;
}

.custom-select-dropdown {
  font-size: 14px;
}

.el-select-dropdown__item.selected {
  font-weight: bold;
  color: #409eff;
}

.history-list {
  width: 100%;
}

.el-table {
  width: 100%;
}

.truncate-text {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pagination-container {
  margin-top: 20px;
  padding: 10px 0;
  display: flex;
  justify-content: flex-end;
}

.session-info {
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.message-filter {
  margin: 20px 0;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.message-container {
  max-height: 50vh;
  overflow-y: auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  scrollbar-width: thin;
  scrollbar-color: #909399 #f4f4f5;
}

.message-container::-webkit-scrollbar {
  width: 6px;
}

.message-container::-webkit-scrollbar-track {
  background: #f4f4f5;
  border-radius: 3px;
}

.message-container::-webkit-scrollbar-thumb {
  background-color: #909399;
  border-radius: 3px;
}

.message-item {
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 8px;
  position: relative;
  transition: all 0.3s ease;
}

.message-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.message-user {
  background-color: #f0f9eb;
  margin-right: 60px;
  margin-left: 10px;
  border-left: 4px solid #67c23a;
}

.message-agent {
  background-color: #ecf5ff;
  margin-left: 60px;
  margin-right: 10px;
  border-left: 4px solid #409eff;
}

.message-ai {
  background-color: #f4f4f5;
  margin-left: 60px;
  margin-right: 10px;
  border-left: 4px solid #909399;
}

.message-item .sender {
  font-weight: 600;
  margin-right: 10px;
  color: #303133;
}

.message-item .time {
  color: #909399;
  font-size: 12px;
}

.message-item .content {
  margin-top: 10px;
  padding: 12px;
  background-color: #fff;
  border-radius: 8px;
  word-break: break-word;
  line-height: 1.6;
  color: #606266;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
}

.message-item .image-content {
  max-width: 300px;
  border-radius: 8px;
  overflow: hidden;
}

.message-item .image-content .el-image {
  width: 100%;
  border-radius: 8px;
  transition: transform 0.3s ease;
}

.message-item .image-content .el-image:hover {
  transform: scale(1.02);
}

.feedback-content {
  font-style: italic;
  color: #606266;
  margin-left: 12px;
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.satisfaction-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

@media screen and (max-width: 768px) {
  .history-container {
    padding: 10px;
  }
  
  .filter-container {
    padding: 15px;
  }
  
  .message-user,
  .message-agent,
  .message-ai {
    margin-left: 5px;
    margin-right: 5px;
  }
  
  .message-item .image-content {
    max-width: 100%;
  }
  
  .el-dialog {
    width: 95% !important;
    margin: 10px auto !important;
  }
}
</style> 