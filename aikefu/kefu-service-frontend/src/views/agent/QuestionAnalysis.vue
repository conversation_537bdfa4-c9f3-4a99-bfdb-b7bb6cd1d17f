<template>
  <div class="question-analysis-container">
    <!-- 筛选条件区域 -->
    <div class="filter-panel">
      <el-form :inline="true">
        <!-- 时间维度选择 -->
        <el-form-item label="时间维度">
          <el-radio-group v-model="timeUnit" @change="handleTimeUnitChange" size="large">
            <el-radio-button label="day">日</el-radio-button>
            <el-radio-button label="week">周</el-radio-button>
            <el-radio-button label="month">月</el-radio-button>
            <el-radio-button label="custom">自定义</el-radio-button>
          </el-radio-group>
        </el-form-item>
        
        <!-- 自定义时间选择器 -->
        <el-form-item v-if="timeUnit === 'custom'">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
            size="large"
          />
        </el-form-item>
        
        <!-- 场景选择 -->
        <el-form-item label="对话场景">
          <el-select 
            v-model="selectedScene" 
            placeholder="全部场景" 
            clearable 
            @change="handleSceneChange"
            filterable
            size="large"
            class="filter-select"
          >
            <el-option label="全部场景" value="" />
            <el-option 
              v-for="item in sceneOptions" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
            />
          </el-select>
        </el-form-item>
        
        <!-- 数据源选择 -->
        <el-form-item label="数据来源">
          <el-select 
            v-model="selectedDatasource" 
            placeholder="全部来源" 
            clearable 
            @change="handleDatasourceChange"
            filterable
            size="large"
            class="filter-select"
          >
            <el-option label="全部来源" value="" />
            <el-option 
              v-for="item in datasourceOptions" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="refreshData" size="large" :icon="Refresh">查询</el-button>
          <el-button @click="resetFilters" size="large" :icon="Delete">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 统计概览卡片 -->
    <div class="stats-overview">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-flex">
              <div class="stat-icon-container">
                <el-icon class="stat-icon"><DataAnalysis /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ overview.totalQuestions || 0 }}</div>
                <div class="stat-title">问题总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-flex">
              <div class="stat-icon-container" style="background-color: #67C23A;">
                <el-icon class="stat-icon"><Collection /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ overview.uniqueQuestions || 0 }}</div>
                <div class="stat-title">独立问题数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-flex">
              <div class="stat-icon-container" style="background-color: #E6A23C;">
                <el-icon class="stat-icon"><PieChart /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ overview.avgQuestionCount || 0 }}</div>
                <div class="stat-title">平均提问次数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-flex">
              <div class="stat-icon-container" style="background-color: #F56C6C;">
                <el-icon class="stat-icon"><Histogram /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value-row">
                  <div class="stat-value">{{ overview.mainSource || '全部' }}
                    <div class="stat-percent">
                      占比 {{ overview.mainSourcePercent || 0 }}%
                    </div></div>
                  <div class="stat-title">主要问题来源</div>
                  <div class="stat-source-info">
                    
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 图表和统计展示区域 -->
    <div class="analysis-display">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick" type="border-card" class="modern-tabs">
        <el-tab-pane label="时间趋势分析" name="trend">
          <el-card shadow="never" v-loading="loadingTrend" class="chart-card">
            <div class="chart-container" ref="trendChartRef"></div>
          </el-card>
        </el-tab-pane>
        
        <el-tab-pane label="分类统计分析" name="category">
          <el-row :gutter="20">
            <el-col :xs="24" :md="12">
              <el-card shadow="never" v-loading="loadingCategoryStats" class="chart-card">
                <template #header>
                  <div class="card-header">
                    <span><el-icon><Histogram /></el-icon> 场景分布</span>
                  </div>
                </template>
                <div class="chart-container" ref="sceneChartRef"></div>
              </el-card>
            </el-col>
            <el-col :xs="24" :md="12">
              <el-card shadow="never" v-loading="loadingCategoryStats" class="chart-card">
                <template #header>
                  <div class="card-header">
                    <span><el-icon><DataLine /></el-icon> 数据源分布</span>
                  </div>
                </template>
                <div class="chart-container" ref="datasourceChartRef"></div>
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>
        
        <el-tab-pane label="关键词分析" name="keyword">
          <el-card shadow="never" v-loading="loadingKeywords" class="chart-card">

            <div class="chart-container wordcloud-container" ref="keywordChartRef"></div>
          </el-card>
        </el-tab-pane>
        
        <el-tab-pane label="热门问题" name="hotQuestions">
          <el-card shadow="never" v-loading="loadingHotQuestions" class="chart-card">
            
            <!-- 搜索和过滤区域 -->
            <div class="hot-questions-filter">
              <el-input
                v-model="questionSearchKeyword"
                placeholder="搜索问题关键词"
                class="filter-input"
                clearable
                @clear="filterHotQuestions"
                @input="filterHotQuestions"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              
            </div>
            
            <!-- 重新设计的表格 -->
            <el-table
              :data="filteredHotQuestions"
              border
              style="width: 100%"
              :header-cell-style="{background: '#f5f7fa', fontWeight: 'bold'}"
              row-class-name="question-table-row"
              :row-style="{cursor: 'pointer'}"
              @row-click="handleRowClick"
              height="450"
              max-height="450"
              class="modern-table"
            >
              <el-table-column type="index" width="50" align="center" />
              <el-table-column prop="content" label="问题内容" min-width="250" show-overflow-tooltip>
                <template #default="scope">
                  <div class="question-content">
                    <span>{{ scope.row.content }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="num" label="提问次数" width="120" sortable align="center">
                <template #default="scope">
                  <div class="question-count">
                    <el-tag 
                      :type="getCountTagType(scope.row.num)"
                      effect="plain"
                      size="large"
                      style="width: 100%; text-align: center; font-weight: bold;"
                    >
                      {{ scope.row.num }}
                    </el-tag>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="scene" label="场景" width="150" align="center">
                <template #default="scope">
                  <el-tag size="small" effect="plain">{{ scope.row.scene || '未知场景' }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="datasource" label="数据源" width="150" align="center">
                <template #default="scope">
                  <el-tag size="small" type="info" effect="plain">{{ scope.row.datasource || '未知来源' }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150" fixed="right" align="center">
                <template #default="scope">
                  <el-button
                    type="primary"
                    size="small"
                    icon="Plus"
                    @click.stop="handleAddToFaq(scope.row)"
                  >
                    添加为FAQ
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <!-- 添加FAQ对话框 -->
    <el-dialog
      v-model="addFaqDialogVisible"
      title="添加到常见问题"
      width="600px"
      destroy-on-close
      class="modern-dialog"
    >
      <el-form
        ref="faqFormRef"
        :model="faqForm"
        :rules="faqFormRules"
        label-width="80px"
        status-icon
      >
        <el-form-item label="问题" prop="question">
          <el-input v-model="faqForm.question" type="textarea" rows="3" />
        </el-form-item>
        <el-form-item label="答案" prop="answer">
          <el-input v-model="faqForm.answer" type="textarea" rows="5" />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select v-model="faqForm.category" placeholder="请选择问题分类" style="width: 100%;">
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addFaqDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddFaq">
            确认添加
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, onBeforeUnmount, watch } from 'vue'
import * as echarts from 'echarts'
import { ElMessage, ElNotification } from 'element-plus'
import { 
  getCategoryStats, 
  getHotQuestions, 
  getQuestionTrend, 
  getKeywordStats
} from '@/api/question-analysis'
import { addFaq } from '@/api/faq'
import { Refresh, Delete, Search, DataAnalysis, Collection, PieChart, Histogram, Connection, DataLine } from '@element-plus/icons-vue'

// 引入词云图
import 'echarts-wordcloud'

// 图表实例
let trendChart = null
let sceneChart = null
let datasourceChart = null
let keywordChart = null

// 图表DOM引用
const trendChartRef = ref(null)
const sceneChartRef = ref(null)
const datasourceChartRef = ref(null)
const keywordChartRef = ref(null)

// 当前页签
const activeTab = ref('keyword')

// 筛选条件状态
const timeUnit = ref('week')
const dateRange = ref([])
const selectedScene = ref('')
const selectedDatasource = ref('')

// 加载状态
const loadingTrend = ref(false)
const loadingCategoryStats = ref(false)
const loadingKeywords = ref(false)
const loadingHotQuestions = ref(false)

// 添加FAQ对话框
const addFaqDialogVisible = ref(false)
const faqFormRef = ref(null)
const faqForm = reactive({
  question: '',
  answer: '',
  category: '',
  scene: '',
  datasource: ''
})

// FAQ表单验证规则
const faqFormRules = {
  question: [
    { required: true, message: '请输入问题', trigger: 'blur' }
  ],
  answer: [
    { required: true, message: '请输入答案', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择问题分类', trigger: 'change' }
  ]
}

// 场景选项
const sceneOptions = ref([])

// 数据源选项
const datasourceOptions = ref([])

// 分类选项
const categoryOptions = [
  { value: 'order', label: '订单相关' },
  { value: 'product', label: '商品相关' },
  { value: 'delivery', label: '配送相关' },
  { value: 'payment', label: '支付相关' },
  { value: 'account', label: '账户相关' },
  { value: 'other', label: '其他问题' }
]

// 数据内容
const overview = reactive({
  totalQuestions: 0,
  questionTrend: 0,
  uniqueQuestions: 0,
  uniqueTrend: 0,
  avgQuestionCount: 0,
  avgTrend: 0,
  mainSource: '',
  mainSourcePercent: 0
})

const hotQuestions = ref([])
const trendData = ref([])
const categoryStats = ref({
  sceneStats: [],
  datasourceStats: []
})
const keywordStats = ref([])

// 热门问题过滤和排序
const questionSearchKeyword = ref('')
const questionSortOrder = ref('desc')
const filteredHotQuestions = ref([])
const currentPage = ref(1)

// 初始化日期范围
const initDateRange = () => {
  const end = new Date()
  const start = new Date()
  
  if (timeUnit.value === 'day') {
    // 近7天
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
  } else if (timeUnit.value === 'week') {
    // 近4周
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 28)
  } else if (timeUnit.value === 'month') {
    // 近6个月
    start.setMonth(start.getMonth() - 5)
  }
  
  // 格式化日期 YYYY-MM-DD
  const formatDate = (date) => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  }
  
  return [formatDate(start), formatDate(end)]
}

// 处理时间单位变化
const handleTimeUnitChange = () => {
  if (timeUnit.value !== 'custom') {
    dateRange.value = initDateRange()
    
    // 刷新所有主要数据，包括分类统计（问题总数等）
    refreshAllData()
  }
}

// 处理日期范围变化
const handleDateRangeChange = () => {
  // 刷新所有数据
  refreshAllData()
}

// 刷新所有主要数据
const refreshAllData = () => {
  // 加载分类统计（获取问题总数等）
  loadCategoryStats()
  
  // 根据当前标签页加载对应数据
  if (activeTab.value === 'trend') {
    loadTrendData()
  } else if (activeTab.value === 'category') {
    // 分类统计已经在上面加载了
  } else if (activeTab.value === 'keyword') {
    loadKeywordStats()
  } else if (activeTab.value === 'hotQuestions') {
    loadHotQuestions()
  }
}

// 刷新数据 (单项刷新)
const refreshData = () => {
  console.log('刷新数据，当前选择场景:', selectedScene.value, '数据源:', selectedDatasource.value)
  
  // 根据当前标签页加载对应数据
  if (activeTab.value === 'trend') {
    loadTrendData()
  } else if (activeTab.value === 'category') {
    loadCategoryStats()
  } else if (activeTab.value === 'keyword') {
    loadKeywordStats()
  } else if (activeTab.value === 'hotQuestions') {
    loadHotQuestions()
  }
}

// 重置筛选条件
const resetFilters = () => {
  timeUnit.value = 'week'
  dateRange.value = initDateRange()
  selectedScene.value = ''
  selectedDatasource.value = ''
  refreshAllData()
}

// 加载趋势数据
const loadTrendData = async () => {
  loadingTrend.value = true
  const [startDate, endDate] = dateRange.value
  
  try {
    const res = await getQuestionTrend({
      timeUnit: timeUnit.value === 'custom' ? 'day' : timeUnit.value,
      scene: selectedScene.value,
      datasource: selectedDatasource.value,
      startDate,
      endDate
    })
    
    if (res.code === 200) {
      trendData.value = res.data
      nextTick(() => {
        renderTrendChart()
      })
    } else {
      ElMessage.error(res.message || '获取趋势数据失败')
    }
  } catch (error) {
    console.error('获取趋势数据异常', error)
    ElMessage.error('获取趋势数据异常')
  } finally {
    loadingTrend.value = false
  }
}

// 加载分类统计
const loadCategoryStats = async () => {
  loadingCategoryStats.value = true
  const [startDate, endDate] = dateRange.value
  
  try {
    const res = await getCategoryStats({
      scene: selectedScene.value,
      datasource: selectedDatasource.value,
      startDate,
      endDate
    })
    
    if (res.code === 200) {
      categoryStats.value = res.data
      
      // 更新概览数据
      overview.totalQuestions = res.data.totalQuestions || 0
      overview.uniqueQuestions = res.data.uniqueQuestions || 0
      overview.avgQuestionCount = parseFloat((overview.totalQuestions / (overview.uniqueQuestions || 1)).toFixed(2))
      
      // 获取主要来源
      if (res.data.datasourceStats && res.data.datasourceStats.length > 0) {
        const mainSource = res.data.datasourceStats[0]
        const totalCount = res.data.datasourceStats.reduce((sum, item) => sum + item.count, 0)
        overview.mainSource = mainSource.datasource
        overview.mainSourcePercent = Math.round(mainSource.count / totalCount * 100)
      }
      
      // 更新场景和数据源下拉选项
      updateFilterOptions(res.data)
      
      nextTick(() => {
        renderCategoryCharts()
      })
    } else {
      ElMessage.error(res.message || '获取分类统计失败')
    }
  } catch (error) {
    console.error('获取分类统计异常', error)
    ElMessage.error('获取分类统计异常')
  } finally {
    loadingCategoryStats.value = false
  }
}

// 更新过滤选项
const updateFilterOptions = (data) => {
  // 更新场景选项
  if (data.sceneStats && data.sceneStats.length > 0) {
    const newSceneOptions = data.sceneStats
      .filter(item => item && item.scene) // 过滤掉没有scene字段的项
      .map(item => {
        const sceneName = item.scene || '未知场景';
        return {
          value: sceneName,
          label: `${sceneName} (${item.count})`,
          count: item.count
        };
      })
      
    // 按数量排序
    newSceneOptions.sort((a, b) => b.count - a.count)
    
    // 更新选项
    sceneOptions.value = newSceneOptions
    
    console.log('场景选项已更新:', sceneOptions.value)
  } else {
    console.warn('未找到场景统计数据')
    sceneOptions.value = []
  }
  
  // 更新数据源选项
  if (data.datasourceStats && data.datasourceStats.length > 0) {
    const newDatasourceOptions = data.datasourceStats
      .filter(item => item && item.datasource) // 过滤掉没有datasource字段的项
      .map(item => {
        const sourceName = item.datasource || '未知来源';
        return {
          value: sourceName,
          label: `${sourceName} (${item.count})`,
          count: item.count
        };
      })
      
    // 按数量排序
    newDatasourceOptions.sort((a, b) => b.count - a.count)
    
    // 更新选项
    datasourceOptions.value = newDatasourceOptions
    
    console.log('数据源选项已更新:', datasourceOptions.value)
  } else {
    console.warn('未找到数据源统计数据')
    datasourceOptions.value = []
  }
}

// 加载关键词统计
const loadKeywordStats = async () => {
  loadingKeywords.value = true
  const [startDate, endDate] = dateRange.value
  
  console.log('加载关键词统计，场景:', selectedScene.value, '数据源:', selectedDatasource.value)
  
  try {
    const res = await getKeywordStats({
      scene: selectedScene.value,
      datasource: selectedDatasource.value,
      startDate,
      endDate,
      limit: 100
    })
    
    if (res.code === 200) {
      keywordStats.value = res.data
      console.log('获取关键词数据:', keywordStats.value)
      nextTick(() => {
        renderKeywordChart()
      })
    } else {
      ElMessage.error(res.message || '获取关键词统计失败')
    }
  } catch (error) {
    console.error('获取关键词统计异常', error)
    ElMessage.error('获取关键词统计异常')
  } finally {
    loadingKeywords.value = false
  }
}

// 加载热门问题
const loadHotQuestions = async () => {
  loadingHotQuestions.value = true
  const [startDate, endDate] = dateRange.value
  
  try {
    const res = await getHotQuestions({
      scene: selectedScene.value,
      datasource: selectedDatasource.value,
      startDate,
      endDate,
      limit: 50
    })
    
    if (res.code === 200) {
      hotQuestions.value = res.data
    } else {
      ElMessage.error(res.message || '获取热门问题失败')
    }
  } catch (error) {
    console.error('获取热门问题异常', error)
    ElMessage.error('获取热门问题异常')
  } finally {
    loadingHotQuestions.value = false
  }
}

// 渲染趋势图表
const renderTrendChart = () => {
  if (!trendChartRef.value) return
  
  // 销毁旧图表
  if (trendChart) {
    trendChart.dispose()
  }
  
  // 创建新图表
  trendChart = echarts.init(trendChartRef.value)
  
  // 准备数据
  const dates = trendData.value.map(item => {
    if (timeUnit.value === 'week') {
      return item.start_date
    }
    if (timeUnit.value === 'month') {
      return item.month
    }
    return item.date
  })
  
  const counts = trendData.value.map(item => item.count)
  
  // 设置图表配置
  const option = {
    title: {
      text: '问题数量趋势',
      left: 'center',
      textStyle: {
        fontWeight: 'normal',
        fontSize: 16
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderColor: '#eee',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      }
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        rotate: timeUnit.value === 'day' ? 45 : 0,
        interval: timeUnit.value === 'day' ? 'auto' : 0,
        color: '#666'
      },
      axisLine: {
        lineStyle: {
          color: '#ddd'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '问题数',
      nameTextStyle: {
        color: '#666'
      },
      axisLabel: {
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#eee'
        }
      }
    },
    series: [
      {
        name: '问题数量',
        type: 'line',
        data: counts,
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        },
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#409EFF'
        },
        areaStyle: {
          opacity: 0.3,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#409EFF' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
          ])
        },
        itemStyle: {
          color: '#409EFF'
        }
      }
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    }
  }
  
  // 渲染图表
  trendChart.setOption(option)
  
  // 响应式调整
  window.addEventListener('resize', () => {
    trendChart && trendChart.resize()
  })
}

// 渲染分类图表
const renderCategoryCharts = () => {
  // 渲染场景分布图
  if (sceneChartRef.value) {
    if (sceneChart) {
      sceneChart.dispose()
    }
    
    sceneChart = echarts.init(sceneChartRef.value)
    
    const sceneData = categoryStats.value.sceneStats || []
    
    // 图表颜色
    const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#9254de', '#36cfc9', '#ffec3d'];
    
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: '#eee',
        borderWidth: 1,
        textStyle: {
          color: '#333'
        }
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: sceneData.map(item => item.scene || '未知'),
        textStyle: {
          color: '#666'
        }
      },
      series: [
        {
          name: '场景分布',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '16',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          color: colors,
          data: sceneData.map(item => ({
            name: item.scene || '未知',
            value: item.count
          }))
        }
      ]
    }
    
    sceneChart.setOption(option)
    
    window.addEventListener('resize', () => {
      sceneChart && sceneChart.resize()
    })
  }
  
  // 渲染数据源分布图
  if (datasourceChartRef.value) {
    if (datasourceChart) {
      datasourceChart.dispose()
    }
    
    datasourceChart = echarts.init(datasourceChartRef.value)
    
    const datasourceData = categoryStats.value.datasourceStats || []
    
    // 图表颜色
    const colors = ['#67C23A', '#E6A23C', '#F56C6C', '#409EFF', '#909399', '#9254de', '#36cfc9', '#ffec3d'];
    
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: '#eee',
        borderWidth: 1,
        textStyle: {
          color: '#333'
        }
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: datasourceData.map(item => item.datasource || '未知'),
        textStyle: {
          color: '#666'
        }
      },
      series: [
        {
          name: '数据源分布',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '16',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          color: colors,
          data: datasourceData.map(item => ({
            name: item.datasource || '未知',
            value: item.count
          }))
        }
      ]
    }
    
    datasourceChart.setOption(option)
    
    window.addEventListener('resize', () => {
      datasourceChart && datasourceChart.resize()
    })
  }
}

// 渲染关键词图表
const renderKeywordChart = () => {
  if (!keywordChartRef.value) {
    console.error('关键词图表DOM引用不存在')
    return
  }
  
  if (keywordChart) {
    keywordChart.dispose()
  }
  
  keywordChart = echarts.init(keywordChartRef.value)
  
  const keywords = keywordStats.value || []
  
  console.log('准备渲染词云，数据项数:', keywords.length)
  
  // 检查数据是否为空
  if (keywords.length === 0) {
    // 显示无数据提示
    keywordChart.setOption({
      title: {
        text: '暂无关键词数据',
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 16,
          color: '#909399'
        }
      }
    })
    return
  }
  
  // 检查数据格式是否正确
  const validData = keywords.filter(item => item && item.keyword && item.count)
  if (validData.length === 0) {
    console.error('关键词数据格式不正确:', keywords)
    keywordChart.setOption({
      title: {
        text: '数据格式不正确',
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 16,
          color: '#909399'
        }
      }
    })
    return
  }
  
  // 转换数据结构 - 确保接口返回的count为数字类型
  const processedData = validData.map(item => ({
    name: item.keyword,
    value: typeof item.count === 'number' ? item.count : parseInt(item.count, 10) || 1,
  }))
  
  console.log('处理后的词云数据:', processedData)
  
  // 为词云生成随机颜色
  const colorList = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#9254de', '#36cfc9', '#ffec3d', '#ff4500', '#00c1de'];
  
  const option = {
    tooltip: {
      show: true,
      formatter: function(params) {
        return params.name + ' : ' + params.value
      },
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderColor: '#eee',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      }
    },
    series: [{
      type: 'wordCloud',
      // 词云形状
      shape: 'circle',
      // 词云位置
      left: 'center',
      top: 'center',
      width: '95%',
      height: '100%',
      // 词云字体大小范围
      sizeRange: [14, 60],
      // 词云旋转角度范围
      rotationRange: [-1, 1],
      rotationStep: 1,
      // 网格大小，影响词云中词语的间距
      gridSize: 20,
      // 是否允许词语画出边界
      drawOutOfBound: false,
      // 是否启用动画效果
      layoutAnimation: true,
      // 文字样式
      textStyle: {
        fontFamily: 'sans-serif',
        fontWeight: 'bold',
        color: function() {
          return colorList[Math.floor(Math.random() * colorList.length)];
        }
      },
      // 强调样式
      emphasis: {
        textStyle: {
          shadowBlur: 12,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
      },
      // 词云数据
      data: processedData
    }]
  }
  
  keywordChart.setOption(option)
  console.log('词云图配置已应用')
  
  // 处理图表大小调整
  const resizeHandler = () => {
    keywordChart && keywordChart.resize()
  }
  
  window.addEventListener('resize', resizeHandler)
  
  // 清理之前的事件监听器
  return () => {
    window.removeEventListener('resize', resizeHandler)
  }
}

// 处理标签页切换
const handleTabClick = () => {
  // 根据当前标签页加载不同数据
  if (activeTab.value === 'trend') {
    loadTrendData()
  } else if (activeTab.value === 'category') {
    loadCategoryStats()
  } else if (activeTab.value === 'keyword') {
    loadKeywordStats()
    // 在切换到关键词标签时添加一个短暂延迟来确保DOM已经渲染
    setTimeout(() => {
      if (keywordChartRef.value && !keywordChart) {
        renderKeywordChart()
      }
    }, 100)
  } else if (activeTab.value === 'hotQuestions') {
    loadHotQuestions()
  }
}

// 处理添加到FAQ
const handleAddToFaq = (row) => {
  faqForm.question = row.content
  faqForm.answer = ''  // 需要手动填写
  faqForm.category = 'other'  // 默认其他分类
  faqForm.scene = row.scene
  faqForm.datasource = row.datasource
  
  addFaqDialogVisible.value = true
}

// 确认添加FAQ
const confirmAddFaq = async () => {
  if (!faqFormRef.value) return
  
  await faqFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const res = await addFaq({
          question: faqForm.question,
          answer: faqForm.answer,
          category: faqForm.category,
          scene: faqForm.scene,
          datasource: faqForm.datasource
        })
        
        if (res.code === 200) {
          ElNotification({
            title: '成功',
            message: '已添加到常见问题',
            type: 'success'
          })
          addFaqDialogVisible.value = false
        } else {
          ElMessage.error(res.message || '添加失败')
        }
      } catch (error) {
        console.error('添加FAQ异常', error)
        ElMessage.error('添加FAQ异常')
      }
    }
  })
}

// 过滤热门问题
const filterHotQuestions = () => {
  if (!questionSearchKeyword.value) {
    filteredHotQuestions.value = [...hotQuestions.value]
  } else {
    filteredHotQuestions.value = hotQuestions.value.filter(question => 
      question.content.toLowerCase().includes(questionSearchKeyword.value.toLowerCase())
    )
  }
  sortHotQuestions()
  currentPage.value = 1
}

// 排序热门问题
const sortHotQuestions = () => {
  if (questionSortOrder.value === 'desc') {
    filteredHotQuestions.value.sort((a, b) => b.num - a.num)
  } else {
    filteredHotQuestions.value.sort((a, b) => a.num - b.num)
  }
}

// 获取计数标签类型
const getCountTagType = (count) => {
  if (count >= 50) return 'danger'
  if (count >= 20) return 'warning'
  if (count >= 10) return 'success'
  return 'info'
}

// 组件挂载时初始化
onMounted(() => {
  // 初始化日期范围
  dateRange.value = initDateRange()
  
  // 先加载分类统计，以获取下拉框选项
  loadInitialCategoryStats()
  
  // 由于默认显示关键词分析，优先加载关键词数据
  loadKeywordStats()
  
  // 然后加载趋势数据作为背景任务
  loadTrendData()
  
  // 确保词云图绘制区域有足够高度
  nextTick(() => {
    if (keywordChartRef.value) {
      keywordChartRef.value.style.minHeight = '400px';
    }
  })
})

// 加载初始分类数据（用于获取下拉选项）
const loadInitialCategoryStats = async () => {
  const [startDate, endDate] = dateRange.value
  
  try {
    const res = await getCategoryStats({
      startDate,
      endDate
    })
    
    if (res.code === 200) {
      // 不更新图表，只更新下拉选项
      updateFilterOptions(res.data)
      
      // 更新概览数据
      overview.totalQuestions = res.data.totalQuestions || 0
      overview.uniqueQuestions = res.data.uniqueQuestions || 0
      overview.avgQuestionCount = parseFloat((overview.totalQuestions / (overview.uniqueQuestions || 1)).toFixed(2))
      
      // 获取主要来源
      if (res.data.datasourceStats && res.data.datasourceStats.length > 0) {
        const mainSource = res.data.datasourceStats[0]
        const totalCount = res.data.datasourceStats.reduce((sum, item) => sum + item.count, 0)
        overview.mainSource = mainSource.datasource
        overview.mainSourcePercent = Math.round(mainSource.count / totalCount * 100)
      }
      
      // 如果当前是分类统计标签页，更新图表
      if (activeTab.value === 'category') {
        categoryStats.value = res.data
        nextTick(() => {
          renderCategoryCharts()
        })
      }
    } else {
      console.error('获取初始分类统计失败:', res.message)
    }
  } catch (error) {
    console.error('获取初始分类统计异常', error)
  }
}

// 场景选择发生变化
const handleSceneChange = (value) => {
  console.log('场景选择变化:', value)
  selectedScene.value = value
  refreshData()
}

// 数据源选择发生变化
const handleDatasourceChange = (value) => {
  console.log('数据源选择变化:', value)
  selectedDatasource.value = value
  refreshData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('查看问题详情:', row)
  // 这里可以添加点击行时的操作，例如显示问题详情等
}

// 处理分页变化
const handlePageChange = (page) => {
  currentPage.value = page
}

// 当热门问题加载后初始化过滤列表
watch(() => hotQuestions.value, (newValue) => {
  if (newValue) {
    filteredHotQuestions.value = [...newValue]
    sortHotQuestions()
  }
}, { immediate: true })

// 清理事件监听器和图表实例
onBeforeUnmount(() => {
  // 清理图表实例
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }
  if (sceneChart) {
    sceneChart.dispose()
    sceneChart = null
  }
  if (datasourceChart) {
    datasourceChart.dispose()
    datasourceChart = null
  }
  if (keywordChart) {
    keywordChart.dispose()
    keywordChart = null
  }
  
  // 清理窗口事件监听
  window.removeEventListener('resize', () => {})
})
</script>

<style scoped>
.question-analysis-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  background-color: #fff;
  min-height: calc(100vh - 120px);
}

.filter-panel {
  background-color: #fff;
  padding: 5px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.filter-select {
  min-width: 180px;
}

.stats-overview {
  margin: 0;
}

.stat-card {
  height: 90px;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  border-radius: 4px;
  transition: all 0.3s;
  overflow: hidden;
}

.stat-flex {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0 15px;
}

.stat-icon-container {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  background-color: #409EFF;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20px;
  flex-shrink: 0;
}

.stat-icon {
  font-size: 22px;
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-title {
  font-size: 13px;
  color: #606266;
  margin-top: 2px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1.2;
}

.stat-value-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.stat-percent {
  font-size: 12px;
  color: #909399;
  background-color: #f5f7fa;
  padding: 1px 4px;
  border-radius: 10px;
  margin-top: 2px;
}

.increase {
  color: #67c23a;
}

.decrease {
  color: #f56c6c;
}

.chart-container {
  height: 400px;
  width: 100%;
}

.wordcloud-container {
  height: 500px !important;
  min-height: 500px !important;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.card-header .el-icon {
  margin-right: 8px;
  vertical-align: middle;
}

.analysis-display {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.chart-card {
  border: none;
  box-shadow: none !important;
}

.modern-tabs :deep(.el-tabs__header) {
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 0 16px;
}

.modern-tabs :deep(.el-tabs__nav) {
  border: none;
}

.modern-tabs :deep(.el-tabs__item) {
  padding: 16px 24px;
  font-size: 15px;
  height: auto;
  transition: all 0.3s;
}

.modern-tabs :deep(.el-tabs__item.is-active) {
  color: #409EFF;
  font-weight: bold;
}

.modern-tabs :deep(.el-tabs__active-bar) {
  height: 3px;
  border-radius: 3px;
}

.modern-table {
  border-radius: 8px;
  overflow: hidden;
  margin-top: 16px;
}

.modern-table :deep(th) {
  padding: 14px 0;
}

.modern-table :deep(.el-table__row:hover) {
  background-color: #f0f7ff !important;
}

.hot-questions-filter {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.filter-input {
  width: 300px;
}

.question-content {
  padding: 10px 0;
}

.question-table-row:hover {
  background-color: #f0f7ff !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .question-analysis-container {
    padding: 12px;
    gap: 16px;
  }
  
  .filter-panel {
    padding: 16px;
  }
  
  .chart-container {
    height: 300px;
  }
  
  .hot-questions-filter {
    flex-direction: column;
  }
  
  .filter-input {
    width: 100%;
  }
  
  .stat-card {
    height: 120px;
  }
  
  .modern-tabs :deep(.el-tabs__item) {
    padding: 12px 16px;
    font-size: 14px;
  }
}

.modern-dialog :deep(.el-dialog__header) {
  padding: 20px;
  margin: 0;
  border-bottom: 1px solid #e4e7ed;
}

.modern-dialog :deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: bold;
}

.modern-dialog :deep(.el-dialog__body) {
  padding: 24px;
}

.modern-dialog :deep(.el-dialog__footer) {
  padding: 16px 20px;
  border-top: 1px solid #e4e7ed;
}

@media (prefers-color-scheme: dark) {
  .question-analysis-container {
    background-color: var(--el-bg-color);
  }
  
  .filter-panel, .analysis-display, .stat-card, .chart-card {
    background-color: var(--el-bg-color-overlay);
    color: var(--el-text-color-primary);
  }
  
  .stat-title {
    color: var(--el-text-color-secondary);
  }
  
  .stat-value {
    color: var(--el-text-color-primary);
  }
  
  .card-header {
    color: var(--el-text-color-primary);
  }
  
  .stat-percent {
    background-color: var(--el-fill-color-light);
    color: var(--el-text-color-secondary);
  }
  
  .modern-tabs :deep(.el-tabs__header) {
    background-color: var(--el-bg-color-overlay);
    border-bottom-color: var(--el-border-color-light);
  }
  
  .modern-table :deep(th) {
    background-color: var(--el-fill-color-light) !important;
    color: var(--el-text-color-primary);
  }
  
  .modern-table :deep(.el-table__row:hover) {
    background-color: var(--el-fill-color-darker) !important;
  }
  
  .modern-dialog :deep(.el-dialog__header),
  .modern-dialog :deep(.el-dialog__footer) {
    border-color: var(--el-border-color-light);
  }
}

.stat-value-row {
  display: flex;
  align-items: center;
  gap: 10px;
}

.stat-source-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-source-info .stat-title {
  margin-top: 0;
  font-size: 12px;
  line-height: 1.2;
}

.stat-source-info .stat-percent {
  margin-top: 2px;
  font-size: 12px;
  color: #909399;
  background-color: #f5f7fa;
  padding: 1px 6px;
  border-radius: 10px;
}
</style> 