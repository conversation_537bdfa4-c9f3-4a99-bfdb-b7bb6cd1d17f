<template>
  <div class="dashboard-container">
    <!-- 欢迎信息 -->
    <div class="welcome-section">
      <div class="welcome-info">
        <h2>{{ welcomeGreeting }}</h2>
        <p class="welcome-subtitle">{{ welcomeSubtitle }}</p>
        <div class="agent-status">
          <span class="status-label">当前状态：</span>
          <el-tag 
            :type="agentInfo?.status === 1 ? 'success' : 'info'"
            effect="plain"
          >
            {{ agentInfo?.status === 1 ? '在线' : '离线' }}
          </el-tag>
          <el-button 
            type="primary" 
            size="small" 
            class="status-btn"
            @click="toggleStatus"
          >
            {{ agentInfo?.status === 1 ? '切换为离线' : '切换为在线' }}
          </el-button>
        </div>
      </div>
      <div class="date-time">
        <div class="date">{{ currentDate }}</div>
        <div class="time">{{ currentTime }}</div>
        <div class="fullscreen-btn">
          <el-tooltip :content="isFullscreen ? '退出全屏' : '全屏展示'" placement="left">
            <el-button 
              circle
              @click="toggleFullscreen"
            >
              <el-icon :class="{ 'rotate-icon': isFullscreen }"><FullScreen /></el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </div>
    
    <!-- 数据卡片区域 -->
    <div class="data-cards">
      <el-card class="data-card">
        <div class="card-content">
          <div class="card-icon chat">
            <el-icon><ChatDotRound /></el-icon>
          </div>
          <div class="card-info">
            <div class="card-title">今日对话</div>
            <div class="card-data-row">
              <div class="data-value-group">
                <div class="card-value">{{ dashboardStats.todaySessionCount }}</div>
                <div class="card-trend" :class="{'up': dashboardStats.todaySessionTrend > 0, 'down': dashboardStats.todaySessionTrend < 0}">
                  {{ dashboardStats.todaySessionTrend > 0 ? '+' : '' }}{{ dashboardStats.todaySessionTrend }}%
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
      
      <el-card class="data-card">
        <div class="card-content">
          <div class="card-icon ai-resolved">
            <el-icon><Service /></el-icon>
          </div>
          <div class="card-info">
            <div class="card-title">AI已回复</div>
            <div class="card-data-row">
              <div class="data-value-group">
                <div class="card-value">{{ dashboardStats.aiResolvedCount }}</div>
                <div class="card-trend up">
                  智能解答
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
      
      <el-card class="data-card">
        <div class="card-content">
          <div class="card-icon waiting">
            <el-icon><Timer /></el-icon>
          </div>
          <div class="card-info">
            <div class="card-title">待处理会话</div>
            <div class="card-data-row">
              <div class="data-value-group">
                <div class="card-value">{{ dashboardStats.waitingSessionCount }}</div>
                <div class="card-actions">
                  <el-button type="primary" size="small" class="mini-btn" @click="handleViewWaiting">处理</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
      
      <el-card class="data-card">
        <div class="card-content">
          <div class="card-icon satisfaction">
            <el-icon><StarFilled /></el-icon>
          </div>
          <div class="card-info">
            <div class="card-title">满意度评分</div>
            <div class="card-data-row">
              <div class="card-value">{{ dashboardStats.averageSatisfaction.toFixed(1) }}</div>
              <el-rate 
                v-model="dashboardStats.averageSatisfaction" 
                disabled 
                allow-half 
                :colors="['#99A9BF', '#F7BA2A', '#FF9900']" 
                :size="'small'"
              />
            </div>
          </div>
        </div>
      </el-card>
      
      <el-card class="data-card">
        <div class="card-content">
          <div class="card-icon resolved">
            <el-icon><CircleCheckFilled /></el-icon>
          </div>
          <div class="card-info">
            <div class="card-title">已解决会话</div>
            <div class="card-data-row">
              <div class="data-value-group">
                <div class="card-value">{{ dashboardStats.resolvedSessionCount }}</div>
                <div class="card-trend" :class="{'up': dashboardStats.resolvedTrend > 0, 'down': dashboardStats.resolvedTrend < 0}">
                  {{ dashboardStats.resolvedTrend > 0 ? '+' : '' }}{{ dashboardStats.resolvedTrend }}%
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="8">
          <div class="chart-card chart-height">
            <div class="chart-header">
              <h3>对话统计</h3>
              <div class="chart-actions">
                <el-radio-group v-model="sessionChartPeriod" size="small" @change="updateSessionChart">
                  <el-radio-button label="day">今日</el-radio-button>
                  <el-radio-button label="week">本周</el-radio-button>
                  <el-radio-button label="month">本月</el-radio-button>
                </el-radio-group>
              </div>
            </div>
            <!-- 使用Element Plus表格替代echarts图表 -->
            <div class="table-container">
              <el-table 
                :data="sessionChartData" 
                style="width: 100%" 
                border 
                stripe 
                v-loading="!sessionChartData.length"
                height="330"
              >
                <el-table-column prop="time" label="时间段" width="120" align="center" fixed="left" />
                <el-table-column prop="count" label="对话数" min-width="180">
                  <template #default="scope">
                    <div class="data-bar">
                      <div class="bar" :style="{ width: Math.min(scope.row.count * 8, 200) + 'px', backgroundColor: '#409EFF' }"></div>
                      <span class="bar-value">{{ scope.row.count }}</span>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <el-empty v-if="!sessionChartData.length" description="暂无数据"></el-empty>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="24" :md="8">
          <div class="chart-card chart-height">
            <div class="chart-header">
              <h3>热门关键词</h3>
              <div class="chart-actions">
                <el-radio-group v-model="keywordChartPeriod" size="small" @change="updateKeywordChart">
                  <el-radio-button label="day">今日</el-radio-button>
                  <el-radio-button label="week">本周</el-radio-button>
                  <el-radio-button label="month">本月</el-radio-button>
                </el-radio-group>
              </div>
            </div>
            <!-- 词云图 -->
            <div class="wordcloud-container" ref="keywordChartRef" v-loading="loadingKeywords"></div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="24" :md="8">
          <MessageCarousel :display-count="5" :storage-limit="50" class="chart-height" />
        </el-col>
      </el-row>
    </div>
    
    <!-- 重新排列的数据统计区域 -->
    <div class="charts-section stats-section">
      <el-row :gutter="20">
        <!-- 对话类型统计 -->
        <el-col :xs="24" :sm="24" :md="8">
          <div class="chart-card">
            <div class="chart-header">
              <h3>对话类型统计</h3>
              <el-radio-group v-model="messageStatsPeriod" size="small" @change="updateMessageStats">
                <el-radio-button label="month">本月</el-radio-button>
                <el-radio-button label="all">全部</el-radio-button>
              </el-radio-group>
            </div>
            <div class="pie-chart-container" v-loading="loadingMessageStats">
              <div class="pie-chart" id="sender-type-chart"></div>
              <div class="pie-chart-legend">
                <div v-for="(item, index) in senderTypeData" :key="index" class="legend-item">
                  <div class="legend-color" :style="{ backgroundColor: senderTypeColors[index] }"></div>
                  <div class="legend-label">{{ getSenderTypeName(item.name) }}</div>
                  <div class="legend-value">{{ item.value }} ({{ getPercentage(item.value, senderTypeTotal) }}%)</div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        
        <!-- 本月对话数据统计 -->
        <el-col :xs="24" :sm="24" :md="8">
          <div class="chart-card">
            <div class="chart-header">
              <h3>本月对话数据统计</h3>
              <div class="chart-subtitle">（可左右滚动查看更多）</div>
            </div>
            
            <div class="chart-scroll-container" v-loading="loadingMonthlyData">
              <div class="pie-chart" id="monthly-bar-chart"></div>
            </div>
          </div>
        </el-col>
        
        <!-- 数据来源统计 -->
        <el-col :xs="24" :sm="24" :md="8">
          <div class="chart-card">
            <div class="chart-header">
              <h3>数据来源统计</h3>
              <el-radio-group v-model="messageStatsPeriod" size="small" @change="updateMessageStats">
                <el-radio-button label="month">本月</el-radio-button>
                <el-radio-button label="all">全部</el-radio-button>
              </el-radio-group>
            </div>
            <div class="pie-chart-container" v-loading="loadingMessageStats">
              <div class="pie-chart" id="datasource-chart"></div>
              <div class="pie-chart-legend">
                <div v-for="(item, index) in datasourceData" :key="index" class="legend-item">
                  <div class="legend-color" :style="{ backgroundColor: datasourceColors[index % datasourceColors.length] }"></div>
                  <div class="legend-label">{{ item.name || '未知来源' }}</div>
                  <div class="legend-value">{{ item.value }} ({{ getPercentage(item.value, datasourceTotal) }}%)</div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, reactive, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { 
  Timer, 
  ChatDotRound, 
  StarFilled, 
  CircleCheckFilled,
  FullScreen,
  Service
} from '@element-plus/icons-vue'
import { useUserStore } from '@/store/user'
import { useChatStore } from '@/store/chat'
import { getDashboardStats, getWaitingSessions, getMessageStats } from '@/api/agent'
import { getMessageTypeAndSourceStats } from '@/api/agent'
import { getKeywordStats } from '@/api/question-analysis'
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment'
import MessageCarousel from '@/components/MessageCarousel.vue'
import * as echarts from 'echarts/core'
import { PieChart, BarChart } from 'echarts/charts'
import { 
  TitleComponent, 
  TooltipComponent, 
  LegendComponent, 
  GridComponent 
} from 'echarts/components'
import { LabelLayout } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
// 引入词云图
import 'echarts-wordcloud'

// 注册 echarts 组件
echarts.use([
  PieChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  LabelLayout,
  CanvasRenderer
])

// 路由
const router = useRouter()

// Store
const userStore = useUserStore()
const chatStore = useChatStore()

// 全屏状态
const isFullscreen = ref(false)

// 切换全屏
const toggleFullscreen = () => {
  if (!isFullscreen.value) {
    // 进入全屏
    const element = document.documentElement
    if (element.requestFullscreen) {
      element.requestFullscreen()
    } else if (element.mozRequestFullScreen) { // Firefox
      element.mozRequestFullScreen()
    } else if (element.webkitRequestFullscreen) { // Chrome, Safari, Opera
      element.webkitRequestFullscreen()
    } else if (element.msRequestFullscreen) { // IE/Edge
      element.msRequestFullscreen()
    }
    
    // 隐藏左侧导航栏
    const sideBar = document.querySelector('.sidebar');
    if (sideBar) {
      sideBar.style.display = 'none';
    }
  } else {
    // 退出全屏
    if (document.exitFullscreen) {
      document.exitFullscreen()
    } else if (document.mozCancelFullScreen) { // Firefox
      document.mozCancelFullScreen()
    } else if (document.webkitExitFullscreen) { // Chrome, Safari, Opera
      document.webkitExitFullscreen()
    } else if (document.msExitFullscreen) { // IE/Edge
      document.msExitFullscreen()
    }
    
    // 恢复左侧导航栏显示
    setTimeout(() => {
      const sideBar = document.querySelector('.sidebar');
      if (sideBar) {
        sideBar.style.display = '';
      }
    }, 100); // 设置延时，确保在退出全屏后再恢复
  }
  isFullscreen.value = !isFullscreen.value
}

// 监听全屏变化事件
const handleFullscreenChange = () => {
  const isInFullscreen = !!(document.fullscreenElement || 
                        document.mozFullScreenElement || 
                        document.webkitFullscreenElement || 
                        document.msFullscreenElement)
  
  isFullscreen.value = isInFullscreen
  
  // 根据全屏状态处理导航栏
  const sideBar = document.querySelector('.sidebar');
  if (sideBar) {
    if (isInFullscreen) {
      sideBar.style.display = 'none';
    } else {
      setTimeout(() => {
        sideBar.style.display = '';
      }, 100);
    }
  }
}

// 客服信息
const agentInfo = computed(() => userStore.userInfo)

// 欢迎语
const welcomeGreeting = computed(() => {
  const hour = new Date().getHours()
  const name = agentInfo.value?.name || '客服专员'
  
  if (hour >= 5 && hour < 9) {
    return `早上好，您辛苦了`
  } else if (hour >= 9 && hour < 12) {
    return `上午好，您辛苦了`
  } else if (hour >= 12 && hour < 14) {
    return `中午好，您辛苦了`
  } else if (hour >= 14 && hour < 18) {
    return `下午好，您辛苦了`
  } else if (hour >= 18 && hour < 22) {
    return `晚上好，您辛苦了`
  } else {
    return `夜深了，您辛苦了`
  }
})

// 欢迎副标题
const welcomeSubtitle = computed(() => {
  const hour = new Date().getHours()
  const randomMessages = [
    '今天也要元气满满哦！',
    '辛苦了，记得适时休息~',
    '您的微笑是最好的服务',
    '工作顺利，加油！',
    '感谢您的辛勤付出'
  ]
  
  // 根据小时数选择不同的信息
  if (hour >= 9 && hour < 12) {
    return '早起的鸟儿有虫吃，祝您工作顺利！'
  } else if (hour >= 12 && hour < 14) {
    return '记得按时用餐，注意休息哦~'
  } else if (hour >= 14 && hour < 17) {
    return '下午也要继续努力，加油！'
  } else if (hour >= 17 && hour < 19) {
    return '一天的工作即将结束，辛苦了！'
  } else if (hour >= 19 && hour < 23) {
    return '晚上好，夜班辛苦了！'
  } else if (hour >= 23 || hour < 5) {
    return '夜深了，注意休息，劳逸结合！'
  } else {
    // 随机返回一条鼓励信息
    return randomMessages[Math.floor(Math.random() * randomMessages.length)]
  }
})

// 当前日期时间
const currentDate = ref(moment().format('YYYY年MM月DD日'))
const currentTime = ref(moment().format('HH:mm:ss'))
let timer = null
let refreshInterval = null // 定义刷新数据的定时器变量

// 统计数据
const dashboardStats = reactive({
  todaySessionCount: 0,
  todaySessionTrend: 0,
  waitingSessionCount: 0,
  averageSatisfaction: 0,
  resolvedSessionCount: 0,
  resolvedTrend: 0,
  aiResolvedCount: 0 // AI已回复问题数
})

// 待处理会话列表
const waitingSessions = ref([])
const loadingWaitingSessions = ref(false)

// 图表周期
const sessionChartPeriod = ref('day')
const keywordChartPeriod = ref('week')

// 图表数据
const sessionChartData = ref([])
const monthlyChartData = ref([])
const loadingMonthlyData = ref(false)
const maxMonthlyCount = ref(0)

// 消息统计数据变量
const messageStatsPeriod = ref('month')
const loadingMessageStats = ref(false)

// 发送者类型数据和颜色
const senderTypeData = ref([])
const senderTypeColors = ['#409EFF', '#67C23A', '#E6A23C']
const senderTypeTotal = ref(0)

// 数据来源数据和颜色
const datasourceData = ref([])
const datasourceColors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#6B778C', '#9254DE', '#36CFC9']
const datasourceTotal = ref(0)

// echarts 实例
let senderTypeChart = null
let datasourceChart = null
let monthlyBarChart = null

// 关键词图表变量
const keywordChartRef = ref(null)
const keywordStats = ref([])
const loadingKeywords = ref(false)
let keywordChart = null

// 生命周期钩子
onMounted(async () => {
  // 从localStorage恢复状态
  userStore.loadFromStorage()
  
  // 如果没有登录或不是客服角色，跳转到登录页
  if (!userStore.isLoggedIn || !userStore.isAgent) {
    router.push('/login')
    return
  }
  
  // 设置时间更新
  updateCurrentTime()
  timer = setInterval(updateCurrentTime, 1000)
  
  // 添加全屏事件监听
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.addEventListener('mozfullscreenchange', handleFullscreenChange)
  document.addEventListener('MSFullscreenChange', handleFullscreenChange)
  
  // 如果客服不在线，提示并询问是否切换为在线状态
  if (userStore.userInfo?.status !== 1) {
    try {
      await ElMessageBox.confirm(
        '您当前处于离线状态，需要切换为在线状态才能处理会话，是否切换？',
        '提示',
        {
          confirmButtonText: '切换为在线',
          cancelButtonText: '保持离线',
          type: 'warning',
        }
      )
      // 用户确认切换为在线状态
      await toggleStatus()
    } catch {
      // 用户选择保持离线状态
      ElMessage.info('您选择了保持离线状态，将无法接入新会话')
    }
  }
  
  try {
    // 加载数据
    await Promise.all([
      loadDashboardStats(),
      loadWaitingSessions(),
      loadMonthlyStats(),
      updateMessageStats(messageStatsPeriod.value),
      updateKeywordChart(keywordChartPeriod.value)  // 加载关键词数据
    ])
    
    // 初始化图表
    await Promise.all([
      updateSessionChart(sessionChartPeriod.value),
      initMonthlyBarChart()
    ])
    
    // 设置自动刷新数据
    refreshInterval = setInterval(async () => {
      if (userStore.isLoggedIn && userStore.isAgent) {
        await Promise.all([
          loadDashboardStats(),
          loadWaitingSessions(),
          loadMonthlyStats(),
          updateMessageStats(messageStatsPeriod.value),
          updateKeywordChart(keywordChartPeriod.value)  // 刷新关键词数据
        ])
      } else {
        clearInterval(refreshInterval)
        refreshInterval = null
      }
    }, 60000) // 每分钟刷新一次
    
    // 监听图表周期变化
    watch(sessionChartPeriod, (newVal) => {
      updateSessionChart(newVal)
    })
    
    watch(keywordChartPeriod, (newVal) => {
      updateKeywordChart(newVal)
    })
    
    // 监听窗口大小变化，以便重绘图表
    window.addEventListener('resize', handleResizeCharts)
    
    // 确保词云图绘制区域有足够高度
    nextTick(() => {
      if (keywordChartRef.value) {
        keywordChartRef.value.style.minHeight = '330px'
      }
    })
  } catch (error) {
    console.error('初始化工作台失败:', error)
    ElMessage.error('加载数据失败，请刷新页面重试')
  }
})

onUnmounted(() => {
  // 清除所有定时器
  if (timer) {
    clearInterval(timer)
    timer = null
  }
  
  if (refreshInterval) {
    clearInterval(refreshInterval)
    refreshInterval = null
  }
  
  // 移除全屏事件监听
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.removeEventListener('mozfullscreenchange', handleFullscreenChange)
  document.removeEventListener('MSFullscreenChange', handleFullscreenChange)
  
  // 移除窗口大小变化事件监听
  window.removeEventListener('resize', handleResizeCharts)
  
  // 销毁 echarts 实例
  if (senderTypeChart) {
    senderTypeChart.dispose()
    senderTypeChart = null
  }
  
  if (datasourceChart) {
    datasourceChart.dispose()
    datasourceChart = null
  }
  
  if (monthlyBarChart) {
    monthlyBarChart.dispose()
    monthlyBarChart = null
  }
  
  if (keywordChart) {
    keywordChart.dispose()
    keywordChart = null
  }
})

// 更新当前时间
const updateCurrentTime = () => {
  currentDate.value = moment().format('YYYY年MM月DD日')
  currentTime.value = moment().format('HH:mm:ss')
  
  // 欢迎语和副标题会自动更新，因为它们是计算属性
  // 依赖于当前时间变化
}

// 加载仪表盘统计数据
const loadDashboardStats = async () => {
  try {
    const res = await getDashboardStats()
    if (res.code === 200) {
      Object.assign(dashboardStats, {
        ...res.data,
        aiResolvedCount: res.data.aiResolvedCount || 0 // 确保即使后端未返回该值也能正常显示
      })
    } else {
      ElMessage.error(res.message || '获取统计数据失败')
    }
  } catch (error) {
    console.error('加载统计数据失败', error)
  }
}

// 加载待处理会话
const loadWaitingSessions = async () => {
  loadingWaitingSessions.value = true
  try {
    const res = await getWaitingSessions()
    if (res.code === 200) {
      waitingSessions.value = res.data.map(session => ({
        id: session.id,
        user_id: session.user_id,
        user_nickname: session.user_nickname,
        user_avatar: session.user_avatar || '',
        waiting_time: Math.floor((new Date() - new Date(session.start_time)) / 1000), // 计算等待时间（秒）
        last_message: session.last_message || '暂无消息',
        priority: getPriorityByUserVip(session.vip_level)
      }))
    } else {
      ElMessage.error(res.message || '获取待处理会话失败')
    }
  } catch (error) {
    console.error('加载待处理会话失败', error)
  } finally {
    loadingWaitingSessions.value = false
  }
}

// 根据用户VIP等级设置优先级
const getPriorityByUserVip = (vipLevel) => {
  if (vipLevel >= 2) return 3  // 高优先级
  if (vipLevel >= 1) return 2  // 中优先级
  return 1  // 低优先级
}

// 更新会话统计图表
const updateSessionChart = async (period) => {
  sessionChartPeriod.value = period
  
  try {
    const res = await getMessageStats(period)
    if (res.code === 200) {
      // 首先映射数据
      const mappedData = res.data.map(item => ({
        time: item.timeLabel,
        count: item.sessionCount
      }))
      
      // 按count值排序，将非0值排在前面，0值排在后面
      // 对于相同count值的项目，保持原有顺序
      sessionChartData.value = mappedData.sort((a, b) => {
        if (a.count > 0 && b.count === 0) return -1;
        if (a.count === 0 && b.count > 0) return 1;
        return 0;
      });
    } else {
      ElMessage.error(res.message || '获取对话统计数据失败')
    }
  } catch (error) {
    console.error('更新对话统计失败', error)
  }
}

// 格式化等待时间
const formatWaitingTime = (seconds) => {
  if (seconds < 60) {
    return `${seconds}秒`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    return `${minutes}分钟`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}小时${minutes}分钟`
  }
}

// 获取等待时间类型
const getWaitingTimeType = (seconds) => {
  if (seconds < 5 * 60) {
    return 'success'
  } else if (seconds < 15 * 60) {
    return 'warning'
  } else {
    return 'danger'
  }
}

// 获取优先级类型
const getPriorityType = (priority) => {
  switch (priority) {
    case 1: return 'info'
    case 2: return 'warning'
    case 3: return 'danger'
    default: return 'info'
  }
}

// 获取优先级标签
const getPriorityLabel = (priority) => {
  switch (priority) {
    case 1: return '低'
    case 2: return '中'
    case 3: return '高'
    default: return '未知'
  }
}

// 处理查看全部待处理会话
const handleViewWaiting = () => {
  router.push('/agent/chat')
}

// 处理接受会话
const handleAccept = (row) => {
  router.push(`/agent/chat?id=${row.id}`)
}

// 处理查看会话
const handleView = (row) => {
  router.push(`/agent/chat?id=${row.id}&view=true`)
}

// 处理行点击
const handleRowClick = (row) => {
  handleView(row)
}

// 切换在线状态
const toggleStatus = async () => {
  if (!agentInfo.value?.id) return
  
  try {
    const newStatus = agentInfo.value.status === 1 ? 0 : 1
    await userStore.updateAgentStatusBulk(agentInfo.value.id, newStatus)
    
    // 更新状态后重新加载数据
    if (newStatus === 1) {
      await Promise.all([
        loadDashboardStats(),
        loadWaitingSessions()
      ])
    }
    
    ElMessage.success(newStatus === 1 ? '已切换为在线状态' : '已切换为离线状态')
  } catch (error) {
    console.error('更新状态失败', error)
    ElMessage.error('切换状态失败')
  }
}

// 加载本月对话统计数据
const loadMonthlyStats = async () => {
  loadingMonthlyData.value = true
  try {
    const res = await getMessageStats('month')
    if (res.code === 200) {
      // 映射数据
      const mappedData = res.data.map(item => ({
        time: item.timeLabel,
        count: item.sessionCount,
        // 提取日期中的数字部分，用于排序
        day: parseInt(item.timeLabel.replace('日', ''))
      }))
      
      // 按照日期顺序排序（从1号到31号）
      const sortedData = mappedData.sort((a, b) => a.day - b.day)
      
      // 计算最大值用于柱形图高度计算
      maxMonthlyCount.value = Math.max(...sortedData.map(item => item.count), 1)
      
      // 获取当前月份的总天数
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth();
      const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
      
      // 确保每一天都有数据，如果没有则补充
      const fullMonthData = [];
      for (let day = 1; day <= daysInMonth; day++) {
        const dayData = sortedData.find(item => item.day === day);
        if (dayData) {
          fullMonthData.push(dayData);
        } else {
          fullMonthData.push({
            time: day + '日',
            count: 0,
            day: day
          });
        }
      }
      
      monthlyChartData.value = fullMonthData;
      
      // 初始化柱状图
      nextTick(() => {
        initMonthlyBarChart()
      })
    } else {
      ElMessage.error(res.message || '获取月度对话统计数据失败')
    }
  } catch (error) {
    console.error('加载月度对话统计失败', error)
  } finally {
    loadingMonthlyData.value = false
  }
}

// 处理窗口大小变化，重绘图表
const handleResizeCharts = () => {
  if (senderTypeChart) {
    senderTypeChart.resize()
  }
  
  if (datasourceChart) {
    datasourceChart.resize()
  }
  
  if (monthlyBarChart) {
    monthlyBarChart.resize()
  }
  
  if (keywordChart) {
    keywordChart.resize()
  }
}

// 更新消息统计数据
const updateMessageStats = async (period) => {
  messageStatsPeriod.value = period
  loadingMessageStats.value = true
  
  try {
    // 设置日期范围
    let startDate = null
    let endDate = null
    
    if (period === 'month') {
      // 当前月份的第一天和最后一天
      const now = new Date()
      startDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0]
      endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().split('T')[0]
    }
    
    const res = await getMessageTypeAndSourceStats(startDate, endDate)
    if (res.code === 200) {
      // 处理发送者类型数据
      senderTypeData.value = res.data.senderTypeStats || []
      senderTypeTotal.value = senderTypeData.value.reduce((sum, item) => sum + item.value, 0)
      
      // 处理数据来源数据
      datasourceData.value = res.data.datasourceStats || []
      datasourceTotal.value = datasourceData.value.reduce((sum, item) => sum + item.value, 0)
      
      // 初始化/更新饼图
      initSenderTypeChart()
      initDatasourceChart()
    } else {
      ElMessage.error(res.message || '获取消息统计数据失败')
    }
  } catch (error) {
    console.error('更新消息统计失败', error)
  } finally {
    loadingMessageStats.value = false
  }
}

// 初始化发送者类型饼图
const initSenderTypeChart = () => {
  // 确保DOM元素存在
  const chartDom = document.getElementById('sender-type-chart')
  if (!chartDom) return
  
  // 初始化或获取图表实例
  senderTypeChart = echarts.getInstanceByDom(chartDom) || echarts.init(chartDom)
  
  // 设置图表选项
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: senderTypeData.value.map((item, index) => ({
          value: item.value,
          name: getSenderTypeName(item.name),
          itemStyle: {
            color: senderTypeColors[index % senderTypeColors.length]
          }
        }))
      }
    ]
  }
  
  // 应用选项
  option && senderTypeChart.setOption(option)
}

// 初始化数据来源饼图
const initDatasourceChart = () => {
  // 确保DOM元素存在
  const chartDom = document.getElementById('datasource-chart')
  if (!chartDom) return
  
  // 初始化或获取图表实例
  datasourceChart = echarts.getInstanceByDom(chartDom) || echarts.init(chartDom)
  
  // 设置图表选项
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: datasourceData.value.map((item, index) => ({
          value: item.value,
          name: item.name || '未知来源',
          itemStyle: {
            color: datasourceColors[index % datasourceColors.length]
          }
        }))
      }
    ]
  }
  
  // 应用选项
  option && datasourceChart.setOption(option)
}

// 根据发送者类型代码获取名称
const getSenderTypeName = (type) => {
  switch (parseInt(type)) {
    case 0: return '用户'
    case 1: return '客服'
    case 2: return 'AI'
    default: return '未知'
  }
}

// 计算百分比
const getPercentage = (value, total) => {
  if (!total) return 0
  return Math.round(value / total * 100)
}

// 添加初始化柱状图的方法
const initMonthlyBarChart = () => {
  // 确保DOM元素存在
  const chartDom = document.getElementById('monthly-bar-chart')
  if (!chartDom) return
  
  // 初始化或获取图表实例
  monthlyBarChart = echarts.getInstanceByDom(chartDom) || echarts.init(chartDom)
  
  const days = monthlyChartData.value.map(item => item.time)
  const counts = monthlyChartData.value.map(item => item.count)
  
  // 设置容器宽度 - 根据数据数量动态调整
  const containerWidth = Math.max(days.length * 50, chartDom.offsetWidth)
  chartDom.style.width = `${containerWidth}px`
  
  // 设置图表选项
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      top: '10%',
      left: '3%',
      right: '3%',
      bottom: '8%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: days,
        axisLabel: {
          interval: 0,
          fontSize: 11,
          color: '#606266'
        },
        axisTick: {
          alignWithLabel: true
        },
        axisLine: {
          lineStyle: {
            color: '#E4E7ED'
          }
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          fontSize: 11,
          color: '#606266'
        },
        splitLine: {
          lineStyle: {
            color: '#EBEEF5',
            type: 'dashed'
          }
        }
      }
    ],
    series: [
      {
        name: '对话数',
        type: 'bar',
        barWidth: 30,
        data: counts,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#5B9DF5' },
            { offset: 1, color: '#409EFF' }
          ]),
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#409EFF' },
              { offset: 1, color: '#2B85E4' }
            ])
          }
        },
        label: {
          show: true,
          position: 'top',
          fontSize: 12,
          color: '#606266',
          formatter: function(params) {
            return params.value > 0 ? params.value : '';
          }
        }
      }
    ]
  }
  
  // 应用选项并调整大小
  option && monthlyBarChart.setOption(option)
  monthlyBarChart.resize({width: containerWidth})
}

// 更新关键词分析图表
const updateKeywordChart = async (period) => {
  keywordChartPeriod.value = period
  loadingKeywords.value = true
  
  try {
    // 设置日期范围
    let startDate, endDate
    const now = new Date()
    
    if (period === 'day') {
      // 今日
      startDate = moment().format('YYYY-MM-DD')
      endDate = moment().format('YYYY-MM-DD')
    } else if (period === 'week') {
      // 本周
      startDate = moment().subtract(6, 'days').format('YYYY-MM-DD')
      endDate = moment().format('YYYY-MM-DD')
    } else if (period === 'month') {
      // 本月
      startDate = moment().startOf('month').format('YYYY-MM-DD')
      endDate = moment().format('YYYY-MM-DD')
    }
    
    const res = await getKeywordStats({
      startDate,
      endDate,
      limit: 100
    })
    
    if (res.code === 200) {
      keywordStats.value = res.data
      console.log('获取关键词数据:', keywordStats.value)
      nextTick(() => {
        renderKeywordChart()
      })
    } else {
      ElMessage.error(res.message || '获取关键词统计失败')
    }
  } catch (error) {
    console.error('更新关键词统计失败', error)
  } finally {
    loadingKeywords.value = false
  }
}

// 渲染关键词图表
const renderKeywordChart = () => {
  if (!keywordChartRef.value) {
    console.error('关键词图表DOM引用不存在')
    return
  }
  
  if (keywordChart) {
    keywordChart.dispose()
  }
  
  keywordChart = echarts.init(keywordChartRef.value)
  
  const keywords = keywordStats.value || []
  
  console.log('准备渲染词云，数据项数:', keywords.length)
  
  // 检查数据是否为空
  if (keywords.length === 0) {
    // 显示无数据提示
    keywordChart.setOption({
      title: {
        text: '暂无关键词数据',
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 16,
          color: '#909399'
        }
      }
    })
    return
  }
  
  // 检查数据格式是否正确
  const validData = keywords.filter(item => item && item.keyword && item.count)
  if (validData.length === 0) {
    console.error('关键词数据格式不正确:', keywords)
    keywordChart.setOption({
      title: {
        text: '数据格式不正确',
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 16,
          color: '#909399'
        }
      }
    })
    return
  }
  
  // 转换数据结构 - 确保接口返回的count为数字类型
  const processedData = validData.map(item => ({
    name: item.keyword,
    value: typeof item.count === 'number' ? item.count : parseInt(item.count, 10) || 1,
  }))
  
  console.log('处理后的词云数据:', processedData)
  
  const option = {
    tooltip: {
      show: true,
      formatter: function(params) {
        return params.name + ' : ' + params.value
      }
    },
    series: [{
      type: 'wordCloud',
      // 词云形状
      shape: 'circle',
      // 词云位置
      left: 'center',
      top: 'center',
      width: '100%',
      height: '100%',
      // 词云字体大小范围
      sizeRange: [14, 50],
      // 词云旋转角度范围
      rotationRange: [-1, 1],
      rotationStep: 1,
      // 网格大小，影响词云中词语的间距
      gridSize: 15,
      // 是否允许词语画出边界
      drawOutOfBound: false,
      // 是否启用动画效果
      layoutAnimation: true,
      // 文字样式
      textStyle: {
        fontFamily: 'sans-serif',
        fontWeight: 'bold',
        color: function() {
          return 'rgb(' + 
            Math.round(Math.random() * 155 + 100) + ',' + 
            Math.round(Math.random() * 155 + 100) + ',' + 
            Math.round(Math.random() * 155 + 100) + ')'
        }
      },
      // 强调样式
      emphasis: {
        textStyle: {
          shadowBlur: 12,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
      },
      // 词云数据
      data: processedData
    }]
  }
  
  keywordChart.setOption(option)
  console.log('词云图配置已应用')
  
  // 处理图表大小调整
  const resizeHandler = () => {
    keywordChart && keywordChart.resize()
  }
  
  window.addEventListener('resize', resizeHandler)
  
  // 清理之前的事件监听器
  return () => {
    window.removeEventListener('resize', resizeHandler)
  }
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background-color: #f5f7fa;
  transition: all 0.3s ease;
}

/* 全屏模式下的样式优化 */
.dashboard-container:fullscreen {
  padding: 30px;
  gap: 30px;
  
  .welcome-section {
    padding: 25px;
    
    h2 {
      font-size: 30px;
    }
    
    .welcome-subtitle {
      font-size: 15px;
    }
    
    .time {
      font-size: 32px;
    }
  }
  
  .data-cards {
    gap: 20px;
    
    .data-card {
      .card-content {
        height: 110px;
      }
      
      .card-icon {
        width: 60px;
        
        .el-icon {
          font-size: 28px;
        }
      }
      
      .card-value {
        font-size: 28px;
      }
    }
  }
  
  .chart-card {
    .chart-header h3 {
      font-size: 18px;
    }
  }
  
  .el-table {
    height: 360px !important;
  }
  
  .monthly-chart {
    height: 360px;
  }
  
  .bar-chart-fill {
    width: 35px;
  }
}

.welcome-section {
  background: /* 渐变色背景 */
  linear-gradient(135deg, 
    rgb(50, 101, 151) 0%, 
    rgba(82, 136, 178, 0.9) 300%, 
    rgba(114, 164, 202, 0.8) 100%);
  color: #f0f8ff;  
  border-radius: 15px;
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  .welcome-info {
    h2 {
      margin: 0 0 5px 0;
      font-size: 22px;
      font-weight: 600;
    }
    
    .welcome-subtitle {
      margin: 0 0 12px 0;
      opacity: 0.9;
      font-size: 14px;
    }
    
    .agent-status {
      display: flex;
      align-items: center;
      gap: 10px;
      
      .status-btn {
        margin-left: 10px;
        background-color: rgba(255, 255, 255, 0.2);
        border: none;
        
        &:hover {
          background-color: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
  
  .date-time {
    text-align: right;
    
    .date {
      font-size: 14px;
      opacity: 0.9;
      margin-bottom: 6px;
    }
    
    .time {
      font-size: 26px;
      font-weight: 600;
    }
    
    .fullscreen-btn {
      margin-top: 8px;
      
      .el-button {
        background-color: rgba(255, 255, 255, 0.2);
        border: none;
        color: #fff;
        padding: 8px;
        
        &:hover {
          background-color: rgba(255, 255, 255, 0.3);
        }
      }
      
      .rotate-icon {
        transform: rotate(180deg);
        transition: transform 0.3s ease;
      }
    }
  }
}

.data-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 15px;
  margin-bottom: 10px;
  
  .data-card {
    transition: all 0.3s ease;
    border-radius: 16px;
    border: none;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    padding: 0;
    overflow: hidden;
    position: relative;
    background: linear-gradient(135deg, rgba(50, 101, 151, 0.04) 0%, rgba(72, 125, 167, 0.08) 100%);
    border-top: 3px solid rgba(64, 116, 162, 0.6);
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: linear-gradient(90deg, rgba(50, 101, 151, 0.6), rgba(114, 164, 202, 0.4));
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
      
      &::before {
        opacity: 1;
      }
      
      .card-icon {
        transform: scale(1.1);
      }
    }
    
    .card-content {
      display: flex;
      height: 60px;
      align-items: center;
      padding: 0 15px;
    }
    
    .card-icon {
      width: 50px;
      height: 50px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      margin-right: 15px;
      transition: all 0.3s ease;
      
      .el-icon {
        font-size: 22px;
      }
      
      &.chat {
        background: linear-gradient(135deg, #e8f4ff, #d6ebff);
        color: #409EFF;
      }
      
      &.waiting {
        background: linear-gradient(135deg, #ffece8, #ffdbd6);
        color: #f56c6c;
      }
      
      &.satisfaction {
        background: linear-gradient(135deg, #fff8e6, #fff2d6);
        color: #e6a23c;
      }
      
      &.resolved {
        background: linear-gradient(135deg, #f0f9eb, #e7f6df);
        color: #67c23a;
      }
      
      &.ai-resolved {
        background: linear-gradient(135deg, #ecf5ff, #dbedff);
        color: #409EFF;
      }
    }
    
    .card-info {
      flex: 1;
      min-width: 0;
      padding: 8px 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      
      .card-title {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
        font-weight: 500;
      }
      
      .card-data-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        
        .data-value-group {
          display: flex;
          align-items: baseline;
          gap: 10px;
        }
        
        .card-value {
          font-size: 28px;
          font-weight: 700;
          color: #303133;
          line-height: 1;
          font-family: 'Montserrat', sans-serif;
        }
        
        .card-trend {
          font-size: 12px;
          line-height: 1;
          white-space: nowrap;
          overflow: visible;
          padding: 4px 8px;
          border-radius: 12px;
          font-weight: 500;
          
          &.up {
            color: #67c23a;
            background-color: rgba(103, 194, 58, 0.1);
          }
          
          &.down {
            color: #f56c6c;
            background-color: rgba(245, 108, 108, 0.1);
          }
        }
        
        .card-actions {
          margin-left: 10px;
        }
        
        .el-rate {
          margin-top: 0;
          line-height: 1;
          transform: scale(0.8);
          transform-origin: right;
        }
      }
    }
  }
}

.charts-section {
  margin-bottom: 8px;
  
  .chart-card {
    background-color: #fff;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.04);
    margin-bottom: 16px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.97) 0%, rgba(240, 248, 255, 0.95) 100%);
    border-left: 3px solid rgba(64, 116, 162, 0.4);
    
    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #32658f;
      }
    }
  }
}

/* 统一图表高度 */
.chart-height {
  height: 350px;
}

.waiting-sessions {
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.04);
  border: none;
  
  .waiting-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .user-cell {
    display: flex;
    align-items: center;
    
    .el-avatar {
      margin-right: 10px;
    }
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;
  }
  
  .welcome-section {
    flex-direction: column;
    align-items: flex-start;
    
    .date-time {
      text-align: left;
      margin-top: 15px;
    }
  }
  
  .data-cards {
    grid-template-columns: 1fr;
  }
  
  .el-col {
    margin-bottom: 12px;
  }
}

.table-container {
  position: relative;
  height: calc(100% - 50px);
  overflow: hidden;
}

.el-table {
  height: 100% !important;
  --el-table-header-bg-color: rgba(50, 101, 151, 0.05);
  --el-table-header-text-color: #32658f;
  
  .el-table__header-wrapper th {
    font-weight: 600;
    background: linear-gradient(180deg, rgba(50, 101, 151, 0.1), rgba(50, 101, 151, 0.05));
  }
  
  .el-table__row:hover > td {
    background-color: rgba(50, 101, 151, 0.05) !important;
  }
}

.data-bar {
  display: flex;
  align-items: center;
  height: 22px;
  
  .bar {
    height: 16px;
    border-radius: 4px;
    margin-right: 8px;
    transition: width 0.5s ease;
    background: linear-gradient(90deg, rgba(64, 116, 162, 0.8), rgba(100, 158, 203, 0.6)) !important;
    box-shadow: 0 2px 4px rgba(64, 116, 162, 0.2);
  }
  
  .bar-value {
    font-weight: 600;
    color: #32658f;
  }
}

.mini-btn {
  padding: 4px 12px;
  font-size: 12px;
  height: 28px;
  line-height: 1;
  border-radius: 14px;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.stats-section {
  margin-top: 0;
  
  .chart-card {
    background-color: #fff;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.04);
    height: 330px;
    margin-bottom: 12px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.97) 0%, rgba(240, 248, 255, 0.95) 100%);
    border-left: 3px solid rgba(64, 116, 162, 0.4);
    
    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #32658f;
      }
      
      .chart-subtitle {
        font-size: 12px;
        color: #909399;
      }
    }
    
    .chart-scroll-container {
      height: calc(100% - 40px);
      overflow-x: auto;
      overflow-y: hidden;
      position: relative;
      
      .pie-chart {
        height: 100%;
        min-width: 100%;
      }
    }
    
    .pie-chart-container {
      display: flex;
      height: calc(100% - 40px);
      
      .pie-chart {
        flex: 1;
        height: 100%;
      }
      
      .pie-chart-legend {
        width: 200px;
        padding-left: 10px;
        overflow-y: auto;
        
        .legend-item {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          flex-wrap: wrap;
          
          .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
            margin-right: 8px;
          }
          
          .legend-label {
            font-size: 14px;
            color: #606266;
            margin-right: 8px;
          }
          
          .legend-value {
            font-size: 12px;
            color: #909399;
            white-space: nowrap;
          }
        }
      }
    }
  }
}

.bar-chart-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  min-width: 50px;
  margin-right: 10px;
}

.bar-chart-column {
  width: 100%;
  height: 200px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.bar-chart-fill {
  width: 24px;
  background-color: #409EFF;
  border-radius: 4px 4px 0 0;
  position: relative;
  transition: height 0.5s ease;
}

.bar-chart-value {
  position: absolute;
  top: -25px;
  width: 100%;
  text-align: center;
  font-size: 12px;
  color: #606266;
  font-weight: bold;
  z-index: 2;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 10px;
  padding: 2px 4px;
  box-sizing: border-box;
}

.bar-chart-label {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

@media (max-width: 768px) {
  .stats-section {
    .pie-chart-container {
      flex-direction: column;
      
      .pie-chart {
        height: 200px;
      }
      
      .pie-chart-legend {
        width: 100%;
        padding-left: 0;
        padding-top: 15px;
        
        .legend-item {
          margin-bottom: 8px;
        }
      }
    }
  }
  
  .bar-chart-item {
    max-width: 40px;
  }
  
  .bar-chart-fill {
    width: 18px;
  }
}

.wordcloud-container {
  height: 330px;
  min-height: 330px;
  width: 100%;
}
</style> 