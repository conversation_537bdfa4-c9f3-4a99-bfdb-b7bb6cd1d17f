<template>
  <div class="faq-container">
    <!-- FAQ列表 -->
    <div class="faq-list">
      <el-card shadow="hover">
        <template #header>
          <div class="card-header">
            <span>问题列表</span>
            <div class="header-operations">
              <el-button type="primary" size="small" @click="addFAQ">
                <el-icon><Plus /></el-icon> 新增问题
              </el-button>
              <el-button type="danger" size="small" @click="batchDelete" :disabled="!hasSelected">
                <el-icon><Delete /></el-icon> 批量删除
              </el-button>
            </div>
          </div>
        </template>
        
        <!-- 搜索筛选区 -->
        <el-form :inline="true" class="search-form">
          <el-form-item label="问题分类">
            <el-select 
              v-model="queryParams.category" 
              placeholder="全部分类" 
              clearable
              @change="handleCategoryChange"
              style="width: 200px"
            >
              <el-option label="全部" value="" />
              <el-option 
                v-for="category in categoryOptions" 
                :key="category.value" 
                :label="category.label" 
                :value="category.value" 
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="关键词">
            <el-input
              v-model="queryParams.keyword"
              placeholder="问题/答案"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        
        <el-table
          v-loading="loading"
          :data="faqList"
          style="width: 100%"
          border
          stripe
          :header-cell-style="{background:'#f5f7fa'}"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="50" />
          <el-table-column label="序号" width="80">
            <template #default="scope">
              {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="问题分类" width="120">
            <template #default="scope">
              <el-tag :type="getCategoryTag(scope.row.category)">
                {{ getCategoryText(scope.row.category) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="question" label="问题" show-overflow-tooltip />
          
          <el-table-column prop="answer" label="答案" show-overflow-tooltip />
          
          <el-table-column prop="createdAt" label="创建时间" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.createdAt) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="updatedAt" label="更新时间" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.updatedAt) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="160" fixed="right">
            <template #default="scope">
              <el-button 
                type="primary" 
                link
                @click="editFAQ(scope.row)"
              >
                编辑
              </el-button>
              <el-button 
                type="danger" 
                link
                @click="deleteFAQ(scope.row)"
              >
                删除
              </el-button>
              <el-button 
                type="success" 
                link
                @click="insertToReply(scope.row)"
              >
              复制
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
    
    <!-- FAQ编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="formTitle"
      width="50%"
      destroy-on-close
    >
      <el-form
        ref="faqFormRef"
        :model="faqForm"
        :rules="faqFormRules"
        label-width="100px"
      >
        <el-form-item label="问题分类" prop="category">
          <el-select v-model="faqForm.category" placeholder="请选择问题分类">
            <el-option 
              v-for="category in categoryOptions" 
              :key="category.value" 
              :label="category.label" 
              :value="category.value" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="问题" prop="question">
          <el-input 
            v-model="faqForm.question" 
            placeholder="请输入问题" 
            type="textarea" 
            :rows="2"
          />
        </el-form-item>
        
        <el-form-item label="答案" prop="answer">
          <el-input 
            v-model="faqForm.answer" 
            placeholder="请输入答案" 
            type="textarea" 
            :rows="5"
          />
        </el-form-item>
        
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { Plus, Check, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment'
import { getAllFaqs, addFaq, updateFaq, deleteFaq } from '@/api/faq'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  category: '',
  keyword: ''
})

// 分类选项
const categoryOptions = [
  { value: '订单相关', label: '订单相关' },
  { value: '商品相关', label: '商品相关' },
  { value: '配送相关', label: '配送相关' },
  { value: '支付相关', label: '支付相关' },
  { value: '账户相关', label: '账户相关' },
  { value: '其他问题', label: '其他问题' }
]

// FAQ列表数据
const faqList = ref([])
const total = ref(0)
const loading = ref(false)
const selectedFAQs = ref([])

// 编辑对话框
const dialogVisible = ref(false)
const faqFormRef = ref(null)
const faqForm = reactive({
  id: '',
  category: '',
  question: '',
  answer: '',
  sort: 0
})
const faqFormRules = {
  category: [
    { required: true, message: '请选择问题分类', trigger: 'change' }
  ],
  question: [
    { required: true, message: '请输入问题', trigger: 'blur' },
    { max: 200, message: '最大长度为 200 个字符', trigger: 'blur' }
  ],
  answer: [
    { required: true, message: '请输入答案', trigger: 'blur' }
  ]
}

// 是否有选中项
const hasSelected = computed(() => selectedFAQs.value.length > 0)

// 表单标题
const formTitle = computed(() => {
  return faqForm.id ? '编辑常见问题' : '新增常见问题'
})

// 生命周期钩子
onMounted(() => {
  // 加载FAQ列表
  loadFAQList()
})

// 加载FAQ列表
const loadFAQList = async () => {
  loading.value = true
  try {
    // 调用后端接口获取所有FAQ
    const response = await getAllFaqs()
    
    if (response.code === 200) {
      const allFaqs = response.data || []
      
      // 进行前端筛选
      let filteredFaqs = allFaqs
      
      // 分类筛选
      if (queryParams.category) {
        filteredFaqs = filteredFaqs.filter(faq => faq.category === queryParams.category)
      }
      
      // 关键词筛选
      if (queryParams.keyword) {
        const keyword = queryParams.keyword.toLowerCase()
        filteredFaqs = filteredFaqs.filter(faq => 
          (faq.question && faq.question.toLowerCase().includes(keyword)) || 
          (faq.answer && faq.answer.toLowerCase().includes(keyword))
        )
      }
      
      // 记录总数
      total.value = filteredFaqs.length
      
      // 按排序值排序
      filteredFaqs.sort((a, b) => (a.sort || 0) - (b.sort || 0))
      
      // 分页处理
      const start = (queryParams.pageNum - 1) * queryParams.pageSize
      const end = start + queryParams.pageSize
      faqList.value = filteredFaqs.slice(start, end)
    } else {
      ElMessage.error(response.message || '加载常见问题失败')
    }
  } catch (error) {
    console.error('加载FAQ列表失败', error)
    ElMessage.error('加载FAQ列表失败：' + error.message || '未知错误')
  } finally {
    loading.value = false
  }
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedFAQs.value = selection
}

// 新增FAQ
const addFAQ = () => {
  resetForm()
  dialogVisible.value = true
}

// 编辑FAQ
const editFAQ = (row) => {
  resetForm()
  Object.assign(faqForm, row)
  dialogVisible.value = true
}

// 删除FAQ
const deleteFAQ = (row) => {
  ElMessageBox.confirm(
    `确定要删除问题"${row.question}"吗？`,
    '删除问题',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      const response = await deleteFaq(row.id)
      
      if (response.code === 200) {
        ElMessage.success('删除成功')
        // 从列表中移除
        faqList.value = faqList.value.filter(item => item.id !== row.id)
        total.value--
      } else {
        ElMessage.error(response.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败', error)
      ElMessage.error('删除失败：' + error.message || '未知错误')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 批量删除
const batchDelete = () => {
  if (selectedFAQs.value.length === 0) {
    ElMessage.warning('请至少选择一条记录')
    return
  }
  
  ElMessageBox.confirm(
    `确定要删除已选中的 ${selectedFAQs.value.length} 条问题吗？`,
    '批量删除',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      const ids = selectedFAQs.value.map(item => item.id)
      
      // 由于后端没有批量删除接口，所以需要逐个删除
      const deletePromises = ids.map(id => deleteFaq(id))
      const results = await Promise.allSettled(deletePromises)
      
      // 统计成功和失败数量
      const successCount = results.filter(result => result.status === 'fulfilled' && result.value.code === 200).length
      const failCount = ids.length - successCount
      
      if (successCount > 0) {
        // 从列表中移除成功删除的项
        faqList.value = faqList.value.filter(item => !ids.includes(item.id) || 
          results.find(r => r.status === 'fulfilled' && r.value.code !== 200 && r.value.id === item.id))
        total.value -= successCount
        
        if (failCount > 0) {
          ElMessage.warning(`已成功删除${successCount}条问题，${failCount}条删除失败`)
        } else {
          ElMessage.success('批量删除成功')
        }
      } else {
        ElMessage.error('批量删除失败')
      }
    } catch (error) {
      console.error('批量删除失败', error)
      ElMessage.error('批量删除失败：' + error.message || '未知错误')
    }
  }).catch(() => {
    // 用户取消
  })
}

// 插入到回复
const insertToReply = (row) => {
  ElMessage.success(`已复制问题"${row.question}"的回答到剪贴板`)
  // TODO: 实际应用中可以通过事件总线或store将内容发送到聊天组件
}

// 重置表单
const resetForm = () => {
  faqForm.id = ''
  faqForm.category = ''
  faqForm.question = ''
  faqForm.answer = ''
  faqForm.sort = 0
}

// 提交表单
const submitForm = async () => {
  if (!faqFormRef.value) return
  
  try {
    await faqFormRef.value.validate()
    
    if (faqForm.id) {
      // 编辑
      const response = await updateFaq(faqForm.id, faqForm)
      
      if (response.code === 200) {
        ElMessage.success('修改成功')
        // 更新列表
        const index = faqList.value.findIndex(item => item.id === faqForm.id)
        if (index !== -1) {
          faqList.value[index] = response.data || {
            ...faqList.value[index],
            ...faqForm
          }
        }
      } else {
        ElMessage.error(response.message || '修改失败')
      }
    } else {
      // 新增
      const response = await addFaq(faqForm)
      
      if (response.code === 200) {
        ElMessage.success('新增成功')
        // 重新加载列表
        loadFAQList()
      } else {
        ElMessage.error(response.message || '新增失败')
      }
    }
    
    dialogVisible.value = false
  } catch (error) {
    console.error('表单验证或提交失败', error)
    ElMessage.error('提交失败：' + error.message || '未知错误')
  }
}

// 处理搜索条件变化
const handleCategoryChange = (val) => {
  console.log('分类变更为:', val)
  nextTick(() => {
    handleSearch()
  })
}

// 搜索
const handleSearch = () => {
  queryParams.pageNum = 1
  loadFAQList()
}

// 重置查询条件
const resetQuery = () => {
  queryParams.category = ''
  queryParams.keyword = ''
  queryParams.pageNum = 1
  loadFAQList()
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  queryParams.pageSize = size
  loadFAQList()
}

// 处理当前页变化
const handleCurrentChange = (page) => {
  queryParams.pageNum = page
  loadFAQList()
}

// 获取分类标签类型
const getCategoryTag = (category) => {
  switch (category) {
    case '订单相关': return ''
    case '商品相关': return 'success'
    case '配送相关': return 'warning'
    case '支付相关': return 'danger'
    case '账户相关': return 'info'
    case '其他问题': return ''
    default: return ''
  }
}

// 获取分类文本
const getCategoryText = (category) => {
  return category || '未知'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return moment(dateTime).format('YYYY-MM-DD HH:mm:ss')
}
</script>

<style lang="scss" scoped>
.faq-container {
  padding: 20px;
  
  .faq-list {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      
      .header-operations {
        display: flex;
        gap: 10px;
      }
    }
    
    .search-form {
      margin-bottom: 15px;
      
      .el-form-item {
        margin-bottom: 10px;
      }

      .el-select {
        width: 200px;
      }
    }
    
    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
  
  .form-help {
    margin-left: 10px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
}
</style> 