<template>
  <div class="agent-layout">
    <!-- 侧边栏 -->
    <div class="sidebar" :class="{ 'collapsed': isCollapsed }">
      <div class="sidebar-header">
        <div class="logo-container" @click="toggleCollapse">
          <div class="logo-inner" v-if="!isCollapsed">
            <h1 class="logo-text">
              <span>熊小智客服</span>
            </h1>
          </div>
          <div class="logo-mini" v-else>
            <span>熊</span>
          </div>
        </div>
      </div>
      
      <div class="sidebar-content">
        <el-menu
          :default-active="activeMenu"
          class="sidebar-menu"
          :collapse="isCollapsed"
          :collapse-transition="false"
          @select="handleSelect"
        >
          <!-- 核心功能区 -->
          <!-- <div class="menu-section-header" v-if="!isCollapsed">工作管理</div> -->
          <el-menu-item index="/agent/dashboard">
            <el-icon><HomeFilled /></el-icon>
            <template #title>工作台</template>
          </el-menu-item>
          
          <el-menu-item index="/agent/chat">
            <el-icon><ChatRound /></el-icon>
            <template #title>会话服务</template>
          </el-menu-item>
          
          <el-menu-item index="/agent/history">
            <el-icon><Clock /></el-icon>
            <template #title>历史会话</template>
          </el-menu-item>
          
          <!-- 知识管理区 -->
          <div class="menu-divider" v-if="!isCollapsed"></div>
          <div class="menu-section-header" v-if="!isCollapsed">内容分析</div>

          <el-menu-item index="/agent/question-analysis">
            <el-icon><DataAnalysis /></el-icon>
            <template #title>问题分析</template>
          </el-menu-item>

          <el-menu-item index="/agent/faq">
            <el-icon><Document /></el-icon>
            <template #title>常见问题</template>
          </el-menu-item>
          
          <!-- 数据分析区 -->
          <div class="menu-divider" v-if="!isCollapsed"></div>
          <div class="menu-section-header" v-if="!isCollapsed">知识管理</div>

          <el-menu-item index="/agent/knowledge-base">
            <el-icon><Collection /></el-icon>
            <template #title>知识库管理</template>
          </el-menu-item>
          
          <!-- 系统管理区 (管理员可见) -->
          <template v-if="hasAdminPrivilege">
            <div class="menu-divider" v-if="!isCollapsed"></div>
            <div class="menu-section-header" v-if="!isCollapsed">系统管理</div>
            <el-menu-item index="/agent/service-agents">
              <el-icon><User /></el-icon>
              <template #title>客服管理</template>
            </el-menu-item>
            <el-menu-item index="/agent/users">
              <el-icon><User /></el-icon>
              <template #title>用户管理</template>
            </el-menu-item>

          </template>
        </el-menu>
      </div>
      
      <div class="sidebar-footer">
        <!-- 在线状态切换 -->
        <!-- <div class="status-toggle">
          <div class="status-label" v-if="!isCollapsed">在线状态</div>
          <el-switch
            v-model="isOnline"
            inline-prompt
            active-text="在线"
            inactive-text="离线"
            @change="toggleStatus"
            class="status-switch"
          />
        </div> -->
        
        <!-- 添加WebSocket连接状态指示器 -->
        <div class="connection-status" v-if="!isCollapsed">
          <ConnectionStatus mode="agent" />
        </div>  

        <!-- 用户信息区域 -->
        <el-dropdown trigger="click" @command="handleCommand">
          <div class="agent-info">
            <el-avatar :size="30" :src="agentInfo?.avatar">
              {{ agentInfo?.name?.substring(0, 1) || '客' }}
            </el-avatar>
            <div v-if="!isCollapsed" class="agent-detail">
              <div class="agent-name">
                {{ agentInfo?.name || agentInfo?.agentNo || '客服' }}
                <el-tag 
                  size="small" 
                  :type="agentInfo?.status === 1 ? 'success' : 'info'"
                >
                  {{ agentInfo?.status === 1 ? '在线' : '离线' }}
                </el-tag>
              </div>
            </div>
            <el-icon v-if="!isCollapsed" class="dropdown-icon"><CaretBottom /></el-icon>
          </div>
          
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人信息修改</el-dropdown-item>
              <!-- <el-dropdown-item command="settings">系统设置</el-dropdown-item> -->
              <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        

      </div>
    </div>
    
    <!-- 主内容区 -->
    <div class="main-content" :class="{ 'expanded': isCollapsed }">
      <router-view />
    </div>

    <!-- 添加个人信息修改对话框 -->
    <el-dialog
      v-model="profileDialogVisible"
      title="个人信息修改"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="profileFormRef"
        :model="profileForm"
        :rules="profileRules"
        label-width="80px"
        label-position="right"
      >
        <el-form-item label="头像">
          <el-avatar :size="80" :src="profileForm.avatar">
            {{ profileForm.name?.substring(0, 1) || '客' }}
          </el-avatar>
          
          <!-- 头像选择器 -->
          <div class="avatar-selector">
            <div class="avatar-options">
              <div 
                v-for="(avatar, index) in avatarOptions" 
                :key="index" 
                class="avatar-option"
                :class="{ 'selected': profileForm.avatar === avatar }"
                @click="profileForm.avatar = avatar"
              >
                <el-avatar :size="40" :src="avatar"></el-avatar>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="profileForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="工号" disabled>
          <el-input v-model="profileForm.agentNo" disabled />
        </el-form-item>
        <el-form-item label="客服类型" prop="agentType">
          <el-select v-model="profileForm.agentType" placeholder="请选择客服类型">
            <el-option :value="1" label="客服" />
            <el-option :value="3" label="管理员（管理员无法接收用户消息）" />
          </el-select>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword" v-if="showPasswordFields">
          <el-input v-model="profileForm.newPassword" type="password" placeholder="请输入新密码" show-password />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword" v-if="showPasswordFields">
          <el-input v-model="profileForm.confirmPassword" type="password" placeholder="请确认新密码" show-password />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" link @click="showPasswordFields = !showPasswordFields">
            {{ showPasswordFields ? '取消修改密码' : '修改密码' }}
          </el-button>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="profileDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitProfileForm" :loading="profileSubmitting">
            确认修改
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import { 
  HomeFilled, 
  ChatRound, 
  Clock, 
  User, 
  Document, 
  Fold, 
  Expand, 
  CaretBottom,
  Collection,
  DataAnalysis
} from '@element-plus/icons-vue'
import { useUserStore } from '@/store/user'
import { useChatStore } from '@/store/chat'
import ConnectionStatus from '@/components/ConnectionStatus.vue'
import { updateAgentInfo } from '@/api/agent'

// 路由
const router = useRouter()
const route = useRoute()

// Store
const userStore = useUserStore()
const chatStore = useChatStore()

// 计算属性：客服信息
const agentInfo = computed(() => userStore.userInfo)

// 是否具有管理员权限（包含agentType=3的用户）
const hasAdminPrivilege = computed(() => agentInfo.value?.agentType === 3)

// 侧边栏折叠状态
const isCollapsed = ref(false)

// 客服在线状态
const isOnline = computed(() => agentInfo.value?.status === 1)

// 活动菜单
const activeMenu = computed(() => route.path)

// 获取连接状态类
const getConnectionStatusClass = computed(() => {
  if (chatStore.connected) return 'status-connected'
  if (chatStore.isPolling) return 'status-polling'
  return 'status-disconnected'
})

// 获取连接状态文本
const getConnectionStatusText = computed(() => {
  if (chatStore.connected) return '已连接'
  if (chatStore.isPolling) return '轮询中'
  return '未连接'
})

// 个人信息修改相关
const profileDialogVisible = ref(false) // 个人信息对话框可见性
const profileSubmitting = ref(false) // 提交状态
const profileFormRef = ref(null) // 表单引用
const showPasswordFields = ref(false) // 是否显示密码修改字段

// 个人信息表单
const profileForm = reactive({
  id: '',
  name: '',
  agentNo: '',
  avatar: '',
  agentType: null,
  newPassword: '',
  confirmPassword: ''
})

// 头像选项
const avatarOptions = [
  'https://file.juranguanjia.com/upfile/2025/04-18/8288c7361d1a40a280122bdd93519f59.png',
  'https://file.juranguanjia.com/upfile/2025/04-18/d70ffb09ffe24e1591ae6bdcb3f3f25e.png',
  'https://file.juranguanjia.com/upfile/2025/04-18/7c4d83fcb68444e9842c1205df145418.png',
  'https://file.juranguanjia.com/upfile/2025/04-18/197095ad9fa34129bf94ec65ca223ec8.png',
  'https://file.juranguanjia.com/upfile/2025/04-18/5368534b64a74b83b68acb8194c87cd7.png',
  'https://file.juranguanjia.com/upfile/2025/04-18/ce8a318cbaeb4b06b8f47f0ac8a34906.png',
  'https://file.juranguanjia.com/upfile/2025/04-18/1dc20d13ae0c465ab68e32154941fad9.png'
]

// 个人信息表单校验规则
const profileRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在2到20个字符之间', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur', validator: (rule, value, callback) => {
      if (showPasswordFields.value && !value) {
        callback(new Error('请输入新密码'))
      } else {
        callback()
      }
    }},
    { min: 6, max: 20, message: '密码长度在6到20个字符之间', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur', validator: (rule, value, callback) => {
      if (showPasswordFields.value && !value) {
        callback(new Error('请确认新密码'))
      } else if (showPasswordFields.value && value !== profileForm.newPassword) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }}
  ]
}

// 打开个人信息修改对话框
const openProfileDialog = () => {
  // 重置密码字段
  showPasswordFields.value = false
  profileForm.newPassword = ''
  profileForm.confirmPassword = ''
  
  // 填充表单数据
  if (agentInfo.value) {
    profileForm.id = agentInfo.value.id
    profileForm.name = agentInfo.value.name || ''
    profileForm.agentNo = agentInfo.value.agentNo || ''
    profileForm.avatar = agentInfo.value.avatar || ''
    profileForm.agentType = agentInfo.value.agentType || 1
  }
  profileDialogVisible.value = true
}

// 提交个人信息表单
const submitProfileForm = () => {
  profileFormRef.value?.validate(async (valid) => {
    if (!valid) return

    try {
      profileSubmitting.value = true
      
      // 构造请求数据
      const requestData = {
        id: profileForm.id,
        name: profileForm.name,
        avatar: profileForm.avatar,
        agentType: profileForm.agentType
      }
      
      // 如果修改密码，添加密码字段
      if (showPasswordFields.value && profileForm.newPassword) {
        requestData.newPassword = profileForm.newPassword
      }
      
      
      // 调用接口更新客服信息
      const result = await updateAgentInfo(requestData)
      
      if (result.code === 200) {
        ElMessage.success('个人信息修改成功')
        profileDialogVisible.value = false
        
        // 更新用户信息
        userStore.setUserInfo(result.data)
      } else {
        ElMessage.error(result.msg || '个人信息修改失败')
      }
    } catch (error) {
      console.error('更新个人信息失败', error)
      ElMessage.error('网络错误，请稍后重试')
    } finally {
      profileSubmitting.value = false
    }
  })
}

// 生命周期钩子
onMounted(() => {
  // 从localStorage恢复状态
  userStore.loadFromStorage()
  
  // 从localStorage恢复侧边栏折叠状态
  const savedCollapsed = localStorage.getItem('sidebar_collapsed')
  if (savedCollapsed !== null) {
    isCollapsed.value = savedCollapsed === '1'
  }
  
  // 如果没有登录，跳转到登录页
  if (!userStore.isLoggedIn) {
    router.push('/login')
    return
  }
  
  // 如果不是客服角色，跳转到登录页
  if (!userStore.isAgent) {
    router.push('/login')
    return
  }
  
  // 初始化WebSocket连接
  chatStore.initWebSocket()
  
  // 监听窗口大小变化，自动折叠侧边栏
  const handleResize = () => {
    if (window.innerWidth < 768) {
      isCollapsed.value = true
      localStorage.setItem('sidebar_collapsed', '1')
    }
  }
  
  // 初始调用一次
  handleResize()
  
  // 添加事件监听
  window.addEventListener('resize', handleResize)
  
  // 组件销毁时移除事件监听
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })
})

// 切换侧边栏折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
  // 将折叠状态保存到localStorage，确保页面刷新后能保持状态
  localStorage.setItem('sidebar_collapsed', isCollapsed.value ? '1' : '0')
}

// 菜单选择处理
const handleSelect = (index) => {
  router.push(index)
}

// 修改下拉菜单命令处理
const handleCommand = (command) => {
  if (command === 'logout') {
    confirmLogout()
  } else if (command === 'profile') {
    // 处理个人信息修改
    openProfileDialog()
  } else if (command === 'settings') {
    // 处理系统设置
    // router.push('/agent/settings')
  }
}

// 确认退出登录
const confirmLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 更新状态为离线
    if (userStore.userInfo?.id) {
      userStore.updateAgentStatus(userStore.userInfo.id, 0)
    }
    
    // 清理聊天状态
    chatStore.clearState()
    
    // 退出登录
    userStore.logout()
    
    // 跳转到登录页
    router.push('/login')
  }).catch(() => {})
}

// 切换在线状态
const toggleStatus = async (value) => {
  if (!userStore.userInfo?.id) return
  
  try {
    const status = value ? 1 : 0
    await userStore.updateAgentStatus(userStore.userInfo.id, status)
  } catch (error) {
    console.error('更新状态失败', error)
  }
}
</script>

<style lang="scss" scoped>
.agent-layout {
  height: 100%;
  display: flex;
}

.sidebar {
  width: 240px;
  height: 100%;
  background: linear-gradient(165deg, #1e2b42 0%, #16213a 100%);
  color: #fff;
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 150px;
    background: radial-gradient(ellipse at top, rgba(75, 139, 235, 0.15), transparent 70%);
    pointer-events: none;
  }
  
  &.collapsed {
    width: 64px;
  }
  
  .sidebar-header {
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 15px;
    position: relative;
    z-index: 1;
    border-bottom: 1px solid rgba(255, 255, 255, 0.06);
    
    .logo-container {
      display: flex;
      align-items: center;
      height: 100%;
      overflow: hidden;
      cursor: pointer;
      width: 100%;
      justify-content: center;
      
      .logo-inner {
        display: flex;
        align-items: center;
        gap: 10px;
        
        .logo-text {
          width: 150px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, rgba(47, 73, 118, 0.85), rgba(29, 46, 77, 0.95));
          border-radius: 10px;
          box-shadow: 0 3px 10px rgba(15, 23, 42, 0.3), inset 0 1px 1px rgba(255, 255, 255, 0.1);
          font-size: 22px;
          font-weight: 600;
          letter-spacing: 0.5px;
          transition: all 0.3s ease;
          border: 1px solid rgba(255, 255, 255, 0.08);
          position: relative;
          
          &::after {
            display: none; /* 隐藏箭头 */
          }
          
          &:hover::after {
            display: none; /* 隐藏箭头 */
          }
          
          span {
            background: linear-gradient(90deg, #ffffff, #88b9ff);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: none;
          }
          
          &:hover {
            transform: translateY(-2px);
            background: linear-gradient(135deg, rgba(57, 83, 128, 0.85), rgba(39, 56, 87, 0.95));
            box-shadow: 0 4px 12px rgba(15, 23, 42, 0.4), inset 0 1px 1px rgba(255, 255, 255, 0.15);
            
            span {
              background: linear-gradient(90deg, #ffffff, #a0cbff);
              -webkit-background-clip: text;
              background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }
      }
      
      .logo-mini {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, rgba(47, 73, 118, 0.85), rgba(29, 46, 77, 0.95));
        border-radius: 10px;
        box-shadow: 0 3px 10px rgba(15, 23, 42, 0.3), inset 0 1px 1px rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 18px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.08);
        position: relative;
        
        &::after {
          display: none; /* 隐藏箭头 */
        }
        
        &:hover {
          transform: translateY(-2px);
          background: linear-gradient(135deg, rgba(57, 83, 128, 0.85), rgba(39, 56, 87, 0.95));
          box-shadow: 0 4px 12px rgba(15, 23, 42, 0.4), inset 0 1px 1px rgba(255, 255, 255, 0.15);
          
          &::after {
            display: none; /* 隐藏箭头 */
          }
          
          span {
            background: linear-gradient(90deg, #ffffff, #a0cbff);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
        
        span {
          background: linear-gradient(90deg, #ffffff, #88b9ff);
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
          text-shadow: none;
        }
      }
    }
  }
  
  .sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 5px 0;
    margin: 5px 10px;
    
    /* 美化滚动条 */
    &::-webkit-scrollbar {
      width: 4px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.15);
      border-radius: 2px;
    }
    
    &::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 0.25);
    }
    
    /* 菜单分组样式 */
    .menu-group {
      padding: 10px 12px 4px;
      
      .menu-group-title {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.45);
        margin-bottom: 4px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
    
    /* 菜单分割线 */
    .menu-divider {
      height: 1px;
      background: rgba(255, 255, 255, 0.06);
      margin: 10px 12px;
    }
    
    /* 菜单部分标题 */
    .menu-section-header {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.6);
      margin: 14px 12px 10px;
      font-weight: 500;
      letter-spacing: 0.5px;
      position: relative;
      padding-left: 10px;
      text-transform: uppercase;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 12px;
        background: linear-gradient(to bottom, #4b8beb, #1e56d8);
        border-radius: 1.5px;
      }
    }
    
    .sidebar-menu {
      border-right: none;
      background-color: transparent;
      
      :deep(.el-menu-item) {
        height: 44px;
        margin: 5px 0;
        padding: 0 14px !important;
        border-radius: 10px;
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
        font-size: 14px;
        transition: all 0.3s;
        display: flex;
        align-items: center;
        letter-spacing: 0.2px;
        position: relative;
        overflow: hidden;
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 0;
          height: 60%;
          background: #4b8beb;
          border-radius: 0 3px 3px 0;
          opacity: 0;
          transition: all 0.3s;
        }
        
        .el-icon {
          margin-right: 10px;
          font-size: 18px;
          transition: all 0.3s;
          opacity: 0.85;
          position: relative;
          z-index: 1;
        }
        
        &:hover {
          color: #fff;
          background: rgba(255, 255, 255, 0.1);
          transform: translateX(2px);
          
          .el-icon {
            opacity: 1;
            transform: scale(1.1);
            color: #4b8beb;
          }
          
          &::before {
            width: 3px;
            opacity: 0.7;
          }
        }
        
        &.is-active {
          color: #fff;
          background: linear-gradient(90deg, rgba(75, 139, 235, 0.2), rgba(30, 86, 216, 0.1));
          font-weight: 500;
          transform: translateX(0);
          box-shadow: none;
          
          &::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(to bottom, #4b8beb, #1e56d8);
            border-radius: 1.5px 0 0 1.5px;
            box-shadow: -1px 0 6px rgba(75, 139, 235, 0.5);
          }
          
          &::before {
            width: 3px;
            opacity: 0;
          }
          
          .el-icon {
            opacity: 1;
            color: #4b8beb;
            transform: scale(1.1);
          }
        }
      }
    }
  }
  
  .sidebar-footer {
    padding: 12px;
    margin: 0 10px 10px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    border-radius: 10px;
    background: rgba(15, 23, 42, 0.5);
    backdrop-filter: blur(10px);
    position: relative;
    border-top: 1px solid rgba(255, 255, 255, 0.03);
    border-left: 1px solid rgba(255, 255, 255, 0.03);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    
    /* 在线状态切换 */
    .status-toggle {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 6px 8px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      margin-bottom: 4px;
      
      .status-label {
        font-size: 13px;
        color: rgba(255, 255, 255, 0.8);
      }
      
      .status-switch {
        --el-switch-on-color: #67c23a;
        --el-switch-off-color: #909399;
      }
    }
    
    .agent-info {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 8px;
      border-radius: 8px;
      transition: all 0.2s;
      background: rgba(255, 255, 255, 0.05);
      
      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }
      
      .el-avatar {
        border: 2px solid rgba(255, 255, 255, 0.15);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
      }
      
      .agent-detail {
        margin-left: 10px;
        flex: 1;
        
        .agent-name {
          font-size: 14px;
          font-weight: 500;
          color: #fff;
          margin-bottom: 4px;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        
        .el-tag {
          font-size: 10px;
          padding: 0 8px;
          height: 18px;
          line-height: 18px;
          border-radius: 9px;
          font-weight: normal;
        }
      }
      
      .dropdown-icon {
        color: rgba(255, 255, 255, 0.7);
        margin-left: 8px;
        font-size: 12px;
        transition: all 0.2s;
      }
    }
    
    /* 简洁版连接状态 */
    .connection-status-wrapper {
      margin-top: 4px;
    }
    
    .connection-status-compact {
      display: flex;
      align-items: center;
      padding: 8px;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.05);
      font-size: 13px;
      
      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;
        position: relative;
        animation: pulse 1.5s infinite;
      }
      
      .status-text {
        color: rgba(255, 255, 255, 0.8);
        font-size: 12px;
      }
      
      &.status-connected .status-dot {
        background-color: #67c23a;
        box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
      }
      
      &.status-polling .status-dot {
        background-color: #e6a23c;
        box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.2);
      }
      
      &.status-disconnected .status-dot {
        background-color: #f56c6c;
        box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
      }
    }
  }
}

.main-content {
  flex: 1;
  height: 100%;
  overflow: hidden;
  background-color: var(--background-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &.expanded {
    margin-left: 0;
  }
}

/* 深色模式下的菜单样式 */
:deep(.el-menu--vertical) {
  background: linear-gradient(145deg, #1e2b42, #16213a);
  padding: 8px;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.05);
  
  .el-menu-item {
    margin: 4px 0;
    border-radius: 6px;
    color: rgba(255, 255, 255, 0.75);
    transition: all 0.3s;
    
    &:hover, &.is-active {
      color: #fff;
      background: linear-gradient(90deg, #4b8beb, #1e56d8);
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  70% {
    transform: scale(1.5);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* 添加的样式 */
.avatar-selector {
  margin-top: 15px;
}

.avatar-options {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.avatar-option {
  cursor: pointer;
  border: 2px solid transparent;
  border-radius: 50%;
  transition: all 0.2s;
  
  &:hover {
    transform: scale(1.1);
  }
  
  &.selected {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);
    transform: scale(1.05);
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 