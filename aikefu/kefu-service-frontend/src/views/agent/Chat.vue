<template>
  <div class="chat-container">
    <!-- 移除待处理会话提醒组件，由App.vue全局处理 -->
    
    <!-- 会话列表面板 -->
    <SessionListPanel
      :sessions="sessionList"
      :current-session="currentSession"
      :search-keyword="searchKeyword"
      :loading="loading"
      :is-desktop="isDesktop"
      :visible="showSessionPanel"
      @select-session="handleSelectSession"
      @update:search-keyword="handleSearchKeywordUpdate"
      @refresh-sessions="loadSessionList"
      @close="handleSessionPanelClose"
      @view-history="viewHistory"
    />
    
    <!-- 聊天界面 -->
    <div class="chat-main">
      <!-- 移动端显示会话列表按钮 -->
      <div class="mobile-menu-btn" @click="toggleSessionPanel" v-if="!isDesktop">
        <el-icon><Menu /></el-icon>
      </div>
      
      <!-- 移动端显示用户信息按钮 -->
      <div class="mobile-user-btn" @click="toggleUserPanel" v-if="!isDesktop && currentSession">
        <el-icon><User /></el-icon>
      </div>
      
      <div v-if="!currentSession" class="no-session-selected">
        <el-empty description="请选择或接入一个会话">
          <el-button type="primary" @click="sessionTabActive = 'waiting'" round>查看待处理会话</el-button>
        </el-empty>
      </div>
      
      <template v-else>
        <!-- 聊天头部信息 -->
        <ChatHeaderToolbar
          :current-session="currentSession"
          :loading="loading"
          :user-detail="userDetail"
          :messages="messages"
          @accept-session="acceptSession"
          @refresh-messages="refreshCurrentMessages"
          @end-chat="handleEndChat"
          @transfer-session="transferSession"
          @view-history="viewHistory"
        />
        
        <!-- 解决方案组件 -->
        <solution-description
          v-if="showSolutionForm"
          :sessionId="currentSession?.id"
          @success="handleSolutionSuccess"
          @cancel="showSolutionForm = false"
        />
        
        <!-- 消息列表区域 -->
        <div class="message-container" ref="messageContainer">
          <div v-if="loading" class="loading-messages">
            <el-skeleton :rows="3" animated />
          </div>
          
          <div v-else-if="messages.length === 0" class="empty-messages">
            <el-empty description="暂无消息记录" />
          </div>
          
          <div v-else class="message-list">
            <!-- 加载更多按钮 -->
            <div v-if="hasMoreMessages" class="load-more">
              <el-button 
                type="primary" 
                link 
                size="small" 
                :loading="loadingMore"
                @click="loadMoreMessages"
              >
                加载更多
              </el-button>
            </div>
            
            <!-- 日期分割线 -->
            <div 
              v-for="(date, index) in messageDates" 
              :key="date"
              class="message-date-divider"
            >
              <div class="date-label">
                <span>{{ formatMessageDate(date) }}</span>
              </div>
              
              <!-- 消息项 -->
              <div 
                v-for="message in getMessagesByDate(date)" 
                :key="message.id"
                class="message-item"
                :class="{ 
                  'message-agent': (message.from === 1 || message.senderType === 1) && message.senderType !== 2 && message.currentAgentType !== 2,
                  'message-user': message.from === 0 || message.senderType === 0,
                  'message-system': message.type === 3 || message.senderType === 3,
                  'message-ai': message.senderType === 2 || (message.from === 1 && message.currentAgentType === 2)
                }"
              >
                <!-- 用户头像 -->
                <template v-if="message.from === 0 || message.senderType === 0">
                  <div class="message-avatar">
                    <el-avatar :size="40" :src="currentSession.userAvatar || userAvatarDefault" shape="circle">
                      {{ currentSession.userNickname ? currentSession.userNickname.substring(0, 1) : '访' }}
                    </el-avatar>
                  </div>
                </template>
                
                <!-- AI客服头像 -->
                <template v-else-if="message.senderType === 2 || (message.from === 1 && message.currentAgentType === 2)">
                  <div class="message-avatar">
                    <el-avatar :size="40" :src="aiAvatarDefault" shape="circle">
                      AI
                    </el-avatar>
                  </div>
                </template>
                
                <!-- 客服头像 -->
                <template v-else-if="message.from === 1 || message.senderType === 1">
                  <div class="message-avatar">
                    <el-avatar :size="40" :src="agentInfo?.avatar || agentAvatarDefault" shape="circle">
                      {{ agentInfo?.name ? agentInfo.name.substring(0, 1) : '客' }}
                    </el-avatar>
                  </div>
                </template>
                
                <!-- 消息内容 -->
                <div class="message-content">
                  <!-- 发送者名称和时间 -->
                  <div class="message-sender">
                    <!-- 用户消息只显示时间，不显示发送者名称 -->
                    <span class="sender-name" v-if="!(message.from === 0 || message.senderType === 0)">
                      {{ message.senderName || '' }}
                    </span>
                    <span class="message-time">{{ formatTime(message.createdAt) }}</span>
                  </div>
                  
                  <!-- 消息正文 -->
                  <div 
                    class="message-body"
                    :class="{ 'status-sending': message.status === 0 }"
                  >
                    <!-- 文本消息 -->
                    <div v-if="message.type === 1 || message.msgType === 0" class="message-text">
                      {{ message.content }}
                    </div>
                    
                    <!-- 图片消息 -->
                    <div v-else-if="(message.type === 2 && message.msgType === 1) || (message.type === 2 && !message.msgType)" class="message-image">
                      <el-image 
                        :src="getImageUrl(message.content)" 
                        :preview-src-list="[getImageUrl(message.content)]"
                        fit="cover"
                        :z-index="3000"
                        loading="lazy"
                        @error="() => handleImageError(message)"
                        @load="() => handleImageLoad(message)"
                      >
                        <template #error>
                          <div class="image-error">
                            <el-icon><Picture /></el-icon>
                            <span>图片加载失败</span>
                          </div>
                        </template>
                        <template #placeholder>
                          <div class="image-loading">
                            <el-icon class="is-loading"><Loading /></el-icon>
                            <span>加载中...</span>
                          </div>
                        </template>
                      </el-image>
                    </div>
                    
                    <!-- 视频消息 -->
                    <div v-else-if="message.type === 2 && message.msgType === 2" class="message-video">
                      <video 
                        :src="getImageUrl(message.content)" 
                        controls
                        preload="metadata"
                        style="max-width: 300px; max-height: 200px; border-radius: 8px; background: #f5f5f5;"
                        @error="() => handleVideoError(message)"
                        @loadedmetadata="() => handleVideoLoad(message)"
                      >
                        您的浏览器不支持视频播放
                      </video>
                    </div>
                    
                    <!-- 系统消息 -->
                    <div v-else-if="message.type === 3 || message.senderType === 3" class="message-text system-message">
                      <span class="system-icon"><el-icon><InfoFilled /></el-icon></span>
                      <span>{{ message.content }}</span>
                    </div>
                    
                    <!-- 发送状态指示器 -->
                    <div v-if="message.from === 1 || message.senderType === 1" class="message-status">
                      <el-icon v-if="message.status === 0"><Loading /></el-icon>
                      <el-icon v-else-if="message.status === 1"><CircleCheck /></el-icon>
                      <el-icon v-else-if="message.status === 2"><View /></el-icon>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 消息输入组件 -->
        <MessageInputPanel
          ref="messageInputRef"
          v-if="currentSession?.status === 1"
          :current-session="currentSession"
          :quick-replies="quickReplies"
          :disabled="loading"
          :loading="sending"
          @send-message="handleSendMessage"
          @insert-reply="handleInsertToReply"
          @paste-image="handlePasteImage"
          @update-quick-replies="handleUpdateQuickReplies"
        />
        
        <!-- 未开始对话提示 -->
        <div v-else-if="currentSession?.status === 0" class="session-ended">
          <el-alert
            title="未开始对话"
            type="info"
            :closable="false"
            center
            show-icon
          >
            <template #default>
              未开始对话，无法发送新消息
            </template>
          </el-alert>
        </div>
        
        <!-- 等待接入提示 -->
        <div v-else-if="currentSession?.status === 2" class="session-waiting">
          <el-alert
            title="等待接入"
            type="warning"
            :closable="false"
            center
            show-icon
          >
            <template #default>
              请先接入会话才能发送消息
              <el-button type="primary" size="small" @click="acceptSession(currentSession)" round>
                接入会话
              </el-button>
            </template>
          </el-alert>
        </div>
      </template>
    </div>
    
    <!-- 用户信息面板组件 -->
    <UserInfoPanel 
      :current-session="currentSession"
      :visible="showUserPanel"
      :is-mobile="!isDesktop"
      @close="handleUserPanelClose"
      @insert-to-reply="handleInsertToReply"
    />
    
    <!-- 历史记录对话框组件 -->
    <HistoryDialog 
      v-model:visible="showHistoryDialog"
      :session-id="historySessionId"
    />

      <!-- 移动端关闭按钮 -->
      <div class="mobile-close-btn" @click="toggleUserPanel" v-if="!isDesktop">
        <el-icon><Close /></el-icon>
      </div>
  </div>
</template>

<script setup>
// 在script setup最上方添加新的引入
import { ref, reactive, computed, onMounted, watch, nextTick, onUnmounted, onBeforeMount, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/store/user'
import { useChatStore } from '@/store/chat'
import { 
  Search, ArrowRight, Picture, Loading, CircleCheck, View, ChatDotSquare, Delete,
  Check, Refresh, CircleClose, Connection, Menu, User, Close, Position, Document, Phone,
  Plus, QuestionFilled, Edit, Folder, InfoFilled
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import moment from 'moment'
import { 
  getSessionsByAgentId, 
  getMessagesBySessionId, 
  acceptChatSession, 
  closeSession, 
  updateReadStatus, 
  getSessionById, 
  assignAgentManually,
  getLatestMessage 
} from '@/api/chat'
import { getUserInfo } from '@/api/user'
import { getOnlineAgents } from '@/api/agent'
import { getAllFaqs, addFaq, updateFaq, deleteFaq } from '@/api/faq'
import SolutionDescription from './SolutionDescription.vue'
import UserInfoPanel from './components/UserInfoPanel.vue'
import HistoryDialog from './components/HistoryDialog.vue'
import SessionListPanel from './components/SessionListPanel.vue'
import ChatHeaderToolbar from './components/ChatHeaderToolbar.vue'
import MessageInputPanel from './components/MessageInputPanel.vue'

// 导入默认头像图片
import userAvatarDefault from '@/assets/images/userAvatar.png'
import aiAvatarDefault from '@/assets/images/ai-avatar.png'
import agentAvatarDefault from '@/assets/images/agentAvatar.png'

// 添加响应式设计相关状态
const isDesktop = ref(true)
const showSessionPanel = ref(false)
const showUserPanel = ref(false)

// 判断当前是否为桌面设备
const checkDeviceType = () => {
  isDesktop.value = window.innerWidth >= 768
  if (isDesktop.value) {
    showSessionPanel.value = true
    showUserPanel.value = true 
  } else {
    showSessionPanel.value = false
    showUserPanel.value = false
  }
}

// 在组件挂载前检查设备类型
onBeforeMount(() => {
  checkDeviceType()
})

// 监听窗口大小变化
onMounted(() => {
  window.addEventListener('resize', checkDeviceType)
})

// 组件卸载时移除监听
onUnmounted(() => {
  window.removeEventListener('resize', checkDeviceType)
})

// 加载常见问题列表
const loadFaqList = async () => {
  faqLoading.value = true
  try {
    // 调用后端接口获取所有FAQ
    const response = await getAllFaqs()
    
    if (response.code === 200) {
      const allFaqs = response.data || []
      
      // 进行前端筛选
      let filteredFaqs = allFaqs
      
      // 分类筛选
      if (faqQueryParams.category) {
        filteredFaqs = filteredFaqs.filter(faq => faq.category === faqQueryParams.category)
      }
      
      // 关键词筛选
      if (faqQueryParams.keyword) {
        const keyword = faqQueryParams.keyword.toLowerCase()
        filteredFaqs = filteredFaqs.filter(faq => 
          (faq.question && faq.question.toLowerCase().includes(keyword)) || 
          (faq.answer && faq.answer.toLowerCase().includes(keyword))
        )
      }
      
      // 记录总数
      faqTotal.value = filteredFaqs.length
      
      // 按排序值排序
      filteredFaqs.sort((a, b) => (a.sort || 0) - (b.sort || 0))
      
      // 分页处理
      const start = (faqQueryParams.pageNum - 1) * faqQueryParams.pageSize
      const end = start + faqQueryParams.pageSize
      faqList.value = filteredFaqs.slice(start, end)
    } else {
      console.error('加载常见问题失败:', response.message)
    }
  } catch (error) {
    console.error('加载FAQ列表失败', error)
  } finally {
    faqLoading.value = false
  }
}

// 处理分类变化
const handleFaqCategoryChange = (category) => {
  faqQueryParams.category = category
  faqQueryParams.pageNum = 1
  loadFaqList()
}

// 处理关键词搜索
const handleFaqSearch = () => {
  faqQueryParams.pageNum = 1
  loadFaqList()
}

// 重置FAQ查询条件
const resetFaqQuery = () => {
  faqQueryParams.keyword = ''
  faqQueryParams.pageNum = 1
  loadFaqList()
}

// 处理分页变化
const handleFaqCurrentChange = (page) => {
  faqQueryParams.pageNum = page
  loadFaqList()
}

// 新增FAQ
const addFAQ = () => {
  resetFaqForm()
  faqDialogVisible.value = true
}

// 编辑FAQ
const editFAQ = (row) => {
  resetFaqForm()
  Object.assign(faqForm, row)
  faqDialogVisible.value = true
}

// 重置表单
const resetFaqForm = () => {
  faqForm.id = ''
  faqForm.category = activeFaqCategory.value || ''
  faqForm.question = ''
  faqForm.answer = ''
  faqForm.sort = 0
}

// 提交表单
const submitFaqForm = async () => {
  if (!faqFormRef.value) return
  
  try {
    await faqFormRef.value.validate()
    
    if (faqForm.id) {
      // 编辑
      const response = await updateFaq(faqForm.id, faqForm)
      
      if (response.code === 200) {
        ElMessage.success('修改成功')
        // 更新列表
        loadFaqList()
      } else {
        ElMessage.error(response.message || '修改失败')
      }
    } else {
      // 新增
      const response = await addFaq(faqForm)
      
      if (response.code === 200) {
        ElMessage.success('新增成功')
        // 重新加载列表
        loadFaqList()
      } else {
        ElMessage.error(response.message || '新增失败')
      }
    }
    
    faqDialogVisible.value = false
  } catch (error) {
    console.error('表单验证或提交失败', error)
    ElMessage.error('提交失败：' + error.message || '未知错误')
  }
}

// 插入到回复
const insertFaqToReply = (row) => {
  if (row && row.answer) {
    messageText.value = row.answer
    ElMessage.success(`已插入问题"${row.question}"的回答`)
  }
}

// 获取分类标签类型
const getFaqCategoryTag = (category) => {
  switch (category) {
    case '订单相关': return ''
    case '商品相关': return 'success'
    case '配送相关': return 'warning'
    case '支付相关': return 'danger'
    case '账户相关': return 'info'
    case '其他问题': return ''
    default: return ''
  }
}

// 切换会话列表显示
const toggleSessionPanel = () => {
  if (!isDesktop.value) {
    showSessionPanel.value = !showSessionPanel.value
    if (showSessionPanel.value) {
      showUserPanel.value = false // 如果显示会话列表，则隐藏用户面板
    }
  }
}

// 切换用户信息面板
const toggleUserPanel = () => {
  if (isDesktop.value) {
    showUserPanel.value = !showUserPanel.value
  } else {
    showUserPanel.value = !showUserPanel.value
    if (showUserPanel.value) {
      showSessionPanel.value = false // 如果显示用户面板，则隐藏会话列表
    }
  }
}

// 路由
const route = useRoute()
const router = useRouter()

// Store
const userStore = useUserStore()
const chatStore = useChatStore()

// 自动刷新定时器
const autoRefreshTimer = ref(null)

// 会话列表筛选
const filterType = ref('waiting')
const searchKeyword = ref('')
const sessionTabActive = ref('waiting')

// 会话列表数据
const sessionList = ref([])

// 客服信息
const agentInfo = computed(() => userStore.userInfo)

// 用户详细信息
const userDetail = ref(null)
const userOrderDetail = ref(null)
// 不同类型的订单数据
const recycleOrders = ref(null)
const currentRecycleOrder = ref(null)
const fleaMarketOrders = ref(null)
const currentFleaMarketOrder = ref(null)
const marketOrders = ref(null)
const currentMarketOrder = ref(null)
const engineerOrders = ref(null)
const currentEngineerOrder = ref(null)
const loadingUserInfo = ref(false)

// 历史记录对话框相关
const showHistoryDialog = ref(false)
const historySessionId = ref(null)
const historyMessages = ref([])
const loadingHistory = ref(false)
const historyHasMore = ref(false)
const historyCurrentPage = ref(1)
const historyPageSize = ref(20)

// 消息相关
const messages = ref([])
const loading = ref(false)
const loadingMore = ref(false)
const hasMoreMessages = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const messageContainer = ref(null)

// 消息输入
const messageText = ref('')
const selectedImage = ref(null)
const selectedImageUrl = ref('')
const sending = ref(false)

// MessageInputPanel组件引用
const messageInputRef = ref(null)

// 默认的快捷回复数组
const defaultQuickReplies = [
  '您好，很高兴为您服务',
  '请问还有其他问题吗？',
  '感谢您的咨询，祝您生活愉快',
  '请稍等，我查询一下信息',
  '已为您处理，请核实'
]

// 快捷回复 - 改为响应式
const quickReplies = ref([...defaultQuickReplies])

// 从localStorage加载保存的快捷回复
const loadQuickRepliesFromStorage = () => {
  try {
    const saved = localStorage.getItem('agent_quick_replies')
    if (saved) {
      const parsedReplies = JSON.parse(saved)
      if (Array.isArray(parsedReplies) && parsedReplies.length > 0) {
        quickReplies.value = parsedReplies
      }
    }
  } catch (error) {
    console.error('加载快捷回复失败:', error)
  }
}

// 保存快捷回复到localStorage
const saveQuickRepliesToStorage = (replies) => {
  try {
    localStorage.setItem('agent_quick_replies', JSON.stringify(replies))
  } catch (error) {
    console.error('保存快捷回复失败:', error)
  }
}

// 处理快捷回复更新
const handleUpdateQuickReplies = (newReplies) => {
  quickReplies.value = newReplies
  saveQuickRepliesToStorage(newReplies)
}

// 常见问题
const commonQuestions = [
  '如何修改订单信息？',
  '退款流程是怎样的？',
  '商品什么时候能发货？',
  '如何修改收货地址？',
  '如何查询物流信息？',
  '忘记密码怎么办？',
  '如何绑定手机号？',
  '如何使用优惠券？'
]

// 日期分组的消息
const messageDates = computed(() => {
  const dates = new Set()
  messages.value.forEach(message => {
    if (message.createdAt) {
      const date = new Date(message.createdAt)
      dates.add(date.toDateString())
    }
  })
  return Array.from(dates)
})

// 按不同条件筛选的会话列表
const waitingSessions = computed(() => {
  // 修改筛选条件: status === 0 表示排队中的会话
  let result = sessionList.value.filter(session => session.status === 0)
  
  // 处理每个会话的waitingTime属性
  result = result.map(session => {
    // 如果waitingTime未定义或无效，根据开始时间计算
    if (!session.waitingTime || isNaN(Number(session.waitingTime))) {
      const startTime = new Date(session.lastActiveTime || session.startTime || session.createTime ||  Date.now())
      const waitingTimeSeconds = Math.floor((Date.now() - startTime.getTime()) / 1000)
      return {
        ...session,
        waitingTime: waitingTimeSeconds > 0 ? waitingTimeSeconds : 0
      }
    }
    return session
  })
  
  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(session => 
      (session.userNickname && session.userNickname.toLowerCase().includes(keyword)) ||
      (session.lastMessage && session.lastMessage.toLowerCase().includes(keyword)) ||
      session.userId.toString().includes(keyword)
    )
  }
  
  // 按最后活跃时间排序（最近活跃的在前）
  return result.sort((a, b) => {
    return new Date(b.lastActiveTime || 0) - new Date(a.lastActiveTime || 0)
  })
})

const activeSessions = computed(() => {
  // 只显示人工会话中的会话(status === 1)
  let result = sessionList.value.filter(session => session.status === 1)
  
  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(session => 
      (session.userNickname && session.userNickname.toLowerCase().includes(keyword)) ||
      (session.lastMessage && session.lastMessage.toLowerCase().includes(keyword)) ||
      session.userId.toString().includes(keyword)
    )
  }
  
  // 按最后活跃时间排序（最近活跃的在前）
  return result.sort((a, b) => {
    return new Date(b.lastActiveTime) - new Date(a.lastActiveTime)
  })
})

// 在 script setup 部分添加 aiSessions 计算属性
const aiSessions = computed(() => {
  // 只显示AI会话中的会话(status === 3)
  let result = sessionList.value.filter(session => session.status === 3)
  
  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(session => 
      (session.userNickname && session.userNickname.toLowerCase().includes(keyword)) ||
      (session.lastMessage && session.lastMessage.toLowerCase().includes(keyword)) ||
      session.userId.toString().includes(keyword)
    )
  }
  
  // 按最后活跃时间排序（最近活跃的在前）
  return result.sort((a, b) => {
    return new Date(b.lastActiveTime) - new Date(a.lastActiveTime)
  })
})

// 当前选中的会话
const currentSession = ref(null)

// 解决方案相关状态
const showSolutionForm = ref(false)

// 生命周期钩子
onMounted(async () => {
  // 从localStorage恢复状态
  userStore.loadFromStorage()
  
  // 加载保存的快捷回复
  loadQuickRepliesFromStorage()
  
  // 如果没有登录或不是客服角色，跳转到登录页
  if (!userStore.isLoggedIn || !userStore.isAgent) {
    router.push('/login')
    return
  }
  
  // 如果客服不在线，提示并询问是否切换为在线状态
  if (userStore.userInfo?.status !== 1) {
    ElMessageBox.confirm(
      '您当前处于离线状态，需要切换为在线状态才能处理会话，是否切换？',
      '提示',
      {
        confirmButtonText: '切换为在线',
        cancelButtonText: '保持离线',
        type: 'warning',
      }
    ).then(() => {
      // 切换为在线状态
      toggleAgentStatus(1)
    }).catch(() => {
      // 保持离线状态
      ElMessage.info('您选择了保持离线状态，将无法接入新会话')
    })
  }
  
  // 设置当前在聊天页面
  chatStore.setInChatPage(true)
  
  // 初始化WebSocket连接（确保显式调用）
  if (!chatStore.connected) {
    chatStore.initWebSocket()
  }
  
  // 加载会话列表
  await loadSessionList()
  
  // 设置轮询，定期更新会话列表
  sessionUpdateInterval = setInterval(async () => {
    if (chatStore.isInChatPage) {
      await loadSessionList(false) // 静默更新，不显示加载状态
    }
  }, 10000) // 每10秒更新一次
  
  // 监听WebSocket消息事件，收到新消息时刷新会话列表和当前会话消息
  window.addEventListener('chat-message-received', handleNewMessage)
  
  // 监听转接通知
  window.addEventListener('chat-transfer-notification', handleTransferNotification)
  
  // 移除对请求人工客服通知的监听，由App.vue统一处理
  // window.addEventListener('agent-request-notification', handleAgentRequestNotification)
  
  // 如果URL中有会话ID，则选中对应会话
  if (route.query.id) {
    const sessionId = route.query.id
    // 查找会话
    const session = sessionList.value.find(s => s.id === sessionId)
    if (session) {
      // 选中会话
      handleSelectSession(session)
      // 切换到对应的标签页
      sessionTabActive.value = session.status === 1 ? 'active' : 'waiting'
    } else {
      // 如果会话不在列表中，尝试获取会话信息
      try {
        const res = await fetchSessionByIdLocal(sessionId)
        if (res.code === 200 && res.data) {
          sessionList.value.push(res.data)
          handleSelectSession(res.data)
          sessionTabActive.value = res.data.status === 1 ? 'active' : 'waiting'
        } else {
          // 会话不存在，显示提示并清除URL参数
          ElMessage.warning(res.message || '该会话不存在或已结束')
          // 清除URL中的会话ID参数
          router.replace({
            path: '/agent/chat',
            query: {}
          })
        }
      } catch (error) {
        console.error('加载指定会话失败', error)
        ElMessage.error('加载指定会话失败')
        // 清除URL中的会话ID参数
        router.replace({
          path: '/agent/chat',
          query: {}
        })
      }
    }
  }
  
  // 检查当前连接状态
  checkConnectionStatus()
  
  // 设置定期检查连接状态的定时器
  connectionStatusTimer = setInterval(() => {
    checkConnectionStatus()
  }, 30000) // 每30秒检查一次
  
  // 加载常见问题列表
  loadFaqList()
})

onUnmounted(() => {
  // 确保在组件卸载时清理相关资源和状态
  console.log('聊天组件卸载，清理资源')
  
  // 清除定时器
  if (sessionUpdateInterval) {
    clearInterval(sessionUpdateInterval)
    sessionUpdateInterval = null
  }
  
  if (refreshMessageInterval) {
    clearInterval(refreshMessageInterval)
    refreshMessageInterval = null
  }
  
  // 移除事件监听
  window.removeEventListener('chat-message-received', handleNewMessage)
  window.removeEventListener('chat-transfer-notification', handleTransferNotification)
  // 已经不再监听agent-request-notification事件
  // window.removeEventListener('agent-request-notification', handleAgentRequestNotification)
  
  // 设置当前不在聊天页面
  chatStore.setInChatPage(false)
  
  // 新增：清除连接状态检查定时器
  if (connectionStatusTimer) {
    clearInterval(connectionStatusTimer)
    connectionStatusTimer = null
  }
})

// 定义定时器变量
let sessionUpdateInterval = null
let refreshMessageInterval = null

// 在setup中添加sessionListUpdateDebounce变量
let sessionListUpdateDebounce = null // 会话列表更新防抖计时器

// 处理新的待处理会话
const checkNewWaitingSessions = (sessions) => {
  // 获取待处理的会话
  const pendingSessions = sessions.filter(session => session.status === 0)
  
  if (pendingSessions.length > 0) {
    // 确保每个会话有正确的waitingTime属性
    const processedPendingSessions = pendingSessions.map(session => {
      // 如果没有waitingTime或waitingTime无效，计算等待时间（当前时间 - 开始时间）
      if (!session.waitingTime || isNaN(Number(session.waitingTime))) {
        const startTime = new Date(session.startTime || session.createTime || session.lastActiveTime || Date.now())
        const waitingTimeSeconds = Math.floor((Date.now() - startTime.getTime()) / 1000)
        return {
          ...session,
          waitingTime: waitingTimeSeconds > 0 ? waitingTimeSeconds : 0
        }
      }
      return session
    })
    
    // 获取当前所有待处理会话的ID
    const currentPendingIds = pendingSessions.map(session => session.id)
    
    // 检查是否有新的待处理会话（与上次已知的待处理会话比较）
    const hasNewPendingSessions = currentPendingIds.some(id => !lastKnownPendingSessionIds.value.includes(id))
    
    if (hasNewPendingSessions) {
      // 有新的待处理会话，但不再显示本地提醒，使用全局提醒
      // pendingSessionsForAlert.value = processedPendingSessions
      // showWaitingAlert.value = true
      
      // 可以考虑调用全局函数显示提醒
      if (window.showAgentRequestAlert && processedPendingSessions.length > 0) {
        const firstSession = processedPendingSessions[0]
        window.showAgentRequestAlert({
          sessionId: firstSession.id,
          data: {
            userId: firstSession.userId
          },
          content: firstSession.lastMessage || '新的待处理会话'
        })
      }
    }

  }
}



// 处理新消息的函数
const handleNewMessage = async (event) => {
  console.log('Chat.vue检测到新消息，消息详情:', event.detail)
  
  // 检查是否为新消息
  const isNewMessage = event.detail.isNewMessage !== false
  if (!isNewMessage) {
    console.log('非新消息，不处理')
    return
  }
  
  // 消息ID，会话ID和发送者类型
  const messageId = event.detail.id
  const sessionId = Number(event.detail.sessionId)
  const senderType = Number(event.detail.from)
  
  // 如果消息属于当前会话，直接添加到当前会话的消息列表中
  if (currentSession.value && Number(currentSession.value.id) === sessionId) {
    console.log('新消息属于当前会话，添加到消息列表')
    
    // 改进的消息重复检查逻辑
    let messageExists = false
    
    // 对于客服发送的消息（senderType === 1），检查是否有对应的临时消息需要更新
    if (senderType === 1) {
      // 查找可能的临时消息（以temp_开头的ID）
      const tempMessageIndex = messages.value.findIndex(msg => 
        msg.id && msg.id.toString().startsWith('temp_') &&
        msg.content === event.detail.content &&
        msg.from === senderType &&
        Math.abs(new Date(msg.createdAt || Date.now()).getTime() - Date.now()) < 10000 // 10秒内
      )
      
      if (tempMessageIndex !== -1) {
        console.log('找到对应的临时消息，更新消息ID和状态:', tempMessageIndex)
        // 更新临时消息为真实消息
        messages.value[tempMessageIndex].id = messageId
        messages.value[tempMessageIndex].status = 1 // 已发送
        messages.value[tempMessageIndex].createdAt = event.detail.createdAt || new Date().toISOString()
        
        // 标记为已存在，避免重复添加
        messageExists = true
        
        // 设置延时将状态改为已读
        setTimeout(() => {
          if (messages.value[tempMessageIndex]) {
            messages.value[tempMessageIndex].status = 2 // 已读
          }
        }, 3000)
      } else {
        // 检查是否已经存在相同ID的消息
        messageExists = messages.value.some(msg => msg.id === messageId)
      }
    } else {
      // 对于用户消息，使用原有的检查逻辑
      messageExists = messages.value.some(msg => 
        msg.id === messageId || 
        (msg.content === event.detail.content && 
         msg.from === senderType && 
         Math.abs(new Date(msg.createdAt || Date.now()).getTime() - Date.now()) < 5000)
      )
    }
    
    if (!messageExists) {
      console.log('消息不存在，添加新消息到列表:', event.detail)
      
      // 创建新消息对象（确保类型一致）
      const newMessage = {
        id: messageId || `received_${Date.now()}`,
        sessionId: sessionId,
        from: senderType,
        senderType: senderType,
        senderId: senderType === 0 ? currentSession.value.userId : agentInfo.value?.id,
        receiverId: senderType === 0 ? agentInfo.value?.id : currentSession.value.userId,
        content: event.detail.content,
        type: event.detail.type === 2 || event.detail.msgType === 1 ? 2 : 1,
        msgType: event.detail.type === 2 || event.detail.msgType === 1 ? 1 : 0,
        createdAt: event.detail.createdAt || new Date().toISOString(),
        status: 1,
        _standardized: true
      }
      
      // 添加到消息列表
      messages.value.push(newMessage)
      
      // 更新最后一条消息内容
      if (newMessage.type === 2 || newMessage.msgType === 1) {
        currentSession.value.lastMessage = '[图片]'
      } else {
        currentSession.value.lastMessage = newMessage.content
      }
      currentSession.value.lastActiveTime = newMessage.createdAt
      
      // 滚动到底部
      await nextTick()
      scrollToBottom()
      
      // 如果是用户发送的消息，更新已读状态
      if (senderType === 0) {
        try {
          console.log('更新用户消息已读状态')
          await updateReadStatus(currentSession.value.id, 0)
        } catch (error) {
          console.error('更新消息已读状态失败', error)
        }
      }
    } else {
      console.log('消息已存在或已更新临时消息，不重复添加')
    }
  } else {
    console.log('新消息不属于当前会话或当前无选中会话')
    
    // 延迟刷新会话列表，避免频繁刷新
    if (!sessionListUpdateDebounce) {
      sessionListUpdateDebounce = setTimeout(async () => {
        await loadSessionList(false)
        sessionListUpdateDebounce = null
      }, 1000)
    }
  }
}

// 处理转接通知
const handleTransferNotification = (event) => {
  console.log('收到转接通知:', event.detail)
  
  // 播放通知提示音
  playNotificationSound()
  
  // 立即刷新会话列表，确保能看到最新状态
  loadSessionList(false)
}

// 处理请求人工客服通知
const handleAgentRequestNotification = (event) => {
  console.log('收到请求人工客服通知:', event.detail)
  
  // 获取会话信息
  const sessionId = event.detail.sessionId
  const userId = event.detail.data?.userId
  const content = event.detail.content
  
  console.log('待处理会话信息:', { sessionId, userId, content })
  
  // 创建待处理会话对象
  const pendingSession = {
    id: sessionId,
    userId: userId,
    userNickname: `用户${userId}`,
    lastMessage: content,
    waitingTime: 0, // 初始等待时间为0
    status: 0 // 待处理状态
  }
  
  console.log('创建的待处理会话对象:', pendingSession)
  
  // 设置待处理会话提醒
  pendingSessionsForAlert.value = [pendingSession]
  
  // 强制显示弹框
  console.log('尝试显示待处理会话弹框，之前状态:', showWaitingAlert.value)
  showWaitingAlert.value = true
  console.log('设置弹框显示状态为:', showWaitingAlert.value)
  
  // 使用全局函数尝试显示弹窗
  if (window.manualShowWaitingAlert) {
    console.log('调用全局manualShowWaitingAlert函数')
    window.manualShowWaitingAlert(pendingSession)
  }
  
  // 使用定时器确保状态更新
  setTimeout(() => {
    if (!showWaitingAlert.value) {
      console.log('通过setTimeout再次尝试显示弹框')
      showWaitingAlert.value = true
      
      // 再次尝试使用全局函数
      if (window.manualShowWaitingAlert) {
        window.manualShowWaitingAlert(pendingSession)
      }
    }
  }, 100)
  
  // 显示消息通知
  ElNotification({
    title: '新的待处理会话',
    message: `有新的待处理会话: ${content}`,
    type: 'warning',
    duration: 0,
    position: 'top-right'
  })
  
  // 播放通知提示音
  playNotificationSound()
  
  // 立即刷新会话列表
  loadSessionList(false)
}

// 播放通知提示音
const playNotificationSound = () => {
  try {
    // 创建音频元素 - 使用相对路径
    const audio = new Audio(import.meta.env.BASE_URL + 'audio/notification.mp3')
    audio.volume = 0.6
    audio.play().catch(e => {
      console.warn('播放通知音效失败:', e)
      // 尝试备用路径
      const backupAudio = new Audio('/audio/notification.mp3')
      backupAudio.volume = 0.6
      backupAudio.play().catch(err => console.error('备用音频播放失败:', err))
    })
  } catch (error) {
    console.error('播放提示音失败:', error)
  }
}

// 加载会话列表
const loadSessionList = async (showLoading = true) => {
  try {
    if (showLoading) {
      loading.value = true
    }
    
    // 调用API获取会话列表
    const res = await getSessionsByAgentId(userStore.userInfo.id)
    if (res.code === 200) {
      // 保存当前会话列表中的最后消息内容
      const currentSessions = new Map()
      sessionList.value.forEach(session => {
        if (session.lastMessage) {
          currentSessions.set(session.id, {
            lastMessage: session.lastMessage,
            lastActiveTime: session.lastActiveTime
          })
        }
      })
      
      // 从新的返回结构中获取三种状态的会话
      const { pendingSessions, inProgressSessions, aiReplySessions } = res.data
      
      // 合并所有会话
      let newSessions = []
      
      // 添加待处理会话
      if (pendingSessions && pendingSessions.records) {
        newSessions = newSessions.concat(pendingSessions.records)
      }
      
      // 添加进行中会话
      if (inProgressSessions && inProgressSessions.records) {
        newSessions = newSessions.concat(inProgressSessions.records)
      }
      
      // 添加AI会话
      if (aiReplySessions && aiReplySessions.records) {
        newSessions = newSessions.concat(aiReplySessions.records)
      }
      
      // 对于每个没有最后消息的会话，获取其最后一条消息
      const sessionsToUpdate = newSessions.filter(session => 
        !session.lastMessage || session.lastMessage === '暂无消息'
      )
      
      if (sessionsToUpdate.length > 0) {
        try {
          // 并行获取所有需要更新的会话的最后消息
          const messagePromises = sessionsToUpdate.map(async (session) => {
            try {
              // 获取会话的最后一条消息（改为getLatestMessage）
              const msgRes = await getLatestMessage(session.id)
              if (msgRes.code === 200 && msgRes.data) {
                const lastMsg = msgRes.data
                return {
                  sessionId: session.id,
                  lastMessage: lastMsg.msgType === 1 ? '[图片]' : lastMsg.content,
                  lastActiveTime: lastMsg.createdAt
                }
              }
            } catch (error) {
              console.error(`获取会话 ${session.id} 的最后消息失败:`, error)
            }
            return null
          })
          
          // 等待所有消息获取完成
          const lastMessages = await Promise.all(messagePromises)
          
          // 更新会话的最后消息
          newSessions = newSessions.map(session => {
            const lastMsg = lastMessages.find(msg => msg && msg.sessionId === session.id)
            if (lastMsg) {
              return {
                ...session,
                lastMessage: lastMsg.lastMessage,
                lastActiveTime: lastMsg.lastActiveTime
              }
            }
            return session
          })
        } catch (error) {
          console.error('获取最后消息失败:', error)
        }
      }
      
      // 确保新会话列表保留最后消息内容
      newSessions = newSessions.map(session => {
        const currentSession = currentSessions.get(session.id)
        if (currentSession && (!session.lastMessage || session.lastMessage === '暂无消息')) {
          return {
            ...session,
            lastMessage: currentSession.lastMessage,
            lastActiveTime: currentSession.lastActiveTime
          }
        }
        return session
      })
      
      // 更新会话列表
      sessionList.value = newSessions
      
      // 检查是否有新的待处理会话需要提醒
      checkNewWaitingSessions(newSessions)
      
      // 如果已经有会话列表，保留当前选中的会话，并更新其他会话
      if (currentSession.value) {
        const currentSessionId = currentSession.value.id
        const updatedCurrentSession = sessionList.value.find(s => s.id === currentSessionId)
        if (updatedCurrentSession) {
          // 确保保留当前会话的最后消息
          if (currentSession.value.lastMessage && (!updatedCurrentSession.lastMessage || updatedCurrentSession.lastMessage === '暂无消息')) {
            updatedCurrentSession.lastMessage = currentSession.value.lastMessage
            updatedCurrentSession.lastActiveTime = currentSession.value.lastActiveTime
          }
          currentSession.value = updatedCurrentSession
        }
      }
    } else {
      //ElMessage.error(res.message || '加载会话列表失败')
    }
  } catch (error) {
    console.error('加载会话列表失败', error)
    if (showLoading) {
      // ElMessage.error('加载会话列表失败')
    }
  } finally {
    if (showLoading) {
      loading.value = false
    }
  }
}

// 获取单个会话信息
const fetchSessionByIdLocal = async (sessionId) => {
  try {
    // 尝试正确调用API获取会话信息
    try {
      const res = await getSessionById(sessionId)
      if (res.code === 200 && res.data) {
        return res
      }
    } catch (apiError) {
      console.error('API调用失败，无法获取会话信息:', apiError)
    }
    
    // API调用失败或会话不存在，直接返回错误
    console.log('会话不存在或无法访问:', sessionId)
    return {
      code: 404,
      message: '会话不存在或已结束'
    }
    
  } catch (error) {
    console.error('获取会话信息失败', error)
    return { code: 500, message: '获取会话信息失败' }
  }
}

// 清除自动刷新定时器
const clearAutoRefreshTimer = () => {
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value)
    autoRefreshTimer.value = null
  }
}

// 启动自动刷新定时器（仅用于AI会话）
const startAutoRefreshTimer = () => {
  clearAutoRefreshTimer() // 先清除可能存在的定时器
  if (currentSession.value?.status === 3) { // 3表示AI会话
    autoRefreshTimer.value = setInterval(async () => {
      await refreshCurrentMessages()
    }, 60000) // 60秒刷新一次
  }
}

// 在组件卸载时清除定时器
onUnmounted(() => {
  clearAutoRefreshTimer()
})

// 处理选择会话
const handleSelectSession = async (session) => {
  // 切换会话时清除之前的定时器
  clearAutoRefreshTimer()

  // 判断是否为相同会话但需要特殊处理的情况
  if (session.id === currentSession.value?.id) {
    // 如果是待处理会话(status === 0)，需要显示接入弹框，不返回
    if (session.status === 0) {
      console.log('相同会话但为待处理状态，需要显示接入弹框')
    } else {
      // 已经选中的非待处理会话，不做任何操作，避免消息显示切换问题
      console.log('已经选中该会话且不是待处理状态，不重新加载消息')
      return
    }
  }
  
  console.log('选择新会话:', session.id, session.userNickname || session.userId)
  
  // 先清空当前消息列表，提供更好的视觉反馈
  messages.value = []
  
  currentSession.value = session
  
  // 更新路由，但不重新加载页面
  router.push({
    path: '/agent/chat',
    query: { id: session.id },
  }, { replace: true })
  
  // 获取用户详细信息
  await fetchUserDetail(session.userId)
  
  // 如果是排队中的会话，显示接入确认对话框
  if (session.status === 0) {
    // 验证会话是否有效，确保有有效的userId或userNickname
    const hasValidUser = session.userId || session.userNickname || (session.user && (session.user.nickname || session.user.phone))
    
    if (hasValidUser) {
      ElMessageBox.confirm(
        `是否接入会话 "${session.userNickname || '访客' + session.userId}"？`,
        '接入会话',
        {
          confirmButtonText: '接入',
          cancelButtonText: '稍后处理',
          type: 'info',
        }
      ).then(() => {
        acceptSession(session)
      }).catch(() => {
        // 用户选择稍后处理，不做操作
      })
    } else {
      console.warn('会话缺少有效的用户信息，不显示接入提示')
      ElMessage.warning('会话信息不完整，请刷新会话列表')
    }
  }
  
  // 获取完整的会话信息，确保获取到scene和datasource字段
  try {
    const sessionRes = await getSessionById(session.id)
    if (sessionRes.code === 200 && sessionRes.data) {
      // 更新当前会话信息，保留原始会话中的部分属性
      const updatedSession = {
        ...session, 
        ...sessionRes.data,
        unreadCount: session.unreadCount // 保留未读计数
      }
      currentSession.value = updatedSession
    }
  } catch (error) {
    console.error('获取完整会话信息失败:', error)
  }
  
  // 加载会话消息
  console.log('准备加载会话消息:', session.id)
  await loadSessionMessages(session.id, true) // 使用强制刷新模式加载
  
  // 如果是AI会话，启动自动刷新定时器
  if (session.status === 3) {
    console.log('启动AI会话自动刷新')
    startAutoRefreshTimer()
  }
  
  // 重置未读消息计数
  if (session.unreadCount > 0) {
    session.unreadCount = 0
    // 调用API更新已读状态
    try {
      await updateReadStatus(session.id, 0)  // 0表示用户发送的消息标为已读
    } catch (error) {
      console.error('更新消息已读状态失败', error)
    }
  }
}

// 添加转换订单状态代码的函数
const getOrderStatusText = (status) => {
  const statusMap = {
    0: '等待确认',
    5: '等待寄回',
    10: '等待签收',
    15: '验机确认',
    20: '等待退回',
    30: '验机确认',
    50: '等待付款',
    60: '等待退回',
    70: '回收取消',
    80: '回收退单',
    90: '订单异常',
    95: '三方完工',
    100: '回收完成'
  }
  return statusMap[status] || `未知(${status})`
}

// 获取用户详细信息
const fetchUserDetail = async (userId) => {
  if (!userId) {
    userDetail.value = null
    return
  }
  
  loadingUserInfo.value = true
  
  try {
    const res = await getUserInfo(userId)
    console.log('获取用户详细信息:', res)
    
    if (res.code === 200 && res.data) {
      // 新的返回格式是一个数组，第一个元素是用户信息，第二个元素是订单信息
      if (Array.isArray(res.data)) {
        userDetail.value = res.data[0]
        // 如果存在订单信息，可以保存到另一个变量中
        if (res.data.length > 1) {
          userOrderDetail.value = res.data[1]
          
          // 解析订单信息
          parseOrderData()
        }
      } else {
        // 兼容旧格式
        userDetail.value = res.data
      }
    } else {
      ElMessage.warning('获取用户详细信息失败')
      userDetail.value = null
    }
  } catch (error) {
    console.error('获取用户详细信息失败:', error)
    ElMessage.warning('获取用户详细信息失败')
    userDetail.value = null
  } finally {
    loadingUserInfo.value = false
  }
}

// 格式化地址
const formatAddress = (order) => {
  if (!order) return ''
  
  let addressParts = []
  if (order.prov_name) addressParts.push(order.prov_name)
  if (order.city_name && order.city_name !== order.prov_name) addressParts.push(order.city_name)
  if (order.area_name) addressParts.push(order.area_name)
  if (order.street_name) addressParts.push(order.street_name)
  if (order.address) addressParts.push(order.address)
  
  return addressParts.join(' ')
}

// 解析订单数据，提取不同类型的订单信息
const parseOrderData = () => {
  try {
    // 重置所有订单数据
    recycleOrders.value = null
    currentRecycleOrder.value = null
    fleaMarketOrders.value = null
    currentFleaMarketOrder.value = null
    marketOrders.value = null
    currentMarketOrder.value = null
    engineerOrders.value = null
    currentEngineerOrder.value = null
    
    if (!userOrderDetail.value) {
      return
    }
    
    // 解析当前订单数据
    if (userOrderDetail.value.currentOrder) {
      try {
        const currentOrderData = JSON.parse(userOrderDetail.value.currentOrder)
        
        // 检查并解析各类型订单
        if (currentOrderData.recycle) {
        // 当前小智回收订单数据
          currentRecycleOrder.value = currentOrderData.recycle
          // console.log('当前小智回收订单数据:', currentRecycleOrder.value)
        }
        
        if (currentOrderData.flea_market) {
        // 当前二手商城订单数据
          currentFleaMarketOrder.value = currentOrderData.flea_market
          // console.log('当前二手商城订单数据:', currentFleaMarketOrder.value)
        }
        
        if (currentOrderData.market) {
        // 当前小智集市订单数据
          currentMarketOrder.value = currentOrderData.market
          // console.log('当前小智集市订单数据:', currentMarketOrder.value)
        }
        
        if (currentOrderData.engineer) {
        // 当前工程师订单数据
          currentEngineerOrder.value = currentOrderData.engineer
          // console.log('当前工程师订单数据:', currentEngineerOrder.value)
        }
      } catch (error) {
        console.error('解析当前订单数据失败:', error)
      }
    }
    
    // 解析历史订单列表
    if (userOrderDetail.value.orderList) {
      try {
        const orderData = JSON.parse(userOrderDetail.value.orderList)
        
        // 检查并解析各类型订单列表
        if (orderData.recycle) {
          // 小智回收订单列表数据
          recycleOrders.value = orderData.recycle
          // console.log('小智回收订单列表数据:', recycleOrders.value)
        }
        
        if (orderData.flea_market) {
          // 二手商城订单列表数据
          fleaMarketOrders.value = orderData.flea_market
          // console.log('二手商城订单列表数据:', fleaMarketOrders.value)
        }
        
        if (orderData.market) {
          // 小智集市订单列表数据
          marketOrders.value = orderData.market
          // console.log('小智集市订单列表数据:', marketOrders.value)
        }
        
        if (orderData.engineer) {
          // 工程师订单列表数据
          engineerOrders.value = orderData.engineer
          // console.log('工程师订单列表数据:', engineerOrders.value)
        }
      } catch (error) {
        console.error('解析订单列表数据失败:', error)
      }
    }
  } catch (error) {
    console.error('解析订单数据失败:', error)
  }
}

// 加载会话消息
const loadSessionMessages = async (sessionId, forceRefresh = false) => {
  if (!sessionId) return
  
  loading.value = true
  
  try {
    console.log('开始加载会话消息, 会话ID:', sessionId, '强制刷新:', forceRefresh)
    
    // 如果是强制刷新，可以先清空当前消息列表，增强视觉反馈
    if (forceRefresh) {
      console.log('强制刷新，先清空消息列表')
      messages.value = []
    }
    
    const res = await getMessagesBySessionId(sessionId)
    console.log('加载消息响应:', res)
    
    if (res.code === 200 && Array.isArray(res.data)) {
      // 检查返回的消息数量
      console.log('API返回消息数量:', res.data.length)
      
      // 如果返回的数据为空但不是强制刷新，可能是缓存问题，尝试再次请求
      if (res.data.length === 0 && !forceRefresh) {
        console.log('返回消息数量为0，疑似缓存问题，将在100ms后重试')
        setTimeout(() => loadSessionMessages(sessionId, true), 100)
        return
      }
      
      // 创建消息ID集合，用于检测重复消息
      const messageIds = new Set()
      
      // 收集当前已有的消息ID
      messages.value.forEach(msg => {
        if (msg.id) {
          messageIds.add(msg.id.toString());
        }
      });
      
      // 标准化消息格式，确保字段一致性
      const normalizedMessages = res.data.map(msg => {
        // 标准化发送方字段 (from 或 senderType)
        if (msg.from === undefined && msg.senderType !== undefined) {
          msg.from = msg.senderType;
        } else if (msg.from !== undefined && msg.senderType === undefined) {
          msg.senderType = msg.from;
        }
        
        // 处理系统消息类型
        if (msg.senderType === 3) {
          msg.type = 3; // 确保系统消息的type也为3
        } else {
          // 标准化消息类型字段 (type 或 msgType)
          if (msg.type === undefined && msg.msgType !== undefined) {
            // 后端API返回的msgType: 0-文本, 1-图片，转换为前端的type: 1-文本, 2-图片
            msg.type = msg.msgType + 1;
          } else if (msg.type !== undefined && msg.msgType === undefined) {
            // 前端的type: 1-文本, 2-图片，转换为后端的msgType: 0-文本, 1-图片
            msg.msgType = msg.type - 1;
          }
        }
        
        // 显式记录消息为已标准化
        msg._standardized = true;
        
        return msg;
      });
      
      // 过滤掉重复的消息，但不按发送者类型过滤
      const uniqueMessages = normalizedMessages.filter(msg => {
        if (!msg.id) return true; // 如果没有ID，保留该消息
        
        // 检查是否已存在相同ID的消息
        const msgId = msg.id.toString();
        if (messageIds.has(msgId)) {
          console.log('过滤掉重复消息:', msgId);
          return false;
        }
        
        // 记录这个消息ID
        messageIds.add(msgId);
        return true;
      });
      
      console.log('加载的消息总数:', normalizedMessages.length);
      console.log('去重后的消息数:', uniqueMessages.length);
      
      // 保留临时消息（状态为发送中或未确认的消息）
      const tempMessages = messages.value.filter(msg => 
        msg.status === 0 || 
        (msg.id && msg.id.toString().startsWith('temp_'))
      );
      
      // 清空现有消息但保留临时消息
      if (tempMessages.length > 0) {
        console.log('保留', tempMessages.length, '条临时消息');
      }
      
      // 按时间排序后添加消息
      const sortedMessages = uniqueMessages.sort((a, b) => 
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );
      
      // 合并临时消息和加载的消息
      messages.value = [...sortedMessages, ...tempMessages];
      
      console.log('加载到的消息数量(去重后):', messages.value.length)
      
      // 检查是否有来自用户的消息
      const userMessages = messages.value.filter(m => m.from === 0 || m.senderType === 0)
      console.log('用户发送的消息数量:', userMessages.length)
      
      // 滚动到底部
      await nextTick()
      scrollToBottom()
      
      // 再次确保滚动到底部（双重保证）
      setTimeout(() => {
        scrollToBottom()
      }, 100)
      
      // 更新会话的最后一条消息
      if (currentSession.value && messages.value.length > 0) {
        const lastMessage = messages.value[messages.value.length - 1]
        currentSession.value.lastMessage = (lastMessage.type === 1 || lastMessage.msgType === 0) ? lastMessage.content : '[图片]'
        currentSession.value.lastActiveTime = lastMessage.createdAt
      }
    } else {
      console.error('加载消息失败:', res)
      ElMessage.error(res.message || '加载消息失败')
    }
  } catch (error) {
    console.error('加载会话消息失败', error)
    ElMessage.error('加载消息失败')
  } finally {
    loading.value = false
  }
}

// 加载更多历史消息
const loadMoreMessages = async () => {
  if (!currentSession.value?.id || loadingMore.value) return
  
  loadingMore.value = true
  
  try {
    // TODO: 调用实际API
    // const nextPage = currentPage.value + 1
    // const res = await getMessagesBySessionId(currentSession.value.id, nextPage, pageSize.value)
    // if (res.code === 200) {
    //   // 合并消息并按时间排序
    //   const newMessages = [...res.data.items, ...messages.value]
    //   newMessages.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
    //   messages.value = newMessages
    //   hasMoreMessages.value = res.data.hasMore
    //   currentPage.value = nextPage
    // }
    
    // 模拟API返回数据
    await new Promise(resolve => setTimeout(resolve, 800)) // 模拟网络延迟
    
    const nextPage = currentPage.value + 1
    const mockOlderMessages = []
    const additionalMessages = Math.floor(Math.random() * 10) + 5 // 5-15条额外消息
    const hasMore = Math.random() > 0.7 // 70%概率没有更多消息
    
    // 记住当前第一条消息的位置，加载更多后需要保持滚动位置
    const firstMessage = messageContainer.value?.querySelector('.message-item')
    const firstMessageTop = firstMessage?.getBoundingClientRect().top
    
    // 创建更早的消息记录
    for (let i = 0; i < additionalMessages; i++) {
      const isUser = Math.random() > 0.5
      const isSystem = !isUser && Math.random() < 0.2
      const messageType = isSystem ? 'system' : (Math.random() < 0.2 ? 'image' : 'text')
      // 更早的时间戳
      const timestamp = new Date(new Date(messages.value[0]?.createdAt || Date.now()).getTime() - (i + 1) * 60000 - Math.random() * 50000)
      
      let content = ''
      if (messageType === 'text') {
        if (isUser) {
          content = ['您好，请问有人在吗？', '我想咨询一下你们的产品', '价格是多少？', '有优惠活动吗？'][Math.floor(Math.random() * 4)]
        } else {
          content = ['您好，有什么可以帮到您的？', '感谢您的咨询', '正在为您查询', '请问还有其他问题吗？'][Math.floor(Math.random() * 4)]
        }
      } else if (messageType === 'image') {
        content = `https://picsum.photos/300/200?random=${Math.floor(Math.random() * 1000)}`
      } else if (messageType === 'system') {
        content = ['会话已创建', '等待客服接入', '系统消息：今日客服繁忙'][Math.floor(Math.random() * 3)]
      }
      
      mockOlderMessages.push({
        id: `msg_old_${currentSession.value.id}_${i}`,
        sessionId: currentSession.value.id,
        senderId: isUser ? currentSession.value.userId : agentInfo.value?.id || 'agent',
        senderType: isUser ? 'user' : 'agent',
        type: messageType,
        content,
        status: 2, // 已读
        createdAt: timestamp.toISOString()
      })
    }
    
    // 按时间顺序排列
    mockOlderMessages.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
    
    // 合并消息
    messages.value = [...mockOlderMessages, ...messages.value]
    hasMoreMessages.value = hasMore
    currentPage.value = nextPage
    
    // 保持滚动位置
    await nextTick()
    if (firstMessageTop && firstMessage) {
      const newFirstMessage = messageContainer.value?.querySelector('.message-item')
      if (newFirstMessage) {
        const newTop = newFirstMessage.getBoundingClientRect().top
        const scrollDiff = newTop - firstMessageTop
        messageContainer.value.scrollTop += scrollDiff
      }
    }
  } catch (error) {
    console.error('加载更多消息失败', error)
    ElMessage.error('加载更多消息失败')
  } finally {
    loadingMore.value = false
  }
}

// 按日期分组获取消息
const getMessagesByDate = (date) => {
  return messages.value.filter(message => {
    if (!message.createdAt) return false
    const messageDate = new Date(message.createdAt)
    return messageDate.toDateString() === date
  })
}

// 格式化消息日期
const formatMessageDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  
  // 今天
  if (date.toDateString() === now.toDateString()) {
    return '今天'
  }
  
  // 昨天
  const yesterday = new Date(now)
  yesterday.setDate(yesterday.getDate() - 1)
  if (date.toDateString() === yesterday.toDateString()) {
    return '昨天'
  }
  
  // 本周内
  const daysDiff = Math.floor((now - date) / (1000 * 60 * 60 * 24))
  if (daysDiff < 7) {
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
    return weekdays[date.getDay()]
  }
  
  // 其他日期
  return moment(date).format('YYYY年MM月DD日')
}

// 接入会话
const acceptSession = async (session) => {
  try {
    // 从用户存储中获取客服ID
    const userStore = useUserStore()
    const agentId = userStore.agentId
    
    if (!agentId) {
      ElMessage.error('获取客服ID失败，请重新登录')
      return
    }
    
    const res = await acceptChatSession(session.id, agentId)
    if (res.code === 200) {
      ElMessage.success('成功接入会话')
      // 更新会话状态
      session.status = 1
      // 将会话从待处理移到进行中
      sessionTabActive.value = 'active'
      // 重新加载会话列表
      await loadSessionList()
    } else {
      ElMessage.error(res.message || '接入会话失败')
    }
  } catch (error) {
    console.error('接入会话失败', error)
    ElMessage.error('接入会话失败')
  }
}

// 处理搜索
const handleSearch = () => {
  // 通过computed属性自动过滤
}

// 处理筛选类型改变
const handleFilterChange = (type) => {
  if (type === 'waiting') {
    sessionTabActive.value = 'waiting'
  } else if (type === 'active') {
    sessionTabActive.value = 'active'
  } else if (type === 'ai') {
    sessionTabActive.value = 'ai'
  }
  // 'all' 不做特殊处理，保持当前标签页
}

// 处理标签页切换
const handleTabChange = (tab) => {
  // 更新筛选类型
  filterType.value = tab
}

// 切换客服在线状态
const toggleAgentStatus = async (status) => {
  if (!userStore.userInfo?.id) return
  
  try {
    await userStore.updateAgentStatus(userStore.userInfo.id, status)
    
    ElMessage.success(status === 1 ? '已切换为在线状态' : '已切换为离线状态')
  } catch (error) {
    console.error('更新状态失败', error)
    ElMessage.error('切换状态失败')
  }
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  
  const date = moment(timestamp)
  
  // 显示月-日 时分秒格式
  return date.format('MM-DD HH:mm:ss')
}

// 格式化等待时间
const formatWaitingTime = (seconds) => {
  // 确保seconds是数字类型
  const waitSeconds = Number(seconds)
  
  // 如果seconds不是有效数字，返回默认值
  if (isNaN(waitSeconds) || waitSeconds === undefined || waitSeconds === null) {
    console.warn('等待时间无效:', seconds)
    return '未知'
  }
  
  if (waitSeconds < 60) {
    return `${waitSeconds}秒`
  } else if (waitSeconds < 3600) {
    const minutes = Math.floor(waitSeconds / 60)
    const remainSeconds = Math.floor(waitSeconds % 60)
    return `${minutes}分${remainSeconds}秒`
  } else {
    const hours = Math.floor(waitSeconds / 3600)
    const minutes = Math.floor((waitSeconds % 3600) / 60)
    return `${hours}小时${minutes}分钟`
  }
}

// 获取等待时间类型
const getWaitingTimeType = (seconds) => {
  // 确保seconds是数字类型
  const waitSeconds = Number(seconds)
  
  // 如果seconds不是有效数字，返回默认值
  if (isNaN(waitSeconds) || waitSeconds === undefined || waitSeconds === null) {
    return 'info'
  }
  
  if (waitSeconds < 5 * 60) {
    return 'success'
  } else if (waitSeconds < 15 * 60) {
    return 'warning'
  } else {
    return 'danger'
  }
}

// 获取优先级类型
const getPriorityType = (priority) => {
  switch (priority) {
    case 1: return 'info'
    case 2: return 'warning'
    case 3: return 'danger'
    default: return 'info'
  }
}

// 获取优先级标签
const getPriorityLabel = (priority) => {
  switch (priority) {
    case 1: return '低'
    case 2: return '中'
    case 3: return '高'
    default: return '未知'
  }
}

// 获取会话状态文本
const getSessionStatusText = (status) => {
  switch (status) {
    case 0: return '排队中';  // 修改为更准确的描述
    case 1: return '人工会话中';
    case 2: return '已结束';  // 修改为更准确的描述
    case 3: return 'AI会话中';
    default: return '未知状态';
  }
}

// 结束会话按钮点击
const handleEndChat = () => {
  if (!currentSession.value || currentSession.value.status !== 1) {
    ElMessage.warning('没有可结束的会话')
    return
  }
  
  // 询问是否要先添加解决方案
  ElMessageBox.confirm(
    '是否要先添加解决方案再结束会话？',
    '提示',
    {
      confirmButtonText: '添加解决方案',
      cancelButtonText: '直接结束会话',
      type: 'info',
      distinguishCancelAndClose: true,
      closeOnClickModal: false
    }
  ).then(() => {
    // 用户选择添加解决方案
    showSolutionForm.value = true
  }).catch(action => {
    if (action === 'cancel') {
      // 用户选择直接结束会话
      endSession()
    }
    // 如果是关闭弹窗，不做任何操作
  })
}

// 结束会话
const endSession = () => {
  if (!currentSession.value || currentSession.value.status !== 1) return
  
  ElMessageBox.confirm(
    '确定要结束当前会话吗？',
    '结束会话',
    {
      confirmButtonText: '结束',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      const res = await closeSession(currentSession.value.id, 1)  // 1表示由客服结束
      if (res.code === 200) {
        ElMessage.success('会话已结束')
        
        // 更新会话状态
        currentSession.value.status = 0
        
        // 通过WebSocket发送会话结束消息给用户
        try {
          await chatStore.sendMessage({
            type: 1, // 文本消息类型，确保后端转发
            from: 1, // 客服发送
            senderId: agentInfo.value?.id,
            receiverId: currentSession.value.userId,
            sessionId: currentSession.value.id,
            content: '感谢您的咨询，您可继续和AI客服沟通',
            senderType: 1, // 客服类型
            msgType: 0, // 文本消息
            systemFlag: true, // 添加标识，表示这是一个系统级消息
            timestamp: Date.now()
          })
          console.log('已发送会话结束通知给用户')
        } catch (wsError) {
          console.error('发送会话结束消息失败:', wsError)
        }
        
        // 添加系统消息到本地消息列表
        const systemMessage = {
          id: `msg_system_${Date.now()}`,
          sessionId: currentSession.value.id,
          senderId: agentInfo.value?.id || 'system',
          from: 1,
          senderType: 'system',
          type: 'system',
          content: '感谢您的咨询，您可继续和AI客服沟通',
          status: 1,
          createdAt: new Date().toISOString()
        };
        
        messages.value.push(systemMessage)
        
        // 重新加载会话列表
        await loadSessionList()
      } else {
        ElMessage.error(res.message || '结束会话失败')
      }
    } catch (error) {
      console.error('结束会话失败', error)
      ElMessage.error('结束会话失败')
    }
  }).catch(() => {
    // 用户取消
  })
}

// 转接会话
const transferSession = () => {
  if (!currentSession.value || (currentSession.value.status !== 1 && currentSession.value.status !== 3)) return
  
  ElMessageBox.confirm(
    '确定要转接当前会话吗？',
    '转接会话',
    {
      confirmButtonText: '转接',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(async () => {
    try {
      // 获取在线客服列表
      const res = await getOnlineAgents()
      if (!res.data || !Array.isArray(res.data) || res.data.length === 0) {
        ElMessage.warning('当前没有在线客服可供选择')
        return
      }
      
      // 使用所有在线客服，不再过滤当前客服
      const onlineAgents = res.data
      
      // 显示客服选择弹窗
      ElMessageBox.confirm(
        h('div', { style: 'width: 100%' }, [
          h('div', { style: 'margin-bottom: 15px' }, '请选择要转接的客服：'),
          h('select', {
            id: 'agent-selector',
            style: 'width: 100%; padding: 8px; border-radius: 4px; border: 1px solid #dcdfe6;'
          }, onlineAgents.map(agent => {
            // 标记当前客服
            const isCurrent = agent.id === agentInfo.value?.id
            return h('option', { value: agent.id }, `${agent.name || '未命名客服'}${isCurrent ? ' (当前客服)' : ''}${agent.status === 1 ? ' (在线)' : ''}`)
          }))
        ]),
        '选择转接客服',
        {
          confirmButtonText: '确认转接',
          cancelButtonText: '取消',
          type: 'info',
          beforeClose: async (action, instance, done) => {
            if (action === 'confirm') {
              // 获取选中的客服ID
              const selector = document.getElementById('agent-selector')
              const targetAgentId = selector ? parseInt(selector.value) : null
              
              if (!targetAgentId) {
                ElMessage.error('请选择要转接的客服')
                return
              }
              
              // 如果选择的是当前客服，询问是否继续
              if (targetAgentId === agentInfo.value?.id) {
                const confirmTransfer = await ElMessageBox.confirm(
                  '您选择的是当前客服，确定要继续转接吗？',
                  '转接确认',
                  {
                    confirmButtonText: '继续转接',
                    cancelButtonText: '取消',
                    type: 'warning'
                  }
                ).catch(() => false)
                
                if (!confirmTransfer) {
                  return
                }
              }
              
              instance.confirmButtonLoading = true
              
              try {
                // 调用手动分配客服API
                console.log('转接会话:', currentSession.value.id, targetAgentId)
                const result = await assignAgentManually(currentSession.value.id, targetAgentId)
                if (result.code === 200 && result.data) {
                  // 转接成功
                  ElMessage.success('转接成功')
                  
                  // 添加系统消息
                  const targetAgent = onlineAgents.find(a => a.id === targetAgentId)
                  const systemMessage = {
                    id: `system-${Date.now()}`,
                    sessionId: currentSession.value.id,
                    content: `会话已转接给客服 ${targetAgent ? targetAgent.name || '未命名客服' : ''}`,
                    from: 2, // 系统消息
                    senderType: 2, // 系统
                    status: 1, // 已发送
                    createdAt: new Date().toISOString()
                  }
                  
                  messages.value.push(systemMessage)
                  
                  // 重新加载会话列表
                  await loadSessionList()
                } else {
                  ElMessage.error(result.message || '转接失败')
                }
              } catch (error) {
                console.error('转接失败', error)
                ElMessage.error('转接失败')
              } finally {
                instance.confirmButtonLoading = false
                done()
              }
            } else {
              done()
            }
          }
        }
      ).catch(() => {
        // 用户取消选择客服
      })
    } catch (error) {
      console.error('获取在线客服列表失败', error)
      ElMessage.error('获取在线客服列表失败')
    }
  }).catch(() => {
    // 用户取消
  })
}

// 滚动到底部
const scrollToBottom = () => {
  // 确保消息容器存在
  if (!messageContainer.value) return
  
  console.log('执行滚动到底部')
  
  // 立即滚动到底部
    messageContainer.value.scrollTop = messageContainer.value.scrollHeight
  
  // 在短暂延迟后再次滚动，确保动态加载的内容也能被滚动显示
  setTimeout(() => {
    if (messageContainer.value) {
      messageContainer.value.scrollTop = messageContainer.value.scrollHeight + 1000 // 加上额外距离确保滚动到底
    }
  }, 50)
  
  // 在更长延迟后第三次滚动，处理可能的图片延迟加载情况
  setTimeout(() => {
    if (messageContainer.value) {
      messageContainer.value.scrollTop = messageContainer.value.scrollHeight + 1000
    }
  }, 300)
}

// 处理图片上传
const handleImageUpload = (file) => {
  // 校验文件类型和大小
  const isImage = file.raw.type.indexOf('image/') !== -1
  const isLt2M = file.raw.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return
  }
  
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return
  }
  
  // 保存选中的图片
  selectedImage.value = file.raw
  
  // 创建预览URL
  selectedImageUrl.value = URL.createObjectURL(file.raw)
}

// 取消图片上传
const cancelImageUpload = () => {
  if (selectedImageUrl.value) {
    URL.revokeObjectURL(selectedImageUrl.value)
  }
  selectedImage.value = null
  selectedImageUrl.value = ''
}

// 插入快捷回复
const insertQuickReply = (text) => {
  messageText.value = text
}

// 插入常见问题
const insertCommonQuestion = (text) => {
  messageText.value = text
  // 关闭常用问题面板
  const popover = document.querySelector('.el-popover')
  if (popover) {
    popover.style.display = 'none'
  }
}

// 处理回车键按下
const handleEnterPress = (e) => {
  // 阻止默认的换行行为
  e.preventDefault()
  
  // 如果按住Ctrl键，则允许换行
  if (e.ctrlKey || e.metaKey) {
    // 在光标位置插入换行符
    const textarea = e.target
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    messageText.value = messageText.value.substring(0, start) + '\n' + messageText.value.substring(end)
    
    // 恢复光标位置
    nextTick(() => {
      textarea.selectionStart = textarea.selectionEnd = start + 1
    })
    return
  }
  
  // 直接按Enter键发送消息
  sendMessage()
}

const handlePaste = async (event) => {
  try {
    const clipboardData = event.clipboardData || window.clipboardData;
    if (!clipboardData) return;

    const items = clipboardData.items;
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.kind === 'file' && item.type.startsWith('image/')) {
        event.preventDefault(); // 阻止默认粘贴行为（例如，粘贴图片文件名）
        const blob = item.getAsFile();
        if (!blob) continue;

        // 更新 selectedImage 和 selectedImageUrl 以进行预览和发送
        // 这模拟了 handleImageUpload 处理文件后的状态
        selectedImage.value = blob;
        selectedImageUrl.value = URL.createObjectURL(blob);
        
        // 可选：清除文本输入框中的任何潜在文本（如果图片粘贴时也粘贴了文本）
        // messageText.value = ''; 

        // 确保 UI 更新以显示图片预览
        await nextTick();
        break; // 处理找到的第一个图片
      }
    }
  } catch (error) {
    console.error('处理粘贴图片失败:', error);
    ElMessage.error('粘贴图片失败，请重试');
  }
};

// 发送消息
const handleSendMessage = async (inputData) => {
  const { text, image, imageUrl, isVideo } = inputData
  
  if ((!text.trim() && !image) || sending.value) return
  
  // 如果会话不是进行中状态，不能发送消息
  if (!currentSession.value || currentSession.value.status !== 1) {
    ElMessage.warning('只能在进行中的会话中发送消息')
    return
  }
  
  sending.value = true
  
  // 创建临时消息ID，确保在整个函数中可用
  const tempMessageId = `temp_${Date.now()}`
  
  try {
    // 确定消息类型
    const messageType = image ? 2 : 1  // 1-文本消息, 2-文件消息(图片/视频)
    const content = messageType === 1 ? text : ''
    
    // 创建临时消息对象
    const newMessage = {
      id: tempMessageId,
      sessionId: currentSession.value.id,
      senderId: agentInfo.value?.id,
      from: 1,  // 1表示客服发送
      senderType: 1, // 确保senderType也设置为1
      receiverId: currentSession.value.userId,
      type: messageType,
      content: messageType === 1 ? text : (imageUrl || (isVideo ? '[视频上传中...]' : '[图片上传中...]')), // 临时使用本地URL或占位符
      msgType: messageType === 2 ? (isVideo ? 2 : 1) : undefined, // 2-视频, 1-图片
      status: 0, // 发送中
      createdAt: new Date().toISOString()
    }
    
    // 添加到消息列表
    messages.value.push(newMessage)
    
    // 滚动到底部
    await nextTick()
    scrollToBottom()
    
    // 根据消息类型发送
    if (messageType === 1) {
      await sendTextMessage(currentSession.value.id, text, currentSession.value.userId, tempMessageId)
    } else if (messageType === 2) {
      if (isVideo) {
        await sendVideoMessage(currentSession.value.id, image, currentSession.value.userId, tempMessageId)
      } else {
        await sendImageMessage(currentSession.value.id, image, currentSession.value.userId, tempMessageId)
      }
    }
    
    // 更新会话最后消息
    currentSession.value.lastMessage = messageType === 1 ? text : (isVideo ? '[视频]' : '[图片]')
    currentSession.value.lastActiveTime = new Date().toISOString()
    
    // 取消图片选择
    cancelImageUpload()
  } catch (error) {
    console.error('发送消息失败', error)
    ElMessage.error('发送消息失败')
    
    // 查找并更新失败的临时消息状态
    const failedMessage = messages.value.find(m => m.id === tempMessageId)
    if (failedMessage) {
      failedMessage.status = -1 // 发送失败
    }
  } finally {
    sending.value = false
  }
}

// 发送文本消息
const sendTextMessage = async (sessionId, content, receiverId, tempId) => {
  try {
    // 使用WebSocket发送消息
    const message = {
      type: 1, // 文本消息
      from: 1, // 1-客服发送
      senderId: agentInfo.value?.id,
      receiverId: receiverId,
      sessionId: sessionId,
      content: content,
      timestamp: Date.now(),
      scene: getMessageScene(),
      datasource: getMessageDatasource(),
      channel: getMessageChannel(),
      collectionName: getMessageCollectionName()
    }
    
    // 使用chatStore的WebSocket连接发送消息
    await chatStore.sendMessage(message)
    console.log('发送WebSocket消息成功:', message)
    
    // 更新临时消息状态为已发送（而不是发送中）
    const tempMessage = messages.value.find(m => m.id === tempId)
    if (tempMessage) {
      // console.log('更新临时消息状态为已发送:', tempId)
      // 对勾图标 (CircleCheck)：消息已发送成功（status === 1）
      // 转圈图标 (Loading)：消息发送中（status === 0）
      // 眼睛图标 (View)：消息已读（status === 2）
      tempMessage.status = 1 // 已发送  
      
      // 注意：真实的消息ID更新仍将在handleNewMessage中处理
      // 当WebSocket返回确认消息时，handleNewMessage会找到这个临时消息并更新它的ID
    } else {
      console.warn('未找到要更新的临时消息:', tempId)
    }
    
    return { code: 200 }
  } catch (error) {
    console.error('发送文本消息失败', error)
    
    // 发送失败时，更新临时消息状态
    const tempMessage = messages.value.find(m => m.id === tempId)
    if (tempMessage) {
      tempMessage.status = -1 // 发送失败
    }
    
    throw error
  }
}

// 发送图片消息
const sendImageMessage = async (sessionId, file, receiverId, tempId) => {
  try {
    // 创建FormData对象用于文件上传
    const formData = new FormData()
    formData.append('file', file)
    formData.append('sessionId', sessionId)
    formData.append('senderType', '1') // 1表示客服
    
    // 调用新的上传接口
    const response = await fetch('https://jms.bearhome.cn/api/file/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userStore.token}`
      },
      body: formData
    })
    
    if (!response.ok) {
      throw new Error('图片上传失败，服务器响应: ' + response.status)
    }
    
    const result = await response.json()
    
    if (result.statusCode !== 200) {
      throw new Error(result.errorInfo || '图片上传失败')
    }
    
    // 从新的返回格式中获取实际的图片URL
    const imageUrl = result.data
    
    // 先更新临时消息的内容为真实URL
    const tempMessage = messages.value.find(m => m.id === tempId)
    if (tempMessage) {
      console.log('更新临时图片消息URL:', tempId, imageUrl)
      tempMessage.content = imageUrl
      tempMessage.status = 1 // 已发送（改为直接设置为已发送）
    }
    
    // 构建消息对象
    const messageData = {
      type: 2, // 图片消息
      from: 1, // 1-客服发送
      senderId: agentInfo.value?.id,
      receiverId: receiverId,
      sessionId: sessionId,
      content: imageUrl,
      timestamp: Date.now(),
      scene: '默认应用场景',
      datasource: '默认数据源',
      channel:"Pc",
      collectionName: '默认查询知识库集合'
    }
    
    // 使用chatStore的WebSocket连接发送消息
    await chatStore.sendMessage(messageData)
    console.log('发送WebSocket图片消息成功:', messageData)
    
    // 注意：真实的消息ID更新仍将在handleNewMessage中处理
    // 当WebSocket返回确认消息时，handleNewMessage会找到这个临时消息并更新它的ID
    
    return { code: 200, data: { url: imageUrl } }
  } catch (error) {
    console.error('发送图片消息失败', error)
    
    // 更新临时消息状态为发送失败
    const tempMessage = messages.value.find(m => m.id === tempId)
    if (tempMessage) {
      tempMessage.status = -1 // 发送失败
    }
    
    // 显示错误提示
    ElMessage.error('图片发送失败: ' + (error.message || '未知错误'))
    
    throw error
  }
}

// 发送视频消息
const sendVideoMessage = async (sessionId, file, receiverId, tempId) => {
  try {
    // 创建FormData对象用于文件上传
    const formData = new FormData()
    formData.append('file', file)
    formData.append('sessionId', sessionId)
    formData.append('senderType', '1') // 1表示客服
    
    // 调用上传接口
    const response = await fetch('https://jms.bearhome.cn/api/file/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userStore.token}`
      },
      body: formData
    })
    
    if (!response.ok) {
      throw new Error('视频上传失败，服务器响应: ' + response.status)
    }
    
    const result = await response.json()
    
    if (result.statusCode !== 200) {
      throw new Error(result.errorInfo || '视频上传失败')
    }
    
    // 从返回格式中获取实际的视频URL
    const videoUrl = result.data
    
    // 先更新临时消息的内容为真实URL
    const tempMessage = messages.value.find(m => m.id === tempId)
    if (tempMessage) {
      console.log('更新临时视频消息URL:', tempId, videoUrl)
      tempMessage.content = videoUrl
      tempMessage.status = 1 // 已发送
    }
    
    // 构建消息对象
    const messageData = {
      type: 2, // 文件消息
      msgType: 2, // 视频消息
      from: 1, // 1-客服发送
      senderId: agentInfo.value?.id,
      receiverId: receiverId,
      sessionId: sessionId,
      content: videoUrl,
      timestamp: Date.now(),
      scene: '默认应用场景',
      datasource: '默认数据源',
      channel: "Pc",
      collectionName: '默认查询知识库集合'
    }
    
    // 使用chatStore的WebSocket连接发送消息
    await chatStore.sendMessage(messageData)
    console.log('发送WebSocket视频消息成功:', messageData)
    
    return { code: 200, data: { url: videoUrl } }
  } catch (error) {
    console.error('发送视频消息失败', error)
    
    // 更新临时消息状态为发送失败
    const tempMessage = messages.value.find(m => m.id === tempId)
    if (tempMessage) {
      tempMessage.status = -1 // 发送失败
    }
    
    // 显示错误提示
    ElMessage.error('视频发送失败: ' + (error.message || '未知错误'))
    
    throw error
  }
}

// 获取标签类型
const getTagTypeByStatus = (status) => {
  switch (status) {
    case 0: return 'warning';
    case 1: return 'success';
    case 2: return 'info';
    case 3: return 'primary';
    default: return 'info';
  }
}

// 获取发送者名称
const getSenderName = (message) => {
  // 系统消息特殊处理
  if (message.type === 3 || message.msgType === 2) {
    return '系统消息';
  }
  
  // AI客服消息处理
  if (message.senderType === 2 || (message.from === 1 && message.currentAgentType === 2)) {
    return 'AI客服';
  }
  
  // 用户消息处理
  if (message.from === 0 || message.senderType === 0) {
    return currentSession.value?.userNickname || (currentSession.value?.user?.nickname) || '用户';
  }
  
  // 人工客服消息处理
  if (message.from === 1 || message.senderType === 1) {
    return agentInfo.value?.name || '客服';
  }
  
  // 默认情况
  return '';
}

// 添加解决方案
const handleAddSolution = () => {
  if (!currentSession.value) {
    ElMessage.warning('请先选择会话')
    return
  }
  
  showSolutionForm.value = true
}

// 解决方案添加成功
const handleSolutionSuccess = async (solutionData) => {
  showSolutionForm.value = false
  ElMessage.success('解决方案添加成功')
  
  // 获取解决方案内容
  const solutionDescription = solutionData?.description || '客服已添加解决方案'
  const isSolved = solutionData?.isSolved !== undefined ? solutionData.isSolved : 1
  
  // 构建消息内容
  const messageContent = isSolved === 1 
    ? `客服已将此问题标记为已解决，解决方案：${solutionDescription}`
    : `客服已添加解决方案：${solutionDescription}`
  
  try {
    // 通过WebSocket发送系统消息
    await chatStore.sendMessage({
      type: 1, // 文本消息，使用文本消息类型确保后端转发
      from: 1, // 客服发送
      senderId: agentInfo.value?.id,
      receiverId: currentSession.value.userId,
      sessionId: currentSession.value.id,
      content: messageContent,
      senderType: 1, // 客服类型
      msgType: 0, // 文本消息类型
      systemFlag: true, // 添加标识，表示这是一个系统级消息
      timestamp: Date.now()
    })
    
    // 添加到本地消息列表 - 确保类型设置正确
    const systemMessage = {
      id: `msg_solution_${Date.now()}`,
      sessionId: currentSession.value.id,
      senderId: agentInfo.value?.id || 'system',
      from: 1, // 客服发送
      senderType: 'system', // 用于前端显示
      type: 'system', // 用于前端显示
      content: messageContent,
      status: 1,
      createdAt: new Date().toISOString()
    }
    
    messages.value.push(systemMessage)
    
    // 更新会话的最后消息
    if (currentSession.value) {
      currentSession.value.lastMessage = messageContent
      currentSession.value.lastActiveTime = systemMessage.createdAt
    }
    
    // 滚动到底部
    await nextTick()
    scrollToBottom()
    
    // 询问是否要结束会话
    ElMessageBox.confirm(
      '解决方案已添加，是否要结束当前会话？',
      '提示',
      {
        confirmButtonText: '结束会话',
        cancelButtonText: '继续会话',
        type: 'info'
      }
    ).then(() => {
      // 用户选择结束会话
      endSession()
    }).catch(() => {
      // 用户选择继续会话，不做任何操作
    })
  } catch (error) {
    console.error('发送解决方案消息失败:', error)
    ElMessage.error('发送解决方案消息失败')
  }
}

// 刷新当前消息列表
const refreshCurrentMessages = async () => {
  if (!currentSession.value) {
    ElMessage.warning('请先选择一个会话')
    return
  }
  
  try {
    // 设置加载中状态
    loading.value = true
    
    // 必须先等待上一次请求完成
    setTimeout(async () => {
      try {
        // 强制重新加载消息
        await loadSessionMessages(currentSession.value.id, true)
        // 删除"消息刷新成功"的提示
        // 确保滚动到底部
        await nextTick()
        scrollToBottom()
      } catch (error) {
        console.error('刷新消息失败:', error)
        ElMessage.error('刷新消息失败')
      } finally {
        loading.value = false
      }
    }, 100)
  } catch (error) {
    console.error('刷新消息失败:', error)
    ElMessage.error('刷新消息失败')
    loading.value = false
  }
}

// 添加图片URL处理函数
const getImageUrl = (url) => {
  if (!url) return ''
  
  // 处理blob URL，直接返回
  if (url.startsWith('blob:')) {
    return url
  }
  
  // 如果已经是完整的URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }
}

// 处理图片加载失败
const handleImageError = (message) => {
  console.error('图片加载失败:', message.content)
}

// 处理图片加载成功
const handleImageLoad = (message) => {
  console.log('图片加载成功:', message.content)
}

// 处理视频加载失败
const handleVideoError = (message) => {
  console.error('视频加载失败:', message.content)
}

// 处理视频加载成功
const handleVideoLoad = (message) => {
  console.log('视频加载成功:', message.content)
}

// 测试弹窗相关代码已删除

const scrollToAnimation = (target) => {
  if (!target) return
  
  // 动画滚动到目标位置
  target.scrollIntoView({ behavior: 'smooth' })
}

// 获取消息场景信息
const getMessageScene = () => {
  if (!messages.value || messages.value.length === 0) return null
  
  // 从senderType为0的最后一条消息中获取scene字段
  for (let i = messages.value.length - 1; i >= 0; i--) {
    const msg = messages.value[i]
    if ((msg.senderType === 0 || msg.from === 0) && msg.scene) {
      return msg.scene
    }
  }
  
  return null
}

// 获取数据源下的渠道来源
const getMessageChannel = () => {
  if (!messages.value || messages.value.length === 0) return null
  
  // 从senderType为0的最后一条消息中获取 channel 字段
  for (let i = messages.value.length - 1; i >= 0; i--) {
    const msg = messages.value[i]
    if ((msg.senderType === 0 || msg.from === 0) && msg.channel) {
      return msg.channel
    }
  }
  
  return null
}

// 获取消息数据源信息
const getMessageDatasource = () => {
  if (!messages.value || messages.value.length === 0) return null
  
  // 从senderType为0的最后一条消息中获取datasource字段
  for (let i = messages.value.length - 1; i >= 0; i--) {
    const msg = messages.value[i]
    if ((msg.senderType === 0 || msg.from === 0) && msg.datasource) {
      return msg.datasource
    }
  }
  
  return null
}

// 获取知识库名称
const getMessageCollectionName = () => {
  if (!messages.value || messages.value.length === 0) return null
  
  // 从senderType为0的最后一条消息中获取collectionName字段
  for (let i = messages.value.length - 1; i >= 0; i--) {
    const msg = messages.value[i]
    if ((msg.senderType === 0 || msg.from === 0) && msg.collectionName) {
      return msg.collectionName
    }
  }
  
  return null
}
// 格式化消息内容
const formatMessage = (content) => {
  // 这里可以添加你想要格式化的逻辑
  return content
}

// 获取会话数据源
const getSessionDatasource = (session) => {
  // 如果会话对象中有datasource字段，直接返回
  if (session.datasource) return session.datasource
  
  // 如果当前会话是被查看的会话，尝试从消息中获取
  if (currentSession.value && session.id === currentSession.value.id && messages.value && messages.value.length > 0) {
    // 尝试从消息中找到包含datasource字段的用户消息
    for (let i = messages.value.length - 1; i >= 0; i--) {
      const msg = messages.value[i]
      if ((msg.senderType === 0 || msg.from === 0) && msg.datasource) {
        return msg.datasource
      }
    }
  }
  
  // 默认返回
  return '默认数据源'
}

// 在已有的ref定义里添加isPollingMode状态
const isPollingMode = ref(false)
// 添加connectionStatusTimer
let connectionStatusTimer = null

// 添加检查连接状态的函数
const checkConnectionStatus = () => {
  const status = {
    isWebSocketConnected: chatStore.connected,
    isPolling: chatStore.isPolling,
    wsReadyState: chatStore.ws ? chatStore.ws.readyState : 'no-websocket',
    reconnectAttempts: chatStore.reconnectAttempts,
    currentMode: chatStore.connected ? 'websocket' : (chatStore.isPolling ? 'polling' : 'none')
  }
  
  console.log('当前连接状态:', status)
  
  // 更新UI状态
  isPollingMode.value = status.isPolling
  
  // 如果既没有WebSocket连接也没有HTTP轮询，尝试重新建立WebSocket连接
  if (!status.isWebSocketConnected && !status.isPolling && chatStore.currentConversationId) {
    console.log('没有活动连接，尝试重新建立WebSocket连接')
    chatStore.initWebSocket()
    
    // 如果5秒后WebSocket仍未连接成功，再使用最佳连接模式(可能会启动轮询)
    setTimeout(() => {
      if (!chatStore.connected && chatStore.currentConversationId) {
        console.log('WebSocket连接失败，尝试使用最佳连接模式')
        chatStore.useOptimalConnectionMode()
      }
    }, 5000)
  }
}

// 复制订单编号或手机号
const copyOrderCode = (code) => {
  // 创建临时输入框
  const tempInput = document.createElement('input')
  tempInput.value = code
  document.body.appendChild(tempInput)
  tempInput.select()
  document.execCommand('copy')
  document.body.removeChild(tempInput)
  
  // 根据长度判断是订单编号还是手机号
  if(/^1[3-9]\d{9}$/.test(code.toString())) {
    ElMessage.success('已复制')
  } else {
    ElMessage.success('已复制')
  }
}

// 查看历史记录
const viewHistory = async (session) => {
  // 阻止事件冒泡，防止触发选中会话
  event.stopPropagation()
  
  // 设置当前查看的会话ID
  historySessionId.value = session.id
  
  // 显示对话框
  showHistoryDialog.value = true
}

// 加载历史消息
const loadHistoryMessages = async () => {
  if (!historySessionId.value) return
  
  loadingHistory.value = true
  
  try {
    // 调用API获取历史消息
    const res = await getMessagesBySessionId(historySessionId.value)
    
    if (res.code === 200 && Array.isArray(res.data)) {
      // 标准化消息格式
      const normalizedMessages = res.data.map(msg => {
        // 标准化发送方字段 (from 或 senderType)
        if (msg.from === undefined && msg.senderType !== undefined) {
          msg.from = msg.senderType
        } else if (msg.from !== undefined && msg.senderType === undefined) {
          msg.senderType = msg.from
        }
        
        // 标准化消息类型字段 (type 或 msgType)
        if (msg.type === undefined && msg.msgType !== undefined) {
          msg.type = msg.msgType + 1
        } else if (msg.type !== undefined && msg.msgType === undefined) {
          msg.msgType = msg.type - 1
        }
        
        return msg
      })
      
      // 按时间排序
      const sortedMessages = normalizedMessages.sort((a, b) => 
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      )
      
      historyMessages.value = sortedMessages
      
      // 假设有更多消息可以加载（后续可根据实际情况修改）
      historyHasMore.value = false
    } else {
      console.error('加载历史消息失败:', res)
      ElMessage.error(res.message || '加载历史消息失败')
    }
  } catch (error) {
    console.error('加载历史消息失败', error)
    ElMessage.error('加载历史消息失败')
  } finally {
    loadingHistory.value = false
  }
}

// 加载更多历史消息
const loadMoreHistoryMessages = async () => {
  if (!historySessionId.value || loadingHistory.value) return
  
  loadingHistory.value = true
  
  try {
    historyCurrentPage.value++
    
    // 这里应该调用分页API，目前模拟加载更多
    const res = await getMessagesBySessionId(historySessionId.value)
    
    if (res.code === 200 && Array.isArray(res.data)) {
      // TODO: 实现实际的分页加载
      // 当前是模拟，实际上应该根据API支持情况实现
      historyHasMore.value = false
    }
  } catch (error) {
    console.error('加载更多历史消息失败', error)
    ElMessage.error('加载更多历史消息失败')
  } finally {
    loadingHistory.value = false
  }
}

// 切换FAQ项展开/折叠
const toggleFaqItem = (index) => {
  if (activeFaqItem.value === index) {
    activeFaqItem.value = null
  } else {
    activeFaqItem.value = index
  }
}

// 处理插入常见问题
const handleInsertToReply = (answer) => {
  try {
    // 通过ref调用MessageInputPanel组件的insertText方法
    if (messageInputRef.value && messageInputRef.value.insertText) {
      messageInputRef.value.insertText(answer)
      // ElMessage.success('内容已插入到输入框')
    } else {
      console.warn('MessageInputPanel组件引用不可用')
      //ElMessage.warning('插入失败，请重试')
    }
  } catch (error) {
    console.error('插入回复失败:', error)
    //ElMessage.error('插入失败，请重试')
  }
}

// 处理用户面板关闭
const handleUserPanelClose = () => {
  showUserPanel.value = false
}

// 处理搜索关键词更新
const handleSearchKeywordUpdate = (keyword) => {
  searchKeyword.value = keyword
}

// 处理会话面板关闭（移动端）
const handleSessionPanelClose = () => {
  showSessionPanel.value = false
}

// 处理图片粘贴事件
const handlePasteImage = (file) => {
  // 图片粘贴处理（如果需要特殊逻辑）
  console.log('粘贴图片:', file)
}

</script>

<style lang="scss" scoped>
.chat-container {
  height: 100%;
  display: flex;
  overflow: hidden;
  background-color: #f5f7fa;
}

.session-panel {
  width: 300px;
  border-right: 1px solid var(--el-border-color-light);
  display: flex;
  flex-direction: column;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  position: relative;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  
  .mobile-menu-btn, .mobile-user-btn {
    position: absolute;
    top: 15px;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 2;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s;
    
    &:hover {
      background-color: #fff;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
    
    .el-icon {
      font-size: 20px;
      color: #409EFF;
    }
  }
  
  .mobile-menu-btn {
    left: 15px;
  }
  
  .mobile-user-btn {
    right: 15px;
  }
  
  .no-session-selected {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-radius: 8px;
    margin: 16px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }
}

.message-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background-color: #f5f7fa;
  
  .loading-messages {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }
  
  .empty-messages {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }
  
  .message-list {
    display: flex;
    flex-direction: column;
    
    .load-more {
      display: flex;
      justify-content: center;
      margin-bottom: 20px;
    }
    
    .message-date-divider {
      margin-bottom: 20px;
      
      .date-label {
        text-align: center;
        margin-bottom: 15px;
        position: relative;
        
        &::before, &::after {
          content: '';
          position: absolute;
          top: 50%;
          width: calc(50% - 50px);
          height: 1px;
          background-color: #e0e0e0;
        }
        
        &::before {
          left: 0;
        }
        
        &::after {
          right: 0;
        }
        
        span {
          background-color: #f5f7fa;
          padding: 0 10px;
          position: relative;
          z-index: 1;
          font-size: 13px;
          color: #909399;
          border-radius: 10px;
        }
      }
    }
    
    .message-item {
      display: flex;
      margin-bottom: 16px;
      
      &.message-user {
        .message-content {
          margin-left: 12px;
          margin-right: auto;
          
          .message-body {
            background-color: #fff;
            border-radius: 0 8px 8px 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            color: #303133;
          }
        }
      }
      
      &.message-agent {
        flex-direction: row-reverse;
        
        .message-content {
          margin-right: 12px;
          margin-left: auto;
          
          .message-sender {
            text-align: right;
          }
          
          .message-body {
            background-color: #e1f3ff;
            color: #0068B7;
            border-radius: 8px 0 8px 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          }
        }
      }
      
      &.message-ai {
        flex-direction: row-reverse;
        
        .message-content {
          margin-right: 12px;
          margin-left: auto;
          
          .message-sender {
            text-align: right;
          }
          
          .message-body {
            background-color: #edf8ff;
            color: #0e5fd8;
            border-radius: 8px 0 8px 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          }
        }
      }
      
      &.message-system {
        justify-content: center;
        
        .message-content {
          max-width: 100%;
          
          .message-body {
            background-color: transparent;
            box-shadow: none;
            padding: 0;
            border-left: none;
            display: block;
          }
        }
      }
      
      .message-avatar {
        flex-shrink: 0;
        
        .el-avatar {
          border: 2px solid #fff;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
      
      .message-content {
        max-width: 70%;
        
        .message-sender {
          margin-bottom: 5px;
          font-size: 13px;
          
          .sender-name {
            color: #909399;
            margin-right: 8px;
          }
          
          .message-time {
            color: #c0c4cc;
            font-size: 12px;
          }
        }
        
        .message-body {
          position: relative;
          border-radius: 8px;
          padding: 0;
          overflow: hidden;
          
          &.status-sending {
            opacity: 0.8;
          }
          
          .message-text {
            padding: 10px 15px;
            word-break: break-word;
            line-height: 1.5;
          }
          
          .message-image {
            max-width: 300px;
            overflow: hidden;
            border-radius: 8px;
            
            .el-image {
              display: block;
              width: 100%;
              border-radius: 8px;
              overflow: hidden;
            }
            
            .image-error, .image-loading {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              height: 150px;
              background-color: #f5f7fa;
              color: #909399;
              padding: 20px;
              border-radius: 8px;
              
              .el-icon {
                font-size: 32px;
                margin-bottom: 10px;
              }
            }
          }
          
          /* 新增：视频消息样式，确保与图片消息一致的对齐与展示 */
          .message-video {
            max-width: 300px; /* 与图片消息一致的最大宽度 */
            overflow: hidden;
            border-radius: 8px;
            background: #f5f5f5;
            display: block; /* 防止作为内联元素受 text-align 影响 */
            margin: 0 !important; /* 显式取消任何 auto 居中 */
            
            video {
              display: block !important; /* 防止被全局样式居中 */
              width: 100%;    /* 填充容器宽度 */
              height: auto;   /* 保持比例 */
              margin: 0 !important;      /* 防止 margin: 0 auto 导致居中 */
              border-radius: 8px;
              background: #f5f5f5;
            }
          }

          /* 新增：确保客服侧视频容器整体靠右（与图片一致） */
          .message-item.message-agent .message-content .message-body .message-video {
            margin-left: auto !important; /* 靠右对齐 */
            margin-right: 0 !important;
          }
          
          .message-status {
            position: absolute;
            right: 5px;
            bottom: 5px;
            font-size: 12px;
            color: #c0c4cc;
          }
        }
      }
    }
  }
}

.message-input {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
  background-color: #fff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  
  .quick-replies {
    margin-bottom: 15px;
    
    .el-scrollbar {
      height: 40px;
      
      .reply-list {
        display: flex;
        align-items: center;
        padding: 4px 0;
        
        .el-button {
          margin-right: 8px;
          font-size: 12px;
          height: 30px;
          padding: 0 14px;
          background-color: #f5f7fa;
          border: none;
          color: #606266;
          font-weight: normal;
          transition: all 0.3s;
          
          &:hover {
            background-color: #ecf5ff;
            color: var(--el-color-primary);
          }
        }
      }
    }
  }
  
  .message-input-container {
    display: flex;
    height: 70px;
    border: 1px solid #dcdfe6;
    border-radius: 8px;
    background-color: #fff;
    overflow: hidden;
    transition: all 0.3s;
    
    &:focus-within {
      border-color: var(--el-color-primary);
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    }
    
    .left-tools-wrapper {
      width: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f9f9f9;
      border-right: 1px solid #f0f0f0;
      
      .tool-btn {
        color: #606266;
        
        &:hover {
          color: var(--el-color-primary);
        }
        
        .el-icon {
          font-size: 20px;
        }
      }
    }
    
    .message-input-wrapper {
      flex: 1;
      
      .el-textarea {
        height: 100%;
        
        :deep(.el-textarea__inner) {
          border: none;
          height: 70px;
          min-height: 70px;
          padding: 10px 15px;
          resize: none;
          font-size: 14px;
          line-height: 24px;
          
          &:focus {
            outline: none;
            box-shadow: none;
          }
        }
      }
    }
    
    .send-btn-wrapper {
      width: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f9f9f9;
      border-left: 1px solid #f0f0f0;
      
      .send-btn {
        width: 60px;
        height: 36px;
        border-radius: 4px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--el-color-primary);
        border: none;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s;
        
        &:hover, &:focus {
          background-color: var(--el-color-primary-light-3);
        }
        
        &:disabled {
          background-color: #c0c4cc;
          color: #fff;
        }
        
        .el-icon {
          margin-right: 4px;
          font-size: 16px;
        }
      }
    }
  }
}

.session-ended, .session-waiting {
  padding: 20px;
  text-align: center;
  background-color: #fff;
  margin: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.common-questions {
  padding: 10px;
  
  h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
    color: #303133;
    font-weight: 500;
  }
  
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    
    li {
      padding: 8px 12px;
      cursor: pointer;
      border-radius: 4px;
      transition: background-color 0.3s;
      font-size: 13px;
      
      &:hover {
        background-color: #ecf5ff;
        color: var(--el-color-primary);
      }
    }
  }
}

.user-panel {
  width: 320px;
  background-color: #f4f7fc;
  border-left: 1px solid #e0e5ea;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  max-height: 100vh;
  transition: all 0.3s ease;
  
  .panel-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e0e5ea;
    background-color: #fff;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .user-info-placeholder {
    padding: 20px;
    
    .el-empty {
      padding: 40px 0;
    }
  }
  
  .user-info-content {
    padding: 10px 15px;
    max-height: calc(100vh - 400px);
    overflow-y: auto;
    
    .user-orders-info {
      margin-top: 10px;
      
      h4 {
        margin: 0 0 10px 0;
        font-size: 14px;
        color: #333;
      }
      
      h5 {
        margin: 0;
        font-size: 13px;
        color: #409EFF;
      }
      
      .current-order {
        margin-bottom: 15px;
        padding: 12px;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        background-color: #f0f9ff;
        border-left: 3px solid #409EFF;
        
        .current-order-card {
          .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
            
            .order-code {
              font-size: 12px;
              font-weight: bold;
              color: #333;
              cursor: pointer;
              transition: all 0.3s;
              border-bottom: 1px dashed #ccc;
              padding-bottom: 2px;
              
              &:hover {
                color: #409EFF;
                border-bottom-color: #409EFF;
              }
            }
          }
          
          .item-content {
            .item-row {
              display: flex;
              gap: 16px; /* 调整标签与值之间的间距 */
              margin-bottom: 5px;
              font-size: 12px;
              
              .item-label {
                width: 30px;
                color: #909399;
              }
              
              .item-value {
                flex: 1;
                color: #333;
                word-break: break-all;
                
                &.price {
                  color: #F56C6C;
                  font-weight: bold;
                }
              }
            }
          }
        }
      }
      
      .recycle-orders {
        margin-bottom: 0;
        padding: 12px;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        background-color: #f0f9ff;
        border-left: 3px solid #409EFF;
        
        .recycle-carousel {
          margin: 2px 0 0;
          max-height: 180px;
          overflow-y: auto;
          
          .el-carousel {
            margin-bottom: 0;
            border-radius: 8px;
            overflow: hidden;
            
            .el-carousel__indicators {
              display: none;
            }
            
            .el-carousel__arrow {
              background-color: rgba(64, 158, 255, 0.7);
              
              &:hover {
                background-color: rgba(64, 158, 255, 0.9);
              }
            }
          }
          
          .recycle-item-card {
            height: 100%;
            padding: 8px;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            background-color: #f9f9f9;
            display: flex;
            flex-direction: column;
            
            &.flea-market-item-card {
              background-color: #f9f0ff;
              border-left: 3px solid #9254de;
              
              .order-code {
                color: #9254de;
                border-bottom-color: #9254de;
                
                &:hover {
                  color: #b37feb;
                  border-bottom-color: #b37feb;
                }
              }
            }
            
            &.market-item-card {
              background-color: #f0fff9;
              border-left: 3px solid #52c41a;
              
              .order-code {
                color: #52c41a;
                border-bottom-color: #52c41a;
                
                &:hover {
                  color: #73d13d;
                  border-bottom-color: #73d13d;
                }
              }
            }
            
            &.engineer-item-card {
              background-color: #fff9f0;
              border-left: 3px solid #fa8c16;
              
              .order-code {
                color: #fa8c16;
                border-bottom-color: #fa8c16;
                
                &:hover {
                  color: #ffa940;
                  border-bottom-color: #ffa940;
                }
              }
            }
            
            .item-header {
              margin-bottom: 6px;
              padding-bottom: 5px;
              border-bottom: 1px solid #eee;
              
              .order-code {
                font-size: 12px;
                font-weight: bold;
                color: #333;
                cursor: pointer;
                transition: all 0.3s;
                border-bottom: 1px dashed #ccc;
                padding-bottom: 2px;
                
                &:hover {
                  color: #409EFF;
                  border-bottom-color: #409EFF;
                }
              }
            }
            
            .item-content {
              flex: 1;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              
              .item-row {
                display: flex;
                margin-bottom: 7px;
                font-size: 12px;
                
                .item-label {
                  width: 60px;
                  color: #909399;
                }
                
                .item-value {
                  flex: 1;
                  color: #333;
                  
                  &.price {
                    color: #F56C6C;
                    font-weight: bold;
                  }
                }
              }
            }
          }
        }
      }
      
      .orders-info-container {
        overflow-y: auto;
        max-height: calc(100vh - 300px);
        
        .info-item {
          display: flex;
          margin-bottom: 10px;
          
          .info-label {
            width: 80px;
            color: #909399;
            font-size: 13px;
          }
          
          .info-value {
            flex: 1;
            color: #333;
            font-size: 13px;
            word-break: break-all;
          }
        }
      }
    }
    
    .user-basic-info {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      
      .el-avatar {
        flex-shrink: 0;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        border: 2px solid #fff;
      }
      
      .user-name-info {
        margin-left: 12px;
        overflow: hidden;
        
        h4 {
          margin: 0 0 5px;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          
          .datasource-tag {
            margin-left: 8px;
            transform: scale(0.9);
            vertical-align: middle;
          }
        }
        
        p {
          margin: 5px 0 0;
          font-size: 12px;
          color: #909399;
          
          &.phone-row {
            margin: 3px 0 0;
  
            .phone-number {
              display: inline-flex;
              align-items: center;
              padding: 2px 5px;
              background-color: #f0f9ff;
              border-radius: 12px;
              cursor: pointer;
              transition: all 0.3s;
              border: 1px dashed #409EFF;
              
              &:hover {
                background-color: #ecf5ff;
                transform: translateY(-1px);
                box-shadow: 0 1px 4px rgba(64, 158, 255, 0.2);
              }
              
              .el-icon {
                font-size: 12px;
                color: #409EFF;
                margin-right: 4px;
              }
              
              span {
                color: #409EFF;
                font-size: 12px;
                font-weight: normal;
              }
            }
          }
        }
      }
    }
    
    .el-divider {
      margin: 8px 0;
    }
    
    .user-detail-info, .session-stats, .session-source-info {
      background-color: #fff;
      border-radius: 8px;
      padding: 15px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      margin-bottom: 15px;
      
      h4 {
        margin-top: 0;
        margin-bottom: 10px;
        font-size: 14px;
        font-weight: 500;
        color: #303133;
      }
      
      .info-item, .stat-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px dashed #e4e7ed;
        
        &:last-child {
          border-bottom: none;
        }
        
        .info-label, .stat-label {
          font-size: 13px;
          color: #909399;
        }
        
        .info-value, .stat-value {
          font-size: 13px;
          font-weight: 500;
          color: #303133;
          max-width: 150px;
          text-align: right;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  
  .mobile-close-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: #f5f7fa;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1;
    
    &:hover {
      background-color: #e0e0e0;
    }
    
    .el-icon {
      font-size: 18px;
      color: #909399;
    }
  }
  
  .faq-card {
    margin: 30px 15px 15px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    overflow: hidden;
    
    /* 紧凑型头部 */
    .faq-compact-header {
      padding: 10px 12px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #ebeef5;
      
      .faq-title {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 500;
        color: #303133;
        
        .el-icon {
          margin-right: 5px;
          color: #409EFF;
          font-size: 16px;
        }
      }
      
      .faq-search-compact {
        width: 180px;
        
        .el-input {
          font-size: 12px;
          
          .el-input__inner {
            height: 28px;
            line-height: 28px;
          }
          
          .el-input__prefix,
          .el-input__suffix {
            display: flex;
            align-items: center;
          }
          
          .search-button,
          .add-button {
            cursor: pointer;
            font-size: 14px;
            padding: 0 5px;
            
            &:hover {
              color: #409EFF;
            }
          }
          
          .add-button {
            color: #409EFF;
          }
        }
      }
    }
    
    /* 紧凑型分类导航 */
    .faq-tabs-compact {
      border-bottom: 1px solid #ebeef5;
      
      .faq-category-nav {
        display: flex;
        white-space: nowrap;
        padding: 0 10px;
        
        .faq-category-item {
          padding: 8px 12px;
          font-size: 12px;
          cursor: pointer;
          color: #606266;
          position: relative;
          transition: all 0.3s;
          
          &:hover {
            color: #409EFF;
          }
          
          &.active {
            color: #409EFF;
            font-weight: 500;
            
            &:after {
              content: '';
              position: absolute;
              bottom: 0;
              left: 50%;
              transform: translateX(-50%);
              width: 20px;
              height: 2px;
              background-color: #409EFF;
              border-radius: 1px;
            }
          }
        }
      }
    }
    
    /* 紧凑型内容区域 */
    .faq-content-compact {
      flex: 1;
      overflow-y: auto;
      padding: 8px 0;
      max-height: 250px;
      
      .empty-faq {
        display: flex;
        justify-content: center;
        padding: 20px 0;
      }
      
      .faq-list {
        .faq-item {
          border-bottom: 1px solid #f0f0f0;
          
          &:last-child {
            border-bottom: none;
          }
          
          .faq-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s;
            
            &:hover {
              background-color: #f5f7fa;
            }
            
            .faq-item-title {
              display: flex;
              align-items: center;
              flex: 1;
              
              .el-tag {
                margin-right: 8px;
                font-size: 10px;
                transform: scale(0.9);
                transform-origin: left;
              }
              
              span {
                font-size: 12px;
                color: #303133;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
            
            .el-icon {
              color: #909399;
              transition: transform 0.3s;
              font-size: 14px;
              
              &.is-active {
                transform: rotate(90deg);
                color: #409EFF;
              }
            }
          }
          
          .faq-item-content {
            padding: 0 12px 10px;
            font-size: 12px;
            color: #606266;
            line-height: 1.5;
            background-color: #f9f9f9;
            border-top: 1px dashed #ebeef5;
            
            p {
              margin: 10px 0;
            }
            
            .faq-item-actions {
              display: flex;
              justify-content: flex-end;
              padding-top: 8px;
              margin-top: 8px;
              
              .el-button {
                padding: 4px 8px;
                font-size: 12px;
                
                .el-icon {
                  margin-right: 2px;
                }
              }
            }
          }
        }
      }
      
      .faq-pagination-compact {
        padding: 8px 0 5px;
        display: flex;
        justify-content: center;
        
        .el-pagination {
          font-size: 12px;
          
          .btn-prev,
          .btn-next,
          .number {
            min-width: 24px;
            height: 24px;
            line-height: 24px;
          }
        }
      }
    }
    
    /* 原有的样式部分 */
    .faq-search {
      padding: 0px 15px 10px;
      border-bottom: 1px solid #ebeef5;
      background-color: transparent;
    }
    
    .faq-header {
      padding: 12px 15px 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: none;
      
      h4 {
        margin: 0;
        font-size: 14px;
        font-weight: 500;
        color: #303133;
        display: flex;
        align-items: center;
        
        .el-icon {
          margin-right: 8px;
          color: #409EFF;
          font-size: 16px;
        }
      }
    }
    
    .faq-tabs {
      .el-tabs__header {
        margin: 0;
        padding: 0 15px;
        background-color: #f9f9f9;
      }
      
      .el-tabs__nav {
        border: none;
      }
      
      .el-tabs__item {
        height: 36px;
        line-height: 36px;
        font-size: 13px;
        color: #606266;
        
        &.is-active {
          color: #409EFF;
        }
      }
      
      .el-tabs__active-bar {
        background-color: #409EFF;
      }
    }
    
    .faq-content {
      padding: 15px;
      min-height: 150px;
      max-height: 300px;
      overflow-y: auto;
      
      .empty-faq {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 150px;
      }
      
      .el-collapse {
        border: none;
        
        .el-collapse-item {
          margin-bottom: 8px;
          border: 1px solid #ebeef5;
          border-radius: 4px;
          overflow: hidden;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .el-collapse-item__header {
            padding: 0 15px;
            font-size: 13px;
            color: #303133;
            background-color: #f5f7fa;
            border-bottom: none;
            
            &.is-active {
              color: #409EFF;
              border-bottom: 1px solid #ebeef5;
            }
          }
          
          .el-collapse-item__content {
            padding: 10px 15px;
          }
          
          .faq-question {
            display: flex;
            align-items: center;
            
            .el-tag {
              margin-right: 8px;
              transform: scale(0.85);
              transform-origin: left;
            }
            
            .question-text {
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
          
          .faq-answer {
            font-size: 13px;
            color: #606266;
            line-height: 1.5;
            word-break: break-all;
            
            p {
              margin: 0 0 10px;
            }
            
            .faq-actions {
              display: flex;
              justify-content: flex-end;
              margin-top: 8px;
              
              .el-button {
                padding: 4px 8px;
                
                & + .el-button {
                  margin-left: 10px;
                }
              }
            }
          }
        }
      }
      
      .faq-pagination {
        margin-top: 15px;
        display: flex;
        justify-content: center;
      }
    }
  }
}

/* 媒体查询，响应式设计 */
@media (max-width: 1200px) {
  .user-panel {
    width: 300px;
  }
}

@media (max-width: 992px) {
  .session-panel {
    width: 250px;
  }
  
  .user-panel {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .session-panel {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 10;
    background-color: #fff;
    transform: translateX(-100%);
    transition: transform 0.3s;
    width: 80%;
    max-width: 300px;
  }
  
  .session-panel.mobile-show {
    transform: translateX(0);
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  }
  
  .user-panel {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 10;
    background-color: #fff;
    transform: translateX(100%);
    transition: transform 0.3s;
    width: 80%;
    max-width: 300px;
  }
  
  .user-panel.mobile-show {
    transform: translateX(0);
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  }
  
  .message-container .message-list .message-item .message-content {
    max-width: 85%;
  }
  
  .chat-main {
    .chat-header {
      margin-top: 10px;
    }
  }
}

/* 添加过渡效果 */
.session-panel, .user-panel {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* 添加自定义标签样式 */
.custom-tags {
  .el-tag {
    border-radius: 12px;
    padding: 0 8px;
  }
}

.image-preview {
  margin-top: 10px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  
  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 15px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
    
    span {
      font-size: 14px;
      color: #606266;
      font-weight: 500;
    }
    
    .el-button {
      padding: 6px;
      border-radius: 4px;
      
      .el-icon {
        font-size: 16px;
      }
    }
  }
  
  .preview-content {
    padding: 15px;
    display: flex;
    justify-content: center;
    
    .el-image {
      max-height: 150px;
      border-radius: 4px;
      overflow: hidden;
    }
  }
}

/* 添加连接状态指示器样式 */
.connection-status {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 999;
  font-size: 12px;
  
  .polling-mode-indicator,
  .websocket-mode-indicator {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }
  
  .polling-mode-indicator {
    background-color: #e6a23c;
  }
  
  .websocket-mode-indicator {
    background-color: #67c23a;
  }
}
/* 历史记录对话框样式 */
.history-dialog-content {
  height: 65vh;
  overflow-y: auto;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.history-loading, .history-empty {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.history-message-list {
  display: flex;
  flex-direction: column;
}

.history-load-more {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.history-message-item {
  display: flex;
  margin-bottom: 16px;
  
  &.history-message-user {
    justify-content: flex-start;
    
    .history-message-content {
      max-width: 70%;
      
      .history-message-body {
        background-color: #fff;
        border-radius: 0 8px 8px 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        color: #303133;
      }
    }
  }
  
  &.history-message-agent {
    justify-content: flex-end;
    
    .history-message-content {
      max-width: 70%;
      
      .history-message-sender {
        text-align: right;
      }
      
      .history-message-body {
        background-color: #e1f3ff;
        color: #0068B7;
        border-radius: 8px 0 8px 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }
    }
  }
  
  &.history-message-ai {
    justify-content: flex-end;
    
    .history-message-content {
      max-width: 70%;
      
      .history-message-sender {
        text-align: right;
      }
      
      .history-message-body {
        background-color: #edf8ff;
        color: #0e5fd8;
        border-radius: 8px 0 8px 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }
    }
  }
  
  &.history-message-system {
    justify-content: center;
    
    .history-message-content {
      max-width: 80%;
      
      .history-message-body {
        background-color: rgba(144, 147, 153, 0.1);
        color: #909399;
        text-align: center;
        padding: 6px 15px;
        font-size: 13px;
        border-radius: 16px;
        box-shadow: none;
      }
    }
  }
  
  .history-message-content {
    display: flex;
    flex-direction: column;
    
    .history-message-sender {
      margin-bottom: 5px;
      font-size: 13px;
      
      .history-sender-name {
        color: #909399;
        margin-right: 8px;
      }
      
      .history-message-time {
        color: #c0c4cc;
        font-size: 12px;
      }
    }
    
    .history-message-body {
      position: relative;
      border-radius: 8px;
      padding: 0;
      overflow: hidden;
      
      .history-message-text {
        padding: 10px 15px;
        word-break: break-word;
        line-height: 1.5;
      }
      
      .history-message-image {
        max-width: 300px;
        overflow: hidden;
        border-radius: 8px;
        
        .el-image {
          display: block;
          width: 100%;
          border-radius: 8px;
          overflow: hidden;
        }
        
        .image-error, .image-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 150px;
          background-color: #f5f7fa;
          color: #909399;
          padding: 20px;
          border-radius: 8px;
        }
      }
      
      .history-message-system {
        padding: 10px 15px;
        font-size: 13px;
        color: #909399;
      }
    }
  }
}

.system-message {
  background-color: #ecf5ff !important;
  color: #409EFF !important;
  text-align: center;
  padding: 10px 20px !important;
  font-size: 14px !important;
  border-radius: 10px !important;
  border-left: 4px solid #409EFF !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08) !important;
  display: flex;
  align-items: center;
  
  .system-icon {
    margin-right: 8px;
    display: flex;
    align-items: center;
    
    .el-icon {
      font-size: 16px;
      color: #409EFF;
    }
  }
}
</style> 