<template>
  <div class="session-panel">
    <!-- 面板头部 -->
    <div class="panel-header">
      <h3>会话列表</h3>
      <div class="header-actions">
        <el-button 
          @click="refreshSessions" 
          :icon="Refresh" 
          size="small" 
          type="primary" 
          text
        >
          刷新
        </el-button>
        <!-- 移动端关闭按钮 -->
        <el-button 
          v-if="!isDesktop" 
          @click="closePanel"
          :icon="Close" 
          size="small" 
          type="danger" 
          text
        >
          关闭
        </el-button>
      </div>
    </div>

    <!-- 搜索框 -->
    <div class="panel-search">
      <el-input
        v-model="localSearchKeyword"
        @input="handleSearchInput"
        @clear="handleSearchClear"
        placeholder="搜索用户昵称、消息内容或用户ID"
        clearable
        size="default"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <!-- 会话Tab切换 -->
    <el-tabs v-model="activeTab" class="session-tabs" @tab-change="handleTabChange">
      <!-- 待处理会话 -->
      <el-tab-pane name="waiting">
        <template #label>
          <span class="tab-label">
            待处理
            <el-badge 
              v-if="waitingSessions.length > 0" 
              :value="waitingSessions.length" 
              :max="99"
              class="tab-badge"
            />
          </span>
        </template>
        
        <div class="session-list" v-loading="loading">
          <div v-if="waitingSessions.length === 0" class="empty-state">
            <el-icon class="empty-icon"><ChatDotSquare /></el-icon>
            <p>暂无待处理会话</p>
          </div>
          <div
            v-for="session in waitingSessions"
            :key="session.id"
            class="session-item"
            :class="{ 'active': currentSession?.id === session.id }"
            @click="selectSession(session)"
          >
            <div class="session-avatar">
              <el-avatar
                :size="45"
                :src="getAvatarUrl(session)"
                :alt="getUserDisplayName(session) || '未知用户'"
              >
                <img src="/src/assets/images/userAvatar.png" alt="默认头像" />
              </el-avatar>
              <div class="online-status" :class="getStatusClass(session)"></div>
            </div>
            
            <div class="session-info">
              <div class="session-header">
                <span class="user-nickname">{{ getUserDisplayName(session) || '未知用户' }}</span>
                <span class="session-time">{{ formatTime(session.lastActiveTime) }}</span>
              </div>
              
              <div class="session-content">
                <span class="last-message">{{ session.lastMessage || '暂无消息' }}</span>
              </div>
              
              <div class="session-meta">
                <el-tag v-if="session.dataSource" size="small" type="info">
                  {{ session.dataSource }}
                </el-tag>
                <span class="waiting-time" v-if="session.waitingTime > 0">
                  等待: {{ formatWaitingTime(session.waitingTime) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 进行中会话 -->
      <el-tab-pane name="active">
        <template #label>
          <span class="tab-label">
            进行中
            <el-badge 
              v-if="activeSessions.length > 0" 
              :value="activeSessions.length" 
              :max="99"
              class="tab-badge"
            />
          </span>
        </template>
        
        <div class="session-list" v-loading="loading">
          <div v-if="activeSessions.length === 0" class="empty-state">
            <el-icon class="empty-icon"><ChatDotSquare /></el-icon>
            <p>暂无进行中会话</p>
          </div>
          <div
            v-for="session in activeSessions"
            :key="session.id"
            class="session-item"
            :class="{ 'active': currentSession?.id === session.id }"
            @click="selectSession(session)"
          >
            <div class="session-avatar">
              <el-avatar
                :size="45"
                :src="getAvatarUrl(session)"
                :alt="getUserDisplayName(session) || '未知用户'"
              >
                <img src="/src/assets/images/userAvatar.png" alt="默认头像" />
              </el-avatar>
              <div class="online-status" :class="getStatusClass(session)"></div>
            </div>
            
            <div class="session-info">
              <div class="session-header">
                <span class="user-nickname">{{ getUserDisplayName(session) || '未知用户' }}</span>
                <span class="session-time">{{ formatTime(session.lastActiveTime) }}</span>
              </div>
              
              <div class="session-content">
                <span class="last-message">{{ session.lastMessage || '暂无消息' }}</span>
              </div>
              
              <div class="session-meta">
                <el-tag v-if="session.dataSource" size="small" type="success">
                  {{ session.dataSource }}
                </el-tag>
                <span class="agent-name" v-if="session.agentName">
                  客服: {{ session.agentName }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- AI会话 -->
      <el-tab-pane name="ai">
        <template #label>
          <span class="tab-label">
            AI会话
            <el-badge 
              v-if="aiSessions.length > 0" 
              :value="aiSessions.length" 
              :max="99"
              class="tab-badge"
            />
          </span>
        </template>
        
        <div class="session-list" v-loading="loading">
          <div v-if="aiSessions.length === 0" class="empty-state">
            <el-icon class="empty-icon"><ChatDotSquare /></el-icon>
            <p>暂无AI会话</p>
          </div>
          <div
            v-for="session in aiSessions"
            :key="session.id"
            class="session-item"
            :class="{ 'active': currentSession?.id === session.id }"
            @click="selectSession(session)"
          >
            <div class="session-avatar">
              <el-avatar
                :size="45"
                :src="getAvatarUrl(session)"
                :alt="getUserDisplayName(session) || '未知用户'"
              >
                <img src="/src/assets/images/userAvatar.png" alt="默认头像" />
              </el-avatar>
              <div class="online-status ai-status"></div>
            </div>
            
            <div class="session-info">
              <div class="session-header">
                <span class="user-nickname">{{ getUserDisplayName(session) || '未知用户' }}</span>
                <span class="session-time">{{ formatTime(session.lastActiveTime) }}</span>
              </div>
              
              <div class="session-content">
                <span class="last-message">{{ session.lastMessage || '暂无消息' }}</span>
              </div>
              
              <div class="session-meta">
                <!-- 数据源标签 -->
                <el-tag v-if="getDataSource(session)" size="small" type="primary">
                  {{ getDataSource(session) }}
                </el-tag>
                
                <!-- AI服务标签 -->
                <el-tag size="small" type="primary">AI服务</el-tag>
                
                <!-- 历史记录按钮 -->
                <el-button 
                  type="text" 
                  size="small" 
                  :icon="Document" 
                  @click.stop="viewHistory(session)"
                  title="查看历史记录"
                  class="history-btn"
                />
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, computed, watch, defineProps, defineEmits } from 'vue'
import { 
  Search, Refresh, Close, ChatDotSquare, Document
} from '@element-plus/icons-vue'
import moment from 'moment'

// Props定义
const props = defineProps({
  sessions: {
    type: Array,
    default: () => []
  },
  currentSession: {
    type: Object,
    default: null
  },
  searchKeyword: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  },
  isDesktop: {
    type: Boolean,
    default: true
  },
  visible: {
    type: Boolean,
    default: true
  }
})

// Emits定义
const emit = defineEmits([
  'select-session',
  'update:search-keyword', 
  'refresh-sessions',
  'close',
  'view-history'
])

// 内部状态
const activeTab = ref('waiting')
const localSearchKeyword = ref('')

// 监听外部searchKeyword变化
watch(() => props.searchKeyword, (newValue) => {
  localSearchKeyword.value = newValue
}, { immediate: true })

// 计算属性：待处理会话
const waitingSessions = computed(() => {
  let result = props.sessions.filter(session => session.status === 0)
  
  // 处理等待时间
  result = result.map(session => {
    if (!session.waitingTime || isNaN(Number(session.waitingTime))) {
      const startTime = new Date(session.lastActiveTime || session.startTime || session.createTime || Date.now())
      const waitingTimeSeconds = Math.floor((Date.now() - startTime.getTime()) / 1000)
      return {
        ...session,
        waitingTime: waitingTimeSeconds > 0 ? waitingTimeSeconds : 0
      }
    }
    return session
  })
  
  // 应用搜索过滤
  if (props.searchKeyword) {
    const keyword = props.searchKeyword.toLowerCase()
    result = result.filter(session => 
      (getUserDisplayName(session) && getUserDisplayName(session).toLowerCase().includes(keyword)) ||
      (session.lastMessage && session.lastMessage.toLowerCase().includes(keyword)) ||
      session.userId.toString().includes(keyword)
    )
  }
  
  // 按最后活跃时间排序
  return result.sort((a, b) => {
    return new Date(b.lastActiveTime || 0) - new Date(a.lastActiveTime || 0)
  })
})

// 计算属性：进行中会话  
const activeSessions = computed(() => {
  let result = props.sessions.filter(session => session.status === 1)
  
  // 应用搜索过滤
  if (props.searchKeyword) {
    const keyword = props.searchKeyword.toLowerCase()
    result = result.filter(session => 
      (getUserDisplayName(session) && getUserDisplayName(session).toLowerCase().includes(keyword)) ||
      (session.lastMessage && session.lastMessage.toLowerCase().includes(keyword)) ||
      session.userId.toString().includes(keyword)
    )
  }
  
  // 按最后活跃时间排序
  return result.sort((a, b) => {
    return new Date(b.lastActiveTime) - new Date(a.lastActiveTime)
  })
})

// 计算属性：AI会话
const aiSessions = computed(() => {
  let result = props.sessions.filter(session => session.status === 3)
  
  // 应用搜索过滤
  if (props.searchKeyword) {
    const keyword = props.searchKeyword.toLowerCase()
    result = result.filter(session => 
      (getUserDisplayName(session) && getUserDisplayName(session).toLowerCase().includes(keyword)) ||
      (session.lastMessage && session.lastMessage.toLowerCase().includes(keyword)) ||
      session.userId.toString().includes(keyword)
    )
  }
  
  // 按最后活跃时间排序
  return result.sort((a, b) => {
    return new Date(b.lastActiveTime) - new Date(a.lastActiveTime)
  })
})

// 方法：选择会话
const selectSession = (session) => {
  emit('select-session', session)
}

// 方法：处理搜索输入
const handleSearchInput = () => {
  emit('update:search-keyword', localSearchKeyword.value)
}

// 方法：处理搜索清空
const handleSearchClear = () => {
  localSearchKeyword.value = ''
  emit('update:search-keyword', '')
}

// 方法：刷新会话列表
const refreshSessions = () => {
  emit('refresh-sessions')
}

// 方法：关闭面板
const closePanel = () => {
  emit('close')
}

// 方法：处理Tab切换
const handleTabChange = (tabName) => {
  activeTab.value = tabName
}

// 方法：格式化时间
const formatTime = (timeString) => {
  if (!timeString) return ''
  const time = moment(timeString)
  const now = moment()
  
  if (now.diff(time, 'minutes') < 1) {
    return '刚刚'
  } else if (now.diff(time, 'hours') < 1) {
    return `${now.diff(time, 'minutes')}分钟前`
  } else if (now.diff(time, 'days') < 1) {
    return `${now.diff(time, 'hours')}小时前`
  } else if (now.diff(time, 'days') < 7) {
    return `${now.diff(time, 'days')}天前`
  } else {
    return time.format('MM-DD HH:mm')
  }
}

// 方法：格式化等待时间
const formatWaitingTime = (seconds) => {
  if (!seconds || seconds < 0) return '0秒'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${remainingSeconds}秒`
  } else {
    return `${remainingSeconds}秒`
  }
}

// 方法：获取头像URL
const getAvatarUrl = (session) => {
  if (session.userAvatar) {
    return session.userAvatar.startsWith('http') ? session.userAvatar : `${process.env.VUE_APP_BASE_URL || '/'}${session.userAvatar}`
  }
  return '/src/assets/images/userAvatar.png'
}

// 方法：获取状态样式类
const getStatusClass = (session) => {
  switch (session.status) {
    case 0: return 'waiting'
    case 1: return 'active'  
    case 3: return 'ai'
    default: return 'offline'
  }
}

// 方法：获取用户显示名称（优化版）
const getUserDisplayName = (session) => {
  // 优先级1：session.userNickname
  if (session.userNickname && session.userNickname.trim()) {
    return session.userNickname.trim()
  }
  
  // 优先级2：session.user.nickname
  if (session.user && session.user.nickname && session.user.nickname.trim()) {
    return session.user.nickname.trim()
  }
  
  // 优先级3：session.user.phone
  if (session.user && session.user.phone && session.user.phone.trim()) {
    return session.user.phone.trim()
  }
  
  // 优先级4：根据userId生成访客名称
  if (session.userId) {
    return `访客${session.userId}`
  }
  
  // 最后兜底
  return '未知用户'
}

// 方法：获取数据源（优化版）
const getDataSource = (session) => {
  // 优先级1：session.dataSource
  if (session.dataSource && session.dataSource.trim()) {
    return session.dataSource.trim()
  }
  
  // 优先级2：session.datasource (兼容性)
  if (session.datasource && session.datasource.trim()) {
    return session.datasource.trim()
  }
  
  // 优先级3：从用户信息获取
  if (session.user && session.user.dataSource) {
    return session.user.dataSource
  }
  
  // 兜底：返回null
  return null
}

// 方法：查看历史记录
const viewHistory = (session) => {
  emit('view-history', session)
}
</script>

<style lang="scss" scoped>
.session-panel {
  width: 300px;
  border-right: 1px solid var(--el-border-color-light);
  display: flex;
  flex-direction: column;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  position: relative;
  
  .panel-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--el-border-color-light);
    background-color: #f8f9fa;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
    
    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .panel-search {
    padding: 12px 15px;
    border-bottom: 1px solid var(--el-border-color-light);
    background-color: #fff;
    
    .el-input {
      .el-input__wrapper {
        box-shadow: 0 0 0 1px #e0e3e9 inset;
        border-radius: 20px;
        padding: 0 12px;
        
        &:hover {
          box-shadow: 0 0 0 1px #c0c4cc inset;
        }
        
        &.is-focus {
          box-shadow: 0 0 0 1px var(--el-color-primary) inset;
        }
      }
      
      .el-input__inner {
        color: #606266;
        font-size: 13px;
      }
    }
  }
  
  .session-tabs {
    flex: 1;
    overflow: hidden;
    
    :deep(.el-tabs__header) {
      margin: 0;
      background-color: #f8f9fa;
      border-bottom: 1px solid #e4e7ed;
    }
    
    :deep(.el-tabs__nav) {
      padding: 0;
      width: 100%;
      display: flex;
      border: none;
    }
    
    :deep(.el-tabs__item) {
      flex: 1;
      text-align: center;
      font-size: 14px;
      height: 40px;
      color: #606266;
      font-weight: 500;
      transition: all 0.3s;
      position: relative;
      
      &.is-active {
        color: var(--el-color-primary);
        background-color: #fff;
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 2px;
          background-color: var(--el-color-primary);
        }
      }
      
      .tab-label {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        
        .tab-badge {
          :deep(.el-badge__content) {
            font-size: 10px;
            height: 16px;
            line-height: 16px;
            padding: 0 4px;
            min-width: 16px;
          }
        }
      }
    }
    
    :deep(.el-tabs__content) {
      padding: 0;
      height: calc(100% - 40px);
      overflow: hidden;
    }
    
    :deep(.el-tab-pane) {
      height: 100%;
    }
  }
  
  .session-list {
    height: 100%;
    overflow-y: auto;
    background-color: #fff;
    
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #909399;
      
      .empty-icon {
        font-size: 48px;
        margin-bottom: 12px;
        opacity: 0.5;
      }
      
      p {
        margin: 0;
        font-size: 14px;
      }
    }
    
    .session-item {
      display: flex;
      padding: 12px 15px;
      border-bottom: 1px solid #f0f2f5;
      cursor: pointer;
      transition: all 0.2s;
      position: relative;
      
      &:hover {
        background-color: #f5f7fa;
      }
      
      &.active {
        background-color: #e1f3ff;
        border-color: #409eff;
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 3px;
          background-color: var(--el-color-primary);
        }
      }
      
      .session-avatar {
        position: relative;
        margin-right: 12px;
        
        .online-status {
          position: absolute;
          bottom: 2px;
          right: 2px;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          border: 2px solid #fff;
          
          &.waiting {
            background-color: #f56c6c;
          }
          
          &.active {
            background-color: #67c23a;
          }
          
          &.ai {
            background-color: #409eff;
          }
          
          &.offline {
            background-color: #c0c4cc;
          }
        }
      }
      
      .session-info {
        flex: 1;
        min-width: 0;
        
        .session-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;
          
          .user-nickname {
            font-weight: 500;
            color: #303133;
            font-size: 14px;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          
          .session-time {
            font-size: 11px;
            color: #c0c4cc;
            flex-shrink: 0;
          }
        }
        
        .session-content {
          margin-bottom: 6px;
          
          .last-message {
            font-size: 13px;
            color: #606266;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.4;
          }
        }
        
        .session-meta {
          display: flex;
          align-items: center;
          gap: 8px;
          flex-wrap: wrap;
          
          .waiting-time {
            font-size: 11px;
            color: #f56c6c;
            background-color: #fef0f0;
            padding: 2px 6px;
            border-radius: 10px;
          }
          
          .agent-name {
            font-size: 11px;
            color: #67c23a;
            background-color: #f0f9ff;
            padding: 2px 6px;
            border-radius: 10px;
          }
          
          .history-btn {
            padding: 2px 4px;
            margin-left: auto;
            
            .el-icon {
              font-size: 30px;
              color: #606266;
            }
            
            &:hover {
              .el-icon {
                color: var(--el-color-primary);
              }
            }
          }
        }
      }
    }
  }
}

/* 移动端适配 */
@media (max-width: 767px) {
  .session-panel {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    
    .panel-header {
      padding: 12px 15px;
      
      h3 {
        font-size: 18px;
      }
    }
    
    .session-meta {
      .history-btn {
        .el-icon {
          font-size: 16px;
        }
      }
    }
  }
}
</style> 