<template>
  <div class="chat-header" v-if="currentSession">
    <!-- 用户信息区域 -->
    <div class="user-info">
      <el-avatar 
        :size="32" 
        :src="getAvatarUrl()" 
        shape="square" 
        class="user-avatar"
      >
        {{ getUserDisplayName().substring(0, 1) }}
      </el-avatar>
      
      <div class="user-details">
        <span class="user-name">{{ getUserDisplayName() }}</span>
      </div>
      
      <!-- 数据源标签 -->
      <el-tag 
        v-if="getDatasource()" 
        size="small" 
        effect="light" 
        class="status-tag datasource-tag"
      >
        {{ getDatasource() }}
      </el-tag>
      
      <!-- 渠道标签 -->
      <el-tag 
        v-if="getChannel()" 
        size="small" 
        effect="light" 
        class="status-tag channel-tag"
      >
        {{ getChannel() }}
      </el-tag>
      
      <!-- 场景标签 -->
      <el-tag 
        v-if="getScene()" 
        size="small" 
        effect="light" 
        class="status-tag scene-tag"
      >
        {{ getScene() }}
      </el-tag>
    </div>
    
    <!-- 操作按钮区域 -->
    <div class="header-actions">
      <el-button-group>
        <!-- 接入会话按钮 -->
        <el-tooltip 
          content="接入会话" 
          placement="bottom" 
          :disabled="isAcceptDisabled"
        >
          <el-button 
            type="primary" 
            :icon="Check"
            size="small" 
            :disabled="isAcceptDisabled"
            @click="handleAcceptSession"
          />
        </el-tooltip>
        
        <!-- 刷新消息按钮 -->
        <el-tooltip content="刷新消息" placement="bottom">
          <el-button 
            type="info" 
            :icon="Refresh"
            size="small"
            :loading="loading"
            @click="handleRefreshMessages"
          />
        </el-tooltip>
        
        <!-- 结束会话按钮 -->
        <el-tooltip 
          content="结束会话" 
          placement="bottom" 
          :disabled="isEndDisabled"
        >
          <el-button 
            type="danger" 
            :icon="CircleClose"
            size="small" 
            :disabled="isEndDisabled"
            @click="handleEndChat"
          />
        </el-tooltip>
        
        <!-- 转接会话按钮 -->
        <el-tooltip 
          content="转接会话" 
          placement="bottom" 
          :disabled="isTransferDisabled"
        >
          <el-button 
            type="info" 
            :icon="Connection"
            size="small" 
            :disabled="isTransferDisabled"
            @click="handleTransferSession"
          />
        </el-tooltip>
        
        <!-- 查看历史记录按钮 -->
        <el-tooltip 
          content="查看历史记录" 
          placement="bottom" 
          :disabled="!currentSession"
        >
          <el-button 
            type="info" 
            :icon="Document"
            size="small" 
            :disabled="!currentSession"
            @click="handleViewHistory"
          />
        </el-tooltip>
      </el-button-group>
    </div>
  </div>
</template>

<script setup>
import { computed, defineProps, defineEmits } from 'vue'
import { 
  Check, Refresh, CircleClose, Connection, Document
} from '@element-plus/icons-vue'

// Props定义
const props = defineProps({
  currentSession: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  userDetail: {
    type: Object,
    default: null
  },
  messages: {
    type: Array,
    default: () => []
  }
})

// Emits定义
const emit = defineEmits([
  'accept-session',
  'refresh-messages',
  'end-chat',
  'transfer-session',
  'view-history'
])

// 计算属性：是否禁用接入按钮
const isAcceptDisabled = computed(() => {
  return !props.currentSession || props.currentSession.status !== 2
})

// 计算属性：是否禁用结束按钮
const isEndDisabled = computed(() => {
  return !props.currentSession || props.currentSession.status !== 1
})

// 计算属性：是否禁用转接按钮
const isTransferDisabled = computed(() => {
  return !props.currentSession || 
         (props.currentSession.status !== 1 && props.currentSession.status !== 3)
})

// 方法：获取用户显示名称
const getUserDisplayName = () => {
  if (!props.currentSession) return '未知用户'
  
  return props.currentSession.userNickname || 
         (props.currentSession.user && props.currentSession.user.nickname) ||
         (props.currentSession.user && props.currentSession.user.phone) ||
         '访客' + props.currentSession.userId
}

// 方法：获取用户头像URL
const getAvatarUrl = () => {
  if (!props.currentSession) return ''
  
  const avatar = props.currentSession.userAvatar
  if (avatar) {
    return avatar.startsWith('http') ? avatar : `${process.env.VUE_APP_BASE_URL || '/'}${avatar}`
  }
  return '/src/assets/images/userAvatar.png'
}

// 方法：获取数据源信息
const getDatasource = () => {
  if (!props.messages || props.messages.length === 0) return null
  
  // 从senderType为0的最后一条消息中获取datasource字段
  for (let i = props.messages.length - 1; i >= 0; i--) {
    const msg = props.messages[i]
    if ((msg.senderType === 0 || msg.from === 0) && msg.datasource) {
      return msg.datasource
    }
  }
  
  // 如果消息中没有，尝试从会话对象获取
  if (props.currentSession && props.currentSession.datasource) {
    return props.currentSession.datasource
  }
  
  return null
}

// 方法：获取渠道信息
const getChannel = () => {
  if (!props.messages || props.messages.length === 0) return null
  
  // 从senderType为0的最后一条消息中获取channel字段
  for (let i = props.messages.length - 1; i >= 0; i--) {
    const msg = props.messages[i]
    if ((msg.senderType === 0 || msg.from === 0) && msg.channel) {
      return msg.channel
    }
  }
  
  // 如果消息中没有，尝试从会话对象获取
  if (props.currentSession && props.currentSession.channel) {
    return props.currentSession.channel
  }
  
  return null
}

// 方法：获取场景信息
const getScene = () => {
  if (!props.messages || props.messages.length === 0) return null
  
  // 从senderType为0的最后一条消息中获取scene字段
  for (let i = props.messages.length - 1; i >= 0; i--) {
    const msg = props.messages[i]
    if ((msg.senderType === 0 || msg.from === 0) && msg.scene) {
      return msg.scene
    }
  }
  
  // 如果消息中没有，尝试从会话对象获取
  if (props.currentSession && props.currentSession.scene) {
    return props.currentSession.scene
  }
  
  return null
}

// 事件处理：接入会话
const handleAcceptSession = () => {
  if (props.currentSession) {
    emit('accept-session', props.currentSession)
  }
}

// 事件处理：刷新消息
const handleRefreshMessages = () => {
  emit('refresh-messages')
}

// 事件处理：结束会话
const handleEndChat = () => {
  emit('end-chat')
}

// 事件处理：转接会话
const handleTransferSession = () => {
  emit('transfer-session')
}

// 事件处理：查看历史记录
const handleViewHistory = () => {
  if (props.currentSession) {
    emit('view-history', props.currentSession)
  }
}
</script>

<style lang="scss" scoped>
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background-color: #fff;
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
  
  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;
    
    .user-avatar {
      flex-shrink: 0;
      border: 2px solid #fff;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .user-details {
      flex-shrink: 0;
      
      .user-name {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
      }
    }
    
    .status-tag {
      flex-shrink: 0;
      font-size: 12px;
      
      &.datasource-tag {
        background-color: #e1f3ff;
        color: #0968b7;
        border-color: #b3d8ff;
      }
      
      &.channel-tag {
        background-color: #f0f9ff;
        color: #0066cc;
        border-color: #b3e0ff;
      }
      
      &.scene-tag {
        background-color: #f5f7fa;
        color: #606266;
        border-color: #dcdfe6;
      }
    }
  }
  
  .header-actions {
    flex-shrink: 0;
    
    .el-button-group {
      .el-button {
        padding: 6px 8px;
        
        &:disabled {
          opacity: 0.5;
        }
        
        &.is-loading {
          .el-icon {
            animation: rotating 2s linear infinite;
          }
        }
      }
    }
  }
}

/* 移动端适配 */
@media (max-width: 767px) {
  .chat-header {
    padding: 8px 12px;
    
    .user-info {
      gap: 8px;
      
      .user-avatar {
        width: 28px;
        height: 28px;
      }
      
      .user-details {
        .user-name {
          font-size: 14px;
          max-width: 100px;
        }
      }
      
      .status-tag {
        font-size: 10px;
        padding: 1px 4px;
        
        &:nth-child(n+4) {
          display: none; /* 移动端隐藏第三个以后的标签 */
        }
      }
    }
    
    .header-actions {
      .el-button-group {
        .el-button {
          padding: 4px 6px;
          font-size: 12px;
        }
      }
    }
  }
}

/* 旋转动画 */
@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style> 