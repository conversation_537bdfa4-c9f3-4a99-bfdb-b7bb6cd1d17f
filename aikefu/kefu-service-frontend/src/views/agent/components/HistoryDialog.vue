<template>
  <!-- 历史记录对话框 -->
  <el-dialog
    v-model="dialogVisible"
    title="历史聊天记录"
    width="80%"
    destroy-on-close
    append-to-body
    @closed="handleDialogClosed"
  >
    <div class="history-dialog-content">
      <div v-if="loadingHistory" class="history-loading">
        <el-skeleton :rows="5" animated />
      </div>
      <div v-else-if="historyMessages.length === 0" class="history-empty">
        <el-empty description="暂无聊天记录" />
      </div>
      <div v-else class="history-message-list">
        <!-- 加载更多按钮 -->
        <div v-if="historyHasMore" class="history-load-more">
          <el-button 
            type="primary" 
            link 
            size="small" 
            :loading="loadingHistory"
            @click="loadMoreHistoryMessages"
          >
            加载更多历史记录
          </el-button>
        </div>
        
        <div 
          v-for="message in historyMessages" 
          :key="message.id"
          class="history-message-item"
          :class="{ 
            'history-message-agent': (message.from === 1 || message.senderType === 1) && message.senderType !== 2 && message.currentAgentType !== 2,
            'history-message-user': message.from === 0 || message.senderType === 0,
            'history-message-system': message.type === 3 || message.senderType === 3,
            'history-message-ai': message.senderType === 2 || (message.from === 1 && message.currentAgentType === 2)
          }"
        >
          <!-- 消息内容 -->
          <div class="history-message-content">
            <!-- 发送者名称和时间 -->
            <div class="history-message-sender">
              <span class="history-sender-name" v-if="!(message.from === 0 || message.senderType === 0)">
                {{ message.senderName || '' }}
              </span>
              <span class="history-message-time">{{ formatTime(message.createdAt) }}</span>
            </div>
            
            <!-- 消息正文 -->
            <div class="history-message-body">
              <!-- 文本消息 -->
              <div v-if="message.type === 1 || message.msgType === 0" class="history-message-text">
                {{ message.content }}
              </div>
              
              <!-- 图片消息 -->
              <div v-else-if="message.type === 2 || message.msgType === 1" class="history-message-image">
                <el-image 
                  :src="getImageUrl(message.content)" 
                  :preview-src-list="[getImageUrl(message.content)]"
                  fit="cover"
                  :z-index="3000"
                  loading="lazy"
                ></el-image>
              </div>
              
              <!-- 系统消息 -->
              <div v-else-if="message.type === 3 || message.senderType === 3" class="history-message-text system-message">
                <span class="system-icon"><el-icon><InfoFilled /></el-icon></span>
                <span>{{ message.content }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCloseDialog">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import moment from 'moment'
import { getMessagesBySessionId } from '@/api/chat'
import { InfoFilled } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  sessionId: {
    type: [String, Number],
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'close'])

// 响应式数据
const historyMessages = ref([])
const loadingHistory = ref(false)
const historyHasMore = ref(false)
const historyCurrentPage = ref(1)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听 sessionId 变化，加载历史消息
watch(() => props.sessionId, async (newSessionId) => {
  if (newSessionId && props.visible) {
    await loadHistoryMessages()
  }
})

// 监听 visible 变化
watch(() => props.visible, async (newVisible) => {
  if (newVisible && props.sessionId) {
    // 重置数据
    historyCurrentPage.value = 1
    historyMessages.value = []
    
    // 加载历史消息
    await loadHistoryMessages()
  }
})

// 方法函数

// 加载历史消息
const loadHistoryMessages = async () => {
  if (!props.sessionId) return
  
  loadingHistory.value = true
  
  try {
    // 调用API获取历史消息
    const res = await getMessagesBySessionId(props.sessionId)
    
    if (res.code === 200 && Array.isArray(res.data)) {
      // 标准化消息格式
      const normalizedMessages = res.data.map(msg => {
        // 标准化发送方字段 (from 或 senderType)
        if (msg.from === undefined && msg.senderType !== undefined) {
          msg.from = msg.senderType
        } else if (msg.from !== undefined && msg.senderType === undefined) {
          msg.senderType = msg.from
        }
        
        // 处理系统消息类型
        if (msg.senderType === 3) {
          msg.type = 3; // 确保系统消息的type也为3
        } else {
          // 标准化消息类型字段 (type 或 msgType)
          if (msg.type === undefined && msg.msgType !== undefined) {
            msg.type = msg.msgType + 1
          } else if (msg.type !== undefined && msg.msgType === undefined) {
            msg.msgType = msg.type - 1
          }
        }
        
        return msg
      })
      
      // 按时间排序
      const sortedMessages = normalizedMessages.sort((a, b) => 
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      )
      
      historyMessages.value = sortedMessages
      
      // 假设有更多消息可以加载（后续可根据实际情况修改）
      historyHasMore.value = false
    } else {
      console.error('加载历史消息失败:', res)
      ElMessage.error(res.message || '加载历史消息失败')
    }
  } catch (error) {
    console.error('加载历史消息失败', error)
    ElMessage.error('加载历史消息失败')
  } finally {
    loadingHistory.value = false
  }
}

// 加载更多历史消息
const loadMoreHistoryMessages = async () => {
  if (!props.sessionId || loadingHistory.value) return
  
  loadingHistory.value = true
  
  try {
    historyCurrentPage.value++
    
    // 这里应该调用分页API，目前模拟加载更多
    const res = await getMessagesBySessionId(props.sessionId)
    
    if (res.code === 200 && Array.isArray(res.data)) {
      // TODO: 实现实际的分页加载
      // 当前是模拟，实际上应该根据API支持情况实现
      historyHasMore.value = false
    }
  } catch (error) {
    console.error('加载更多历史消息失败', error)
    ElMessage.error('加载更多历史消息失败')
  } finally {
    loadingHistory.value = false
  }
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''
  return moment(time).format('MM-DD HH:mm')
}

// 获取图片URL
const getImageUrl = (imagePath) => {
  if (!imagePath) return ''
  
  // 如果已经是完整的URL，直接返回
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath
  }
  
  // 如果是相对路径，需要拼接基础URL
  // 这里根据实际项目配置调整
  const baseUrl = import.meta.env.VITE_API_BASE_URL || ''
  return `${baseUrl}${imagePath.startsWith('/') ? '' : '/'}${imagePath}`
}

// 关闭对话框
const handleCloseDialog = () => {
  emit('update:visible', false)
  emit('close')
}

// 对话框关闭事件
const handleDialogClosed = () => {
  // 清理数据
  historyMessages.value = []
  historyCurrentPage.value = 1
  historyHasMore.value = false
}

</script>

<style lang="scss" scoped>
/* 历史记录对话框样式 */
.history-dialog-content {
  height: 65vh;
  overflow-y: auto;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.history-loading, .history-empty {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.history-message-list {
  display: flex;
  flex-direction: column;
}

.history-load-more {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.history-message-item {
  display: flex;
  margin-bottom: 16px;
  
  &.history-message-user {
    justify-content: flex-start;
    
    .history-message-content {
      max-width: 70%;
      
      .history-message-body {
        background-color: #fff;
        border-radius: 0 8px 8px 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        color: #303133;
      }
    }
  }
  
  &.history-message-agent {
    justify-content: flex-end;
    
    .history-message-content {
      max-width: 70%;
      
      .history-message-sender {
        text-align: right;
      }
      
      .history-message-body {
        background-color: #e1f3ff;
        color: #0068B7;
        border-radius: 8px 0 8px 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }
    }
  }
  
  &.history-message-ai {
    justify-content: flex-end;
    
    .history-message-content {
      max-width: 70%;
      
      .history-message-sender {
        text-align: right;
      }
      
      .history-message-body {
        background-color: #edf8ff;
        color: #0e5fd8;
        border-radius: 8px 0 8px 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }
    }
  }
  
  &.history-message-system {
    justify-content: center;
    
    .history-message-content {
      max-width: 80%;
      
      .history-message-body {
        background-color: rgba(144, 147, 153, 0.1);
        color: #909399;
        text-align: center;
        padding: 6px 15px;
        font-size: 13px;
        border-radius: 16px;
        box-shadow: none;
      }
    }
  }
  
  .history-message-content {
    display: flex;
    flex-direction: column;
    
    .history-message-sender {
      margin-bottom: 5px;
      font-size: 13px;
      
      .history-sender-name {
        color: #909399;
        margin-right: 8px;
      }
      
      .history-message-time {
        color: #c0c4cc;
        font-size: 12px;
      }
    }
    
    .history-message-body {
      position: relative;
      border-radius: 8px;
      padding: 0;
      overflow: hidden;
      
      .history-message-text {
        padding: 10px 15px;
        word-break: break-word;
        line-height: 1.5;
      }
      
      .history-message-image {
        max-width: 300px;
        overflow: hidden;
        border-radius: 8px;
        
        .el-image {
          display: block;
          width: 100%;
          border-radius: 8px;
          overflow: hidden;
        }
        
        .image-error, .image-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 150px;
          background-color: #f5f7fa;
          color: #909399;
          padding: 20px;
          border-radius: 8px;
        }
      }
      
      .history-message-system {
        padding: 10px 15px;
        font-size: 13px;
        color: #909399;
      }
    }
  }
}

.system-message {
  background-color: #ecf5ff !important;
  color: #409EFF !important;
  text-align: center;
  padding: 10px 20px !important;
  font-size: 14px !important;
  border-radius: 10px !important;
  border-left: 4px solid #409EFF !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08) !important;
  display: flex;
  align-items: center;
  
  .system-icon {
    margin-right: 8px;
    display: flex;
    align-items: center;
    
    .el-icon {
      font-size: 16px;
      color: #409EFF;
    }
  }
}
</style> 