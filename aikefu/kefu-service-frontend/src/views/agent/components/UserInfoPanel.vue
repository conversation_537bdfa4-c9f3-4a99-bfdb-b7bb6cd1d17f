<template>
  <!-- 用户信息面板 -->
  <div class="user-panel" v-if="currentSession" :class="{'mobile-show': visible}">
    
    <div v-if="loadingUserInfo" class="user-info-placeholder">
      <el-skeleton :rows="5" animated />
    </div>
    
    <div v-else-if="userDetail" class="user-info-content">
      <div class="user-basic-info">
        <el-avatar :size="64" :src="currentSession.userAvatar || userAvatarDefault" shape="circle">
          {{ userDetail.nickname ? userDetail.nickname.substring(0, 1) : '访' }}
        </el-avatar>
        <div class="user-name-info">
          <h4>
            {{ userDetail.nickname || userDetail.phone || '访客' + userDetail.id }}
            <el-tag v-if="getMessageDatasource()" size="small" effect="light" class="datasource-tag">{{ getMessageDatasource() }}</el-tag>
          </h4>
          <!-- 手机号放在数据源下面 -->
          <p v-if="userDetail.phone" class="phone-row">
            <span class="phone-number" @click="copyOrderCode(userDetail.phone)" title="点击复制手机号">
              <el-icon><Phone /></el-icon>
              <span>{{ userDetail.phone }}</span>
          </span>
          </p>
        </div>
      </div>
      
      <!-- 用户订单信息 -->
      <div class="user-orders-info" v-if="userOrderDetail">
        
        <div class="orders-info-container">
          <!-- 当前小智回收订单 -->
          <div class="current-order" v-if="currentRecycleOrder">
            <div class="current-order-card">
              <div class="item-header">
                <h5>当前订单</h5>
                <span class="order-code" @click="copyOrderCode(currentRecycleOrder.code)" title="点击复制订单编号">{{currentRecycleOrder.code}}</span>
              </div>
              <div class="item-content">
                <div class="item-row">
                  <span class="item-label">姓名:</span>
                  <span class="item-value">{{currentRecycleOrder.name}}</span>
                </div>
                <div class="item-row">
                  <span class="item-label">号码:</span>
                  <span class="item-value">{{currentRecycleOrder.mobile}}</span>
                </div>
                <div class="item-row">
                  <span class="item-label">价格:</span>
                  <span class="item-value price">¥{{currentRecycleOrder.price}}</span>
                </div>
                <div class="item-row">
                  <span class="item-label">来源:</span>
                  <span class="item-value">{{currentRecycleOrder.source_name}}</span>
                </div>
                <div class="item-row">
                  <span class="item-label">状态:</span>
                  <span class="item-value">{{currentRecycleOrder.status_name}}</span>
                </div>
                <div class="item-row">
                  <span class="item-label">地址:</span>
                  <span class="item-value">{{formatAddress(currentRecycleOrder)}}</span>
                </div>
                <div class="item-row" v-if="currentRecycleOrder.detail">
                  <span class="item-label">备注:</span>
                  <span class="item-value">{{currentRecycleOrder.detail}}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 当前二手商城订单 -->
          <div class="current-order" v-if="currentFleaMarketOrder">
            <div class="current-order-card flea-market-card">
              <div class="item-header">
                <h5>当前订单</h5>
                <span class="order-code" @click="copyOrderCode(currentFleaMarketOrder.code)" title="点击复制订单编号">{{currentFleaMarketOrder.code}}</span>
              </div>
              <div class="item-content">
                <!-- 这里根据二手商城订单的具体字段调整 -->
                <div class="item-row" v-if="currentFleaMarketOrder.name">
                  <span class="item-label">姓名:</span>
                  <span class="item-value">{{currentFleaMarketOrder.name}}</span>
                </div>
                <div class="item-row" v-if="currentFleaMarketOrder.mobile">
                  <span class="item-label">号码:</span>
                  <span class="item-value">{{currentFleaMarketOrder.mobile}}</span>
                </div>
                <div class="item-row" v-if="currentFleaMarketOrder.price">
                  <span class="item-label">价格:</span>
                  <span class="item-value price">¥{{currentFleaMarketOrder.price}}</span>
                </div>
                <div class="item-row" v-if="currentFleaMarketOrder.status_name">
                  <span class="item-label">状态:</span>
                  <span class="item-value">{{currentFleaMarketOrder.status_name}}</span>
                </div>
                <!-- 其他二手商城订单相关字段 -->
              </div>
            </div>
          </div>

          <!-- 当前小智集市订单 -->
          <div class="current-order" v-if="currentMarketOrder">
            <div class="current-order-card market-card">
              <div class="item-header">
                <h5>当前订单</h5>
                <span class="order-code" @click="copyOrderCode(currentMarketOrder.code)" title="点击复制订单编号">{{currentMarketOrder.code}}</span>
              </div>
              <div class="item-content">
                <!-- 这里根据小智集市订单的具体字段调整 -->
                <div class="item-row" v-if="currentMarketOrder.name">
                  <span class="item-label">姓名:</span>
                  <span class="item-value">{{currentMarketOrder.name}}</span>
                </div>
                <div class="item-row" v-if="currentMarketOrder.mobile">
                  <span class="item-label">号码:</span>
                  <span class="item-value">{{currentMarketOrder.mobile}}</span>
                </div>
                <div class="item-row" v-if="currentMarketOrder.price">
                  <span class="item-label">价格:</span>
                  <span class="item-value price">¥{{currentMarketOrder.price}}</span>
                </div>
                <div class="item-row" v-if="currentMarketOrder.status_name">
                  <span class="item-label">状态:</span>
                  <span class="item-value">{{currentMarketOrder.status_name}}</span>
                </div>
                <!-- 其他小智集市订单相关字段 -->
              </div>
            </div>
          </div>

          <!-- 当前工程师订单 -->
          <div class="current-order" v-if="currentEngineerOrder">
            <div class="current-order-card engineer-card">
              <div class="item-header">
                <h5>当前订单</h5>
                <span class="order-code" @click="copyOrderCode(currentEngineerOrder.code)" title="点击复制订单编号">{{currentEngineerOrder.code}}</span>
              </div>
              <div class="item-content">
                <!-- 这里根据工程师订单的具体字段调整 -->
                <div class="item-row" v-if="currentEngineerOrder.name">
                  <span class="item-label">姓名:</span>
                  <span class="item-value">{{currentEngineerOrder.name}}</span>
                </div>
                <div class="item-row" v-if="currentEngineerOrder.mobile">
                  <span class="item-label">号码:</span>
                  <span class="item-value">{{currentEngineerOrder.mobile}}</span>
                </div>
                <div class="item-row" v-if="currentEngineerOrder.service_name">
                  <span class="item-label">服务:</span>
                  <span class="item-value">{{currentEngineerOrder.service_name}}</span>
                </div>
                <div class="item-row" v-if="currentEngineerOrder.status_name">
                  <span class="item-label">状态:</span>
                  <span class="item-value">{{currentEngineerOrder.status_name}}</span>
                </div>
                <!-- 其他工程师订单相关字段 -->
              </div>
            </div>
          </div>

          <!-- 历史订单列表 -->
          <div class="recycle-orders" v-if="recycleOrders && recycleOrders.total > 0">
            <h5>小智回收订单列表 (共{{recycleOrders.total}}件)</h5>
            
            <div class="recycle-carousel">
              <el-carousel :interval="4000" height="150px" indicator-position="none">
                <el-carousel-item v-for="(item, index) in recycleOrders.list" :key="index">
                  <div class="recycle-item-card">
                    <div class="item-header">
                      <span class="order-code" @click="copyOrderCode(item.code)" title="点击复制订单编号"> {{item.code}}</span>
                    </div>
                    <div class="item-content">
                      <div class="item-row">
                        <span class="item-label">商品名称:</span>
                        <span class="item-value">{{item.recover_item.item_cates}}</span>
                      </div>
                      <div class="item-row">
                        <span class="item-label">订单状态:</span>
                        <span class="item-value">{{getOrderStatusText(item.status)}}</span>
                      </div>
                      <div class="item-row">
                        <span class="item-label">预估价格:</span>
                        <span class="item-value price">¥{{item.price}}</span>
                      </div>
                      <div class="item-row">
                        <span class="item-label">订单类型:</span>
                        <span class="item-value">{{item.order_type}}</span>
                      </div>
                      <div class="item-row">
                        <span class="item-label">创建时间:</span>
                        <span class="item-value">{{item.create_time}}</span>
                      </div>
                    </div>
                  </div>
                </el-carousel-item>
              </el-carousel>
            </div>
          </div>

          <!-- 二手商城历史订单列表 -->
          <div class="recycle-orders" v-if="fleaMarketOrders && fleaMarketOrders.total > 0">
            <h5>二手商城订单列表 (共{{fleaMarketOrders.total}}件)</h5>
            
            <div class="recycle-carousel">
              <el-carousel :interval="4000" height="150px" indicator-position="none">
                <el-carousel-item v-for="(item, index) in fleaMarketOrders.list" :key="index">
                  <div class="recycle-item-card flea-market-item-card">
                    <div class="item-header">
                      <span class="order-code" @click="copyOrderCode(item.code)" title="点击复制订单编号"> {{item.code}}</span>
                    </div>
                    <div class="item-content">
                      <!-- 这里根据二手商城订单的具体字段调整 -->
                      <div class="item-row" v-if="item.product_name">
                        <span class="item-label">商品名称:</span>
                        <span class="item-value">{{item.product_name}}</span>
                      </div>
                      <div class="item-row" v-if="item.status_name">
                        <span class="item-label">订单状态:</span>
                        <span class="item-value">{{item.status_name}}</span>
                      </div>
                      <div class="item-row" v-if="item.price">
                        <span class="item-label">价格:</span>
                        <span class="item-value price">¥{{item.price}}</span>
                      </div>
                      <div class="item-row" v-if="item.create_time">
                        <span class="item-label">创建时间:</span>
                        <span class="item-value">{{item.create_time}}</span>
                      </div>
                    </div>
                  </div>
                </el-carousel-item>
              </el-carousel>
            </div>
          </div>

          <!-- 小智集市历史订单列表 -->
          <div class="recycle-orders" v-if="marketOrders && marketOrders.total > 0">
            <h5>小智集市订单列表 (共{{marketOrders.total}}件)</h5>
            
            <div class="recycle-carousel">
              <el-carousel :interval="4000" height="150px" indicator-position="none">
                <el-carousel-item v-for="(item, index) in marketOrders.list" :key="index">
                  <div class="recycle-item-card market-item-card">
                    <div class="item-header">
                      <span class="order-code" @click="copyOrderCode(item.code)" title="点击复制订单编号"> {{item.code}}</span>
                    </div>
                    <div class="item-content">
                      <!-- 这里根据小智集市订单的具体字段调整 -->
                      <div class="item-row" v-if="item.product_name">
                        <span class="item-label">商品名称:</span>
                        <span class="item-value">{{item.product_name}}</span>
                      </div>
                      <div class="item-row" v-if="item.status_name">
                        <span class="item-label">订单状态:</span>
                        <span class="item-value">{{item.status_name}}</span>
                      </div>
                      <div class="item-row" v-if="item.price">
                        <span class="item-label">价格:</span>
                        <span class="item-value price">¥{{item.price}}</span>
                      </div>
                      <div class="item-row" v-if="item.create_time">
                        <span class="item-label">创建时间:</span>
                        <span class="item-value">{{item.create_time}}</span>
                      </div>
                    </div>
                  </div>
                </el-carousel-item>
              </el-carousel>
            </div>
          </div>

          <!-- 工程师历史订单列表 -->
          <div class="recycle-orders" v-if="engineerOrders && engineerOrders.total > 0">
            <h5>工程师订单列表 (共{{engineerOrders.total}}件)</h5>
            
            <div class="recycle-carousel">
              <el-carousel :interval="4000" height="150px" indicator-position="none">
                <el-carousel-item v-for="(item, index) in engineerOrders.list" :key="index">
                  <div class="recycle-item-card engineer-item-card">
                    <div class="item-header">
                      <span class="order-code" @click="copyOrderCode(item.code)" title="点击复制订单编号"> {{item.code}}</span>
                    </div>
                    <div class="item-content">
                      <!-- 这里根据工程师订单的具体字段调整 -->
                      <div class="item-row" v-if="item.service_name">
                        <span class="item-label">服务类型:</span>
                        <span class="item-value">{{item.service_name}}</span>
                      </div>
                      <div class="item-row" v-if="item.status_name">
                        <span class="item-label">订单状态:</span>
                        <span class="item-value">{{item.status_name}}</span>
                      </div>
                      <div class="item-row" v-if="item.price">
                        <span class="item-label">价格:</span>
                        <span class="item-value price">¥{{item.price}}</span>
                      </div>
                      <div class="item-row" v-if="item.create_time">
                        <span class="item-label">创建时间:</span>
                        <span class="item-value">{{item.create_time}}</span>
                      </div>
                    </div>
                  </div>
                </el-carousel-item>
              </el-carousel>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div v-else class="user-info-placeholder">
      <el-empty description="未能获取用户信息" />
    </div>
    
    <!-- 快捷回复功能 -->
    <div class="faq-card">
      <div class="faq-compact-header">
        <div class="faq-title">
          <strong>快捷回复</strong>
        </div>
        
        <!-- 紧凑型搜索栏 -->
        <div class="faq-search-compact">
          <el-input
            v-model="faqQueryParams.keyword"
            placeholder="搜索问题..."
            clearable
            size="small"
            @keyup.enter="handleFaqSearch"
            @clear="resetFaqQuery"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #suffix>
              <el-icon v-if="faqQueryParams.keyword" class="search-button" @click="handleFaqSearch"><Search /></el-icon>
            </template>
          </el-input>
          
          <!-- 添加新增按钮 -->
          <el-button 
            type="primary" 
            size="small" 
            circle 
            @click="addFAQ" 
            title="新增问题"
            class="faq-add-btn"
          >
            <el-icon><Plus /></el-icon>
          </el-button>
        </div>
      </div>
      
      <!-- 紧凑型标签页 -->
      <div class="faq-tabs-compact">
        <div class="faq-category-nav">
          <div 
            class="faq-category-item" 
            :class="{ 'active': activeFaqCategory === '' }"
            @click="handleFaqCategoryChange('')"
          >
            全部
          </div>
          <div 
            v-for="category in categoryOptions" 
            :key="category.value"
            class="faq-category-item"
            :class="{ 'active': activeFaqCategory === category.value }"
            @click="handleFaqCategoryChange(category.value)"
          >
            {{ category.label }}
          </div>
        </div>
      </div>
      
      <!-- 问题列表内容 -->
      <div class="faq-content-compact" v-loading="faqLoading">
        <div v-if="faqList.length === 0" class="empty-faq">
          <el-empty description="暂无常见问题" :image-size="48" />
        </div>
        
        <div v-else class="faq-list">
          <div 
            v-for="(item, index) in faqList" 
            :key="index"
            class="faq-item"
          >
            <div class="faq-item-header" @click="toggleFaqItem(index)">
              <div class="faq-item-title">
                <span>{{ item.question }}</span>
              </div>
              <el-icon :class="{'is-active': activeFaqItem === index}"><ArrowRight /></el-icon>
            </div>
            
            <div class="faq-item-content" v-show="activeFaqItem === index">
              <p>{{ item.answer }}</p>
              <div class="faq-item-actions">
                <el-button type="warning" link size="small" @click="editFAQ(item)">
                  <el-icon><Edit /></el-icon> 编辑问题
                </el-button>
                <el-button type="primary" link size="small" @click="insertFaqToReply(item)">
                  <el-icon><Document /></el-icon> 复制到输入框
                </el-button>

              </div>
            </div>
          </div>
        </div>
        
        <!-- 简化的分页 -->
        <div class="faq-pagination-compact" v-if="faqTotal > faqQueryParams.pageSize">
          <el-pagination
            small
            background
            layout="prev, pager, next"
            :total="faqTotal"
            :page-size="faqQueryParams.pageSize"
            :current-page="faqQueryParams.pageNum"
            @current-change="handleFaqCurrentChange"
          />
        </div>
      </div>
    </div>
    
    <!-- 移动端关闭按钮 -->
    <div class="mobile-close-btn" @click="handleClose" v-if="isMobile">
      <el-icon><Close /></el-icon>
    </div>
    
    <!-- FAQ编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="formTitle"
      width="80%"
      destroy-on-close
      append-to-body
    >
      <el-form
        ref="faqFormRef"
        :model="faqForm"
        :rules="faqFormRules"
        label-width="80px"
        size="small"
      >
        <el-form-item label="问题分类" prop="category">
          <el-select v-model="faqForm.category" placeholder="请选择问题分类">
            <el-option 
              v-for="category in categoryOptions" 
              :key="category.value" 
              :label="category.label" 
              :value="category.value" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="问题" prop="question">
          <el-input 
            v-model="faqForm.question" 
            placeholder="请输入问题" 
            type="textarea" 
            :rows="2"
          />
        </el-form-item>
        
        <el-form-item label="答案" prop="answer">
          <el-input 
            v-model="faqForm.answer" 
            placeholder="请输入答案" 
            type="textarea" 
            :rows="5"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false" size="small">取消</el-button>
          <el-button type="primary" @click="submitForm" size="small">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Phone, Close, Search, ArrowRight, QuestionFilled, Document, Plus, Edit } from '@element-plus/icons-vue'
import { getUserInfo } from '@/api/user'
import { getAllFaqs, addFaq, updateFaq, deleteFaq } from '@/api/faq'

// 导入默认头像图片
import userAvatarDefault from '@/assets/images/userAvatar.png'

// Props定义
const props = defineProps({
  currentSession: {
    type: Object,
    default: null
  },
  visible: {
    type: Boolean,
    default: false
  },
  isMobile: {
    type: Boolean,
    default: false
  }
})

// Emits定义
const emit = defineEmits(['close', 'insert-to-reply'])

// 响应式数据
const userDetail = ref(null)
const userOrderDetail = ref(null)
const loadingUserInfo = ref(false)
const recycleOrders = ref(null)
const fleaMarketOrders = ref(null)
const marketOrders = ref(null)
const engineerOrders = ref(null)
const currentUserId = ref(null)

// FAQ相关状态
const faqList = ref([])
const faqTotal = ref(0)
const faqLoading = ref(false)
const activeFaqItem = ref(null)
const faqQueryParams = reactive({
  keyword: '',
  category: '',
  pageNum: 1,
  pageSize: 5
})

// FAQ分类选项
const categoryOptions = ref([
  { label: '订单', value: '订单相关' },
  { label: '商品', value: '商品相关' },
  { label: '配送', value: '配送相关' },
  { label: '支付', value: '支付相关' },
  { label: '账户', value: '账户相关' },
  { label: '其他', value: '其他问题' }
])

// 新增激活的FAQ分类状态
const activeFaqCategory = ref('')

// FAQ编辑对话框状态
const dialogVisible = ref(false)
const faqFormRef = ref(null)
const faqForm = reactive({
  id: '',
  category: '',
  question: '',
  answer: '',
  sort: 0
})
const faqFormRules = {
  category: [
    { required: true, message: '请选择问题分类', trigger: 'change' }
  ],
  question: [
    { required: true, message: '请输入问题', trigger: 'blur' },
    { max: 200, message: '最大长度为 200 个字符', trigger: 'blur' }
  ],
  answer: [
    { required: true, message: '请输入答案', trigger: 'blur' }
  ]
}

// 表单标题
const formTitle = computed(() => {
  return faqForm.id ? '编辑常见问题' : '新增常见问题'
})

// 计算属性：当前订单
const currentRecycleOrder = computed(() => {
  if (!userOrderDetail.value || !userOrderDetail.value.currentOrder) return null
  try {
    const currentOrderData = JSON.parse(userOrderDetail.value.currentOrder)
    return currentOrderData.recycle || null
  } catch (error) {
    console.error('解析当前回收订单数据失败:', error)
    return null
  }
})

const currentFleaMarketOrder = computed(() => {
  if (!userOrderDetail.value || !userOrderDetail.value.currentOrder) return null
  try {
    const currentOrderData = JSON.parse(userOrderDetail.value.currentOrder)
    return currentOrderData.flea_market || null
  } catch (error) {
    console.error('解析当前二手商城订单数据失败:', error)
    return null
  }
})

const currentMarketOrder = computed(() => {
  if (!userOrderDetail.value || !userOrderDetail.value.currentOrder) return null
  try {
    const currentOrderData = JSON.parse(userOrderDetail.value.currentOrder)
    return currentOrderData.market || null
  } catch (error) {
    console.error('解析当前集市订单数据失败:', error)
    return null
  }
})

const currentEngineerOrder = computed(() => {
  if (!userOrderDetail.value || !userOrderDetail.value.currentOrder) return null
  try {
    const currentOrderData = JSON.parse(userOrderDetail.value.currentOrder)
    return currentOrderData.engineer || null
  } catch (error) {
    console.error('解析当前工程师订单数据失败:', error)
    return null
  }
})

// 方法函数
const fetchUserDetail = async (userId) => {
  try {
    userDetail.value = null
    userOrderDetail.value = null
    loadingUserInfo.value = true
    
    const res = await getUserInfo(userId)
    
    if (res.code === 200) {
      if (Array.isArray(res.data)) {
        userDetail.value = res.data[0]
        userOrderDetail.value = res.data[1]
      } else {
        userDetail.value = res.data
      }
      
      // 处理订单数据
      processOrderData()
    } else {
      userDetail.value = null
      console.error('获取用户信息失败:', res.message)
    }
  } catch (error) {
    userDetail.value = null
    console.error('获取用户信息出错:', error)
  } finally {
    loadingUserInfo.value = false
  }
}

const processOrderData = () => {
  // 重置订单数据
  recycleOrders.value = null
  fleaMarketOrders.value = null
  marketOrders.value = null
  engineerOrders.value = null
  
  // 解析历史订单列表
  if (!userOrderDetail.value) {
    console.log('没有用户订单详情数据')
    return
  }

  if (userOrderDetail.value.orderList) {
    try {
      const orderData = JSON.parse(userOrderDetail.value.orderList)
      
      // 小智回收订单列表
      if (orderData.recycle) {
        recycleOrders.value = orderData.recycle
      }
      
      // 二手商城订单列表
      if (orderData.flea_market) {
        fleaMarketOrders.value = orderData.flea_market
      }
      
      // 小智集市订单列表
      if (orderData.market) {
        marketOrders.value = orderData.market
      }
      
      // 工程师订单列表
      if (orderData.engineer) {
        engineerOrders.value = orderData.engineer
      }
    } catch (error) {
      console.error('解析订单列表数据失败:', error)
    }
  }
}

const getMessageDatasource = () => {
  if (props.currentSession && props.currentSession.source) {
    switch (props.currentSession.source) {
      case 1: return '小智回收'
      case 2: return '二手商城'
      case 3: return '小智集市'
      case 4: return '工程师'
      default: return ''
    }
  }
  return ''
}

const copyOrderCode = (code) => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(code).then(() => {
      ElMessage.success('已复制到剪贴板')
    }).catch(() => {
      ElMessage.error('复制失败')
    })
  } else {
    // 兼容旧浏览器
    const textArea = document.createElement('textarea')
    textArea.value = code
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      ElMessage.success('已复制到剪贴板')
    } catch (err) {
      ElMessage.error('复制失败')
    }
    document.body.removeChild(textArea)
  }
}

const formatAddress = (order) => {
  const parts = []
  if (order.province) parts.push(order.province)
  if (order.city) parts.push(order.city)
  if (order.district) parts.push(order.district)
  if (order.address) parts.push(order.address)
  return parts.join(' ')
}

const getOrderStatusText = (status) => {
  const statusMap = {
    0: '待处理',
    1: '已接单',
    2: '处理中',
    3: '已完成',
    4: '已取消'
  }
  return statusMap[status] || '未知状态'
}

const handleClose = () => {
  emit('close')
}

// 监听当前会话变化，自动获取用户信息
watch(() => props.currentSession, (newSession) => {
  if (newSession && newSession.userId) {
    if (currentUserId.value !== newSession.userId) {
      currentUserId.value = newSession.userId
      fetchUserDetail(newSession.userId)
    }
  } else {
    currentUserId.value = null
    userDetail.value = null
    userOrderDetail.value = null
  }
}, { immediate: true })

// FAQ相关方法
const fetchFaqList = async () => {
  try {
    faqLoading.value = true
    // 调用后端接口获取所有FAQ
    const response = await getAllFaqs()
    
    if (response.code === 200) {
      const allFaqs = response.data || []
      
      // 进行前端筛选
      let filteredFaqs = allFaqs
      
      // 分类筛选
      if (faqQueryParams.category) {
        filteredFaqs = filteredFaqs.filter(faq => faq.category === faqQueryParams.category)
      }
      
      // 关键词筛选
      if (faqQueryParams.keyword) {
        const keyword = faqQueryParams.keyword.toLowerCase()
        filteredFaqs = filteredFaqs.filter(faq => 
          (faq.question && faq.question.toLowerCase().includes(keyword)) || 
          (faq.answer && faq.answer.toLowerCase().includes(keyword))
        )
      }
      
      // 记录总数
      faqTotal.value = filteredFaqs.length
      
      // 按排序值排序
      filteredFaqs.sort((a, b) => (a.sort || 0) - (b.sort || 0))
      
      // 分页处理
      const start = (faqQueryParams.pageNum - 1) * faqQueryParams.pageSize
      const end = start + faqQueryParams.pageSize
      faqList.value = filteredFaqs.slice(start, end)
    } else {
      console.error('加载常见问题失败:', response.message)
    }
  } catch (error) {
    console.error('获取FAQ列表出错:', error)
  } finally {
    faqLoading.value = false
  }
}

const handleFaqSearch = debounce(() => {
  faqQueryParams.pageNum = 1
  fetchFaqList()
}, 300)

const handleFaqCategoryChange = (category) => {
  activeFaqCategory.value = category
  faqQueryParams.category = category
  faqQueryParams.pageNum = 1
  fetchFaqList()
}

const toggleFaqItem = (index) => {
  activeFaqItem.value = activeFaqItem.value === index ? null : index
}

const insertFaqToReply = (item) => {
  // 发送到输入框追加内容
  emit('insert-to-reply', item.answer)
  
  // 同时复制内容到剪贴板
  if (navigator.clipboard) {
    navigator.clipboard.writeText(item.answer).then(() => {
      ElMessage.success('已复制到输入框和剪贴板')
    }).catch(() => {
      ElMessage.success('已复制到输入框，但复制到剪贴板失败')
    })
  } else {
    // 兼容不支持clipboard API的浏览器
    const textArea = document.createElement('textarea')
    textArea.value = item.answer
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      ElMessage.success('已复制到输入框和剪贴板')
    } catch (err) {
      ElMessage.success('已复制到输入框，但复制到剪贴板失败')
    }
    document.body.removeChild(textArea)
  }
}

const handleFaqCurrentChange = (pageNum) => {
  faqQueryParams.pageNum = pageNum
  fetchFaqList()
}

const resetFaqQuery = () => {
  activeFaqCategory.value = ''
  faqQueryParams.keyword = ''
  faqQueryParams.category = ''
  faqQueryParams.pageNum = 1
  fetchFaqList()
}

const addFAQ = () => {
  resetForm()
  dialogVisible.value = true
}

const editFAQ = (item) => {
  resetForm()
  Object.assign(faqForm, item)
  dialogVisible.value = true
}

const resetForm = () => {
  faqForm.id = ''
  faqForm.category = ''
  faqForm.question = ''
  faqForm.answer = ''
  faqForm.sort = 0
  
  // 如果表单引用存在，重置验证结果
  if (faqFormRef.value) {
    faqFormRef.value.resetFields()
  }
}

const submitForm = async () => {
  if (!faqFormRef.value) return
  
  try {
    await faqFormRef.value.validate()
    
    if (faqForm.id) {
      // 编辑
      const response = await updateFaq(faqForm.id, faqForm)
      
      if (response.code === 200) {
        ElMessage.success('修改成功')
        // 更新列表
        const index = faqList.value.findIndex(item => item.id === faqForm.id)
        if (index !== -1) {
          faqList.value[index] = response.data || {
            ...faqList.value[index],
            ...faqForm
          }
        }
      } else {
        ElMessage.error(response.message || '修改失败')
      }
    } else {
      // 新增
      const response = await addFaq(faqForm)
      
      if (response.code === 200) {
        ElMessage.success('新增成功')
        // 重新加载列表
        fetchFaqList()
      } else {
        ElMessage.error(response.message || '新增失败')
      }
    }
    
    dialogVisible.value = false
  } catch (error) {
    console.error('表单验证或提交失败', error)
    ElMessage.error('提交失败：' + (error.message || '未知错误'))
  }
}

// 防抖函数
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 组件挂载时加载FAQ列表
onMounted(() => {
  fetchFaqList()
})

</script>

<style lang="scss" scoped>
.user-panel {
  width: 320px;
  background-color: #fff;
  border-left: 1px solid var(--el-border-color-light);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  max-height: 100vh;
  position: relative;
  
  .user-info-placeholder {
    padding: 20px;
    text-align: center;
    color: var(--el-text-color-secondary);
  }
  
  .user-info-content {
    padding: 0;
    
    .user-basic-info {
      display: flex;
      align-items: flex-start;
      padding: 15px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      border-bottom: 1px solid var(--el-border-color-light);
      
      .el-avatar {
        margin-right: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      
      .user-name-info {
        flex: 1;
        
        h4 {
          margin: 0 0 8px 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 8px;
        }
        
        .datasource-tag {
          font-size: 12px;
          background-color: var(--el-color-primary);
          color: white;
          border: none;
        }
        
        .phone-row {
          margin: 0;
          
          .phone-number {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            color: var(--el-color-primary);
            cursor: pointer;
            font-size: 14px;
            
            &:hover {
              color: var(--el-color-primary-dark-2);
            }
          }
        }
      }
    }
    
    .user-orders-info {
      padding: 0;
      
      .orders-info-container {
        .current-order {
          margin-bottom: 5px;
          
          .current-order-card {
            background: #fff;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            margin: 16px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            
            &.flea-market-card {
              border-left: 4px solid #67c23a;
            }
            
            &.market-card {
              border-left: 4px solid #e6a23c;
            }
            
            &.engineer-card {
              border-left: 4px solid #f56c6c;
            }
            
            .item-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 16px;
              background-color: #f8f9fa;
              border-bottom: 1px solid #e4e7ed;
              
              h5 {
                margin: 0;
                font-size: 14px;
                font-weight: 600;
                color: #303133;
              }
              
              .order-code {
                font-size: 12px;
                color: var(--el-color-primary);
                cursor: pointer;
                background: #ecf5ff;
                padding: 4px 8px;
                border-radius: 4px;
                border: 1px solid #b3d8ff;
                
                &:hover {
                  background: #d9ecff;
                }
              }
            }
            
            .item-content {
              padding: 12px;
              
              .item-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;
                font-size: 13px;
                
                &:last-child {
                  margin-bottom: 0;
                }
                
                .item-label {
                  color: #909399;
                  min-width: 60px;
                  font-weight: 500;
                }
                
                .item-value {
                  color: #303133;
                  text-align: right;
                  flex: 1;
                  
                  &.price {
                    color: #f56c6c;
                    font-weight: 600;
                  }
                }
              }
            }
          }
        }
        
        .recycle-orders {
          margin: 20px;
          height: 170px; /* 设置固定高度 */
          
          h5 {
            margin: 0 0 12px 0;
            font-size: 14px;
            font-weight: 600;
            color: #303133;
            padding-bottom: 2px;
            border-bottom: 2px solid var(--el-color-primary);
          }
          
          .recycle-carousel {
            .recycle-item-card,
            .flea-market-item-card,
            .market-item-card,
            .engineer-item-card {
              background: #fff;
              border: 1px solid #e4e7ed;
              border-radius: 6px;
              margin: 0px;
              height: 170px;
              overflow: hidden;
              
              .item-header {
                padding: 8px 20px;
                background-color: #f8f9fa;
                border-bottom: 1px solid #e4e7ed;
                
                .order-code {
                  font-size: 11px;
                  color: var(--el-color-primary);
                  cursor: pointer;
                  
                  &:hover {
                    text-decoration: underline;
                  }
                }
              }
              
              .item-content {
                padding: 8px 12px;
                height: calc(100% - 32px);
                overflow: hidden;
                
                .item-row {
                  display: flex;
                  justify-content: space-between;
                  margin-bottom: 4px;
                  font-size: 12px;
                  
                  .item-label {
                    color: #909399;
                    min-width: 50px;
                  }
                  
                  .item-value {
                    color: #303133;
                    text-align: right;
                    flex: 1;
                    
                    &.price {
                      color: #f56c6c;
                      font-weight: 600;
                    }
                  }
                }
              }
            }
            
            .flea-market-item-card {
              border-left: 3px solid #67c23a;
            }
            
            .market-item-card {
              border-left: 3px solid #e6a23c;
            }
            
            .engineer-item-card {
              border-left: 3px solid #f56c6c;
            }
          }
        }
      }
    }
  }
  
  .mobile-close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 32px;
    height: 32px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    z-index: 10;
    
    &:hover {
      background: rgba(0, 0, 0, 0.7);
    }
  }
  
  /* FAQ快捷回复样式 */
  .faq-card {
    margin: 10px 16px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    overflow: hidden;
    
    /* 紧凑型头部 */
    .faq-compact-header {
      padding: 8px 15px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #ebeef5;
      
      .faq-title {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 500;
        color: #303133;
        
        .el-icon {
          margin-right: 5px;
          color: #409EFF;
          font-size: 16px;
        }
      }
      
      .faq-search-compact {
        width: 180px;
        display: flex;
        align-items: center;
        gap: 8px;
        
        .el-input {
          font-size: 12px;
          flex: 1;
          
          .el-input__inner {
            height: 28px;
            line-height: 28px;
          }
          
          .el-input__prefix,
          .el-input__suffix {
            display: flex;
            align-items: center;
          }
          
          .search-button {
            cursor: pointer;
            font-size: 14px;
            padding: 0 5px;
            
            &:hover {
              color: #409EFF;
            }
          }
        }
        
        .faq-add-btn {
          flex-shrink: 0;
        }
      }
    }
    
    /* 紧凑型标签页 */
    .faq-tabs-compact {
      border-bottom: 1px solid #ebeef5;
      padding: 8px 10px;
      
      .faq-category-nav {
        display: flex;
        flex-wrap: nowrap;
        gap: 2px;
        justify-content: space-between;
        
        .faq-category-item {
          padding: 4px 6.5px;
          font-size: 12px;
          cursor: pointer;
          color: #606266;
          background-color: #f4f4f5;
          border-radius: 12px;
          text-align: center;
          transition: all 0.3s;
          min-width: 25px;
          
          &:hover {
            color: #409EFF;
            background-color: #ecf5ff;
          }
          
          &.active {
            color: #fff;
            background-color: #409EFF;
            font-weight: 500;
            
            &:after {
              display: none;
            }
          }
        }
      }
    }
    
    /* 紧凑型内容区域 */
    .faq-content-compact {
      flex: 1;
      overflow-y: auto;
      padding: 8px 0;
      max-height: 250px;
      
      /* 自定义滚动条样式 */
      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
        
        &:hover {
          background: #a8a8a8;
        }
      }
      
      &::-webkit-scrollbar-thumb:active {
        background: #888;
      }
      
      .empty-faq {
        display: flex;
        justify-content: center;
        padding: 20px 0;
      }
      
      .faq-list {
        .faq-item {
          border-bottom: 1px solid #f0f0f0;
          
          &:last-child {
            border-bottom: none;
          }
          
          .faq-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s;
            
            &:hover {
              background-color: #f5f7fa;
            }
            
            .faq-item-title {
              display: flex;
              align-items: center;
              flex: 1;
              
              span {
                font-size: 12px;
                color: #303133;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
            
            .el-icon {
              color: #909399;
              transition: transform 0.3s;
              font-size: 14px;
              
              &.is-active {
                transform: rotate(90deg);
                color: #409EFF;
              }
            }
          }
          
          .faq-item-content {
            padding: 0 12px 10px;
            font-size: 12px;
            color: #606266;
            line-height: 1.5;
            background-color: #f9f9f9;
            border-top: 1px dashed #ebeef5;
            
            p {
              margin: 10px 0;
            }
            
            .faq-item-actions {
              display: flex;
              justify-content: flex-end;
              padding-top: 8px;
              margin-top: 8px;
              gap: 8px;
              
              .el-button {
                padding: 4px 8px;
                font-size: 12px;
                
                .el-icon {
                  margin-right: 2px;
                }
              }
            }
          }
        }
      }
      
      .faq-pagination-compact {
        padding: 8px 0 5px;
        display: flex;
        justify-content: center;
        
        .el-pagination {
          font-size: 12px;
          
          .btn-prev,
          .btn-next,
          .number {
            min-width: 24px;
            height: 24px;
            line-height: 24px;
          }
        }
      }
    }
  }
}

/* 移动端响应式样式 */
@media (max-width: 768px) {
  .user-panel {
    position: fixed;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100%;
    z-index: 1000;
    transition: right 0.3s ease;
    
    &.mobile-show {
      right: 0;
    }
  }
}

/* 动画定义 */
@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
  to {
    opacity: 1;
    max-height: 200px;
    padding-top: 0;
    padding-bottom: 16px;
  }
}

/* Element Plus 轮播组件样式覆盖 */
:deep(.el-carousel__indicator) {
  background-color: #c0c4cc;
  
  &.is-active {
    background-color: var(--el-color-primary);
  }
}

:deep(.el-carousel__button) {
  background-color: transparent;
}

/* FAQ对话框样式覆盖 */
:deep(.el-dialog) {
  max-width: 90%;
  
  .el-dialog__body {
    padding: 15px 20px;
  }
  
  .el-form-item {
    margin-bottom: 15px;
  }
}
</style> 