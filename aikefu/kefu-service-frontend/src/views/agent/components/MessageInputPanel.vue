<template>
  <div class="message-input-panel">
    <!-- 快捷回复工具栏 -->
    <div class="quick-replies">
      <el-scrollbar>
        <div class="reply-list">
          <template v-for="(reply, index) in quickReplies" :key="index">
            <div class="quick-reply-item">
              <el-tooltip :content="reply" placement="top" :disabled="reply.length < 15">
                <el-button 
                  size="small"
                  @click="insertQuickReply(reply)"
                  round
                >
                  {{ reply.length > 15 ? reply.substring(0, 15) + '...' : reply }}
                </el-button>
              </el-tooltip>
              <div class="quick-reply-actions">
                <el-button
                  class="action-btn"
                  type="primary"
                  size="small"
                  circle
                  @click.stop="showEditReplyDialog(index, reply)"
                >
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button
                  class="action-btn"
                  type="danger"
                  size="small"
                  circle
                  @click.stop="confirmDeleteReply(index)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
          <!-- 添加回复按钮 -->
          <el-button
            v-if="quickReplies.length < 25"
            size="small"
            type="primary"
            circle
            @click="showAddReplyDialog"
          >
            <el-icon><Plus /></el-icon>
          </el-button>
          <el-tooltip v-else content="已达到最大数量限制(25个)" placement="top">
            <el-button
              size="small"
              type="info"
              circle
              disabled
            >
              <el-icon><Plus /></el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </el-scrollbar>
    </div>
    
    <!-- 输入区域容器 -->
    <div class="message-input-container">
      <!-- 左侧工具栏 -->
      <div class="left-tools-wrapper">
        <el-tooltip content="发送图片或视频" placement="top">
          <el-upload
            class="file-upload"
            action=""
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleFileUpload"
            accept="image/*,video/*"
          >
            <el-button type="primary" text :icon="Picture" class="tool-btn" />
          </el-upload>
        </el-tooltip>
      </div>
      
      <!-- 中间输入框 -->
      <div class="message-input-wrapper">
        <el-input
          v-model="messageText"
          type="textarea"
          :rows="3"
          placeholder="请输入消息，按Enter发送，Ctrl+Enter换行"
          resize="none"
          :disabled="disabled"
          @keydown.enter.prevent="handleEnterPress"
          @paste="handlePaste"
        />
      </div>
      
      <!-- 右侧发送按钮 -->
      <div class="send-btn-wrapper">
        <el-button 
          type="primary" 
          :disabled="!canSend"
          :loading="sending"
          @click="handleSendMessage"
          class="send-btn"
          :icon="Position"
        >
          发送
        </el-button>
      </div>
    </div>
    
    <!-- 文件预览区域 -->
    <div v-if="selectedFile" class="file-preview">
      <div class="preview-header">
        <span>{{ isSelectedVideo ? '已选择视频' : '已选择图片' }}</span>
        <el-button type="danger" text :icon="Delete" @click="cancelFileUpload" />
      </div>
      <div class="preview-content">
        <el-image v-if="!isSelectedVideo" :src="selectedFileUrl" fit="contain" />
        <video v-else :src="selectedFileUrl" controls style="max-width: 100%; max-height: 200px;" />
      </div>
    </div>
    
    <!-- 添加快捷回复对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogMode === 'add' ? '添加快捷回复' : '编辑快捷回复'"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-form :model="replyForm" label-width="80px" @submit.prevent="submitReplyForm">
        <el-form-item label="回复内容" required>
          <el-input 
            v-model="replyForm.content" 
            type="textarea" 
            :rows="3"
            placeholder="请输入回复内容"
            maxlength="200"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitReplyForm" :disabled="!replyForm.content.trim()">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Picture, Delete, Position, Plus, Edit } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// Props定义
const props = defineProps({
  currentSession: {
    type: Object,
    default: null
  },
  quickReplies: {
    type: Array,
    default: () => []
  },
  disabled: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits定义
const emit = defineEmits(['send-message', 'insert-reply', 'paste-image', 'update-quick-replies'])

// 内部状态管理
const messageText = ref('')
const selectedFile = ref(null)
const selectedFileUrl = ref('')
const isSelectedVideo = ref(false)
const sending = ref(false)
const dialogVisible = ref(false)
const dialogMode = ref('add')
const editIndex = ref(null)
const replyForm = ref({ content: '' })

// 兼容性别名
const selectedImage = computed(() => selectedFile.value)
const selectedImageUrl = computed(() => selectedFileUrl.value)

// 计算属性
const canSend = computed(() => {
  return (messageText.value.trim() || selectedFile.value) && 
         !sending.value && 
         !props.disabled &&
         !props.loading
})

// 文件上传处理
const handleFileUpload = (file) => {
  try {
    if (!file || !file.raw) {
      ElMessage.error('请选择有效的文件')
      return
    }
    
    const isImage = file.raw.type.startsWith('image/')
    const isVideo = file.raw.type.startsWith('video/')
    
    if (!isImage && !isVideo) {
      ElMessage.error('请选择图片或视频文件')
      return
    }
    
    // 检查文件大小（限制为50MB）
    const maxSize = 50 * 1024 * 1024
    if (file.raw.size > maxSize) {
      ElMessage.error('文件大小不能超过50MB')
      return
    }
    
    selectedFile.value = file.raw
    selectedFileUrl.value = URL.createObjectURL(file.raw)
    isSelectedVideo.value = isVideo
    
    // 触发事件，保持兼容性
    emit('paste-image', file.raw)
  } catch (error) {
    console.error('文件上传处理失败:', error)
    ElMessage.error('文件处理失败，请重试')
  }
}

// 兼容性方法
const handleImageUpload = handleFileUpload

// 快捷回复插入
const insertQuickReply = (text) => {
  messageText.value = text
  emit('insert-reply', text)
}

// 发送消息
const handleSendMessage = () => {
  if (!canSend.value) return
  
  try {
    sending.value = true
    
    emit('send-message', {
      text: messageText.value.trim(),
      image: selectedFile.value,
      imageUrl: selectedFileUrl.value,
      isVideo: isSelectedVideo.value
    })
    
    // 发送后清空输入
    messageText.value = ''
    cancelFileUpload()
  } catch (error) {
    console.error('发送消息处理失败:', error)
    ElMessage.error('发送失败，请重试')
  } finally {
    sending.value = false
  }
}

// 取消文件上传
const cancelFileUpload = () => {
  try {
    if (selectedFileUrl.value) {
      URL.revokeObjectURL(selectedFileUrl.value)
    }
    selectedFile.value = null
    selectedFileUrl.value = ''
    isSelectedVideo.value = false
  } catch (error) {
    console.error('清理文件预览失败:', error)
  }
}

// 兼容性方法
const cancelImageUpload = cancelFileUpload

// 键盘事件处理
const handleEnterPress = (event) => {
  try {
    if (event.ctrlKey) {
      // Ctrl+Enter换行
      const textarea = event.target
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      messageText.value = messageText.value.substring(0, start) + 
                         '\n' + 
                         messageText.value.substring(end)
      
      // 设置光标位置到新行
      setTimeout(() => {
        textarea.selectionStart = textarea.selectionEnd = start + 1
      }, 0)
    } else {
      // Enter发送
      handleSendMessage()
    }
  } catch (error) {
    console.error('键盘事件处理失败:', error)
  }
}

// 粘贴事件处理
const handlePaste = async (event) => {
  try {
    const items = event.clipboardData?.items
    if (!items) return
    
    for (const item of items) {
      if (item.type.startsWith('image/')) {
        const blob = item.getAsFile()
        if (blob) {
          // 检查文件大小
          const maxSize = 10 * 1024 * 1024
          if (blob.size > maxSize) {
            ElMessage.error('粘贴的图片文件大小不能超过10MB')
            return
          }
          
          selectedFile.value = blob
          selectedFileUrl.value = URL.createObjectURL(blob)
          isSelectedVideo.value = false
          emit('paste-image', blob)
        }
        break
      }
    }
  } catch (error) {
    console.error('粘贴处理失败:', error)
    ElMessage.error('粘贴失败，请重试')
  }
}

// 组件卸载时清理
import { onBeforeUnmount } from 'vue'
onBeforeUnmount(() => {
  cancelFileUpload()
})

// 暴露方法给父组件调用
import { defineExpose } from 'vue'

// 插入文本到输入框的方法
const insertText = (text) => {
  if (text) {
    // 追加文本而不是替换
    if (messageText.value) {
      // 如果当前输入框不为空，先加一个空格符再追加内容
      messageText.value = messageText.value + ' ' + text
    } else {
      // 如果当前输入框为空，直接赋值
      messageText.value = text
    }
  }
}

// 暴露方法
defineExpose({
  insertText
})

// 显示添加回复对话框
const showAddReplyDialog = () => {
  // 检查数量限制
  if (props.quickReplies.length >= 25) {
    ElMessage.warning('快捷回复已达到最大数量限制(25个)')
    return
  }
  
  dialogVisible.value = true
  dialogMode.value = 'add'
  replyForm.value = { content: '' }
}

// 提交回复表单
const submitReplyForm = () => {
  if (!replyForm.value.content.trim()) return
  
  try {
    // 获取回复内容
    const replyContent = replyForm.value.content.trim()
    
    // 处理表单提交逻辑
    if (dialogMode.value === 'add') {
      // 添加新回复
      // 检查是否已存在相同内容
      if (props.quickReplies.includes(replyContent)) {
        ElMessage.warning('已存在相同内容的快捷回复')
        return
      }
      
      // 检查数量限制
      if (props.quickReplies.length >= 25) {
        ElMessage.warning('快捷回复已达到最大数量限制(25个)')
        return
      }
      
      // 添加新回复到末尾
      const updatedQuickReplies = [...props.quickReplies, replyContent]
      emit('update-quick-replies', updatedQuickReplies)
      
      // 将新回复插入到输入框
      messageText.value = replyContent
      
      ElMessage.success('添加快捷回复成功')
    } else {
      // 编辑现有回复
      // 检查是否与其他回复内容重复
      const otherReplies = [...props.quickReplies]
      otherReplies.splice(editIndex.value, 1)
      
      if (otherReplies.includes(replyContent)) {
        ElMessage.warning('已存在相同内容的快捷回复')
        return
      }
      
      // 更新回复
      const updatedQuickReplies = [...props.quickReplies]
      updatedQuickReplies[editIndex.value] = replyContent
      emit('update-quick-replies', updatedQuickReplies)
      
      ElMessage.success('编辑快捷回复成功')
    }
  } catch (error) {
    console.error('提交回复失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    dialogVisible.value = false
    replyForm.value.content = ''
    editIndex.value = null
  }
}

// 显示编辑回复对话框
const showEditReplyDialog = (index, reply) => {
  dialogVisible.value = true
  dialogMode.value = 'edit'
  editIndex.value = index
  replyForm.value = { content: reply }
}

// 确认删除回复
const confirmDeleteReply = (index) => {
  ElMessageBox.confirm(
    '确定要删除这条快捷回复吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 删除快捷回复
    const updatedReplies = [...props.quickReplies]
    updatedReplies.splice(index, 1)
    emit('update-quick-replies', updatedReplies)
    ElMessage.success('删除成功')
  }).catch(() => {
    // 取消删除
  })
}
</script>

<style scoped>
.message-input-panel {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-top: 1px solid #e6e6e6;
}

/* 快捷回复样式 */
.quick-replies {
  padding: 12px 16px 8px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.reply-list {
  display: flex;
  gap: 8px;
  align-items: center;
  white-space: nowrap;
}

.reply-list .el-button {
  font-size: 12px;
  padding: 4px 12px;
  background: #fff;
  border: 1px solid #d9d9d9;
  color: #666;
  flex-shrink: 0;
}

.reply-list .el-button:hover {
  background: #409eff;
  border-color: #409eff;
  color: #fff;
}

/* 输入容器样式 */
.message-input-container {
  display: flex;
  align-items: flex-end;
  padding: 12px 16px;
  gap: 12px;
  background: #fff;
}

.left-tools-wrapper {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.tool-btn {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-input-wrapper {
  flex: 1;
  min-width: 0;
}

.message-input-wrapper .el-textarea {
  width: 100%;
}

.message-input-wrapper .el-textarea__inner {
  resize: none;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  padding: 8px 12px;
  line-height: 1.5;
  font-size: 14px;
}

.message-input-wrapper .el-textarea__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.send-btn-wrapper {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.send-btn {
  height: 36px;
  padding: 0 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
}

.send-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 文件预览样式 */
.file-preview {
  margin: 0 16px 12px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  background: #fafafa;
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f5f5f5;
  border-bottom: 1px solid #e6e6e6;
  font-size: 13px;
  color: #666;
}

.preview-content {
  padding: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
  max-height: 200px;
}

.preview-content .el-image {
  max-width: 100%;
  max-height: 100%;
  border-radius: 4px;
}

.preview-content video {
  max-width: 100%;
  max-height: 100%;
  border-radius: 4px;
}

/* 上传组件样式 */
.file-upload {
  display: inline-block;
}

.file-upload .el-upload {
  display: inline-block;
}

/* 兼容性样式 */
.image-upload {
  display: inline-block;
}

.image-upload .el-upload {
  display: inline-block;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .quick-replies {
    padding: 8px 12px 6px;
  }
  
  .reply-list {
    gap: 6px;
  }
  
  .reply-list .el-button {
    font-size: 11px;
    padding: 3px 8px;
  }
  
  .message-input-container {
    padding: 8px 12px;
    gap: 8px;
  }
  
  .tool-btn {
    width: 32px;
    height: 32px;
  }
  
  .send-btn {
    height: 32px;
    padding: 0 16px;
    font-size: 13px;
  }
  
  .message-input-wrapper .el-textarea__inner {
    padding: 6px 10px;
    font-size: 13px;
  }
  
  .image-preview {
    margin: 0 12px 8px;
  }
  
  .preview-content {
    padding: 8px;
    min-height: 80px;
    max-height: 150px;
  }
}

/* 响应式滚动条 */
.el-scrollbar__wrap {
  overflow-x: auto;
  overflow-y: hidden;
}

.el-scrollbar__bar.is-horizontal {
  height: 4px;
  bottom: 0;
}

.el-scrollbar__thumb {
  background-color: rgba(144, 147, 153, 0.3);
  border-radius: 2px;
}

.el-scrollbar__thumb:hover {
  background-color: rgba(144, 147, 153, 0.5);
}

/* 快捷回复项样式 */
.quick-reply-item {
  position: relative;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.quick-reply-item .el-button {
  margin-right: 0;
}

.quick-reply-actions {
  display: none;
  position: absolute;
  right: -5px;
  top: -8px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.quick-reply-item:hover .quick-reply-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  width: 20px !important;
  height: 20px !important;
  padding: 0 !important;
}

.action-btn .el-icon {
  font-size: 10px;
}
</style> 