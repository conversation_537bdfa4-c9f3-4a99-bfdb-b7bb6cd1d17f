<template>
  <div class="mobile-chat-container">
    <!-- 顶部导航栏 -->
    <div class="mobile-header">
      <div class="back-button" @click="goBack">
        <i class="el-icon-arrow-left"></i>
      </div>
      <div class="title">
        {{ currentConversation ? currentConversation.title || '熊小智' : '熊小智客服' }}
        <el-tag v-if="isAIAgent" type="success" size="small" class="agent-tag">AI</el-tag>
        <el-tag v-else-if="currentConversation && currentConversation.currentAgentType === 1" type="primary" size="small" class="agent-tag">人工</el-tag>
        <ConnectionStatus v-if="userStore.isLoggedIn" mode="mobile" />
      </div>
      <div class="header-actions">
        <!-- 如果是AI客服且没有转接请求，在右上角显示转人工按钮 -->
        <div class="header-transfer-btn" v-if="isAIAgent && !transferRequested && currentConversation" @click="requestHumanAgent">
          <span>转人工</span>
        </div>
        <i class="el-icon-more" v-if="currentConversation" @click="showActionMenu = true"></i>
      </div>
    </div>

    <!-- 消息区域 -->
    <div class="message-area" ref="messageContainer">
      <div v-if="loadingMessages" class="loading-messages">
        <i class="el-icon-loading"></i> 加载中...
      </div>
      
      <div v-if="messages.length === 0 && !loadingMessages" class="empty-messages">
        <div class="empty-tip">暂无消息，开始咨询吧</div>
      </div>
      
      <div v-else class="message-list">
        <div 
          v-for="(message, index) in messages" 
          :key="message.id || index"
          :class="[
            'message-item', 
            message.from === 0 || message.senderType === 0 ? 'user-message' : 
            isSystemMessage(message) ? 'system-message' :
            isMessageFromAI(message) ? 'ai-message' : 'agent-message',
            isImageMessage(message) ? 'image-message-item' : '',
            isVideoMessage(message) ? 'video-message-item' : ''
          ]"
        >
          <div class="message-avatar" v-if="!isSystemMessage(message) && (message.from !== 0 && message.senderType !== 0)">
            <img :src="getAvatarUrl(message)" alt="头像">
          </div>
          <div :class="[
            'message-content',
            {'system-content': isSystemMessage(message)},
            {'no-background': isImageMessage(message) || isVideoMessage(message)}
          ]">
            <!-- 文本消息 -->
            <div class="message-text" v-if="isTextMessage(message)" v-html="formatMessage(message.content)"></div>
            
            <!-- 图片消息 -->
            <div class="message-image" v-else-if="isImageMessage(message)">
              <img
                :src="getImageUrl(message.content)"
                alt="图片消息"
                @load="handleImageLoad(message)"
                @error="handleImageError(message)"
                @click="previewMessageImage(message.content)"
              />
              <div class="image-loading" v-if="message.loading">
                <i class="el-icon-loading"></i>
              </div>
            </div>
            
            <!-- 视频消息 -->
            <div class="message-video" v-else-if="isVideoMessage(message)">
              <video
                :src="getVideoUrl(message.content)"
                controls
                preload="metadata"
                @loadeddata="handleVideoLoad(message)"
                @error="handleVideoError(message)"
              >
                您的浏览器不支持视频播放
              </video>
              <div class="video-loading" v-if="message.loading">
                <i class="el-icon-loading"></i>
              </div>
            </div>
            
            <div class="message-time" v-if="!isSystemMessage(message)">{{ formatTime(message.createdAt) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部输入区域 -->
    <div class="input-area">
      <!-- 如果有转接请求，显示转接中 -->
      <div class="transfer-status" v-if="transferRequested">
        <i class="el-icon-loading"></i> 正在转接人工客服...
      </div>
      
      <!-- 常见问题区域 -->
      <div class="faq-container" v-if="faqList.length > 0 && !transferRequested">
        <div class="faq-list">
          <div 
            v-for="(faq, index) in displayedFaqs" 
            :key="index" 
            class="faq-item"
            @click="selectFaq(faq)"
          >
            <span class="faq-question">{{ faq.content || faq.question }}</span>
          </div>
        </div>
        <!-- 翻页按钮 -->
        <div class="faq-pagination" v-if="faqList.length > 3">
          <div class="pagination-btn prev" @click="prevFaqPage" :class="{disabled: faqPage === 0}">
            <i class="el-icon-arrow-left"></i>
          </div>
          <div class="pagination-indicator">{{ faqPage + 1 }}/{{ totalFaqPages }}</div>
          <div class="pagination-btn next" @click="nextFaqPage" :class="{disabled: faqPage >= totalFaqPages - 1}">
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </div>
      
      <!-- 聊天输入框区域容器 -->
      <div class="chat-input-container">
        <!-- 添加文件上传按钮 -->
        <div class="file-upload">
          <!-- 文件上传（图片和视频） -->
          <input 
            ref="fileInput" 
            type="file" 
            accept="image/*,video/*" 
            style="display:none" 
            @change="handleFileUploadAndSend"
          />
          <div class="input-action-button file-button" @click="triggerFileUpload" title="发送图片或视频">
            <i class="el-icon-folder-add"></i>
            <span class="button-tooltip">文件</span>
          </div>
        </div>
        
        <div class="input-wrapper">
          <textarea 
            class="message-input" 
            v-model="textMessage" 
            placeholder="请输入您要咨询的问题..." 
            :disabled="submitting"
            @keyup.enter="sendMessage"
          ></textarea>
        </div>
        
        <div 
          class="input-action-button send-button" 
          @click="sendMessage" 
          :class="{ 'disabled': !textMessage.trim() || submitting, 'active': textMessage.trim() && !submitting }"
          title="发送消息"
          v-show="textMessage.trim()"
        >
          <i class="el-icon-position"></i>
          <span class="send-text" v-if="textMessage.trim() && !submitting">发送</span>
        </div>
      </div>
    </div>

    <!-- 操作菜单弹出层 -->
    <div class="action-menu" v-if="showActionMenu" @click="showActionMenu = false">
      <div class="action-menu-content" @click.stop>
        <div class="action-menu-item" @click="handleEndChat">
          <i class="el-icon-close"></i> 结束会话
        </div>
        <div class="action-menu-item" v-if="isAIAgent && !transferRequested" @click="requestHumanAgent">
          <i class="el-icon-user"></i> 转接人工客服
        </div>
        <div class="action-menu-item" @click="startNewChat">
          <i class="el-icon-refresh"></i> 重新开始会话
        </div>
        <div class="action-menu-item" @click="showActionMenu = false">
          <i class="el-icon-close-circle"></i> 取消
        </div>
      </div>
    </div>

    <!-- 添加图片预览组件 -->
    <div class="fullscreen-image-preview" v-if="previewingImage" @click="closeImagePreview">
      <img :src="previewingImage" alt="预览" />
    </div>
  </div>
</template>

<script>
import { useChatStore } from '@/store/chat';
import { useUserStore } from '@/store/user';
import { watch, getCurrentInstance } from 'vue';
import { getSessionById as getConversationById, updateMessageStatus, updateReadStatus } from '@/api/chat';
import { getFaq } from '@/api/connect';
import companyLogo from '@/assets/images/ai-avatar.png'
import agentAvatarPic from '@/assets/images/agentAvatar.png'
import userAvatarPic from '@/assets/images/userAvatar.png'
import { ElMessage } from 'element-plus'
import ConnectionStatus from '@/components/ConnectionStatus.vue'

export default {
  name: 'MobileChat',
  components: {
    ConnectionStatus
  },
  setup() {
    const store = useChatStore();
    const userStore = useUserStore();
    
    // 监听store中的消息变化
    watch(() => store.messages, (newMessages) => {
      if (store.currentConversationId && newMessages.length > 0) {
        // 更新当前组件实例的messages
        setTimeout(() => {
          const instance = getCurrentInstance();
          if (instance && instance.proxy) {
            instance.proxy.messages = [...newMessages];
            instance.proxy.scrollToBottom();
          }
        }, 0);
      }
    }, { deep: true });
    
    return {
      store,
      userStore
    };
  },
  data() {
    return {
      store: useChatStore(),
      conversations: [],
      messages: [],
      messagesCount: 0,
      pageSize: 20,
      pageNum: 1,
      hasMoreMessages: true,
      loadingMessages: false,
      submitting: false,
      textMessage: '',
      currentClientId: null,
      isAgent: false,
      statusTimer: null,
      isPollingMode: false,
      userAvatar: userAvatarPic,
      agentAvatar: agentAvatarPic,
      aiAvatar: companyLogo,
      currentConversation: null,
      transferRequested: false,
      showActionMenu: false,
      previewImageUrl: null,
      previewingImage: null,
      faqList: [],
      faqPage: 0,
      totalFaqPages: 0,
      displayedFaqs: [],
    }
  },
  computed: {
    isAIAgent() {
      if (!this.currentConversation) return false;
      
      // 优先使用currentAgentType字段判断
      if (this.currentConversation.currentAgentType === 2) return true;
      
      // 备用: 使用agent.agentType判断 
      if (this.currentConversation.agent && this.currentConversation.agent.agentType === 2) return true;
      
      return false;
    }
  },
  mounted() {
    this.isMobile = true; // 标记为移动端视图
    this.fetchBasicInfo();
    
    // 设置viewport
    this.setMobileViewport();
    
    // 获取URL中的sessionId
    const sessionId = this.$route.query.sessionId;
    if (sessionId) {
      // 检查用户是否已登录
      const userStore = useUserStore();
      if (!userStore.isLoggedIn) {
        console.log('用户未登录，尝试自动注册');
        this.autoRegisterAndLogin(sessionId);
      } else {
        // 设置当前会话
        this.loadConversation(sessionId);
      }
    }
    
    // 初始化WebSocket连接
    this.store.setInChatPage(true);
    this.store.initWebSocket();
    this.checkConnectionStatus();
    
    // 初始化事件监听
    this.initEventListeners();
    
    // 添加消息监听
    this.initMessageListeners();
    
    // 设置检查用户状态的定时器
    this.statusTimer = setInterval(() => {
      this.checkUserStatus();
      this.checkConnectionStatus();
    }, 30000);

    // 自动滚动到底部
    this.$nextTick(() => {
      this.scrollToBottom();
    });
    
    // 加载常见问题
    this.loadFaqs();
  },
  beforeUnmount() {
    console.log('MobileChat.vue被销毁，清理资源');
    
    window.removeEventListener('chat-message-received', this.handleNewMessage);
    window.removeEventListener('chat-conversation-updated', this.handleConversationUpdated);
    window.removeEventListener('chat-polling-started', this.handlePollingStarted);
    window.removeEventListener('chat-polling-stopped', this.handlePollingStopped);
    window.removeEventListener('chat-transfer-notification', this.handleTransferNotification);
    
    if (this.statusTimer) {
      clearInterval(this.statusTimer);
    }
    
    this.store.setInChatPage(false);
  },
  methods: {
    // 复用原有逻辑方法
    initEventListeners() {
      window.addEventListener('chat-message-received', this.handleNewMessage);
      window.addEventListener('chat-conversation-updated', this.handleConversationUpdated);
      window.addEventListener('chat-polling-started', this.handlePollingStarted);
      window.addEventListener('chat-polling-stopped', this.handlePollingStopped);
      window.addEventListener('chat-transfer-notification', this.handleTransferNotification);
    },
    
    // 添加自动注册登录方法
    async autoRegisterAndLogin(sessionId) {
      try {
        // 调用自动注册API
        const { autoRegister } = await import('@/api/user');
        const res = await autoRegister();
        
        if (res.code === 200) {
          console.log('自动注册成功:', res.data);
          
          // 获取user store实例
          const userStore = useUserStore();
          
          // 保存用户信息和token
          userStore.setUserInfo(res.data.user);
          userStore.setToken(res.data.token);
          
          // 如果有sessionId就载入会话，否则创建新会话
          if (sessionId) {
            await this.loadConversation(sessionId);
          } else {
            // 创建新会话
            const sessionRes = await this.store.createNewConversation({ 
              userId: res.data.user.id,
              assignHuman: false, // 默认使用AI客服
              from: 0,  // 添加from字段，0代表用户发起
              channel: "Android", // 数据源下的渠道来源 如 ios Android 微信 支付宝 H5 等
              datasource: "移动端", // 数据来源
              collection_name: "information", // 查询知识库集合
              scene: "移动端场景" // 对话应用场景
            });
            
            if (sessionRes.code === 200 && sessionRes.data) {
              await this.loadConversation(sessionRes.data.id);
            }
          }
          
          return res;
        } else {
          console.error('自动注册失败:', res.message);
          return Promise.reject(res);
        }
      } catch (error) {
        console.error('自动注册异常:', error);
        return Promise.reject(error);
      }
    },
    
    // 载入会话方法
    async loadConversation(sessionId) {
      try {
        if (!sessionId) return;
        
        this.loadingMessages = true;
        console.log('开始加载会话:', sessionId);
        
        // 直接调用API获取会话详情
        const res = await getConversationById(sessionId);
        
        if (res.code === 200 && res.data) {
          const conversation = res.data;
          await this.store.setCurrentConversation(conversation);
          this.currentConversation = conversation;
          
          // 获取会话消息，增加消息条数到100条以确保完整历史记录
          await this.store.loadMessages(sessionId, 100);
          
          // 检查加载的消息数量，如果过少可能需要重试
          if (this.store.messages.length === 0) {
            console.log('首次加载无消息，尝试重新获取消息历史');
            await new Promise(resolve => setTimeout(resolve, 300)); // 稍微延迟一下再重试
            await this.store.loadMessages(sessionId, 100);
          }
          
          // 从store获取消息列表
          this.messages = [...this.store.messages];
          this.messagesCount = this.messages.length;
          
          // 尝试更新消息已读状态
          if (conversation.status !== 2) { // 如果会话未结束
            try {
              console.log('正在尝试更新消息已读状态, 会话ID:', sessionId, '发送者类型:', 1);
              // 使用updateReadStatus代替updateMessageStatus
              await updateReadStatus(sessionId, 1); // 1表示客服消息，将用户消息标记为已读
              console.log('消息已读状态已更新成功');
            } catch (e) {
              console.warn('更新消息已读状态失败，但不影响功能:', e);
            }
          }
          
          // 加载或刷新常见问题
          this.loadFaqs();
          
          console.log('会话加载成功，消息数量:', this.messages.length);
        } else {
          console.error('获取会话详情失败:', res);
          ElMessage.error('加载会话失败');
        }
      } catch (error) {
        console.error('加载会话失败:', error);
        ElMessage.error('加载失败，请重试');
      } finally {
        this.loadingMessages = false;
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    },
    
    // 复用原有消息处理逻辑
    handleNewMessage(event) {
      console.log('MobileChat.vue检测到新消息，消息详情:', event.detail);
      
      const message = event.detail;
      const isNewMessage = message.isNewMessage !== false;
      
      if (!isNewMessage) {
        console.log('非新消息，只更新状态不添加到列表');
        return;
      }
      
      const currentSessionId = this.store.currentConversationId || 
                             (this.currentConversation ? this.currentConversation.id : null);
      
      const msgSessionId = Number(message.sessionId);
      const currentConvId = Number(currentSessionId);
      
      console.log('当前会话ID:', currentConvId, '消息会话ID:', msgSessionId);
      
      const isCurrentSession = msgSessionId === currentConvId;
      
      if (isCurrentSession) {
        console.log('新消息属于当前会话，立即添加到消息列表');
        this.addMessageToList(message);
      } else {
        console.log('消息不属于当前会话，更新会话列表');
        this.updateConversationList(message.sessionId);
      }
    },
    
    handleConversationUpdated(event) {
      // 复用原有逻辑
      console.log('会话更新:', event.detail);
      
      const updatedConversation = event.detail;
      const idx = this.conversations.findIndex(c => c.id === updatedConversation.id);
      
      if (idx !== -1) {
        this.conversations[idx] = { ...this.conversations[idx], ...updatedConversation };
        
        if (updatedConversation.unreadCount > 0) {
          this.highlightConversation(updatedConversation.id);
        }
      } else {
        console.log('会话不存在，刷新会话列表');
        this.getConversations();
      }
    },
    
    // 移动端特有的方法
    goBack() {
      this.$router.go(-1);
    },
    
    formatMessage(content) {
      if (!content) return '';
      
      // 简单的消息格式化，可以根据需要扩展
      const formatted = content
        .replace(/\n/g, '<br>')
        .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');
      
      return formatted;
    },
    
    formatTime(timestamp) {
      if (!timestamp) return '';
      
      // 检查timestamp是否为有效日期格式，如果不是，返回当前时间
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) {
        // 使用当前时间代替无效的时间戳
        const now = new Date();
        return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
      }
      
      // 格式化时间
      const now = new Date();
      const diffMs = now - date;
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMs / 3600000);
      const diffDays = Math.floor(diffMs / 86400000);
      
      // 今天内的消息只显示时间
      if (diffDays === 0) {
        return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      }
      
      // 昨天的消息显示"昨天 时间"
      if (diffDays === 1) {
        return `昨天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      }
      
      // 一周内的消息显示星期几
      if (diffDays < 7) {
        const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
        return `星期${weekdays[date.getDay()]} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      }
      
      // 更早的消息显示完整日期
      return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    },
    
    sendMessage() {
      if (!this.textMessage.trim() || this.submitting) {
        return;
      }
      
      this.submitting = true;
      
      // 确保用户已登录
      const userStore = useUserStore();
      if (!userStore.isLoggedIn) {
        console.warn('用户未登录，尝试自动注册');
        try {
          this.autoRegisterAndLogin().then(() => {
            this.sendMessageAfterLogin();
          }).catch(err => {
            console.error('自动注册失败:', err);
            this.submitting = false;
            alert('登录失败，请刷新页面重试');
          });
        } catch (err) {
          console.error('自动注册过程中出错:', err);
          this.submitting = false;
          alert('登录失败，请刷新页面重试');
        }
        return;
      }
      
      this.sendMessageAfterLogin();
    },
    
    sendMessageAfterLogin() {
      // 确保有会话ID
      if (!this.currentConversation || !this.currentConversation.id) {
        console.warn('没有当前会话，创建新会话');
        const userStore = useUserStore();
        this.store.createNewConversation({
          userId: userStore.userId,
          assignHuman: false,
          from: 0  // 添加from字段，0代表用户发起
        }).then(res => {
          if (res.code === 200 && res.data) {
            console.log('创建会话成功:', res.data);
            // 设置当前会话数据
            this.store.setCurrentConversation(res.data).then(() => {
              this.currentConversation = res.data;
              this.doSendMessage();
            });
          } else {
            console.error('创建会话失败:', res);
            this.submitting = false;
            alert('创建会话失败，请重试');
          }
        }).catch(err => {
          console.error('创建会话错误:', err);
          this.submitting = false;
          alert('发送失败，请重试');
        });
        return;
      }
      
      this.doSendMessage();
    },
    
    doSendMessage() {
      // 检查当前会话是否存在
      if (!this.currentConversation || !this.currentConversation.id) {
        console.error('无法发送消息：当前会话不存在或ID为空');
        this.submitting = false;
        return;
      }
      
      // 确保WebSocket已连接
      if (!this.store.connected) {
        this.store.initWebSocket();
      }
      
      const message = this.textMessage;
      
      // 添加用户消息到UI（本地预览）
      const localMessageId = 'local-' + Date.now();
      this.messages.push({
        id: localMessageId,
        sessionId: this.currentConversation.id,
        content: message,
        from: 0, // 修改为数字类型，0代表用户
        senderType: 0,
        createdAt: new Date().toISOString()
      });
      
      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });
      
      // 判断是否为 AI 客服
      const isAI = this.isAIAgent;
      console.log('是否为AI客服对话:', isAI);
      
      // 如果是AI客服，使用流式API接口
      if (isAI) {
        this.handleAIChat(message, localMessageId);
      } else {
        // 通过store的sendTextMessage方法发送消息，这样能确保正确设置from参数
        this.store.sendTextMessage(message).then(() => {
          // 发送成功后清空输入框
          this.textMessage = '';
          this.submitting = false;
        }).catch(err => {
          console.error('发送消息失败:', err);
          this.submitting = false;
          
          // 移除本地预览的消息
          const index = this.messages.findIndex(m => m.id === localMessageId);
          if (index !== -1) {
            this.messages.splice(index, 1);
          }
          
          alert('发送失败，请重试');
        });
      }
    },
    
    // 处理AI聊天逻辑
    async handleAIChat(message, localMessageId) {
      try {
        // 先通过WebSocket发送用户消息，保存到数据库
        await this.store.sendTextMessage(message, null, true);
        
        // 发送成功后清空输入框
        this.textMessage = '';
        this.submitting = false;
        
        // 添加AI思考中的临时消息
        const loadingMsgId = 'loading-' + Date.now();
        const currentTime = new Date().toISOString();
        this.messages.push({
          id: loadingMsgId,
          sessionId: this.currentConversation.id,
          content: "正在思考中...",
          from: 1,
          senderType: 2, // AI客服
          createdAt: currentTime
        });
        
        // 滚动到底部
        this.$nextTick(() => {
          this.scrollToBottom();
        });
        
        // 创建临时消息对象用于流式更新
        const aiMsgId = 'ai-' + Date.now();
        let aiContent = '';
        
        // 替换临时消息为实际AI回复(初始为空)
        const loadingIndex = this.messages.findIndex(m => m.id === loadingMsgId);
        if (loadingIndex !== -1) {
          // 替换消息对象
          const updatedMessage = {
            id: aiMsgId,
            sessionId: this.currentConversation.id,
            content: aiContent,
            from: 1,
            senderType: 2, // AI客服
            createdAt: currentTime
          };
          
          // 替换消息对象
          this.messages.splice(loadingIndex, 1, updatedMessage);
          console.log('已将loadingMsg替换为aiMsg，ID:', aiMsgId, '索引:', loadingIndex);
        } else {
          console.warn('未找到loadingMsg，ID:', loadingMsgId);
          // 如果找不到loadingMsg，直接添加新消息
          this.messages.push({
            id: aiMsgId,
            sessionId: this.currentConversation.id,
            content: aiContent,
            from: 1,
            senderType: 2, // AI客服
            createdAt: currentTime
          });
          console.log('已添加新的aiMsg，ID:', aiMsgId);
        }
        
        // 创建EventSource连接流式API
        const apiUrl = `${import.meta.env.VITE_API_URL_LLM}/chat/stream`;
        
        // 检查环境变量是否正确配置
        if (!import.meta.env.VITE_API_URL_LLM) {
          throw new Error('VITE_API_URL_LLM 环境变量未配置，请检查 .env 文件');
        }
        
        console.log('使用流式API:', apiUrl);
        
        // 准备请求数据
        const history = this.generateHistoryMessages();
        console.log('对话历史:', history);
        console.log('对话历史数量:', history.length);

        const requestData = {
          messages: message,
          collection_name: "information", // 指定集合名称
          //model: "qwen2.5:3b",
          stream: true,
          history: history,
          scene: 'PC端',
          channel: 'PC端',
          datasource: 'PC端'
        };
        console.log('请求数据:', requestData);
        
        // 发起流式请求
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream'
          },
          body: JSON.stringify(requestData)
        });
        
        console.log('API响应状态:', response.status);
        
        if(!response.ok) {
          throw new Error(`HTTP错误，状态: ${response.status}`);
        }
        
        // 处理SSE流
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        let totalChunks = 0;
        
        // 开始处理流数据
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            console.log('数据流已关闭，总共处理块数:', totalChunks);
            break;
          }
          
          // 添加到缓冲区
          const newText = decoder.decode(value, { stream: true });
          buffer += newText;
          console.log(`收到新数据: ${newText.length} 字节，当前缓冲区: ${buffer.length} 字节`);
          
          // 持续处理所有完整的SSE消息
          let eventEndIndex;
          let processedEvents = 0;
          
          while ((eventEndIndex = buffer.indexOf('\n\n')) > -1) {
            const eventData = buffer.substring(0, eventEndIndex);
            buffer = buffer.substring(eventEndIndex + 2);
            processedEvents++;
            
            // 提取实际内容
            let content = '';
            
            // 处理各种可能的数据格式
            if (eventData.startsWith('data:')) {
              // SSE标准格式：data: 内容，去除前缀并清理空格
              content = eventData.substring(5).trim();
              // 如果内容仅为data:，则跳过
              if (!content || content === 'data:' || content === 'data: ') {
                console.log('跳过空data前缀');
                continue;
              }
            } else if (eventData.startsWith('大模型输出：')) {
              // 处理"大模型输出："格式
              content = eventData.substring(6).trim();
            } else {
              // 其他格式，直接使用
              content = eventData.trim();
            }
            
            // 如果内容为空，跳过处理
            if (!content) {
              console.log('跳过空内容块');
              continue;
            }
            
            // 特殊处理：检查是否有换行标记
            if (content === '\\n' || content === '\n' || content === '<br>' || content === '<br/>') {
              console.log('检测到独立换行符');
              content = '\n';
            }
            
            // 移除任何剩余的 "data:" 前缀
            content = content.replace(/^data:\s*/g, '');
            
            // 如果清理后内容为空，跳过处理
            if (!content.trim()) {
              console.log('清理前缀后内容为空，跳过');
              continue;
            }
            
            // 处理句子结束自动换行
            if ((aiContent.length > 0) && (content.endsWith('.') || content.endsWith('。') || content.endsWith('!') || content.endsWith('！'))) {
              console.log('检测到句子结束，添加换行');
              content = content + '\n';
            }

            // 记录收到的片段并添加到累积内容
            totalChunks++;
            
            // 保留原始格式，包括换行符
            if (content.includes('\n') || content.endsWith('.') || content.endsWith('。')) {
              console.log('检测到换行或句子结束，保留格式');
              // 如果检测到换行符或句号，保持原始格式
              aiContent += content;
            } else {
              // 否则正常追加内容
              aiContent += content;
            }
            
            // 更新聊天UI
            const aiIndex = this.messages.findIndex(m => m.id === aiMsgId);
            if (aiIndex !== -1) {
              // 直接更新内容，保持原始格式
              this.messages[aiIndex].content = aiContent;
              
              // 滚动到底部
              this.$nextTick(() => {
                this.scrollToBottom();
              });
            } else {
              console.warn('找不到AI消息，ID:', aiMsgId);
              
              // 添加新消息
              this.messages.push({
                id: aiMsgId,
                sessionId: this.currentConversation.id,
                content: aiContent,
                from: 1,
                senderType: 2, // AI客服
                createdAt: currentTime
              });
              
              // 滚动到底部
              this.$nextTick(() => {
                this.scrollToBottom();
              });
            }
          }
          
          if (processedEvents > 0) {
            console.log(`本次处理了 ${processedEvents} 个事件，缓冲区剩余 ${buffer.length} 字节`);
          }
        }
        
        console.log('流式接收完成，最终内容长度:', aiContent.length, '最终内容:', aiContent);
        
        // 确保最终内容的换行符被正确保留
        aiContent = aiContent.replace(/\\n/g, '\n');
        
        // 调整格式，如果有句子结束没有换行的情况，添加换行
        aiContent = aiContent.replace(/([.。!！?？])\s+([^\n])/g, '$1\n$2');
        
        // 流式接收完成后，保存完整回复到数据库
        console.log('流式接收完成，保存完整回复到数据库，内容长度:', aiContent.length);
        
        try {
          // 构建API地址
          const javaApiUrl = `${import.meta.env.VITE_API_BASE_URL || '/api'}/message/send`;
          
          // 使用fetch直接调用后端API
          const response = await fetch(javaApiUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `${localStorage.getItem('token') || ''}`
            },
            body: JSON.stringify({
              sessionId: this.currentConversation.id,
              content: aiContent,
              msgType: 0, // 0=文本消息
              senderType: 2, // 2=AI客服
              isRead: 0,
              scene: '默认应用场景', // 应用场景
              datasource: '默认数据源', // 数据源
              collectionName: "information", // 集合名称
              senderId: 0, // 发送者id
              channel: 'PC端' // 渠道
            })
          });
          
          if (!response.ok) {
            throw new Error(`API响应错误: ${response.status}`);
          }
          
          const data = await response.json();
          if (data.code !== 200) {
            throw new Error(data.message || 'API请求失败');
          }
          
          console.log('AI回复保存成功', data);
          
          // 更新本地消息对象，将临时ID替换为实际数据库ID
          if (data.data && data.data.id) {
            const aiIndex = this.messages.findIndex(m => m.id === aiMsgId);
            if (aiIndex !== -1) {
              this.messages[aiIndex].id = data.data.id;
              console.log('已更新AI消息ID为数据库ID:', data.data.id);
            }
          }
        } catch (e) {
          console.error('保存AI回复到数据库失败:', e);
          // 错误不影响用户体验，仅记录日志
        }
        
      } catch (error) {
        console.error('流式获取AI回复失败:', error);
        // 显示错误消息
        this.messages.push({
          id: 'err-' + Date.now(),
          sessionId: this.currentConversation.id,
          content: "抱歉，AI服务暂时无法回应，请稍后再试或转接人工客服",
          from: 1,
          senderType: 2, // AI客服
          createdAt: new Date().toISOString()
        });
        alert('获取AI回复失败，请稍后重试');
        this.submitting = false;
      }
    },
    
    // 生成历史消息
    generateHistoryMessages() {
      // 只从messages数组中获取用户消息和AI回复消息
      const historyMessages = this.messages
        .filter(m => {
          // 获取用户和AI消息，排除系统消息和加载状态消息
          const isUser = m.from === 0 || m.senderType === 0;
          const isAI = m.senderType === 2 || (m.from === 1 && this.currentConversation && this.currentConversation.currentAgentType === 2);
          return (isUser || isAI) && 
                 (!m.id || !m.id.toString().startsWith('loading-')) && 
                 (!m.id || !m.id.toString().startsWith('thinking-')) && 
                 m.content && m.content.trim() !== "";
        })
        .map(m => {
          // 转换为API要求的格式
          const role = (m.from === 0 || m.senderType === 0) ? "user" : "assistant";
          return {
            role: role,
            content: m.content
          };
        });
      
      // 限制历史消息数量以避免超长上下文，保留最新的10组对话
      const maxHistoryPairs = 10;
      if (historyMessages.length > maxHistoryPairs * 2) {
        // 仅保留最新的n组对话
        return historyMessages.slice(-maxHistoryPairs * 2);
      }
      
      return historyMessages;
    },
    
    scrollToBottom() {
      if (this.$refs.messageContainer) {
        this.$refs.messageContainer.scrollTop = this.$refs.messageContainer.scrollHeight;
      }
    },
    
    // 继承其他必要的方法...
    checkConnectionStatus() {
      const status = {
        isWebSocketConnected: this.store.connected,
        isPolling: this.store.isPolling,
        wsReadyState: this.store.ws ? this.store.ws.readyState : 'no-websocket',
        reconnectAttempts: this.store.reconnectAttempts,
        currentMode: this.store.connected ? 'websocket' : (this.store.isPolling ? 'polling' : 'none')
      };
      
      console.log('当前连接状态:', status);
      
      this.isPollingMode = status.isPolling;
      
      // 如果既没有WebSocket连接也没有HTTP轮询，尝试重新建立WebSocket连接
      if (!status.isWebSocketConnected && !status.isPolling && this.store.currentConversationId) {
        console.log('没有活动连接，尝试重新建立WebSocket连接');
        this.store.initWebSocket();
        
        // 如果3秒后WebSocket仍未连接成功，再使用最佳连接模式(可能会启动轮询)
        setTimeout(() => {
          if (!this.store.connected && this.store.currentConversationId) {
            console.log('WebSocket连接失败，尝试使用最佳连接模式');
            this.store.useOptimalConnectionMode();
          }
        }, 3000);
      }
    },
    addMessageToList(message) {
      // 处理消息，确保格式一致
      const exists = this.messages.some(m => m.id === message.id);
      
      if (!exists) {
        // 标准化消息格式
        const messageObj = {
          id: message.id,
          sessionId: message.sessionId,
          from: message.from, // 保持原始数值类型
          senderType: message.senderType || message.from,
          senderId: message.senderId,
          receiverId: message.receiverId,
          content: message.content,
          type: message.type || (message.msgType === 0 ? 1 : 2),
          msgType: message.msgType || (message.type === 1 ? 0 : 1),
          createdAt: message.createdAt || new Date().toISOString(),
          status: message.status || 1,
        };
        
        // 针对图片消息，保留额外属性
        if (this.isImageMessage(messageObj)) {
          console.log('添加图片消息到列表:', messageObj);
          // 确保图片消息类型正确
          messageObj.type = 2;
          messageObj.msgType = 1;
        }
        
        this.messages.push(messageObj);
        this.messagesCount = this.messages.length;
        
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    },
    
    // 其他必要的方法复用...
    fetchBasicInfo() {
      // 实现获取基本信息的逻辑
    },
    
    getConversations() {
      // 实现获取会话列表的逻辑
    },
    
    highlightConversation() {
      // 移动端可能不需要高亮会话，或者实现不同的高亮方式
    },
    
    handlePollingStarted(event) {
      console.log('HTTP轮询已开始:', event.detail);
      this.isPollingMode = true;
    },
    
    handlePollingStopped(event) {
      console.log('HTTP轮询已停止:', event.detail);
      this.isPollingMode = false;
    },
    
    updateConversationList(sessionId) {
      // 更新会话列表的逻辑
      const sessionIndex = this.conversations.findIndex(s => s.id === sessionId);
      
      if (sessionIndex === -1) {
        console.log('会话不在列表中，刷新会话列表');
        this.getConversations();
      }
    },
    
    checkUserStatus() {
      const userStore = useUserStore();
      if (!userStore.isLoggedIn) {
        console.log('用户未登录，尝试自动登录');
        // 检查本地存储中是否有登录信息
        const token = localStorage.getItem('user_token');
        const userInfo = localStorage.getItem('user_info');
        
        if (token && userInfo) {
          try {
            const user = JSON.parse(userInfo);
            userStore.setToken(token);
            userStore.setUserInfo(user);
            console.log('从本地存储恢复用户登录状态成功');
          } catch (error) {
            console.error('解析本地存储的用户信息失败:', error);
          }
        }
      }
    },
    
    // 添加消息监听方法
    initMessageListeners() {
      // 监听store中消息变化，确保消息在UI上更新，但保留图片消息
      watch(() => this.store.messages, (newMessages) => {
        if (newMessages.length === 0) return; // 如果是空消息列表，不要更新
        
        console.log('Store消息更新，处理图片消息保留...');
        
        // 创建旧消息的ID映射，用于快速查找
        const existingMessageMap = {};
        this.messages.forEach(msg => {
          if (msg.id) {
            existingMessageMap[msg.id] = msg;
          }
        });
        
        // 合并消息，保留现有图片消息
        const mergedMessages = [...newMessages];
        
        // 检查现有消息中是否有未包含在新消息中的图片消息
        this.messages.forEach(oldMsg => {
          // 如果是图片消息且不在新消息中
          if (this.isImageMessage(oldMsg) && !newMessages.some(m => m.id === oldMsg.id)) {
            console.log('保留图片消息，ID:', oldMsg.id);
            mergedMessages.push(oldMsg);
          }
        });
        
        // 按时间排序
        mergedMessages.sort((a, b) => {
          const timeA = new Date(a.createdAt || Date.now()).getTime();
          const timeB = new Date(b.createdAt || Date.now()).getTime();
          return timeA - timeB;
        });
        
        // 更新消息列表
        this.messages = mergedMessages;
        
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }, { deep: true });
    },
    
    isMessageFromAI(message) {
      if (message.senderType === 2) return true;
      if (message.from === 2) return true;
      if (message.from === 1 && this.currentConversation && this.currentConversation.currentAgentType === 2) return true;
      return false;
    },
    
    isSystemMessage(message) {
      if (message.type === 'system' || message.msgType === 2) return true;
      // 检查ID前缀是否为系统消息
      if (message.id && typeof message.id === 'string') {
        const systemPrefixes = ['system-', 'transfer-', 'end-'];
        for (const prefix of systemPrefixes) {
          if (message.id.startsWith(prefix)) return true;
        }
      }
      // 检查内容是否为系统通知
      if (message.content && typeof message.content === 'string') {
        const systemContents = [
          '会话已结束', 
          '正在请求转接人工客服', 
          '已成功转接到人工客服',
          '转接人工客服失败',
          '转接人工客服请求失败'
        ];
        for (const content of systemContents) {
          if (message.content.includes(content)) return true;
        }
      }
      return false;
    },
    
    getAvatarUrl(message) {
      // 系统消息无头像
      if (this.isSystemMessage(message)) {
        return '';
      }
      
      // 用户消息
      if (message.from === 0 || message.senderType === 0) {
        return this.userAvatar;
      }
      
      // AI消息
      if (this.isMessageFromAI(message)) {
        return this.aiAvatar;
      }
      
      // 人工客服消息
      return this.agentAvatar;
    },
    
    // 请求转接人工客服
    async requestHumanAgent() {
      if (!this.currentConversation) {
        alert('当前没有活跃会话');
        return;
      }
      
      this.transferRequested = true;
      this.showActionMenu = false;
      
      try {
        // 添加转接请求消息
        this.messages.push({
          id: 'transfer-' + Date.now(),
          sessionId: this.currentConversation.id,
          content: "正在请求转接人工客服，请稍候...",
          from: 1, // 修改为数字，代表系统/客服
          senderType: 2,
          createdAt: new Date().toISOString()
        });
        
        // 滚动到底部
        this.$nextTick(() => {
          this.scrollToBottom();
        });
        
        // 准备请求数据，后端可根据场景和数据源分配合适的客服
        const transferData = {
          sessionId: this.currentConversation.id,
          scene: this.currentConversation.scene || '默认应用场景',
          datasource: this.currentConversation.datasource || '默认数据源',
          // 如果当前会话中有指定的客服ID，则传递该ID
          targetAgentId: this.currentConversation.preferredAgentId || null
        };
        
        // 调用API请求转接，增加请求体传递参数
        const res = await fetch(`${window.location.origin}/api/session/${this.currentConversation.id}/transfer-to-human`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `${localStorage.getItem('token') || ''}`
          },
          body: JSON.stringify(transferData)
        });
        
        const data = await res.json();
        console.log('转人工响应:', data);
        
        if (data.code === 200) {
          // 更新当前会话状态
          this.currentConversation.currentAgentType = 1;
          this.currentConversation.transferRequested = 1;
          
          // 如果响应中返回了分配的客服ID，保存到当前会话
          if (data.data && data.data.agentId) {
            this.currentConversation.agentId = data.data.agentId;
          }
          
          // 转接成功，结束请求状态
          this.transferRequested = false;
          
          // 刷新会话信息
          await this.refreshConversation();
        } else {
          // 转接失败
          this.messages.push({
            id: 'system-' + Date.now(),
            sessionId: this.currentConversation.id,
            content: data.message || "转接人工客服失败，请稍后再试",
            from: 1, // 修改为数字，代表系统/客服
            senderType: 1,
            createdAt: new Date().toISOString()
          });
          
          // alert(data.message || '转接请求失败，请稍后再试');
          this.transferRequested = false;
        }
      } catch (error) {
        console.error('请求转接人工客服失败', error);
        
        // 添加错误消息
        this.messages.push({
          id: 'system-' + Date.now(),
          sessionId: this.currentConversation.id,
          content: "转接人工客服请求失败，请稍后重试",
          from: 1, // 修改为数字，代表系统/客服
          senderType: 1,
          createdAt: new Date().toISOString()
        });
        
        alert('请求转接人工客服失败，请稍后重试');
        this.transferRequested = false;
      }
    },
    
    // 刷新当前会话
    async refreshConversation() {
      if (!this.currentConversation || !this.currentConversation.id) {
        return;
      }
      
      try {
        const res = await getConversationById(this.currentConversation.id);
        if (res.code === 200 && res.data) {
          await this.store.setCurrentConversation(res.data);
          this.currentConversation = res.data;
        }
      } catch (error) {
        console.error('刷新会话失败:', error);
      }
    },
    
    // 结束会话
    async handleEndChat() {
      if (!this.currentConversation) return;
      
      try {
        this.showActionMenu = false;
        
        // 显示确认框
        if (window.confirm('确定要结束当前会话吗？')) {
          // 关闭会话
          const res = await this.store.closeConversation({ 
            conversationId: this.currentConversation.id,
            closedBy: 'user'
          });
          
          if (res.code === 200) {
            alert('会话已结束');
            
            // 重新加载会话列表
            await this.store.fetchConversations();
            
            // 显示结束消息
            this.messages.push({
              id: 'end-' + Date.now(),
              sessionId: this.currentConversation.id,
              content: "会话已结束",
              from: 1, // 修改为数字类型
              senderType: 1,
              createdAt: new Date().toISOString()
            });
            
            // 清空当前会话
            this.currentConversation = null;
          } else {
            alert(res.message || '结束会话失败');
          }
        }
      } catch (error) {
        console.error('结束会话失败', error);
        alert('结束会话失败，请稍后重试');
      }
    },
    
    // 开始新会话
    async startNewChat() {
      try {
        this.showActionMenu = false;
        
        const userStore = useUserStore();
        if (!userStore.isLoggedIn || !userStore.userId) {
          ElMessage.warning('请先登录');
          return;
        }
        
        // 创建新会话
        const sessionRes = await this.store.createNewConversation({ 
          userId: userStore.userId,
          assignHuman: false // 默认使用AI客服
        });
        
        if (sessionRes.code === 200) {
          // 加载新会话
          await this.loadConversation(sessionRes.data.id);
          
          // 更新URL参数
          this.$router.replace({
            path: '/mobile-chat',
            query: { 
              sessionId: sessionRes.data.id,
              t: new Date().getTime() // 添加时间戳避免缓存
            }
          });
          
          ElMessage.success('已创建新会话');
        } else {
          ElMessage.error(sessionRes.message || '创建新会话失败');
        }
      } catch (error) {
        console.error('创建新会话失败:', error);
        ElMessage.error('创建新会话失败，请重试');
      }
    },

    // 添加设置移动端viewport的方法
    setMobileViewport() {
      // 设置移动端视图
      const meta = document.createElement('meta');
      meta.name = 'viewport';
      meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
      document.getElementsByTagName('head')[0].appendChild(meta);
    },

    // 添加图片上传逻辑
    async handleImageUploadAndSend(event) {
      const file = event.target.files[0];
      if (!file) return;
      
      // 检查会话
      if (!this.currentConversation) {
        ElMessage.warning('请先开始会话');
        return;
      }
      
      // 检查会话状态
      if (this.currentConversation.status === 2) {
        ElMessage.warning('会话已结束，无法发送消息');
        return;
      }
      
      // 校验文件
      const isImage = file.type.startsWith('image/');
      const isLt100M = file.size / 1024 / 1024 < 100;

      if (!isImage) {
        ElMessage.error('只能上传图片文件');
        return;
      }

      if (!isLt100M) {
        ElMessage.error('图片大小不能超过 100MB');
        return;
      }
      
      this.submitting = true;
      
      try {
        
        // 创建本地URL用于临时显示
        const localImageUrl = URL.createObjectURL(file);
        
        // 创建临时消息
        const tempMessageId = 'temp-' + Date.now();
        this.messages.push({
          id: tempMessageId,
          sessionId: this.currentConversation.id,
          content: localImageUrl,
          from: 0, // 用户发送
          senderType: 0, // 用户
          type: 'image', // 图片消息
          msgType: 1, // 图片消息 (后端格式)
          createdAt: new Date().toISOString(),
          sending: true,
          loading: true
        });
        
        // 滚动到底部
        this.$nextTick(() => {
          this.scrollToBottom();
        });
        
        // 创建FormData
        const formData = new FormData();
        formData.append('file', file);

        console.log('开始上传图片...');

        // 调用后端的上传接口
        const response = await fetch('/api/file/upload', {
          method: 'POST',
          headers: {
            'Authorization': `${localStorage.getItem('token') || ''}`
          },
          body: formData
        });

        console.log('上传响应状态:', response.status);

        if (!response.ok) {
          throw new Error('图片上传失败，服务器响应: ' + response.status);
        }

        const result = await response.json();
        console.log('上传响应结果:', result);

        if (result.code !== 200) {
          throw new Error(result.message || '图片上传失败');
        }

        // 获取上传后的图片URL
        const imageUrl = result.data.fileUrl;
        console.log('图片上传成功，URL:', imageUrl);
        
        // 使用WebSocket发送图片消息
        const userStore = useUserStore();
        const wsMessage = {
          type: 2, // 图片消息类型
          from: 0, // 0-用户发送
          senderId: userStore.userId,
          receiverId: this.currentConversation.agentId,
          sessionId: this.currentConversation.id,
          content: imageUrl,
          msgType: 1, // 1-图片消息类型
          timestamp: Date.now()
        };
        
        // 发送WebSocket消息
        const msgResult = await this.store.sendMessage(wsMessage);
        console.log('WebSocket图片消息发送成功:', wsMessage);
        
        // 更新临时消息
        const messageIndex = this.messages.findIndex(m => m.id === tempMessageId);
        if (messageIndex !== -1) {
          // 更新消息
          this.messages[messageIndex] = {
            ...this.messages[messageIndex],
            id: msgResult?.id || tempMessageId,
            content: imageUrl,
            sending: false,
            loading: false
          };
          
          // 强制更新
          this.messages = [...this.messages];
        }
        
        ElMessage.success('图片发送成功');
      } catch (error) {
        console.error('图片发送失败:', error);
        ElMessage.error(error.message || '图片发送失败，请稍后重试');
        
        // 移除临时消息
        const messageIndex = this.messages.findIndex(m => m.sending === true);
        if (messageIndex !== -1) {
          this.messages.splice(messageIndex, 1);
        }
      } finally {
        this.submitting = false;
        // 清空文件输入
        if (this.$refs.fileInput) {
          this.$refs.fileInput.value = '';
        }
        
        // 释放临时URL
        URL.revokeObjectURL(localImageUrl);
      }
    },

    // 添加图片预览逻辑
    previewMessageImage(imageUrl) {
      console.log('预览图片:', imageUrl);
      this.previewingImage = this.getImageUrl(imageUrl);
    },

    // 添加图片预览取消逻辑
    closeImagePreview() {
      this.previewingImage = null;
    },

    // 添加图片加载逻辑
    handleImageLoad(message) {
      console.log('图片加载成功:', message.content);
      // 更新消息对象，移除加载状态
      if (message.loading) {
        const index = this.messages.findIndex(m => m.id === message.id);
        if (index !== -1) {
          this.messages[index].loading = false;
        }
      }
    },

    // 添加图片错误处理逻辑
    handleImageError(message) {
      console.error('图片加载失败:', message.content);
      // 更新消息对象，标记加载失败
      const index = this.messages.findIndex(m => m.id === message.id);
      if (index !== -1) {
        this.messages[index].loadError = true;
        this.messages[index].loading = false;
      }
      
      // 显示错误提示
      ElMessage.error('图片加载失败');
    },

    // 添加文本消息判断逻辑
    isTextMessage(message) {
      if (message.type === 1 || message.msgType === 0) return true;
      if (message.type === 'text') return true;
      return !this.isImageMessage(message) && !this.isVideoMessage(message) && !this.isSystemMessage(message);
    },

    // 添加图片消息判断逻辑
    isImageMessage(message) {
      if (!message) return false;
      
      // 显式检查消息类型标记 - 排除视频消息
      if (message.msgType === 2) return false; // 视频消息，不是图片
      if (message.type === 2 && message.msgType === 1) return true; // 图片消息
      if (message.type === 'image') return true;
      
      // 检查内容是否为图片URL
      if (message.content && typeof message.content === 'string') {
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'];
        const isImageUrl = imageExtensions.some(ext => message.content.toLowerCase().includes(ext)) ||
                          message.content.startsWith('data:image/') ||
                          (message.content.startsWith('blob:') && !message.content.includes('video'));
        
        if (isImageUrl) {
          // 如果是图片URL，主动更新消息类型
          message.type = 2;
          message.msgType = 1;
          return true;
        }
      }
      return false;
    },

    // 添加视频消息判断逻辑
    isVideoMessage(message) {
      if (!message) return false;
      
      // 显式检查消息类型标记
      if (message.type === 3 || message.msgType === 2) return true;
      if (message.type === 'video') return true;
      
      // 检查内容是否为视频URL
      if (message.content && typeof message.content === 'string') {
        const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v'];
        const isVideoUrl = videoExtensions.some(ext => message.content.toLowerCase().includes(ext)) ||
                          message.content.startsWith('data:video/') ||
                          (message.content.startsWith('blob:') && message.content.includes('video'));
        
        if (isVideoUrl) {
          // 如果是视频URL，主动更新消息类型
          message.type = 3;
          message.msgType = 2;
          return true;
        }
      }
      return false;
    },

    // 添加获取图片URL逻辑
    getImageUrl(content) {
      if (!content) return '';
      
      // 处理blob URL
      if (content.startsWith('blob:')) {
        return content;
      }
      
      // 处理base64图片
      if (content.startsWith('data:image/')) {
        return content;
      }
      
      // 如果已经是完整的URL，直接返回
      if (content.startsWith('http://') || content.startsWith('https://')) {
        return content;
      }
      
      // 否则拼接MinIO URL
      // const minioEndpoint = import.meta.env.VITE_MINIO_ENDPOINT;
      // const minioBucket = import.meta.env.VITE_MINIO_BUCKET;
      // if (minioEndpoint && minioBucket) {
      //   return `${minioEndpoint}/${minioBucket}/${content}`;
      // }
      
      // 如果没有配置MinIO，直接返回
      return content;
    },

    // 统一文件选择触发逻辑
    triggerFileUpload() {
      const fileInput = this.$refs.fileInput;
      if (fileInput) {
        fileInput.click();
      }
    },

    // 统一文件上传处理逻辑
    async handleFileUploadAndSend(event) {
      const file = event.target.files[0];
      if (!file) return;
      
      // 检查文件类型
      const isImage = file.type.startsWith('image/');
      const isVideo = file.type.startsWith('video/');
      
      if (!isImage && !isVideo) {
        ElMessage.error('只支持图片和视频文件');
        return;
      }
      
      // 根据文件类型调用相应的处理函数
      if (isImage) {
        await this.handleImageUploadAndSend(event);
      } else if (isVideo) {
        await this.handleVideoUploadAndSend(event);
      }
      
      // 清空input值，允许重复选择同一文件
      event.target.value = '';
    },

    // 添加视频上传逻辑
    async handleVideoUploadAndSend(event) {
      const file = event.target.files[0];
      if (!file) return;
      
      // 检查会话
      if (!this.currentConversation) {
        ElMessage.warning('请先开始会话');
        return;
      }
      
      // 检查会话状态
      if (this.currentConversation.status === 2) {
        ElMessage.warning('会话已结束，无法发送消息');
        return;
      }
      
      // 校验文件
      const isVideo = file.type.startsWith('video/');
      const isLt500M = file.size / 1024 / 1024 < 500; // 视频文件限制500MB

      if (!isVideo) {
        ElMessage.error('只能上传视频文件');
        return;
      }

      if (!isLt500M) {
        ElMessage.error('视频大小不能超过 500MB');
        return;
      }
      
      this.submitting = true;
      
      try {
        // 创建本地URL用于临时显示
        const localVideoUrl = URL.createObjectURL(file);
        
        // 创建临时消息
        const tempMessageId = 'temp-video-' + Date.now();
        this.messages.push({
          id: tempMessageId,
          sessionId: this.currentConversation.id,
          content: localVideoUrl,
          from: 0, // 用户发送
          senderType: 0, // 用户
          type: 'video', // 视频消息
          msgType: 2, // 视频消息 (后端格式)
          createdAt: new Date().toISOString(),
          sending: true,
          loading: true
        });
        
        // 滚动到底部
        this.$nextTick(() => {
          this.scrollToBottom();
        });
        
        // 创建FormData
        const formData = new FormData();
        formData.append('file', file);

        console.log('开始上传视频...');

        // 调用后端的上传接口
        const response = await fetch('/api/file/upload', {
          method: 'POST',
          headers: {
            'Authorization': `${localStorage.getItem('token') || ''}`
          },
          body: formData
        });

        console.log('上传响应状态:', response.status);

        if (!response.ok) {
          throw new Error('视频上传失败，服务器响应: ' + response.status);
        }

        const result = await response.json();
        console.log('上传响应结果:', result);

        if (result.code !== 200) {
          throw new Error(result.message || '视频上传失败');
        }

        // 获取上传后的视频URL
        const videoUrl = result.data.fileUrl;
        console.log('视频上传成功，URL:', videoUrl);
        
        // 使用WebSocket发送视频消息
        const userStore = useUserStore();
        const wsMessage = {
          type: 2, // 文件消息类型（包括图片和视频）
          from: 0, // 0-用户发送
          senderId: userStore.userId,
          receiverId: this.currentConversation.agentId,
          sessionId: this.currentConversation.id,
          content: videoUrl,
          msgType: 2, // 2-视频消息类型
          timestamp: Date.now()
        };
        
        // 发送WebSocket消息
        const msgResult = await this.store.sendMessage(wsMessage);
        console.log('WebSocket视频消息发送成功:', wsMessage);
        
        // 更新临时消息
        const messageIndex = this.messages.findIndex(m => m.id === tempMessageId);
        if (messageIndex !== -1) {
          // 更新消息
          this.messages[messageIndex] = {
            ...this.messages[messageIndex],
            id: msgResult?.id || tempMessageId,
            content: videoUrl,
            sending: false,
            loading: false
          };
          
          // 强制更新
          this.messages = [...this.messages];
        }
        
        ElMessage.success('视频发送成功');
      } catch (error) {
        console.error('视频发送失败:', error);
        ElMessage.error(error.message || '视频发送失败，请稍后重试');
        
        // 移除临时消息
        const messageIndex = this.messages.findIndex(m => m.sending === true);
        if (messageIndex !== -1) {
          this.messages.splice(messageIndex, 1);
        }
      } finally {
        this.submitting = false;
        // 清空文件输入
        if (this.$refs.videoInput) {
          this.$refs.videoInput.value = '';
        }
        
        // 释放临时URL
        URL.revokeObjectURL(localVideoUrl);
      }
    },

    // 添加视频加载逻辑
    handleVideoLoad(message) {
      console.log('视频加载成功:', message.content);
      // 更新消息对象，移除加载状态
      if (message.loading) {
        const index = this.messages.findIndex(m => m.id === message.id);
        if (index !== -1) {
          this.messages[index].loading = false;
        }
      }
    },

    // 添加视频错误处理逻辑
    handleVideoError(message) {
      console.error('视频加载失败:', message.content);
      // 更新消息对象，标记加载失败
      const index = this.messages.findIndex(m => m.id === message.id);
      if (index !== -1) {
        this.messages[index].loadError = true;
        this.messages[index].loading = false;
      }
      
      // 显示错误提示
      ElMessage.error('视频加载失败');
    },

    // 添加获取视频URL逻辑
    getVideoUrl(content) {
      if (!content) return '';
      
      // 处理blob URL
      if (content.startsWith('blob:')) {
        return content;
      }
      
      // 处理base64视频
      if (content.startsWith('data:video/')) {
        return content;
      }
      
      // 如果已经是完整的URL，直接返回
      if (content.startsWith('http://') || content.startsWith('https://')) {
        return content;
      }
      
      // 否则拼接MinIO URL
      // const minioEndpoint = import.meta.env.VITE_MINIO_ENDPOINT;
      // const minioBucket = import.meta.env.VITE_MINIO_BUCKET;
      // if (minioEndpoint && minioBucket) {
      //   return `${minioEndpoint}/${minioBucket}/${content}`;
      // }
      
      // 如果没有配置MinIO，直接返回
      return content;
    },
    
    // 添加处理转接通知的方法
    handleTransferNotification(event) {
      console.log('移动端收到转接人工客服通知:', event.detail);
      
      // 设置转接状态
      this.transferRequested = true;
      
      // 获取通知详情
      const { content, agentId, sessionId } = event.detail;
      
      // 确认是否为当前会话的通知
      if (this.currentConversation && this.currentConversation.id === sessionId) {
        // 更新当前会话状态
        this.currentConversation.currentAgentType = 1; // 设置为人工客服
        this.currentConversation.transferRequested = 1; // 设置为已请求转接状态
        
        // 如果有agentId，更新客服ID
        if (agentId) {
          this.currentConversation.agentId = agentId;
        }
        
        // 添加系统消息到UI
        this.messages.push({
          id: 'system-' + Date.now(),
          sessionId: sessionId,
          content: content,
          from: 1, // 系统/客服
          senderType: 1, // 系统/客服
          createdAt: new Date().toISOString()
        });
        
        // 滚动到底部
        this.$nextTick(() => {
          this.scrollToBottom();
        });
        
        // 显示转接成功的提示弹窗
        this.$notify({
          title: '转接通知',
          message: content,
          type: 'success',
          duration: 4500
        });
        
        // 请求刷新会话详情
        this.refreshConversation();
      }
      
      // 延迟5秒后重置转接状态
      setTimeout(() => {
        this.transferRequested = false;
      }, 5000);
    },
    selectFaq(faq) {
      // 将问题填入输入框，适配不同的数据结构
      this.textMessage = faq.content || faq.question;
      
      // 聚焦输入框
      this.$nextTick(() => {
        const textarea = document.querySelector('.message-input');
        if (textarea) {
          textarea.focus();
        }
      });
    },
    prevFaqPage() {
      this.faqPage = Math.max(0, this.faqPage - 1);
      this.updateDisplayedFaqs();
    },
    nextFaqPage() {
      this.faqPage = Math.min(this.totalFaqPages - 1, this.faqPage + 1);
      this.updateDisplayedFaqs();
    },
    updateDisplayedFaqs() {
      const start = this.faqPage * 3;
      this.displayedFaqs = this.faqList.slice(start, start + 3);
    },
    // 加载常见问题
    async loadFaqs() {
      try {
        console.log('开始加载常见问题');
        
        // 获取场景和数据源，优先使用当前会话的信息
        let scene = '默认应用场景';
        let datasource = '默认数据源';
        
        // 如果有当前会话，尝试获取会话中的场景和数据源
        if (this.currentConversation) {
          if (this.currentConversation.scene) {
            scene = this.currentConversation.scene;
          }
          if (this.currentConversation.datasource) {
            datasource = this.currentConversation.datasource;
          }
        }
        
        console.log('请求常见问题，场景:', scene, '数据源:', datasource);
        
        const res = await getFaq({
          scene,
          datasource
        });
        
        console.log('常见问题加载结果:', res);
        
        if (res.code === 200 && res.data) {
          this.faqList = res.data;
          this.totalFaqPages = Math.ceil(this.faqList.length / 3);
          this.faqPage = 0; // 重置为第一页
          this.updateDisplayedFaqs();
          console.log('加载了常见问题:', this.faqList.length, '条');
        } else {
          console.warn('常见问题加载失败:', res.message);
          // 设置一些默认问题
          this.setDefaultFaqs();
        }
      } catch (error) {
        console.error('加载常见问题异常:', error);
        // 发生异常时设置默认问题
        this.setDefaultFaqs();
      }
    },
    
    // 设置默认常见问题
    setDefaultFaqs() {
      this.faqList = [
        { content: '你们公司是做什么的？', scene: '默认应用场景', datasource: '默认数据源' },
        { content: '你是谁？', scene: '默认应用场景', datasource: '默认数据源' },
        { content: '你们有哪些产品？', scene: '默认应用场景', datasource: '默认数据源' },
        { content: '如何查看订单状态', scene: '默认应用场景', datasource: '默认数据源' },
        { content: '如何确认收货', scene: '默认应用场景', datasource: '默认数据源' }
      ];
      this.totalFaqPages = Math.ceil(this.faqList.length / 3);
      this.faqPage = 0;
      this.updateDisplayedFaqs();
      console.log('已设置默认常见问题');
    },
  }
}
</script>

<style scoped>
.mobile-chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f9fb;
  position: relative;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
}

.mobile-header {
  display: flex;
  align-items: center;
  height: 50px;
  padding: 0 10px;
  background-color: #409eff;
  color: white;
  position: relative;
  z-index: 10;
}

.back-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.title {
  flex-grow: 1;
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title .agent-tag {
  margin-left: 8px;
  font-size: 10px;
  height: 20px;
  line-height: 1;
  display: flex;
  align-items: center;
  border-radius: 10px;
  padding: 0 6px;
}

.header-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 10px;
}

.header-transfer-btn {
  background-color: #ffffff;
  color: #3a7bd5;
  border-radius: 14px;
  padding: 2px 10px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.header-transfer-btn:active {
  transform: scale(0.95);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-area {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  -webkit-overflow-scrolling: touch;
  background-image: linear-gradient(to bottom, rgba(235, 242, 254, 0.7) 0%, rgba(255, 255, 255, 0.9) 100%);
}

.loading-messages {
  text-align: center;
  padding: 15px 0;
  color: #999;
  font-size: 14px;
}

.empty-messages {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;
}

.empty-tip {
  color: #999;
  font-size: 15px;
  background: rgba(255, 255, 255, 0.8);
  padding: 15px 25px;
  border-radius: 20px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
}

.message-list {
  display: flex;
  flex-direction: column;
  padding: 5px 0;
}

.message-item {
  display: flex;
  margin-bottom: 30px; /* 增加间距来容纳时间戳 */
  max-width: 85%;
  align-items: flex-start;
  transition: all 0.3s ease;
  animation: message-fade-in 0.3s ease;
  position: relative; /* 为时间戳定位提供参考 */
}

@keyframes message-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-message {
  flex-direction: row-reverse;
  align-self: flex-end;
  margin-right: 0;
}

.agent-message, .ai-message {
  align-self: flex-start;
  margin-left: 0;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 8px;
  flex-shrink: 0;
  background-color: #e5e9f2;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
}

.message-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ai-message .message-avatar {
  padding: 3px;
  border: 2px solid #dceaff;
  background-color: white;
}

.message-content {
  position: relative;
  max-width: 100%;
  word-wrap: break-word;
  padding: 12px 16px;
  border-radius: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.message-content::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  top: 11px;
}

.user-message .message-content {
  background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%);
  color: white;
  border-radius: 20px 5px 20px 20px;
  margin-right: 5px;
  box-shadow: 0 2px 5px rgba(0, 35, 80, 0.1);
}

.user-message .message-content::before {
  display: none; /* 隐藏用户消息的三角形指示器 */
}

.ai-message .message-content {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #333;
  border-radius: 5px 20px 20px 20px;
  margin-left: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.ai-message .message-content::before {
  left: -8px;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid #e3f2fd;
}

.agent-message .message-content {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #333;
  border-radius: 5px 20px 20px 20px;
  margin-left: 5px;
}

.agent-message .message-content::before {
  left: -8px;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid #e3f2fd;
}

.message-text {
  font-size: 15px;
  line-height: 1.5;
  word-break: break-word;
  white-space: pre-wrap;
}

.message-time {
  font-size: 10px;
  position: absolute;
  bottom: -18px;
  color: #999;
  padding: 2px 4px;
  border-radius: 10px;
  background-color: rgba(255, 255, 255, 0.6);
  right: 0;
}

.user-message .message-time {
  color: #666;
  right: 0;
}

.ai-message .message-time, .agent-message .message-time {
  color: #666;
  left: 0;
  right: auto;
}

.input-area {
  min-height: 70px;
  padding: 12px 15px;
  background-color: #f9fafb;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}

.chat-input-container {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 10px;
  transition: all 0.3s ease; /* 添加过渡效果 */
  margin-bottom: 20px; /* 抬高底部的距离 */
}

.file-upload {
  display: flex;
  gap: 8px;
  align-items: center;
}

.input-wrapper {
  flex: 1;
  background-color: white;
  border-radius: 22px;
  padding: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06), inset 0 1px 2px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.08);
  position: relative;
  margin-right: 10px; /* 添加右侧边距，避免在按钮隐藏时输入框过于靠右 */
}

.input-wrapper:focus-within {
  box-shadow: 0 2px 12px rgba(58, 123, 213, 0.15), inset 0 1px 2px rgba(58, 123, 213, 0.05);
  border-color: rgba(58, 123, 213, 0.3);
}

.message-input {
  width: 100%;
  height: 40px;
  border: none;
  border-radius: 20px;
  padding: 10px 15px;
  font-size: 15px;
  resize: none;
  outline: none;
  max-height: 80px;
  overflow-y: auto;
  background: transparent;
  font-family: inherit;
  line-height: 1.5;
}

.input-action-button {
  width: 42px;
  height: 42px;
  border-radius: 21px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);
  cursor: pointer;
  flex-shrink: 0;
  position: relative;
}

.input-action-button:active {
  transform: scale(0.92);
}

.button-tooltip {
  position: absolute;
  bottom: -20px;
  font-size: 10px;
  color: #909399;
  opacity: 0;
  transition: opacity 0.2s ease;
  white-space: nowrap;
}

.file-button:hover .button-tooltip {
  opacity: 1;
}

.file-button {
  background-color: #f5f7fa;
  color: #606266;
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.file-button:hover {
  background-color: #f0f7ff;
  color: #3a7bd5;
}

.file-button i {
  font-size: 22px;
}

.send-button {
  background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%);
  color: white;
  font-size: 18px;
  box-shadow: 0 3px 8px rgba(58, 123, 213, 0.2);
  opacity: 0.8;
  transition: all 0.3s ease;
  min-width: 42px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.send-text {
  font-size: 14px;
  margin-left: 2px;
  font-weight: 500;
  display: none;
}

.send-button.active {
  opacity: 1;
  box-shadow: 0 4px 10px rgba(58, 123, 213, 0.3);
  min-width: 70px;
}

.send-button.active .send-text {
  display: inline-block;
}

.send-button.active i {
  font-size: 16px;
}

.send-button.disabled {
  background: linear-gradient(135deg, #c4c4c4 0%, #9e9e9e 100%);
  opacity: 0.6;
  box-shadow: none;
  cursor: default;
  min-width: 42px;
}

.send-button.disabled .send-text {
  display: none;
}

.transfer-status {
  margin-bottom: 10px;
  padding: 8px 16px;
  background-color: #e6f7ff;
  border-radius: 18px;
  color: #1890ff;
  font-size: 13px;
  align-self: center;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.1);
}

/* 适配iPhone X等刘海屏手机的底部安全区域 */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .input-area {
    padding-bottom: calc(12px + env(safe-area-inset-bottom));
  }
}

.action-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  animation: fade-in 0.2s ease;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.action-menu-content {
  background-color: white;
  padding: 20px;
  border-radius: 15px;
  width: 80%;
  max-width: 300px;
  max-height: 80%;
  overflow-y: auto;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
  animation: slide-up 0.3s ease;
}

@keyframes slide-up {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.action-menu-item {
  padding: 12px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border-radius: 10px;
  transition: all 0.2s ease;
  margin-bottom: 5px;
}

.action-menu-item i {
  margin-right: 10px;
  font-size: 18px;
}

.action-menu-item:hover {
  background-color: #f5f7fa;
}

.action-menu-item:active {
  background-color: #e6f7ff;
  transform: scale(0.98);
}

.action-button i {
  margin-right: 4px;
}

.system-message {
  justify-content: center;
  align-self: center;
  max-width: 90%;
}

.system-content {
  background-color: rgba(240, 249, 235, 0.9);
  color: #67c23a;
  text-align: center;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 13px;
  margin: 15px 0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* 添加媒体查询确保在小屏幕设备上正确显示 */
@media screen and (max-width: 480px) {
  .message-item {
    max-width: 85%;
  }
  
  .message-avatar {
    width: 32px;
    height: 32px;
    margin: 0 5px;
  }
  
  .message-text {
    font-size: 14px;
  }
  
  .input-area {
    height: 60px;
  }
  
  .message-input, .send-button, .file-button {
    height: 38px;
  }
  
  .send-button, .file-button {
    width: 38px;
  }
}

.fullscreen-image-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1003;
  animation: fade-in 0.3s ease;
}

.fullscreen-image-preview img {
  max-width: 90%;
  max-height: 90%;
  border-radius: 8px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.3);
  animation: zoom-in 0.3s ease;
}

@keyframes zoom-in {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.message-image, .message-video {
  width: auto;
  max-width: 200px;
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  background-color: transparent;
  min-height: 100px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  margin: 0;
}

.message-image img, .message-video video {
  width: 100%;
  height: auto;
  display: block;
  object-fit: contain;
  border-radius: 8px;
}

.message-video video {
  max-height: 300px;
}

.user-message .message-image,
.user-message .message-video {
  background: transparent;
  border-radius: 8px;
}

.message-video .video-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 1;
}

.image-message-item .message-time,
.video-message-item .message-time {
  position: absolute;
  bottom: -18px;
  color: #666;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 10px;
  background-color: rgba(255, 255, 255, 0.6);
}

.user-message.image-message-item .message-time,
.user-message.video-message-item .message-time {
  right: 0;
}

.ai-message.image-message-item .message-time,
.agent-message.image-message-item .message-time,
.ai-message.video-message-item .message-time,
.agent-message.video-message-item .message-time {
  left: 0;
}

.faq-container {
  margin: 0;
  background-color: transparent;
  border: none;
  padding: 0px 0 12px 0;
}

.faq-list {
  display: flex;
  overflow-x: auto;
  padding: 0 12px;
  -webkit-overflow-scrolling: touch;
  white-space: nowrap;
  scrollbar-width: none; /* Firefox */
  gap: 8px;
}

.faq-list::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

.faq-item {
  display: inline-block;
  padding: 6px 12px;
  background-color: #f7f7f7;
  border: 1px solid #ebebeb;
  border-radius: 16px;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  font-size: 12px;
  color: #606266;
  box-shadow: none;
}

.faq-item:active {
  background-color: #f0f0f0;
}

.faq-question {
  font-size: 10px;
  color: #606266;
}

.faq-pagination {
  display: none; /* 隐藏分页，使用横向滚动替代 */
}

.action-area {
  display: flex;
  justify-content: center;
  margin-top: auto;
  margin-bottom: 20px;
}

/* 删除连接状态样式 */
.connection-status {
  display: none;
}

.polling-mode-indicator,
.websocket-mode-indicator {
  display: none;
}
</style> 