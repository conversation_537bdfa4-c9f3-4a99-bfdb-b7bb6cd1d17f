<template>
  <div class="not-found-container">
    <div class="not-found-content">
      <h1 class="error-code">404</h1>
      <div class="error-message">页面不存在</div>
      <p class="error-description">抱歉，您访问的页面不存在或已被删除。</p>
      <el-button type="primary" @click="goHome">返回首页</el-button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/user'

const router = useRouter()
const userStore = useUserStore()

// 返回首页
const goHome = () => {
  if (userStore.isUser) {
    router.push('/user/chat')
  } else if (userStore.isAgent) {
    router.push('/agent/dashboard')
  } else {
    router.push('/login')
  }
}
</script>

<style lang="scss" scoped>
.not-found-container {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}

.not-found-content {
  text-align: center;
  padding: 40px;
  
  .error-code {
    font-size: 120px;
    color: var(--primary-color);
    margin: 0;
    line-height: 1;
  }
  
  .error-message {
    font-size: 32px;
    color: var(--primary-text-color);
    margin: 20px 0;
  }
  
  .error-description {
    font-size: 16px;
    color: var(--regular-text-color);
    margin-bottom: 30px;
  }
}
</style> 