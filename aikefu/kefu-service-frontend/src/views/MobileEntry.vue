<template>
  <div class="mobile-entry-container">
    <div class="header">
      <img :src="companyLogo" alt="公司Logo" class="company-logo">
      <h1 class="company-name">{{ companyName }}</h1>
    </div>
    
    <div class="content">
      <div class="welcome-message">
        <h2>欢迎咨询</h2>
        <p>{{ welcomeText }}</p>
      </div>
      
      <!-- <div class="support-info">
        <div class="support-item">
          <i class="el-icon-time"></i>
          <span>服务时间: {{ serviceTime }}</span>
        </div>
        <div class="support-item">
          <i class="el-icon-phone"></i>
          <span>联系电话: {{ contactPhone }}</span>
        </div>
      </div> -->
      
      <div class="action-area">
        <button class="consult-button" @click="startConsult" :disabled="loading">
          <i :class="[loading ? 'el-icon-loading' : 'el-icon-chat-dot-round']"></i>
          {{ loading ? '加载中...' : '开始咨询' }}
        </button>
      </div>
    </div>
    
    <div class="notice-area" v-if="noticeText">
      <div class="notice-title">
        <i class="el-icon-bell"></i>
        公告
      </div>
      <div class="notice-content">
        {{ noticeText }}
      </div>
    </div>
    
    <div class="footer">
      <p>© {{ new Date().getFullYear() }} 熊小智AI. 版权所有</p>
    </div>
  </div>
</template>

<script>
import companyLogo from '@/assets/images/ai-avatar.png'
import { autoRegister } from '@/api/user'
import { useUserStore } from '@/store/user'
import { useChatStore } from '@/store/chat'
import { ElMessage } from 'element-plus'
import { getSessionsByUserId } from '@/api/chat'

export default {
  name: 'MobileEntry',
  data() {
    return {
      companyLogo: companyLogo,
      companyName: '熊小智AI',
      welcomeText: '有任何问题，请随时咨询我们的在线客服，我们将竭诚为您服务。',
      // serviceTime: '周一至周五 9:00-18:00',
      // contactPhone: '************',
      noticeText: '熊小智智能客服目前处于测试阶段，给您带来不便敬请谅解。',
      loading: false
    }
  },
  methods: {
    async startConsult() {
      try {
        this.loading = true;
        // 获取 store 实例
        const store = useUserStore();
        const chatStore = useChatStore();
        
        // 检查本地是否已有用户信息和token
        const localToken = localStorage.getItem('user_token');
        const localUserInfo = localStorage.getItem('user_info');
        
        let userId;
        
        if (localToken && localUserInfo) {
          // 如果本地已有用户信息，直接使用
          const userInfo = JSON.parse(localUserInfo);
          store.setUserInfo(userInfo);
          store.setToken(localToken);
          userId = userInfo.id;
        } else {
          // 本地没有用户信息，自动注册用户
          // 生成随机手机号
          const randomPhone = "1" + String(Math.floor(Math.random() * 9000000000) + 1000000000);
          
          // 创建用户数据
          const userData = {
            userId: "mobile_" + Date.now(),
            phone: randomPhone,
            nickname: "用户" + randomPhone.substring(7),
            avatar: "https://file.juranguanjia.com/upfile/2025/04-18/7c4d83fcb68444e9842c1205df145418.png",
            remark: "前端随机注册",
            email: "user" + randomPhone.substring(7) + "@example.com",
            address: "未知",
            age: 18,
            gender: "未知",
            datasource: "移动端",
            isMerchant: "0",
            isTechnician: "0",
            channel: "Android" // 数据源下的渠道来源 如 ios Android 微信 支付宝 H5 等
          };
          
          const res = await autoRegister(userData);
          if (res.code === 200) {
            // 保存用户信息和token
            store.setUserInfo(res.data.user);
            store.setToken(res.data.token);
            
            // 同时保存到本地存储
            localStorage.setItem('user_token', res.data.token);
            localStorage.setItem('user_info', JSON.stringify(res.data.user));
            
            userId = res.data.user.id;
          } else {
            ElMessage.error('注册失败，请重试');
            return;
          }
        }
        
        // 查询用户历史会话
        const sessionsRes = await getSessionsByUserId(userId);
        
        if (sessionsRes.code === 200 && sessionsRes.data && sessionsRes.data.length > 0) {
          // 用户有历史会话，找到最新的一个会话
          const sessions = sessionsRes.data;
          
          // 按最后活跃时间排序，获取最新的会话
          sessions.sort((a, b) => {
            return new Date(b.lastActiveTime || 0) - new Date(a.lastActiveTime || 0);
          });
          
          const latestSession = sessions[0];
          
          // 判断会话状态，如果会话已结束则创建新会话，否则加入已有会话
          if (latestSession.status === 2) { // 假设状态2表示已结束
            // 创建新会话
            const sessionRes = await chatStore.createNewConversation({ 
              userId: userId,
              assignHuman: false, // 默认使用AI客服
              channel: "Android", // 数据源下的渠道来源 如 ios Android 微信 支付宝 H5 等
              datasource: "移动端", // 数据来源
              collection_name: "information", // 查询知识库集合
              scene: "customer_service" // 对话应用场景
            });
            
            if (sessionRes.code !== 200) {
              ElMessage.error('创建会话失败，请重试');
              return;
            }
            
            // 跳转到移动端聊天页面
            this.$router.push({
              path: '/mobile-chat',
              query: { 
                source: 'entry-page',
                sessionId: sessionRes.data.id,
                t: new Date().getTime() // 添加时间戳避免缓存
              }
            });
          } else {
            // 会话未结束，直接加入最新会话
            
            // 加载会话的历史消息
            await chatStore.loadMessages(latestSession.id);
            
            // 设置当前会话
            chatStore.setCurrentConversation(latestSession.id);
            
            // 跳转到移动端聊天页面
            this.$router.push({
              path: '/mobile-chat',
              query: { 
                source: 'entry-page',
                sessionId: latestSession.id,
                t: new Date().getTime() // 添加时间戳避免缓存
              }
            });
          }
        } else {
          // 用户没有历史会话，创建新会话
          const sessionRes = await chatStore.createNewConversation({ 
            userId: userId,
            assignHuman: false, // 默认使用AI客服
            channel: "Android", // 数据源下的渠道来源 如 ios Android 微信 支付宝 H5 等
            datasource: "移动端", // 数据来源
            collection_name: "information", // 查询知识库集合
            scene: "customer_service" // 对话应用场景
          });
          
          if (sessionRes.code !== 200) {
            ElMessage.error('创建会话失败，请重试');
            return;
          }
          
          // 跳转到移动端聊天页面
          this.$router.push({
            path: '/mobile-chat',
            query: { 
              source: 'entry-page',
              sessionId: sessionRes.data.id,
              t: new Date().getTime() // 添加时间戳避免缓存
            }
          });
        }
      } catch (error) {
        console.error('会话创建失败:', error);
        ElMessage.error('创建会话失败，请重试');
      } finally {
        this.loading = false;
      }
    },
    fetchCompanyInfo() {
      // 这里可以添加获取公司信息的API调用
      // 例如：
      /*
      this.$api.getCompanyInfo().then(res => {
        if (res.data) {
          this.companyName = res.data.name;
          this.welcomeText = res.data.welcomeMessage;
          this.serviceTime = res.data.serviceHours;
          this.contactPhone = res.data.contactPhone;
          this.noticeText = res.data.notice;
        }
      }).catch(err => {
        console.error('获取公司信息失败:', err);
      });
      */
    }
  },
  mounted() {
    // 可以从API获取公司信息、公告等
    this.fetchCompanyInfo();
  }
}
</script>

<style scoped>
.mobile-entry-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.header {
  padding: 20px;
  background-color: #2c2c2c;
  color: white;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.company-logo {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 15px;
  border: 3px solid rgba(255,255,255,0.9);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  background-color: #fff;
  padding: 5px;
}

.company-name {
  font-size: 24px;
  margin: 0;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.welcome-message {
  text-align: center;
  margin-bottom: 30px;
}

.welcome-message h2 {
  font-size: 22px;
  color: #333;
  margin-bottom: 10px;
}

.welcome-message p {
  color: #666;
  font-size: 15px;
  line-height: 1.5;
}

.support-info {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.support-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  color: #555;
  font-size: 14px;
}

.support-item i {
  margin-right: 10px;
  font-size: 18px;
  color: #2c2c2c;
}

.action-area {
  display: flex;
  justify-content: center;
  margin-top: auto;
  margin-bottom: 20px;
}

.consult-button {
  background-color: #2c2c2c;
  color: white;
  border: none;
  border-radius: 24px;
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
}

.consult-button:hover {
  background-color: #3a3a3a;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

.consult-button i {
  margin-right: 8px;
  font-size: 18px;
}

.notice-area {
  background-color: #fff8e1;
  padding: 15px;
  margin: 0 20px 20px;
  border-radius: 8px;
  border-left: 4px solid #ffb74d;
}

.notice-title {
  font-weight: 500;
  color: #f57c00;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.notice-title i {
  margin-right: 6px;
}

.notice-content {
  color: #5d4037;
  font-size: 14px;
  line-height: 1.5;
}

.footer {
  background-color: #f0f2f5;
  padding: 15px;
  text-align: center;
  font-size: 12px;
  color: #909399;
  border-top: 1px solid #e8eaec;
}

/* 适配iPhone X等刘海屏手机 */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .footer {
    padding-bottom: calc(15px + env(safe-area-inset-bottom));
  }
}
</style> 