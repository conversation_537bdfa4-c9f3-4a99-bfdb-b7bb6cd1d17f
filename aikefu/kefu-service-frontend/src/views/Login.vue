<template>
  <div class="login-container">
    <div class="geometric-lines"></div>
    <div class="blob-shape"></div>
    <div class="particles-container"></div>
    <div class="login-box card-shadow rounded">
      <div class="logo-container">
        <img src="/ai-avatar.png" alt="熊小智" class="logo-image" />
        <h2 class="title">熊小智客服</h2>
      </div>
      
      <el-tabs v-model="activeTab" class="login-tabs">
        <el-tab-pane label="客服登录" name="agent">
          <el-form 
            :model="agentForm" 
            :rules="agentRules" 
            ref="agentFormRef"
            label-width="0"
          >
            <el-form-item prop="agentNo">
              <el-input 
                v-model="agentForm.agentNo" 
                placeholder="请输入工号">
                <template #prefix>
                  <el-icon><User /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            
            <el-form-item prop="password">
              <el-input 
                v-model="agentForm.password" 
                placeholder="请输入密码" 
                type="password" 
                show-password>
                <template #prefix>
                  <el-icon><Lock /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            
            <el-form-item>
              <el-button 
                type="primary" 
                :loading="loading" 
                class="login-button" 
                @click="handleAgentLogin"
              >登录</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
<!--        <el-tab-pane label="用户登录" name="user">-->
<!--          <el-form -->
<!--            :model="userForm" -->
<!--            :rules="userRules" -->
<!--            ref="userFormRef"-->
<!--            label-width="0"-->
<!--          >-->
<!--            <el-form-item prop="phone">-->
<!--              <el-input -->
<!--                v-model="userForm.phone" -->
<!--                placeholder="请输入手机号">-->
<!--                <template #prefix>-->
<!--                  <el-icon><Phone /></el-icon>-->
<!--                </template>-->
<!--              </el-input>-->
<!--            </el-form-item>-->
<!--            -->
<!--            <el-form-item prop="password">-->
<!--              <el-input -->
<!--                v-model="userForm.password" -->
<!--                placeholder="请输入密码" -->
<!--                type="password" -->
<!--                show-password>-->
<!--                <template #prefix>-->
<!--                  <el-icon><Lock /></el-icon>-->
<!--                </template>-->
<!--              </el-input>-->
<!--            </el-form-item>-->
<!--            -->
<!--            <el-form-item>-->
<!--              <el-button -->
<!--                type="primary" -->
<!--                :loading="loading" -->
<!--                class="login-button" -->
<!--                @click="handleUserLogin"-->
<!--              >登录</el-button>-->
<!--            </el-form-item>-->
<!--            -->
<!--            <div class="form-footer">-->
<!--              <span>还没有账号？</span>-->
<!--              <router-link to="/register" class="register-link">立即注册</router-link>-->
<!--            </div>-->
<!--          </el-form>-->
<!--        </el-tab-pane>-->
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Phone, Lock, User } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/user'

// 路由
const router = useRouter()
const route = useRoute()

// 用户store
const userStore = useUserStore()

// 表单标签
const activeTab = ref('agent')

// 加载状态
const loading = ref(false)

// 表单引用
const userFormRef = ref(null)
const agentFormRef = ref(null)

// 用户登录表单
const userForm = ref({
  phone: '',
  password: ''
})

// 客服登录表单
const agentForm = ref({
  agentNo: '',
  password: ''
})

// 用户表单校验规则
const userRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应为6-20位', trigger: 'blur' }
  ]
}

// 客服表单校验规则
const agentRules = {
  agentNo: [
    { required: true, message: '请输入工号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应为6-20位', trigger: 'blur' }
  ]
}

// 初始化背景动画
onMounted(() => {
  initParticles()
})

// 初始化粒子动画
const initParticles = () => {
  // 简单模拟粒子效果，实际项目可以使用 particles.js 等库
  const container = document.querySelector('.particles-container')
  if (!container) return

  for (let i = 0; i < 50; i++) {
    const particle = document.createElement('div')
    particle.classList.add('particle')
    
    // 随机大小
    const size = Math.random() * 4 + 1
    particle.style.width = `${size}px`
    particle.style.height = `${size}px`
    
    // 随机位置
    particle.style.left = `${Math.random() * 100}%`
    particle.style.top = `${Math.random() * 100}%`
    
    // 随机动画延迟
    particle.style.animationDelay = `${Math.random() * 5}s`
    
    container.appendChild(particle)
  }
}

// 用户登录
const handleUserLogin = async () => {
  if (!userFormRef.value) return
  
  try {
    await userFormRef.value.validate()
    
    loading.value = true
    
    console.log('开始用户登录...', userForm.value)
    
    // 使用本地数据进行登录（实际项目中应调用API）
    const loginData = {
      phone: userForm.value.phone,
      password: userForm.value.password
    }
    
    const result = await userStore.userLogin(loginData)
    console.log('用户登录成功，返回数据:', result)
    console.log('当前用户状态:', userStore.isLoggedIn, userStore.role)
    
    ElMessage({
      type: 'success',
      message: '登录成功'
    })
    
    // 获取重定向地址或跳转到用户首页
    const redirect = route.query.redirect || '/user/chat'
    console.log('准备跳转到:', redirect)
    router.push(redirect)
    console.log('路由跳转已执行')
  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error(error.message || '登录失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 客服登录
const handleAgentLogin = async () => {
  if (!agentFormRef.value) return
  
  try {
    await agentFormRef.value.validate()
    
    loading.value = true
    
    console.log('开始客服登录...', agentForm.value)
    
    const loginData = {
      agentNo: agentForm.value.agentNo,
      password: agentForm.value.password
    }
    
    const result = await userStore.agentLogin(loginData)
    console.log('客服登录成功，返回数据:', result)
    console.log('当前用户状态:', userStore.isLoggedIn, userStore.role)
    
    // 自动上线
    if (userStore.userInfo && userStore.userInfo.id) {
      await userStore.updateAgentStatus(userStore.userInfo.id, 1)
    }
    
    ElMessage({
      type: 'success',
      message: '登录成功'
    })
    
    // 使用 nextTick 确保状态更新后再跳转
    nextTick(() => {
      // 获取重定向地址或跳转到工作台（无论是管理员还是普通客服都跳转到相同页面）
      const redirect = route.query.redirect || '/agent/dashboard'
      
      router.replace(redirect)
        .then(() => {
          console.log('导航成功，跳转到:', redirect)
        })
        .catch(error => {
          console.error('导航失败:', error)
          // 如果导航失败，尝试重新加载页面
          window.location.href = redirect
        })
    })
  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error(error.message || '登录失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
@import url('https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap');

.login-container {
  height: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  background: linear-gradient(135deg, #0D47A1 0%, #1976D2 30%, #2196F3 60%, #64B5F6 100%);
  overflow: hidden;
  position: relative;
  font-family: 'Nunito', sans-serif;
  
  padding-left: 61.8%;
  padding-right: 5%;
  box-sizing: border-box;
  
  &::before {
    content: '';
    position: absolute;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 70%);
    top: -50%;
    left: -50%;
    animation: pulse 15s infinite ease-in-out;
  }
  
  @keyframes pulse {
    0% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.05); }
    100% { opacity: 0.5; transform: scale(1); }
  }
}

.geometric-lines {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 130%;
  opacity: 0.5;
  overflow: hidden;
  z-index: 1;
  
  &::before {
    content: '';
    position: absolute;
    top: -10%;
    right: -5%;
    width: 120%;
    height: 70%;
    background: 
      radial-gradient(circle at 70% 20%, rgba(255, 160, 122, 0.5) 0%, rgba(255, 160, 122, 0.2) 20%, transparent 40%),
      repeating-linear-gradient(
        60deg,
        transparent,
        transparent 15px,
        rgba(255, 160, 122, 0.3) 15px,
        rgba(255, 160, 122, 0.3) 30px
      );
    transform: rotate(-12deg);
    animation: floatPattern1 20s infinite alternate ease-in-out;
  }
  
  &::after {
    content: '';
    position: absolute;
    bottom: -10%;
    right: -5%;
    width: 120%;
    height: 60%;
    background: 
      radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.1) 30%, transparent 50%),
      repeating-linear-gradient(
        -60deg,
        transparent,
        transparent 12px,
        rgba(255, 255, 255, 0.2) 12px,
        rgba(255, 255, 255, 0.2) 24px
      );
    transform: rotate(15deg);
    animation: floatPattern2 25s infinite alternate-reverse ease-in-out;
  }
}

.blob-shape {
  position: absolute;
  top: 25%;
  right: 10%;
  width: 30%;
  height: 40%;
  background: 
    repeating-conic-gradient(
      from 0deg,
      rgba(33, 150, 243, 0.3) 0deg 10deg,
      transparent 10deg 20deg
    );
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  opacity: 0.6;
  z-index: 1;
  animation: morphShape 15s infinite alternate ease-in-out;
}

.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.particle {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: float 15s infinite ease-in-out;
}

@keyframes float {
  0% { transform: translateY(0) translateX(0); opacity: 0; }
  50% { opacity: 0.8; }
  100% { transform: translateY(-100px) translateX(50px); opacity: 0; }
}

@keyframes floatPattern1 {
  0% { transform: rotate(-12deg) translateY(0); }
  50% { transform: rotate(-10deg) translateY(-10px); }
  100% { transform: rotate(-15deg) translateY(15px); }
}

@keyframes floatPattern2 {
  0% { transform: rotate(15deg) translateY(0); }
  50% { transform: rotate(12deg) translateY(10px); }
  100% { transform: rotate(18deg) translateY(-15px); }
}

@keyframes morphShape {
  0% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
  50% { border-radius: 70% 30% 30% 70% / 70% 70% 30% 30%; }
  100% { border-radius: 40% 60% 50% 50% / 60% 40% 60% 40%; }
}

.login-box {
  width: 350px; 
  padding: 28px 40px; 
  max-width: 100%;
  background-color: rgba(255, 255, 255, 0.92);
  backdrop-filter: blur(10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25), 0 0 40px rgba(33, 150, 243, 0.1) inset;
  border-radius: 16px;
  position: relative;
  z-index: 2;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3), 0 0 40px rgba(33, 150, 243, 0.15) inset;
  }
  
  .logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 25px;
    
    .logo-image {
      width: 120px;
      height: 120px;
      border-radius: 60px;
      margin-bottom: 18px;
      object-fit: cover;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
      border: 3px solid white;
      transition: transform 0.3s ease;
      
      &:hover {
        transform: scale(1.05);
      }
    }
  }
  
  .title {
    font-size: 26px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 20px;
    color: #1565C0;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.1);
    letter-spacing: 0.5px;
  }
  
  .login-tabs {
    margin-bottom: 25px;
    
    :deep(.el-tabs__item) {
      font-size: 16px;
      color: #4a5568;
      padding: 0 18px;
      height: 40px;
      line-height: 40px;
      transition: all 0.3s ease;
      
      &.is-active {
        color: #2196F3;
        font-weight: 700;
      }
      
      &:hover {
        color: #1976D2;
      }
    }
    
    :deep(.el-tabs__active-bar) {
      background-color: #2196F3;
      height: 3px;
      border-radius: 3px;
    }
    
    :deep(.el-tabs__nav-wrap::after) {
      height: 1px;
      background-color: rgba(0, 0, 0, 0.08);
    }
  }
  
  :deep(.el-form-item) {
    margin-bottom: 22px;
  }
  
  .login-button {
    width: 100%;
    margin-top: 25px;
    height: 48px;
    font-size: 17px;
    font-weight: 700;
    background: linear-gradient(90deg, #1976D2 0%, #2196F3 100%);
    border: none;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.35);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba(33, 150, 243, 0.5);
      background: linear-gradient(90deg, #2196F3 0%, #64B5F6 100%);
    }
    
    &:active {
      transform: translateY(1px);
    }
    
    &.is-loading {
      .el-loading-spinner {
        .path {
          stroke: white;
        }
      }
    }
  }
  
  .form-footer {
    margin-top: 25px;
    text-align: center;
    font-size: 15px;
    color: #4a5568;
    
    .register-link {
      margin-left: 6px;
      color: #2196F3;
      font-weight: 600;
      text-decoration: none;
      transition: color 0.3s ease;
      
      &:hover {
        color: #FFA07A;
        text-decoration: underline;
      }
    }
  }
}

:deep(.el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  height: 48px;
  padding: 0 15px;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #90CAF9;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.15);
  }
  
  &.is-focus {
    border-color: #2196F3;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.2);
  }
}

:deep(.el-input__inner) {
  color: #2d3748;
  font-size: 16px;
}

:deep(.el-icon) {
  color: #4a5568;
  font-size: 18px;
  margin-right: 8px;
  transition: color 0.3s ease;
}

:deep(.el-input--password) {
  .el-input__suffix {
    .el-icon {
      color: #FFA07A;
      
      &:hover {
        color: #FF7F50;
      }
    }
  }
}

@media (max-width: 1200px) {
  .login-container {
    padding-left: 50%;
  }
}

@media (max-width: 768px) {
  .login-container {
    justify-content: center;
    padding-left: 0;
    padding-right: 0;
  }
  
  .geometric-lines {
    width: 100%;
  }
  
  .blob-shape {
    width: 60%;
    right: 20%;
  }
}

@media (max-width: 480px) {
  .login-box {
    width: 90%;
    max-width: 380px;
    padding: 25px 20px;
    
    .logo-image {
      width: 100px;
      height: 100px;
    }
    
    .title {
      font-size: 22px;
    }
  }
  
  .geometric-lines {
    width: 25%;
  }
  
  .blob-shape {
    width: 40%;
    right: 10%;
  }
}
</style> 