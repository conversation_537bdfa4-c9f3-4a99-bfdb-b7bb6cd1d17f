<template>
  <div class="sso-login-container">
    <div class="sso-login-box card-shadow rounded">
      <div class="logo-container">
        <img src="/ai-avatar.png" alt="熊小智" class="logo-image" />
        <h2 class="title">熊小智客服</h2>
      </div>
      
      <div class="sso-login-content">
        <h3>SSO 快捷登录</h3>
        <p v-if="!isProcessing && !error">系统正在进行SSO登录验证，请稍候...</p>
        <p v-if="error" class="error-message">{{ error }}</p>
        <div v-if="isProcessing" class="loading-spinner">
          <el-icon class="is-loading"><Loading /></el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/user'

// 路由
const router = useRouter()
const route = useRoute()

// 用户store
const userStore = useUserStore()

// 加载状态
const isProcessing = ref(true)
const error = ref('')

// 自动登录处理
onMounted(async () => {
  // 从URL获取token参数
  const token = route.query.token
  
  if (!token) {
    error.value = '未提供有效的SSO令牌'
    isProcessing.value = false
    return
  }
  
  try {
    // 调用SSO登录接口
    await userStore.ssoLogin(token)
    
    // 自动上线
    if (userStore.userInfo && userStore.userInfo.id) {
      await userStore.updateAgentStatus(userStore.userInfo.id, 1)
    }
    
    ElMessage({
      type: 'success',
      message: 'SSO登录成功'
    })
    
    // 跳转到工作台
    const redirect = route.query.redirect || '/agent/dashboard'
    router.replace(redirect)
  } catch (err) {
    console.error('SSO登录失败:', err)
    error.value = err.message || 'SSO登录验证失败，请重试'
    isProcessing.value = false
  }
})
</script>

<style lang="scss" scoped>
.sso-login-container {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #0D47A1 0%, #1976D2 30%, #2196F3 60%, #64B5F6 100%);
}

.sso-login-box {
  width: 400px;
  background-color: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.logo-image {
  width: 80px;
  height: 80px;
  margin-bottom: 10px;
}

.title {
  font-size: 24px;
  color: #1976D2;
  margin: 0;
}

.sso-login-content {
  text-align: center;
  padding: 20px 0;
}

h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  margin: 20px 0;
  font-size: 30px;
  color: #1976D2;
}

.error-message {
  color: #f56c6c;
  margin: 20px 0;
}
</style> 