<template>
  <!-- No changes to template section -->
</template>

<script>
import ConnectionStatus from '@/components/ConnectionStatus.vue'

export default {
  components: {
    ConnectionStatus
  },
  mounted() {
    this.fetchBasicInfo()
    this.getConversations()
    
    // 初始化WebSocket
    if (this.isAgent) {
      this.store.setInChatPage(true)
      this.store.initWebSocket()
      
      // 检查当前连接状态
      this.checkConnectionStatus()
    }
    
    // 初始化事件监听
    this.initEventListeners()
    
    // 设置检查用户状态的定时器
    this.statusTimer = setInterval(() => {
      this.checkUserStatus();
      // 同时定期检查连接状态
      if (this.isAgent) {
        this.checkConnectionStatus();
      }
    }, 10000)
  },

  methods: {
    initEventListeners() {
      // 监听新消息事件
      window.addEventListener('chat-message-received', this.handleNewMessage)
      
      // 监听会话更新事件
      window.addEventListener('chat-conversation-updated', this.handleConversationUpdated)
      
      // 监听HTTP轮询开始事件
      window.addEventListener('chat-polling-started', this.handlePollingStarted)
      
      // 监听HTTP轮询结束事件
      window.addEventListener('chat-polling-stopped', this.handlePollingStopped)
    },
    
    beforeDestroy() {
      console.log('Chat.vue被销毁，清理资源')
      
      // 移除事件监听
      window.removeEventListener('chat-message-received', this.handleNewMessage)
      window.removeEventListener('chat-conversation-updated', this.handleConversationUpdated)
      window.removeEventListener('chat-polling-started', this.handlePollingStarted)
      window.removeEventListener('chat-polling-stopped', this.handlePollingStopped)
      
      if (this.statusTimer) {
        clearInterval(this.statusTimer)
      }
      
      // 设置不在聊天页面
      this.store.setInChatPage(false)
    },
    
    handleNewMessage(event) {
      console.log('Chat.vue检测到新消息，消息详情:', event.detail)
      
      const message = event.detail
      const isNewMessage = message.isNewMessage !== false // 如果未定义，默认为新消息
      
      // 如果不是新消息，只更新状态，不添加到列表
      if (!isNewMessage) {
        console.log('非新消息，只更新状态不添加到列表')
        return
      }
      
      // 获取当前会话ID（从store或组件属性）
      const currentSessionId = this.store.currentConversationId || 
                              (this.currentConversation ? this.currentConversation.id : null)
      
      // 确保ID比较是数字格式
      const msgSessionId = Number(message.sessionId)
      const currentConvId = Number(currentSessionId)
      
      console.log('当前会话ID:', currentConvId, '消息会话ID:', msgSessionId)
      
      // 判断消息是否属于当前会话
      const isCurrentSession = msgSessionId === currentConvId
      
      if (isCurrentSession) {
        // 消息属于当前显示的会话，直接添加到视图
        console.log('新消息属于当前会话，立即添加到消息列表')
        this.addMessageToList(message)
      } else {
        // 消息不属于当前会话，但我们仍然需要更新会话列表
        console.log('消息不属于当前会话，更新会话列表')
        this.updateConversationList(message.sessionId)
      }
    },
    
    handleConversationUpdated(event) {
      console.log('会话更新:', event.detail)
      
      const updatedConversation = event.detail
      
      // 查找会话索引
      const idx = this.conversations.findIndex(c => c.id === updatedConversation.id)
      
      if (idx !== -1) {
        // 更新会话信息
        this.conversations[idx] = { ...this.conversations[idx], ...updatedConversation }
        
        // 如果有未读消息，显示提示
        if (updatedConversation.unreadCount > 0) {
          this.highlightConversation(updatedConversation.id)
        }
      } else {
        // 会话不存在，可能需要刷新会话列表
        console.log('会话不存在，刷新会话列表')
        this.getConversations()
      }
    },
    
    highlightConversation(sessionId) {
      // 查找会话
      const conv = this.conversations.find(c => c.id === sessionId)
      
      if (conv) {
        // 高亮效果 - 这里可以根据UI设计添加具体实现
        this.$nextTick(() => {
          const el = document.querySelector(`.conversation-item[data-id="${sessionId}"]`)
          if (el) {
            el.classList.add('highlight-new-message')
            
            // 3秒后移除高亮
            setTimeout(() => {
              el.classList.remove('highlight-new-message')
            }, 3000)
          }
        })
      }
    },
    
    handlePollingStarted(event) {
      console.log('HTTP轮询已开始:', event.detail)
      this.isPollingMode = true
      // 可以在界面显示连接状态指示
    },
    
    handlePollingStopped(event) {
      console.log('HTTP轮询已停止:', event.detail)
      this.isPollingMode = false
      // 更新界面连接状态指示
    },
    
    // 检查连接状态
    checkConnectionStatus() {
      const status = this.store.getConnectionStatus()
      console.log('当前连接状态:', status)
      
      // 更新UI状态
      this.isPollingMode = status.isPolling
      
      // 如果没有任何连接，尝试使用最佳连接模式
      if (!status.isWebSocketConnected && !status.isPolling && this.store.currentConversationId) {
        console.log('没有活动连接，尝试使用最佳连接模式')
        this.store.useOptimalConnectionMode()
      }
    },

    // 新增：将消息添加到列表的方法
    addMessageToList(message) {
      // 再次检查消息是否已存在，避免重复
      const exists = this.messages.some(m => m.id === message.id)
      
      if (!exists) {
        // 构建标准消息对象
        const messageObj = {
          id: message.id,
          sessionId: message.sessionId,
          from: message.from,
          senderType: message.senderType || message.from,
          senderId: message.senderId,
          receiverId: message.receiverId,
          content: message.content,
          type: message.type || (message.msgType === 0 ? 1 : 2),
          msgType: message.msgType || (message.type === 1 ? 0 : 1),
          createdAt: message.createdAt || new Date().toISOString(),
          status: message.status || 1,
          _standardized: true
        }
        
        console.log('构建的消息对象:', JSON.stringify(messageObj))
        
        // 直接添加到列表末尾
        this.messages.push(messageObj)
        
        // 更新消息计数
        this.messagesCount = this.messages.length
        
        // 滚动到底部
        this.$nextTick(() => {
          this.scrollToBottom()
        })
        
        // 如果是用户发来的消息，标记为已读 (现在改为在store中处理)
        // 这里不再主动调用标记已读，避免重复请求
      } else {
        console.log('消息已存在，不重复添加')
      }
    },

    // 新增：更新会话列表的方法
    updateConversationList(sessionId) {
      // 查找会话是否存在于会话列表中
      const sessionIndex = this.conversations.findIndex(s => s.id === sessionId)
      
      if (sessionIndex !== -1) {
        // 会话存在，高亮提示有新消息
        this.highlightConversation(sessionId)
      } else {
        // 会话不存在，刷新会话列表
        console.log('会话不在列表中，刷新会话列表')
        this.getConversations().then(() => {
          // 刷新后再次尝试高亮
          this.$nextTick(() => {
            this.highlightConversation(sessionId)
          })
        })
      }
    },
  },

  data() {
    return {
      // 已有字段
      store: useChatStore(),
      conversations: [],
      messages: [],
      messagesCount: 0,
      pageSize: 20,
      pageNum: 1,
      hasMoreMessages: true,
      loadingMessages: false,
      submitting: false,
      textMessage: '',
      currentClientId: null,
      isAgent: false,
      statusTimer: null,
      // 新增字段
      isPollingMode: false,
    }
  },
}
</script>

<style scoped>
.connection-status {
  position: fixed;
  top: 10px;
  right: 20px;
  z-index: 1000;
  font-size: 12px;
}

/* 可以在这里添加更多组件相关的样式 */
</style> 