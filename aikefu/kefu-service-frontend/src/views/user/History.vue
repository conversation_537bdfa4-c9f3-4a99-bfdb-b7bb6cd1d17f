<template>
  <div class="history-container">
    <div class="history-header">
      <h2>历史会话</h2>
      <el-input 
        v-model="searchKeyword" 
        placeholder="搜索历史会话" 
        :prefix-icon="Search"
        clearable 
        class="search-input"
      />
    </div>
    
    <div class="history-content" v-loading="loading">
      <!-- 无数据提示 -->
      <el-empty v-if="filteredSessions.length === 0" description="暂无历史会话"></el-empty>
      
      <!-- 会话列表 -->
      <div v-else class="session-list">
        <el-card 
          v-for="session in filteredSessions" 
          :key="session.id" 
          class="session-card"
          shadow="hover"
          @click="viewSessionDetail(session)"
        >
          <div class="session-info">
            <div class="session-header">
              <div class="agent-info">
                <el-avatar :size="40" :src="session.agentAvatar">
                  {{ session.agentName ? session.agentName.substring(0, 1) : '客' }}
                </el-avatar>
                <span class="agent-name">{{ session.agentName || '系统客服' }}</span>
              </div>
              <div class="session-status" :class="getStatusClass(session.status)">
                {{ getStatusText(session.status) }}
              </div>
            </div>
            
            <div class="session-time">
              <time>{{ formatTime(session.createdAt) }}</time>
              <time v-if="session.status === 0">结束于: {{ formatTime(session.closedAt) }}</time>
            </div>
            
            <div class="session-message">
              <p v-if="session.lastMessage">{{ session.lastMessage }}</p>
              <p v-else class="no-message">暂无消息记录</p>
            </div>
            
            <div class="session-actions">
              <el-button 
                type="primary" 
                size="small" 
                plain
                @click.stop="viewSessionDetail(session)"
              >
                查看详情
              </el-button>
              
              <el-button 
                v-if="session.status === 0 && !session.evaluation" 
                type="warning" 
                size="small" 
                plain
                @click.stop="evaluateSession(session)"
              >
                去评价
              </el-button>
              
              <el-tag v-else-if="session.evaluation" size="small" type="success">
                已评价: {{ session.evaluation.score }}分
              </el-tag>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :page-size="pageSize"
          :current-page="currentPage"
          @current-change="handlePageChange"
        />
      </div>
    </div>
    
    <!-- 会话详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="会话详情"
      width="70%"
      destroy-on-close
    >
      <div class="dialog-content" v-loading="detailLoading">
        <div v-if="currentMessages.length > 0" class="message-list">
          <div 
            v-for="(message, index) in currentMessages" 
            :key="message.id || index" 
            class="message-item"
            :class="message.userId ? 'message-item-user' : 'message-item-agent'"
          >
            <div class="message-avatar">
              <el-avatar :size="40" :src="message.userId ? userInfo?.avatar : message.agentAvatar">
                {{ message.userId ? (userInfo?.nickname?.substring(0, 1) || '用') : (message.agentName?.substring(0, 1) || '客') }}
              </el-avatar>
            </div>
            
            <div class="message-content">
              <div class="message-sender">
                {{ message.userId ? (userInfo?.nickname || '我') : (message.agentName || '客服') }}
                <span class="message-time">{{ formatTime(message.createdAt) }}</span>
              </div>
              <div class="message-text" v-if="message.type === 'text'">{{ message.content }}</div>
              <div class="message-image" v-else-if="message.type === 'image'">
                <el-image :src="getImageUrl(message.content)" :preview-src-list="[getImageUrl(message.content)]" fit="cover" />
              </div>
              <div class="message-system" v-else-if="message.type === 'system'">{{ message.content }}</div>
            </div>
          </div>
        </div>
        <el-empty v-else description="暂无消息记录"></el-empty>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button 
            v-if="currentSession && currentSession.status === 0 && !currentSession.evaluation" 
            type="primary" 
            @click="evaluateSession(currentSession)"
          >
            评价服务
          </el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 评价对话框 -->
    <el-dialog
      v-model="evaluateDialogVisible"
      title="服务评价"
      width="400px"
      destroy-on-close
    >
      <el-form ref="evaluateForm" :model="evaluateForm" label-width="80px">
        <el-form-item label="服务评分">
          <el-rate
            v-model="evaluateForm.score"
            :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
            :max="5"
            show-text
            :texts="['很差', '一般', '满意', '很好', '非常满意']"
          />
        </el-form-item>
        
        <el-form-item label="评价内容">
          <el-input
            v-model="evaluateForm.content"
            type="textarea"
            :rows="3"
            placeholder="请输入您对本次服务的评价..."
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="evaluateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEvaluation" :loading="submitting">提交评价</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/user'
import { getSessionsByUserId, getMessagesBySessionId } from '@/api/chat'
import { addEvaluation } from '@/api/evaluation'
import { ElMessage } from 'element-plus'
import moment from 'moment'
import { useRouter } from 'vue-router'

// 用户store
const userStore = useUserStore()

// 计算属性：用户信息
const userInfo = computed(() => userStore.userInfo)

// 搜索关键词
const searchKeyword = ref('')

// 会话列表
const sessions = ref([])
const filteredSessions = computed(() => {
  if (!searchKeyword.value) return sessions.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return sessions.value.filter(session => 
    (session.agentName && session.agentName.toLowerCase().includes(keyword)) ||
    (session.lastMessage && session.lastMessage.toLowerCase().includes(keyword))
  )
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 加载状态
const loading = ref(false)
const detailLoading = ref(false)
const submitting = ref(false)

// 对话框显示状态
const dialogVisible = ref(false)
const evaluateDialogVisible = ref(false)

// 当前会话和消息
const currentSession = ref(null)
const currentMessages = ref([])

// 评价表单
const evaluateForm = ref({
  sessionId: '',
  userId: '',
  agentId: '',
  score: 5,
  content: ''
})

// 路由
const router = useRouter()

// 生命周期钩子
onMounted(async () => {
  // 从localStorage恢复状态
  userStore.loadFromStorage()
  
  // 检查登录状态和用户角色
  if (!userStore.isLoggedIn || !userStore.isUser) {
    router.push('/login')
    return
  }
  
  // 加载会话列表
  await loadSessions()
})

// 加载会话列表
const loadSessions = async () => {
  if (!userStore.isLoggedIn || !userStore.userId) return
  
  loading.value = true
  
  try {
    const res = await getSessionsByUserId(userStore.userId)
    
    if (res.code === 200) {
      sessions.value = res.data
      
      // 按最后活跃时间排序
      sessions.value.sort((a, b) => {
        return new Date(b.lastActiveTime || 0) - new Date(a.lastActiveTime || 0)
      })
      
      total.value = res.data.length
    } else {
      ElMessage.error(res.message || '加载会话列表失败')
    }
  } catch (error) {
    console.error('加载会话列表失败', error)
    ElMessage.error('加载会话列表失败')
  } finally {
    loading.value = false
  }
}

// 查看会话详情
const viewSessionDetail = async (session) => {
  currentSession.value = session
  dialogVisible.value = true
  
  // 加载会话消息
  await loadSessionMessages(session.id)
}

// 加载会话消息
const loadSessionMessages = async (sessionId) => {
  detailLoading.value = true
  currentMessages.value = []
  
  try {
    const res = await getMessagesBySessionId(sessionId)
    
    if (res.code === 200) {
      currentMessages.value = res.data
    } else {
      ElMessage.error(res.message || '加载消息失败')
    }
  } catch (error) {
    console.error('加载消息失败', error)
    ElMessage.error('加载消息失败')
  } finally {
    detailLoading.value = false
  }
}

// 评价会话
const evaluateSession = (session) => {
  currentSession.value = session
  evaluateForm.value = {
    sessionId: session.id,
    userId: userStore.userId,
    agentId: session.agentId,
    score: 5,
    content: ''
  }
  evaluateDialogVisible.value = true
}

// 提交评价
const submitEvaluation = async () => {
  if (!evaluateForm.value.score) {
    ElMessage.warning('请选择评分')
    return
  }
  
  submitting.value = true
  
  try {
    const res = await addEvaluation(evaluateForm.value)
    
    if (res.code === 200) {
      ElMessage.success('评价成功')
      
      // 关闭对话框
      evaluateDialogVisible.value = false
      
      // 刷新会话列表
      await loadSessions()
      
      // 如果当前有打开的详情对话框，也关闭它
      dialogVisible.value = false
    } else {
      ElMessage.error(res.message || '提交评价失败')
    }
  } catch (error) {
    console.error('提交评价失败', error)
    ElMessage.error('提交评价失败')
  } finally {
    submitting.value = false
  }
}

// 处理分页变化
const handlePageChange = (page) => {
  currentPage.value = page
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 0: return '已结束'
    case 1: return '进行中'
    case 2: return '等待中'
    default: return '未知状态'
  }
}

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 0: return 'status-closed'
    case 1: return 'status-active'
    case 2: return 'status-waiting'
    default: return ''
  }
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  
  const date = moment(timestamp)
  const now = moment()
  
  // 如果是今天，只显示时间
  if (date.isSame(now, 'day')) {
    return date.format('HH:mm:ss')
  }
  
  // 如果是昨天，显示"昨天 时间"
  if (date.isSame(now.clone().subtract(1, 'day'), 'day')) {
    return `昨天 ${date.format('HH:mm')}`
  }
  
  // 如果是今年，显示"月-日 时间"
  if (date.isSame(now, 'year')) {
    return date.format('MM-DD HH:mm')
  }
  
  // 其他情况，显示完整日期时间
  return date.format('YYYY-MM-DD HH:mm')
}

// 添加图片URL处理函数
const getImageUrl = (url) => {
  if (!url) return ''
  // 如果已经是完整的URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }
  // 否则拼接MinIO URL
  // return `${import.meta.env.VITE_MINIO_ENDPOINT}/${import.meta.env.VITE_MINIO_BUCKET}/${url}`
}
</script>

<style lang="scss" scoped>
.history-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h2 {
    margin: 0;
    font-size: 18px;
    color: var(--primary-text-color);
  }
  
  .search-input {
    width: 240px;
  }
}

.history-content {
  flex: 1;
  overflow-y: auto;
}

.session-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  grid-gap: 20px;
  margin-bottom: 20px;
}

.session-card {
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    transform: translateY(-5px);
  }
  
  .session-info {
    display: flex;
    flex-direction: column;
    
    .session-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      
      .agent-info {
        display: flex;
        align-items: center;
        
        .agent-name {
          margin-left: 10px;
          font-weight: 500;
        }
      }
      
      .session-status {
        font-size: 13px;
        padding: 2px 8px;
        border-radius: 10px;
        
        &.status-closed {
          background-color: #f0f2f5;
          color: var(--info-color);
        }
        
        &.status-active {
          background-color: #ecf5ff;
          color: var(--primary-color);
        }
        
        &.status-waiting {
          background-color: #fdf6ec;
          color: var(--warning-color);
        }
      }
    }
    
    .session-time {
      margin-bottom: 10px;
      font-size: 13px;
      color: var(--secondary-text-color);
      
      time {
        margin-right: 10px;
      }
    }
    
    .session-message {
      margin-bottom: 15px;
      
      p {
        margin: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: var(--regular-text-color);
      }
      
      .no-message {
        color: var(--secondary-text-color);
        font-style: italic;
      }
    }
    
    .session-actions {
      display: flex;
      justify-content: space-between;
    }
  }
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-content {
  max-height: 60vh;
  overflow-y: auto;
  padding: 0 10px;
  
  .message-list {
    .message-item {
      display: flex;
      margin-bottom: 20px;
      
      .message-avatar {
        margin-right: 10px;
      }
      
      .message-content {
        max-width: 70%;
        
        .message-sender {
          margin-bottom: 5px;
          font-size: 13px;
          color: var(--secondary-text-color);
          
          .message-time {
            margin-left: 5px;
            font-size: 12px;
          }
        }
        
        .message-text {
          padding: 10px 15px;
          background-color: #f4f7fa;
          border-radius: 4px;
          word-break: break-word;
          line-height: 1.5;
        }
        
        .message-image {
          max-width: 300px;
          
          .el-image {
            border-radius: 4px;
            overflow: hidden;
          }
        }
        
        .message-system {
          padding: 8px 12px;
          background-color: #f0f9eb;
          color: #67c23a;
          border-radius: 4px;
          font-size: 13px;
          text-align: center;
        }
      }
      
      &-user {
        flex-direction: row-reverse;
        
        .message-avatar {
          margin-right: 0;
          margin-left: 10px;
        }
        
        .message-content {
          .message-sender {
            text-align: right;
          }
          
          .message-text {
            background-color: #ecf5ff;
            color: var(--primary-color);
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .history-header {
    flex-direction: column;
    align-items: flex-start;
    
    h2 {
      margin-bottom: 10px;
    }
    
    .search-input {
      width: 100%;
    }
  }
  
  .session-list {
    grid-template-columns: 1fr;
  }
}
</style> 