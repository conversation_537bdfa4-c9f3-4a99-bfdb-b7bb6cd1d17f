<template>
  <div class="chat-container">
    <div class="chat-header">
      <div class="chat-title">
        <span v-if="currentConversation">
          <el-tag v-if="isAIAgent" type="success" size="small" effect="dark" class="agent-tag">AI客服</el-tag>
          <el-tag v-else type="primary" size="small" effect="dark" class="agent-tag">人工客服</el-tag>
          <template v-if="isAIAgent">
            与AI智能客服对话中
          </template>
          <template v-else-if="currentConversation.agent && currentConversation.agent.name">
            与客服 {{ currentConversation.agent.name }} 对话中
          </template>
          <template v-else>
            与客服对话中
          </template>
        </span>
        <span v-else>在线客服</span>
      </div>
      <div class="chat-actions" v-if="currentConversation">
        <el-button 
          v-if="isAIAgent && !transferRequested && currentConversation.currentAgentType === 2 && currentConversation.transferRequested !== 1" 
          type="primary" 
          plain 
          size="small" 
          @click="requestHumanAgent"
        >转接人工客服</el-button>
        <el-button 
          v-if="isAIAgent && transferRequested" 
          type="warning" 
          plain 
          size="small" 
          disabled
        >正在转接人工...</el-button>
        <el-button type="danger" plain size="small" @click="handleEndChat">结束会话</el-button>
      </div>
    </div>
    
    <!-- 显示评价组件 -->
    <session-evaluation
      v-if="showEvaluation"
      :sessionId="evaluationSessionId"
      @evaluationComplete="handleEvaluationComplete"
      @skipEvaluation="handleSkipEvaluation"
      @startNewChat="startNewChat"
    />
    
    <div ref="chatContent" class="chat-content" v-loading="loading" v-show="!showEvaluation">
      <!-- 欢迎信息 -->
      <div v-if="!hasActiveConversation && !loading" class="welcome-container">
        <div class="welcome-image">
          <img src="/customer-service.svg" alt="客服系统">
        </div>
        <div class="welcome-text">
          <h2>欢迎使用在线客服系统</h2>
          <p>我们的客服人员随时为您提供帮助和支持</p>
          <div class="session-status" v-if="chatStore.conversations.length > 0">
            <p>您有 {{ chatStore.conversations.filter(c => [0, 1, 3].includes(c.status)).length }} 个未结束的会话</p>
            <el-button type="text" @click="showHistoryDialog = true">查看历史会话</el-button>
          </div>
          <el-button type="primary" @click="startNewChat">开始咨询</el-button>
        </div>
      </div>
      
      <!-- 聊天消息 -->
      <template v-else-if="messages.length > 0">
        <div 
          v-for="(message, index) in messages" 
          :key="message.id || index" 
          class="message-item" 
          :class="{
            'message-item-user': message.from === 0 || message.senderType === 0 || message.userId,
            'message-item-agent': (message.from === 1 || message.senderType === 1 || message.agentId) && message.senderType !== 2 && message.type !== 'system' && message.msgType !== 2,
            'message-item-ai': message.senderType === 2 || (message.from === 1 && message.currentAgentType === 2),
            'message-item-system': message.type === 'system' || message.msgType === 2
          }"
        >
          <!-- 调试信息 -->
          <div v-if="false" class="debug-info">
            id: {{ message.id }}, from: {{ message.from }}, senderType: {{ message.senderType }}, 
            msgType: {{ message.msgType }}, type: {{ message.type }}
          </div>
          
          <div class="message-avatar" v-if="message.type !== 'system' && message.msgType !== 2">
            <el-avatar 
              :size="40" 
              :src="getAvatarUrl(message)"
              :fallback-src="''"
              @error="handleAvatarError"
            >
              {{ getAvatarText(message) }}
            </el-avatar>
          </div>
          
          <div class="message-content">
            <div class="message-sender" v-if="message.type !== 'system' && message.msgType !== 2">
              {{ getSenderName(message) }}
              <span class="message-time">{{ formatTime(message.createdAt || message.timestamp) }}</span>
            </div>
            <div v-if="message.type === 'image' || message.type === 2 || message.msgType === 1" class="message-image">
              <el-image 
                :src="getImageUrl(message.content)" 
                :preview-src-list="[getImageUrl(message.content)]" 
                fit="cover"
                :initial-index="0"
                @error="() => handleImageError(message)"
                @load="() => handleImageLoad(message)"
              >
                <template #error>
                  <div class="image-error">
                    <el-icon><Picture /></el-icon>
                    <span>图片加载失败</span>
                  </div>
                </template>
                <template #placeholder>
                  <div class="image-loading">
                    <el-icon class="is-loading"><Loading /></el-icon>
                    <span>加载中...</span>
                  </div>
                </template>
              </el-image>
            </div>
            <div v-else-if="message.type === 'system' || message.msgType === 2" class="message-system">
              {{ message.content }}
            </div>
            <div v-else class="message-text" :style="{ whiteSpace: 'pre-wrap' }">{{ message.content }}</div>
          </div>
        </div>
      </template>
      
      <!-- 无消息提示 -->
      <div v-else-if="hasActiveConversation && !loading" class="empty-message">
        <p>暂无消息，开始发送第一条消息吧</p>
      </div>
    </div>
    
    <div class="chat-input" v-if="hasActiveConversation && !showEvaluation">
      <!-- 添加常用问题区域 -->
      <div class="common-questions" v-if="showCommonQuestions && commonQuestions.length > 0">
        <div class="question-title">
          <span>常见问题</span>
          <el-button type="text" size="small" @click="showCommonQuestions = false">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
        <div class="question-list">
          <div 
            v-for="(question, index) in commonQuestions" 
            :key="index" 
            class="question-item"
            @click="selectQuestion(question)"
          >
            {{ question }}
          </div>
        </div>
      </div>
      
      <div class="input-actions">
        <el-button 
          type="primary" 
          plain
          @click="showCommonQuestions = !showCommonQuestions"
          :title="showCommonQuestions ? '隐藏常见问题' : '显示常见问题'"
        >
          <el-icon><QuestionFilled /></el-icon>
        </el-button>
        <el-upload
          action=""
          :auto-upload="false"
          :show-file-list="false"
          :on-change="handleImageUpload"
          accept="image/*"
        >
          <el-button type="primary" plain>
            <el-icon><Picture /></el-icon>
          </el-button>
        </el-upload>
      </div>
      
      <div class="input-textarea">
        <el-input
          v-model="inputMessage"
          type="textarea"
          :rows="3"
          placeholder="请输入消息..."
          resize="none"
          :disabled="!currentConversation || currentConversation.status === 2"
          @keydown.enter.exact.prevent="sendMessage"
        />
        <div v-if="currentConversation && currentConversation.status === 2" class="chat-closed-tip">
          会话已结束
        </div>
      </div>
      
      <div class="input-send">
        <el-button 
          type="primary" 
          @click="sendMessage" 
          :disabled="!inputMessage.trim() || !currentConversation || currentConversation.status === 2">
          发送
        </el-button>
      </div>
    </div>
  </div>

  <!-- 添加历史会话对话框 -->
  <el-dialog
    v-model="showHistoryDialog"
    title="历史会话"
    width="80%"
    :before-close="handleHistoryDialogClose"
  >
    <el-table :data="chatStore.conversations" style="width: 100%">
      <el-table-column prop="id" label="会话ID" width="100" />
      <el-table-column label="状态" width="120">
        <template #default="scope">
          <el-tag :type="getSessionStatusType(scope.row.status)">
            {{ getSessionStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="客服类型" width="120">
        <template #default="scope">
          <el-tag v-if="scope.row.currentAgentType === 2" type="success">AI客服</el-tag>
          <el-tag v-else type="primary">人工客服</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" label="创建时间" width="180">
        <template #default="scope">
          {{ formatTime(scope.row.createdAt) }}
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button 
            v-if="[0, 1, 3].includes(scope.row.status)"
            type="primary" 
            link 
            @click="continueSession(scope.row)"
          >继续会话</el-button>
          <el-button 
            v-else 
            type="info" 
            link 
            @click="viewSessionHistory(scope.row)"
          >查看记录</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import { Picture, QuestionFilled, Close, Loading } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/user'
import { useChatStore } from '@/store/chat'
import { sendMessage as sendMessageAPI, getSessionById } from '@/api/chat'
import moment from 'moment'
import SessionEvaluation from './SessionEvaluation.vue'

// 路由实例
const router = useRouter()

// Store
const userStore = useUserStore()
const chatStore = useChatStore()

// 聊天DOM引用
const chatContent = ref(null)

// 加载状态
const loading = ref(false)

// 输入的消息
const inputMessage = ref('')

// 评价相关状态
const showEvaluation = ref(false)
const evaluationSessionId = ref(null)

// 常用问题控制
const showCommonQuestions = ref(false)
const commonQuestions = ref([
  '熊洞智家是做什么的？',
  '忘记密码怎么办？',
  '如何查看订单状态？',
  '如何申请退款？',
  '会员等级有哪些权益？',
  '如何联系客服？'
])

// 选择常用问题
const selectQuestion = (question) => {
  inputMessage.value = question
  // 自动关闭常用问题面板
  showCommonQuestions.value = false
}

// 计算属性
const userInfo = computed(() => userStore.userInfo)
const hasActiveConversation = computed(() => chatStore.hasActiveConversation)
const currentConversation = computed(() => chatStore.currentConversation)
const messages = computed(() => chatStore.messages)

// 添加转接状态变量
const transferRequested = ref(false)

// 添加判断是否为AI客服的计算属性
const isAIAgent = computed(() => {
  if (!currentConversation.value) return false
  
  // 优先使用currentAgentType字段判断
  if (currentConversation.value.currentAgentType === 2) return true
  
  // 备用: 使用agent.agentType判断 
  if (currentConversation.value.agent && currentConversation.value.agent.agentType === 2) return true
  
  // 备用: 使用agentId与AI客服ID比较
  if (currentConversation.value.agentId && chatStore.aiAgentId && 
      currentConversation.value.agentId === chatStore.aiAgentId) return true
  
  return false
})

// 监听消息列表变化，自动滚动到底部
watch(messages, () => {
  nextTick(() => {
  scrollToBottom()
  })
}, { deep: true })

// 生命周期钩子
onMounted(async () => {
  console.log('Chat组件挂载 - 开始初始化')
  
  // 从localStorage恢复状态
  userStore.loadFromStorage()
  
  // 如果没有登录或不是用户角色，跳转到登录页
  if (!userStore.isLoggedIn || !userStore.isUser) {
    console.warn('用户未登录或不是用户角色，跳转到登录页')
    router.push('/login')
    return
  }
  
  // 检查并记录设备信息
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  console.log('设备信息:', {
    isMobile,
    userAgent: navigator.userAgent,
    userId: userStore.userId,
    token: userStore.token ? '存在' : '不存在',
    platform: navigator.platform,
    windowWidth: window.innerWidth,
    windowHeight: window.innerHeight
  })
  
  // 设置当前在聊天页面
  chatStore.setInChatPage(true)
  
  // 确保WebSocket连接初始化 - 强制关闭可能存在的旧连接
  if (chatStore.ws) {
    console.log('关闭可能存在的旧WebSocket连接')
    chatStore.closeWebSocket()
  }
  
  // 注册监听转接人工客服通知消息的事件
  window.addEventListener('chat-transfer-notification', handleTransferNotification)
  
  // 初始化过程的总体状态记录
  console.log('初始化过程状态 - 开始:', {
    inChatPage: chatStore.isInChatPage,
    connected: chatStore.connected,
    hasWs: !!chatStore.ws
  })
  
  try {
    // 显式初始化新的WebSocket连接
    console.log('开始初始化WebSocket连接')
    chatStore.initWebSocket()
    
    // 检查连接状态
    const checkConnectionStatus = () => {
      console.log('WebSocket连接状态检查:', {
        connected: chatStore.connected,
        readyState: chatStore.ws ? chatStore.ws.readyState : 'no-websocket',
        inChatPage: chatStore.isInChatPage
      })
      
      // 如果还没有连接成功，但已经有WebSocket实例且处于连接中状态，给更多时间
      if (!chatStore.connected && chatStore.ws && chatStore.ws.readyState === WebSocket.CONNECTING) {
        console.log('WebSocket正在连接中，再等待3秒...')
        setTimeout(checkConnectionStatus, 3000)
        return
      }
      
      // 如果连接失败，尝试重新连接
      if (!chatStore.connected) {
        console.log('首次连接失败，启动备用机制...')
        
        // 再次尝试初始化，如果还是失败则启动轮询
        chatStore.initWebSocket()
        
        // 如果还是连接失败，启动HTTP轮询
        setTimeout(() => {
          if (!chatStore.connected) {
            console.log('再次连接失败，启动HTTP轮询作为备份')
            chatStore.startPolling()
          }
        }, 5000)
      }
    }
    
    // 延迟3秒检查连接状态
    setTimeout(checkConnectionStatus, 3000)
    
  } catch (error) {
    console.error('WebSocket初始化过程中出错:', error)
  }
  
  // 加载会话列表
  await loadConversations()
  
  // 确保自动滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
  
  // 添加新消息通知事件监听
  const handleNewMessage = () => {
    nextTick(() => {
      scrollToBottom()
    })
  }
  
  window.addEventListener('chat-message-received', handleNewMessage)
  
  // 添加消息状态调试
  console.log('会话列表:', JSON.stringify(chatStore.conversations.map(c => ({ 
    id: c.id, 
    status: c.status,
    currentAgentType: c.currentAgentType
  }))))
  
  // 每次页面加载或切换会话后，输出一次当前消息数组
  setTimeout(() => {
    console.log('当前消息状态:', 
      chatStore.messages.map(m => ({
        id: m.id,
        content: m.content && m.content.length > 20 ? m.content.substring(0, 20) + '...' : m.content,
        from: m.from,
        senderType: m.senderType,
        msgType: m.msgType,
        display: isMessageVisible(m)
      }))
    );
  }, 2000);
  
  // 添加网络状态变化监听
  window.addEventListener('online', handleNetworkChange)
  window.addEventListener('offline', handleNetworkChange)
  
  console.log('Chat组件挂载 - 初始化完成')
})

// 处理网络状态变化
const handleNetworkChange = (event) => {
  console.log('网络状态变化:', event.type)
  
  if (event.type === 'online') {
    // 网络恢复时，尝试重新连接WebSocket
    console.log('网络恢复，尝试重新连接WebSocket')
    chatStore.closeWebSocket() // 先关闭旧连接
    setTimeout(() => {
      chatStore.initWebSocket() // 初始化新连接
    }, 1000)
  } else if (event.type === 'offline') {
    // 网络断开时，标记WebSocket为断开状态
    console.log('网络断开，标记WebSocket为断开状态')
    chatStore.connected = false
    // 停止心跳
    chatStore.stopHeartbeat()
  }
}

// 在onUnmounted中清理资源
onUnmounted(() => {
  console.log('用户聊天组件卸载，清理资源')
  
  // 清除定时器
  if (sessionUpdateInterval) {
    clearInterval(sessionUpdateInterval)
    sessionUpdateInterval = null
  }
  
  // 移除事件监听
  window.removeEventListener('chat-message-received', handleNewMessage)
  window.removeEventListener('online', handleNetworkChange)
  window.removeEventListener('offline', handleNetworkChange)
  window.removeEventListener('chat-transfer-notification', handleTransferNotification)
  
  // 设置当前不在聊天页面
  chatStore.setInChatPage(false)
  
  // 停止HTTP轮询
  chatStore.stopPolling()
})

// 加载会话列表
const loadConversations = async () => {
  if (!userStore.isLoggedIn || !userStore.isUser) return
  
  loading.value = true
  
  try {
    await chatStore.fetchConversations()
    console.log('获取会话列表成功，会话数量:', chatStore.conversations.length)
    
    // 检查是否存在未结束的会话
    const unfinishedConversation = chatStore.conversations.find(c => [0, 1, 3].includes(c.status))
    console.log('未结束的会话:', unfinishedConversation)
    
    if (unfinishedConversation) {
      // 如果存在未结束会话，自动进入该会话
      await chatStore.setCurrentConversation(unfinishedConversation)
      console.log('已设置当前会话:', unfinishedConversation.id)
      
      // 加载最近100条消息历史
      console.log('开始加载历史消息, 会话ID:', unfinishedConversation.id)
      await chatStore.loadMessages(unfinishedConversation.id, 100)
      console.log('消息加载完成, 消息数量:', chatStore.messages.length)
      
      // 显示提示信息
      ElMessage.info('检测到未结束的会话，已自动恢复')
    } else {
      // 如果没有未结束会话，显示欢迎界面
      chatStore.clearCurrentConversation()
      console.log('没有未结束的会话，显示欢迎界面')
    }
  } catch (error) {
    console.error('加载会话失败', error)
    ElMessage.error('加载会话失败')
  } finally {
    loading.value = false
  }
}

// 开始新的咨询
const startNewChat = async () => {
  loading.value = true
  
  try {
    // 检查是否已有进行中的会话
    const activeConversation = chatStore.conversations.find(c => c.status === 1)
    if (activeConversation) {
      await chatStore.setCurrentConversation(activeConversation)
      ElMessage.info('您已有正在进行的会话')
      return
    }
    
    // 确保用户ID是数字类型
    const userId = parseInt(userStore.userId, 10)
    console.log('准备创建会话，用户ID:', userId, '用户信息:', userStore.userInfo)
    
    // 创建新会话，默认分配AI客服
    const res = await chatStore.createNewConversation({ 
      userId,
      channel: "PC端", // 数据源下的渠道来源
      datasource: "PC端", // 数据来源
      collection_name: "information", // 查询知识库集合
      scene: "PC端" // 对话应用场景
    })
    
    if (res.code === 200) {
      ElMessage.success('AI客服已为您接入，请问有什么可以帮您？')
    } else {
      ElMessage.error(res.message || '创建会话失败')
    }
  } catch (error) {
    console.error('创建会话失败', error)
    ElMessage.error('创建会话失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 添加对话历史记录
const dialogHistory = ref([]);

// 生成对话历史格式
const generateHistoryMessages = () => {
  // 只从messages数组中获取用户消息和AI回复消息
  const historyMessages = chatStore.messages
    .filter(m => {
      // 获取用户和AI消息，排除系统消息和加载状态消息
      const isUser = m.from === 0 || m.senderType === 0;
      const isAI = m.senderType === 2 || (m.from === 1 && m.currentAgentType === 2);
      return (isUser || isAI) && !m.id.toString().startsWith('loading-') && m.content.trim() !== "";
    })
    .map(m => {
      // 转换为API要求的格式
      const role = (m.from === 0 || m.senderType === 0) ? "user" : "assistant";
      return {
        role: role,
        content: m.content
      };
    });
  
  // 限制历史消息数量以避免超长上下文，保留最新的10组对话
  const maxHistoryPairs = 10;
  if (historyMessages.length > maxHistoryPairs * 2) {
    // 仅保留最新的n组对话
    return historyMessages.slice(-maxHistoryPairs * 2);
  }
  
  return historyMessages;
};

// 发送消息
const sendMessage = async () => {
  if (!inputMessage.value.trim()) return
  
  if (!currentConversation.value) {
    ElMessage.warning('请先开始会话')
    return
  }
  
  // 检查会话是否已关闭
  if (currentConversation.value.status === 2) {
    ElMessage.warning('会话已结束，无法发送消息')
    return
  }
  
  const message = inputMessage.value.trim()
  inputMessage.value = ''
  
  try {
    console.log('发送消息:', message)
    console.log('当前会话状态:', {
      id: currentConversation.value.id,
      status: currentConversation.value.status,
      agentType: currentConversation.value.currentAgentType
    })
    
    // 直接检查是否为AI客服对话
    const isAI = currentConversation.value.currentAgentType === 2
    console.log('是否为AI客服对话:', isAI)
    
    // 检查是否已经转为人工客服
    if (currentConversation.value.transferRequested === 1 && !isAI) {
      console.log('会话已转为人工客服，使用正常消息发送流程')
    }
    
    // 先添加用户消息到聊天界面
    const userMessageId = 'local-' + Date.now()
    chatStore.messages.push({
      id: userMessageId,
      sessionId: currentConversation.value.id,
      content: message,
      from: 0, // 用户发送
      senderType: 0, // 用户
      createdAt: new Date().toISOString()
    })
    
    // 记录当前会话状态
    console.log('发送消息前的会话状态:', {
      id: currentConversation.value.id,
      agent: currentConversation.value.agent,
      status: currentConversation.value.status,
      currentAgentType: currentConversation.value.currentAgentType,
      messagesCount: chatStore.messages.length
    });
    
    // 滚动到底部
    await nextTick()
    scrollToBottom()
    
    // 如果是AI客服，使用流式API接口
    if (isAI) {
      try {
        // 先通过WebSocket发送用户消息，保存到数据库
        await chatStore.sendTextMessage(message, null, true)
        
        // 添加AI思考中的临时消息
        const loadingMsgId = 'loading-' + Date.now()
        chatStore.messages.push({
          id: loadingMsgId,
          sessionId: currentConversation.value.id,
          content: "正在思考中...",
          from: 1,
          senderType: 2, // AI客服
          createdAt: new Date().toISOString()
        })
        
        // 滚动到底部
        await nextTick()
        scrollToBottom()
        
        // 创建临时消息对象用于流式更新
        const aiMsgId = 'ai-' + Date.now()
        let aiContent = ''
        
        // 替换临时消息为实际AI回复(初始为空)
        const loadingIndex = chatStore.messages.findIndex(m => m.id === loadingMsgId)
        if (loadingIndex !== -1) {
          // 重要：直接修改数组中的对象，而不是替换整个对象，保持响应性
          const updatedMessage = {
            id: aiMsgId,
            sessionId: currentConversation.value.id,
            content: aiContent,
            from: 1,
            senderType: 2, // AI客服
            createdAt: new Date().toISOString()
          };
          
          // 替换消息对象
          chatStore.messages.splice(loadingIndex, 1, updatedMessage);
          console.log('已将loadingMsg替换为aiMsg，ID:', aiMsgId, '索引:', loadingIndex);
          
          // 确保Vue检测到更新
          chatStore.messages = [...chatStore.messages];
        } else {
          console.warn('未找到loadingMsg，ID:', loadingMsgId);
          // 如果找不到loadingMsg，直接添加新消息
          chatStore.messages.push({
            id: aiMsgId,
            sessionId: currentConversation.value.id,
            content: aiContent,
            from: 1,
            senderType: 2, // AI客服
            createdAt: new Date().toISOString()
          });
          console.log('已添加新的aiMsg，ID:', aiMsgId);
        }

        // 确认一下消息是否真的添加到了数组中
        const confirmCheck = chatStore.messages.findIndex(m => m.id === aiMsgId);
        if (confirmCheck === -1) {
          console.error('严重错误：消息未能成功添加到数组中，请检查Vue的响应式更新');
          // 尝试再次添加
          chatStore.messages.push({
            id: aiMsgId,
            sessionId: currentConversation.value.id,
            content: aiContent,
            from: 1,
            senderType: 2, // AI客服
            createdAt: new Date().toISOString()
          });
          console.log('已尝试再次添加消息');
        } else {
          console.log('确认消息已存在于数组中，索引:', confirmCheck);
        }
        
        // 创建EventSource连接流式API
        const apiUrl = `${import.meta.env.VITE_API_URL_LLM}/chat/stream`
        
        // 检查环境变量是否正确配置
        if (!import.meta.env.VITE_API_URL_LLM) {
          throw new Error('VITE_API_URL_LLM 环境变量未配置，请检查 .env 文件')
        }
        
        console.log('使用流式API:', apiUrl)
        console.log('环境变量:', {
          VITE_API_URL_LLM: import.meta.env.VITE_API_URL_LLM,
          VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL
        })
        
        // 准备请求数据
        const history = generateHistoryMessages();
        console.log('对话历史:', history);
        console.log('对话历史数量:', history.length);

        const requestData = {
          messages: message,
          collection_name: "information", // 指定集合名称
          //model: "qwen2.5:3b",
          stream: true,
          history: history,
          scene: 'PC端',
          channel: 'PC端',
          datasource: 'PC端'
        }
        console.log('请求数据:', requestData)
        
        // 使用EventSource建立流式连接
        // 注意：由于我们需要发送POST请求体，需要通过fetch+ReadableStream方式实现
        
        // 发起流式请求
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream'
          },
          body: JSON.stringify(requestData)
        })
        
        console.log('API响应状态:', response.status)
        console.log('API响应头:', Object.fromEntries(response.headers.entries()))
        
        if(!response.ok) {
          throw new Error(`HTTP错误，状态: ${response.status}`)
        }
        
        // 处理SSE流
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        let totalChunks = 0;
        
        // 初始化累积内容
        aiContent = "";
        console.log('开始处理流数据');
        
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            console.log('数据流已关闭，总共处理块数:', totalChunks);
            break;
          }
          
          // 添加到缓冲区
          const newText = decoder.decode(value, { stream: true });
          buffer += newText;
          console.log(`收到新数据: ${newText.length} 字节，当前缓冲区: ${buffer.length} 字节`);
          
          // 持续处理所有完整的SSE消息
          let eventEndIndex;
          let processedEvents = 0;
          
          while ((eventEndIndex = buffer.indexOf('\n\n')) > -1) {
            const eventData = buffer.substring(0, eventEndIndex);
            buffer = buffer.substring(eventEndIndex + 2);
            processedEvents++;
            
            // 提取实际内容
            let content = '';
            
            // 处理各种可能的数据格式
            if (eventData.startsWith('data:')) {
              // SSE标准格式：data: 内容，去除前缀并清理空格
              content = eventData.substring(5).trim();
              // 如果内容仅为data:，则跳过
              if (!content || content === 'data:' || content === 'data: ') {
                console.log('跳过空data前缀');
                continue;
              }
            } else if (eventData.startsWith('大模型输出：')) {
              // 处理"大模型输出："格式
              content = eventData.substring(6).trim();
            } else {
              // 其他格式，直接使用
              content = eventData.trim();
            }
            
            // 如果内容为空，跳过处理
            if (!content) {
              console.log('跳过空内容块');
              continue;
            }
            
            // 特殊处理：检查是否有换行标记
            if (content === '\\n' || content === '\n' || content === '<br>' || content === '<br/>') {
              console.log('检测到独立换行符');
              content = '\n';
            }
            
            // 移除任何剩余的 "data:" 前缀
            content = content.replace(/^data:\s*/g, '');
            
            // 如果清理后内容为空，跳过处理
            if (!content.trim()) {
              console.log('清理前缀后内容为空，跳过');
              continue;
            }
            
            // 处理句子结束自动换行
            if ((aiContent.length > 0) && (content.endsWith('.') || content.endsWith('。') || content.endsWith('!') || content.endsWith('！'))) {
              console.log('检测到句子结束，添加换行');
              content = content + '\n';
            }

            // 记录调试信息
            console.log(`处理消息块 ${totalChunks+1}: ${content.length} 字符，内容: "${content}"`);
            
            // 记录收到的片段并添加到累积内容
            totalChunks++;
            
            // 保留原始格式，包括换行符
            if (content.includes('\n') || content.endsWith('.') || content.endsWith('。')) {
              console.log('检测到换行或句子结束，保留格式');
              // 如果检测到换行符或句号，保持原始格式
              aiContent += content;
            } else {
              // 否则正常追加内容
              aiContent += content;
            }
            
            // 调试当前累积的内容
            console.log(`当前累积内容(${aiContent.length}字符): "${aiContent.substring(0, 50)}${aiContent.length > 50 ? '...' : ''}"`);
            
            // 更新聊天UI
            const aiIndex = chatStore.messages.findIndex(m => m.id === aiMsgId);
            if (aiIndex !== -1) {
              // 直接更新内容，保持原始格式
              chatStore.messages[aiIndex].content = aiContent;
              
              // 强制Vue重新渲染
              chatStore.messages = [...chatStore.messages];
              
              // 滚动到底部
              await nextTick();
              scrollToBottom();
            } else {
              console.warn('找不到AI消息，ID:', aiMsgId);
              
              // 添加新消息
              chatStore.messages.push({
                id: aiMsgId,
                sessionId: currentConversation.value.id,
                content: aiContent,
                from: 1,
                senderType: 2, // AI客服
                createdAt: new Date().toISOString()
              });
              
              // 强制更新和滚动
              chatStore.messages = [...chatStore.messages];
              await nextTick();
              scrollToBottom();
            }
          }
          
          if (processedEvents > 0) {
            console.log(`本次处理了 ${processedEvents} 个事件，缓冲区剩余 ${buffer.length} 字节`);
          }
        }
        
        console.log('流式接收完成，最终内容长度:', aiContent.length, '最终内容:', aiContent);
        
        // 确保最终内容的换行符被正确保留
        aiContent = aiContent.replace(/\\n/g, '\n');
        
        // 调整格式，如果有句子结束没有换行的情况，添加换行
        aiContent = aiContent.replace(/([.。!！?？])\s+([^\n])/g, '$1\n$2');
        
        // 流式接收完成后，只有此时才保存完整回复到数据库
        console.log('流式接收完成，保存完整回复到数据库，内容长度:', aiContent.length)
        
        // 保存当前消息数组状态，以防后续操作清空
        const currentMessages = [...chatStore.messages];
        console.log('流式处理完成时的消息数量:', currentMessages.length);
        
        try {
          // 构建Java后端API地址，避免使用Axios默认baseURL
          const javaApiUrl = `${import.meta.env.VITE_API_BASE_URL || '/api'}/message/send`;
          
          // 使用fetch直接调用后端API
          const response = await fetch(javaApiUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `${localStorage.getItem('token') || ''}`
            },
            body: JSON.stringify({
              sessionId: currentConversation.value.id,
              content: aiContent,
              msgType: 0, // 0=文本消息
              senderType: 2, // 2=AI客服
              isRead: 0,
              scene: '默认应用场景',
              datasource: '默认数据源',
              collectionName: "information"
            })
          });
          
          if (!response.ok) {
            throw new Error(`API响应错误: ${response.status}`);
          }
          
          const data = await response.json();
          if (data.code !== 200) {
            throw new Error(data.message || 'API请求失败');
          }
          
          console.log('AI回复保存成功', data);
          
          // 更新本地消息对象，将临时ID替换为实际数据库ID
          if (data.data && data.data.id) {
            const aiIndex = chatStore.messages.findIndex(m => m.id === aiMsgId);
            if (aiIndex !== -1) {
              chatStore.messages[aiIndex].id = data.data.id;
              console.log('已更新AI消息ID为数据库ID:', data.data.id);
            }
          }
          
          // 检查保存后是否消息数组被清空
          if (chatStore.messages.length === 0 && currentMessages.length > 0) {
            console.warn('警告：消息数组被清空，尝试恢复...');
            chatStore.messages = currentMessages;
            console.log('已恢复消息数组，现有消息数量:', chatStore.messages.length);
          }
          
          // 不再需要刷新消息列表，因为我们已经在流处理中实时更新了UI
          // 如果实在需要刷新，可以只替换当前消息而不是重新加载所有消息
        } catch (e) {
          console.error('保存AI回复到数据库失败:', e);
          // 错误不影响用户体验，仅记录日志
        }
        
      } catch (error) {
        console.error('流式获取AI回复失败:', error)
        // 显示错误消息
        chatStore.messages.push({
          id: 'err-' + Date.now(),
          sessionId: currentConversation.value.id,
          content: "抱歉，AI服务暂时无法回应，请稍后再试或转接人工客服",
          from: 1,
          senderType: 2, // AI客服
          createdAt: new Date().toISOString()
        })
        ElMessage.error('获取AI回复失败，请稍后重试')
      }
    } else {
      // 非AI客服对话，使用WebSocket发送消息
      try {
        // 通过WebSocket发送消息
        await chatStore.sendTextMessage(message, null, true)
      } catch (error) {
        console.error('发送消息失败:', error)
        ElMessage.error('发送消息失败，请稍后重试')
      }
    }
  } catch (error) {
    console.error('发送消息失败', error)
    // 如果是会话关闭错误，给出特定提示
    if (error.message && error.message.includes('会话已关闭')) {
      ElMessage.warning('会话已结束，无法发送消息')
      // 重新加载会话信息
      await chatStore.refreshCurrentConversation()
    } else {
      ElMessage.error('发送消息失败，请稍后重试')
    }
  }
}

// 结束会话
const handleEndChat = () => {
  if (!currentConversation.value) return
  
  ElMessageBox.confirm('确定要结束当前会话吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 关闭会话
      const res = await chatStore.closeConversation({ 
        conversationId: currentConversation.value.id,
        closedBy: 'user'
      })
      
      if (res.code === 200) {
        ElMessage.success('会话已结束')
        
        // 保存会话ID用于评价
        evaluationSessionId.value = currentConversation.value.id
        
        // 重新加载会话列表
        await chatStore.fetchConversations()
        
        // 清空当前会话
        chatStore.clearCurrentConversation()
        
        // 显示评价组件
        showEvaluation.value = true
      } else {
        ElMessage.error(res.message || '结束会话失败')
      }
    } catch (error) {
      console.error('结束会话失败', error)
      ElMessage.error('结束会话失败，请稍后重试')
    }
  }).catch(() => {})
}

// 处理评价完成
const handleEvaluationComplete = () => {
  ElMessage.success('感谢您的评价！')
  setTimeout(() => {
    showEvaluation.value = false
    evaluationSessionId.value = null
  }, 1500)
}

// 处理跳过评价
const handleSkipEvaluation = () => {
  showEvaluation.value = false
  evaluationSessionId.value = null
  ElMessage.info('您已跳过评价')
}

// 处理图片上传
const handleImageUpload = async (file) => {
  if (!file || !file.raw) return
  
  if (!currentConversation.value) {
    ElMessage.warning('请先开始会话')
    return
  }
  
  // 检查会话状态
  if (currentConversation.value.status === 2) {
    ElMessage.warning('会话已结束，无法发送消息')
    return
  }
  
  // 校验文件类型和大小
  const isImage = file.raw.type.startsWith('image/')
  const isLt10M = file.raw.size / 1024 / 1024 < 10 // 修改为10MB，与后端配置一致
  
  if (!isImage) {
    ElMessage.error('只能上传图片文件')
    return
  }
  
  if (!isLt10M) {
    ElMessage.error('图片大小不能超过 10MB')
    return
  }
  
  // 添加加载中的提示
  // ElMessage.info('图片发送中...')
  
  try {
    // 创建临时图片URL以在本地预览
    const localImageUrl = URL.createObjectURL(file.raw)
    
    // 创建临时消息对象用于本地显示
    const tempMessageId = 'temp-' + Date.now()
    chatStore.messages.push({
      id: tempMessageId,
      sessionId: currentConversation.value.id,
      content: localImageUrl,
      from: 0, // 用户发送
      senderType: 0, // 用户
      type: 'image', // 图片消息
      msgType: 1, // 图片消息 (后端格式)
      createdAt: new Date().toISOString(),
      sending: true
    })
    
    // 滚动到最新消息
    await nextTick()
    scrollToBottom()
    
    // 使用formData上传图片
    const formData = new FormData()
    formData.append('file', file.raw)
    formData.append('sessionId', currentConversation.value.id)
    formData.append('senderType', '0') // 0表示用户
    
    // 调用新的上传接口
    const response = await fetch('https://jms.bearhome.cn/api/file/upload', {
      method: 'POST',
      headers: {
        'Authorization': `${localStorage.getItem('token') || ''}`
      },
      body: formData
    })
    
    if (!response.ok) {
      throw new Error('图片上传失败，服务器响应: ' + response.status)
    }
    
    const result = await response.json()
    
    if (result.statusCode !== 200) {
      throw new Error(result.errorInfo || '图片上传失败')
    }
    
    // 获取上传后的图片URL
    const imageUrl = result.data
    
    // 构建消息数据
    const messageData = {
      sessionId: currentConversation.value.id,
      content: imageUrl,
      msgType: 1, // 1=图片消息
      senderType: 0, // 0=用户
      isRead: 0
    }
    
    // 发送消息到服务器
    const msgResult = await sendMessageAPI(messageData)
    
    // 上传成功，更新临时消息
    const messageIndex = chatStore.messages.findIndex(m => m.id === tempMessageId)
    if (messageIndex !== -1) {
      // 用实际的消息替换临时消息
      chatStore.messages[messageIndex] = {
        ...chatStore.messages[messageIndex],
        id: msgResult?.data?.id || tempMessageId,
        content: imageUrl,
        sending: false
      }
      
      // 强制Vue重新渲染消息列表
      chatStore.messages = [...chatStore.messages]
    }
    
    // 使用WebSocket发送图片消息，确保客服能实时收到
    try {
      const wsMessage = {
        type: 2, // 图片消息类型
        from: 0, // 0-用户发送
        senderId: userStore.userId,
        receiverId: currentConversation.value.agentId,
        sessionId: currentConversation.value.id,
        content: imageUrl,
        msgType: 1, // 1-图片消息类型
        timestamp: Date.now()
      }
      
      console.log('准备通过WebSocket发送图片消息:', wsMessage)
      console.log('WebSocket连接状态:', {
        connected: chatStore.connected,
        readyState: chatStore.ws ? chatStore.ws.readyState : 'no-websocket'
      })
      
      await chatStore.sendMessage(wsMessage)
      console.log('WebSocket图片消息发送成功:', wsMessage)
      
      // 确保本地图片预览也用正确的URL
      const updatedMessageIndex = chatStore.messages.findIndex(m => m.id === msgResult?.data?.id || tempMessageId)
      if (updatedMessageIndex !== -1) {
        console.log('再次确认消息URL已更新:', imageUrl)
        chatStore.messages[updatedMessageIndex].content = imageUrl
        // 确保消息类型正确
        chatStore.messages[updatedMessageIndex].type = 'image'
        chatStore.messages[updatedMessageIndex].msgType = 1
        // 强制Vue重新渲染
        chatStore.messages = [...chatStore.messages]
      }
    } catch (wsError) {
      console.error('WebSocket发送图片消息失败:', wsError)
      // 即使WebSocket发送失败，不影响用户体验，因为消息已经通过HTTP保存到了数据库
    }
    
    console.log('图片上传成功，消息已发送')
    ElMessage.success('图片发送成功')
    
  } catch (error) {
    console.error('发送图片失败:', error)
    ElMessage.error('图片发送失败，请稍后重试')
    
    // 移除临时消息
    const messageIndex = chatStore.messages.findIndex(m => m.sending === true)
    if (messageIndex !== -1) {
      chatStore.messages.splice(messageIndex, 1)
    }
  } finally {
    // 释放临时URL
    if (file.raw) {
      URL.revokeObjectURL(URL.createObjectURL(file.raw))
    }
  }
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  
  const messageDate = moment(timestamp)
  const now = moment()
  
  // 如果是今天，只显示时间
  if (messageDate.isSame(now, 'day')) {
    return messageDate.format('HH:mm')
  }
  
  // 如果是昨天，显示"昨天 时间"
  if (messageDate.isSame(now.clone().subtract(1, 'day'), 'day')) {
    return `昨天 ${messageDate.format('HH:mm')}`
  }
  
  // 如果是今年，显示"月-日 时间"
  if (messageDate.isSame(now, 'year')) {
    return messageDate.format('MM-DD HH:mm')
  }
  
  // 其他情况，显示完整日期时间
  return messageDate.format('YYYY-MM-DD HH:mm')
}

// 滚动到底部
const scrollToBottom = () => {
    if (chatContent.value) {
    // 确保在DOM更新后执行滚动
    nextTick(() => {
      chatContent.value.scrollTop = chatContent.value.scrollHeight
      console.log('已滚动到底部', chatContent.value.scrollHeight)
    })
  }
}

// 请求转接人工客服
const requestHumanAgent = async () => {
  if (!currentConversation.value) return
  
  transferRequested.value = true
  
  try {
    // 显示转接中的消息
    const transferMsgId = 'transfer-' + Date.now()
    chatStore.messages.push({
      id: transferMsgId,
      sessionId: currentConversation.value.id,
      content: "正在请求转接人工客服，请稍候...",
      from: 1,
      senderType: 2, // AI客服
      createdAt: new Date().toISOString()
    })
    
    // 保存转接请求消息到数据库
    try {
      await sendMessageAPI({
        sessionId: currentConversation.value.id,
        content: "正在请求转接人工客服，请稍候...",
        msgType: 0, // 0=文本消息
        senderType: 2, // 2=AI客服
        isRead: 0
      })
    } catch (e) {
      console.error('保存转接请求消息失败:', e)
    }
    
    // 使用HTTP API直接请求转接
    const res = await fetch(`${import.meta.env.VITE_API_BASE_URL || '/api'}/session/${currentConversation.value.id}/transfer-to-human`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `${localStorage.getItem('token') || ''}`
      }
    })
    
    const data = await res.json()
    console.log('转人工响应:', data)
    
    if (data.code === 200) {
      // 立即更新当前会话状态，不等轮询
      currentConversation.value.currentAgentType = 1;
      currentConversation.value.transferRequested = 1;
      
      // 转接成功，结束请求状态
      transferRequested.value = false;
      
      // 添加转接成功消息
      // const systemMessageId = 'system-' + Date.now();
      // const systemMsg = {
      //   id: systemMessageId,
      //   sessionId: currentConversation.value.id,
      //   content: "已成功转接到人工客服，请稍候人工客服接入",
      //   type: 'system',
      //   from: 1,
      //   senderType: 1, // 系统消息
      //   createdAt: new Date().toISOString()
      // };
      // chatStore.messages.push(systemMsg);
      
      // 保存系统消息到数据库
      try {
        await sendMessageAPI({
          sessionId: currentConversation.value.id,
          content: systemMsg.content,
          msgType: 0, // 0=文本消息，使用文本消息类型确保客服端能看到
          senderType: 1, // 1=系统/客服
          isRead: 0,
          from: 1 // 指定是客服发送的
        });
      } catch (e) {
        console.error('保存系统消息失败:', e);
      }
      
      
      
      // 立即刷新会话信息
      await chatStore.refreshCurrentConversation()
      
      // 强制重新加载消息历史
      await chatStore.loadMessages(currentConversation.value.id)
      
      // 模拟延迟后检查会话agent信息
      setTimeout(async () => {
        // 再次刷新获取人工客服信息
        try {
          const agentInfoRes = await getSessionById(currentConversation.value.id)
          console.log('获取人工客服信息:', agentInfoRes)
          
          if (agentInfoRes.code === 200 && agentInfoRes.data.agent) {
            // 更新到当前会话
            currentConversation.value.agent = agentInfoRes.data.agent
            
            // 添加人工客服接入消息
            const agentMsg = {
              id: 'agent-connected-' + Date.now(),
              sessionId: currentConversation.value.id,
              content: `人工客服 ${agentInfoRes.data.agent.name} 已接入，请开始咨询`,
              type: 'system',
              from: 1,
              senderType: 1,
              createdAt: new Date().toISOString()
            };
            
            chatStore.messages.push(agentMsg)
            
            // 保存系统消息
            await sendMessageAPI({
              sessionId: currentConversation.value.id,
              content: agentMsg.content,
              msgType: 0, // 文本消息
              senderType: 1, // 系统
              isRead: 0
            })
          }
        } catch (e) {
          console.error('获取人工客服信息失败:', e)
        }
      }, 2000)
    } else {
      // 转接失败
      chatStore.messages.push({
        id: 'system-' + Date.now(),
        sessionId: currentConversation.value.id,
        content: data.message || "转接人工客服失败，请稍后再试",
        type: 'system',
        from: 1,
        senderType: 1,
        createdAt: new Date().toISOString()
      })
      
      ElMessage.warning(data.message || '转接请求失败，请稍后再试')
      transferRequested.value = false
    }
  } catch (error) {
    console.error('请求转接人工客服失败', error)
    
    // 添加错误消息
    chatStore.messages.push({
      id: 'system-' + Date.now(),
      sessionId: currentConversation.value.id,
      content: "转接人工客服请求失败，请稍后重试",
      type: 'system',
      from: 1,
      senderType: 1,
      createdAt: new Date().toISOString()
    })
    
    ElMessage.error('请求转接人工客服失败，请稍后重试')
    transferRequested.value = false
  }
}

// 获取头像文本
const getAvatarText = (message) => {
  if (message.from === 0 || message.senderType === 0 || message.userId) {
    return (message.from === 0 || message.senderType === 0 || message.userId) ? (userInfo?.nickname?.substring(0, 1) || '我') : (message.agentName?.substring(0, 1) || '客')
  } else if (message.from === 1 || message.senderType === 1 || message.agentId) {
    return (message.from === 1 || message.senderType === 1 || message.agentId) ? (message.agentName?.substring(0, 1) || '客') : (message.agentName?.substring(0, 1) || '客')
  } else if (message.senderType === 2 || (message.from === 1 && message.currentAgentType === 2)) {
    return 'AI'
  }
  return ''
}

// 获取发送者名称
const getSenderName = (message) => {
  // 系统消息特殊处理
  if (message.type === 'system' || message.msgType === 2) {
    return '系统消息';
  }
  
  // AI客服消息处理 - 修改逻辑，确保正确识别AI消息
  if (message.senderType === 2 || message.from === 2 || 
     (message.from === 1 && message.currentAgentType === 2) ||
     (message.senderId && message.senderId === chatStore.aiAgentId)) {
    return 'AI客服';
  }
  
  // 用户消息处理
  if (message.from === 0 || message.senderType === 0 || message.userId) {
    return userInfo?.nickname || '我';
  }
  
  // 人工客服消息处理
  if (message.from === 1 || message.senderType === 1 || message.agentId) {
    return message.agentName || '客服';
  }
  
  // 默认情况
  return '未知用户';
}

// 添加检查消息是否可见的函数
const isMessageVisible = (message) => {
  // 检查消息是否有内容
  if (!message.content) return false;
  
  // 检查消息DOM是否正确渲染
  const messageElements = document.querySelectorAll('.message-item');
  for (let i = 0; i < messageElements.length; i++) {
    const el = messageElements[i];
    if (el.textContent.includes(message.content.substring(0, 10))) {
      return true;
    }
  }
  
  return false;
}

// 在script setup部分添加以下内容
const showHistoryDialog = ref(false)

// 获取会话状态类型
const getSessionStatusType = (status) => {
  switch (status) {
    case 0: return 'warning' // 排队中
    case 1: return 'success' // 会话中
    case 2: return 'info'    // 已结束
    case 3: return 'primary' // AI会话中
    default: return 'info'
  }
}

// 获取会话状态文本
const getSessionStatusText = (status) => {
  switch (status) {
    case 0: return '排队中'
    case 1: return '会话中'
    case 2: return '已结束'
    case 3: return 'AI会话中'
    default: return '未知状态'
  }
}

// 继续会话
const continueSession = async (session) => {
  try {
    await chatStore.setCurrentConversation(session)
    await chatStore.loadMessages(session.id, 100)
    showHistoryDialog.value = false
    ElMessage.success('已恢复会话')
  } catch (error) {
    console.error('恢复会话失败:', error)
    ElMessage.error('恢复会话失败')
  }
}

// 查看会话历史
const viewSessionHistory = async (session) => {
  try {
    await chatStore.setCurrentConversation(session)
    await chatStore.loadMessages(session.id, 100)
    showHistoryDialog.value = false
  } catch (error) {
    console.error('加载会话历史失败:', error)
    ElMessage.error('加载会话历史失败')
  }
}

// 关闭历史对话框
const handleHistoryDialogClose = (done) => {
  done()
}

// 处理图片加载错误
const handleImageError = (message) => {
  console.error('图片加载失败:', message.content)
  console.error('图片消息详情:', {
    id: message.id,
    type: message.type,
    msgType: message.msgType,
    from: message.from,
    senderType: message.senderType,
    isImageUrl: message.content && (message.content.startsWith('http://') || message.content.startsWith('https://'))
  })
  //ElMessage.error('图片加载失败，请稍后重试')
}

// 处理图片加载完成
const handleImageLoad = (message) => {
  console.log('图片加载成功:', message.content)
  console.log('图片消息详情:', {
    id: message.id,
    type: message.type,
    msgType: message.msgType,
    from: message.from,
    senderType: message.senderType
  })
}

// 添加图片URL处理函数
const getImageUrl = (url) => {
  if (!url) return ''
  // 如果已经是完整的URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }
  // 否则拼接MinIO URL
  // return `${import.meta.env.VITE_MINIO_ENDPOINT}/${import.meta.env.VITE_MINIO_BUCKET}/${url}`
}

// 在 script setup 部分添加 getAvatarUrl 函数
const getAvatarUrl = (message) => {
  // 用户头像
  if (message.from === 0 || message.senderType === 0 || message.userId) {
    return userInfo.value?.avatar || ''
  }
  // 客服头像
  if (message.from === 1 || message.senderType === 1 || message.agentId) {
    return message.agentAvatar || ''
  }
  // AI客服头像
  if (message.senderType === 2 || (message.from === 1 && message.currentAgentType === 2)) {
    return '/ai-avatar.png' // 确保在 public 目录下有这个图片
  }
  return ''
}

// 在 script setup 部分添加 handleAvatarError 函数
const handleAvatarError = () => {
  // 阻止默认的占位图片加载
  return true
}

// 添加处理转接通知的方法
const handleTransferNotification = (event) => {
  console.log('收到转接人工客服通知:', event.detail)
  
  // 设置转接状态
  transferRequested.value = true
  
  // 获取通知详情
  const { content, agentId } = event.detail
  
  // 显示消息提示
  ElMessage({
    message: content,
    type: 'success',
    duration: 5000,
    showClose: true,
  })
  
  // 如果当前会话存在，更新状态
  if (currentConversation.value) {
    currentConversation.value.currentAgentType = 1 // 设置为人工客服
    currentConversation.value.transferRequested = 1 // 设置为已请求转接
    
    // 如果有agentId，更新客服ID
    if (agentId) {
      currentConversation.value.agentId = agentId
    }
    
    // 添加系统消息
    chatStore.messages.push({
      id: 'system-' + Date.now(),
      sessionId: currentConversation.value.id,
      content: content,
      type: 'system',
      msgType: 2,
      from: 1,
      senderType: 1,
      createdAt: new Date().toISOString()
    })
    
    // 更新会话信息
    chatStore.refreshCurrentConversation()
  }
  
  // 等待5秒后重置转接状态
  setTimeout(() => {
    transferRequested.value = false
  }, 5000)
}
</script>

<style lang="scss" scoped>
.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  
  .welcome-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    
    .welcome-text {
      h2 {
        color: var(--primary-color);
        margin-bottom: 10px;
      }
      
      p {
        color: var(--regular-text-color);
        margin-bottom: 20px;
      }
      
      .session-status {
        margin: 15px 0;
        padding: 10px;
        background-color: #f0f9ff;
        border-radius: 4px;
        
        p {
          color: #409eff;
          margin-bottom: 10px;
        }
      }
    }
  }
}

.chat-header {
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .chat-title {
    font-size: 16px;
    font-weight: 500;
  }
}

.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  
  .message-item {
    display: flex;
    margin-bottom: 20px;
    
    .message-avatar {
      margin-right: 10px;
    }
    
    .message-content {
      max-width: 70%;
      
      .message-sender {
        margin-bottom: 5px;
        font-size: 13px;
        color: var(--secondary-text-color);
        
        .message-time {
          margin-left: 5px;
          font-size: 12px;
        }
      }
      
      .message-text {
        padding: 10px 15px;
        background-color: #f4f7fa;
        border-radius: 4px;
        word-break: break-word;
        line-height: 1.5;
        white-space: pre-wrap;
        display: inline-block;
      }
      
      .message-image {
        max-width: 300px;
        border-radius: 4px;
        overflow: hidden;
        background-color: #f5f7fa;
        
        .el-image {
          width: 100%;
          max-height: 400px;
          border-radius: 4px;
          
          :deep(.el-image__inner) {
            transition: all 0.3s ease;
          }
          
          :deep(.el-image__placeholder),
          :deep(.el-image__error) {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 150px;
            background-color: #f5f7fa;
            color: #909399;
            
            .el-icon {
              font-size: 24px;
              margin-bottom: 8px;
            }
          }
        }
        
        .image-error,
        .image-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 150px;
          background-color: #f5f7fa;
          color: #909399;
          
          .el-icon {
            font-size: 24px;
            margin-bottom: 8px;
            
            &.is-loading {
              animation: rotating 2s linear infinite;
            }
          }
          
          span {
            font-size: 14px;
          }
        }
      }
      
      .message-system {
        padding: 8px 12px;
        background-color: #f0f9eb;
        color: #67c23a;
        border-radius: 4px;
        font-size: 13px;
        text-align: center;
      }
    }
    
    &-user {
      flex-direction: row-reverse;
      
      .message-avatar {
        margin-right: 0;
        margin-left: 10px;
      }
      
      .message-content {
        .message-sender {
          text-align: right;
        }
        
        .message-text {
          background-color: #ecf5ff;
          color: var(--primary-color);
        }
      }
    }
    
    &-ai {
      .message-content {
        .message-text {
          background-color: #e6f7ff;
          color: #1890ff;
          border-left: 3px solid #1890ff;
        }
      }
    }
  }
  
  .empty-message {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--secondary-text-color);
  }
}

.chat-input {
  padding: 15px 20px;
  border-top: 1px solid var(--border-color-light);
  display: flex;
  align-items: flex-end;
  position: relative; /* 添加相对定位以便常用问题面板定位 */
  
  .input-actions {
    margin-right: 10px;
  }
  
  .input-textarea {
    flex: 1;
  }
  
  .input-send {
    margin-left: 10px;
  }
}

@media (max-width: 768px) {
  .chat-content {
    padding: 15px;
    
    .message-item {
      .message-content {
        max-width: 80%;
        
        .message-image {
          max-width: 200px;
        }
      }
    }
  }
  
  .chat-input {
    padding: 10px 15px;
  }
}

/* 添加AI客服标签样式 */
.agent-tag {
  margin-right: 8px;
}

.message-item {
  /* AI客服消息特殊样式 */
  &[class*="message-item-agent"] {
    .message-content {
      .message-text {
        background-color: #f4f7fa;
      }
    }
  }
  
  &.message-item-user {
    .message-content {
      .message-sender {
        text-align: right;
      }
      
      .message-text {
        background-color: #ecf5ff;
        color: var(--primary-color);
      }
    }
  }
  
  /* 增加AI客服消息样式 */
  &.message-item-ai {
    .message-content {
      .message-text {
        background-color: #e6f7ff;
        color: #1890ff;
        border-left: 3px solid #1890ff;
      }
    }
  }
  
  /* 系统消息样式 */
  &.message-item-system {
    justify-content: center;
    
    .message-avatar {
      display: none;
    }
    
    .message-content {
      max-width: 90%;
      
      .message-sender {
        display: none;
      }
      
      .message-text, .message-system {
        background-color: #f0f9eb;
        color: #67c23a;
        border-radius: 4px;
        padding: 8px 16px;
        font-size: 13px;
        text-align: center;
      }
    }
  }
}

/* 加载中消息样式 */
.message-loading {
  opacity: 0.7;
  .message-text {
    position: relative;
    &:after {
      content: "...";
      animation: loading-dots 1.5s infinite;
    }
  }
}

@keyframes loading-dots {
  0% { content: "."; }
  33% { content: ".."; }
  66% { content: "..."; }
}

.chat-closed-tip {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 5px;
  text-align: center;
  background-color: #fef0f0;
  padding: 5px;
  border-radius: 4px;
}

/* 常用问题样式 */
.common-questions {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background-color: white;
  border: 1px solid var(--border-color-light);
  border-radius: 4px;
  margin: 0 20px 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  max-height: 300px;
  overflow-y: auto;
  z-index: 100;
  
  .question-title {
    padding: 8px 12px;
    border-bottom: 1px solid var(--border-color-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
  }
  
  .question-list {
    padding: 8px 0;
    
    .question-item {
      padding: 8px 12px;
      cursor: pointer;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: #f0f9ff;
      }
    }
  }
}

/* 历史会话对话框样式 */
.el-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }
  
  :deep(.el-table) {
    border-radius: 4px;
    overflow: hidden;
    
    .el-table__header {
      background-color: #f5f7fa;
    }
    
    .el-button {
      padding: 4px 0;
    }
  }
}

/* 会话状态标签样式 */
.el-tag {
  &:deep(.el-tag--warning) {
    background-color: #fdf6ec;
    border-color: #faecd8;
    color: #e6a23c;
  }
  
  &:deep(.el-tag--success) {
    background-color: #f0f9eb;
    border-color: #e1f3d8;
    color: #67c23a;
  }
  
  &:deep(.el-tag--primary) {
    background-color: #ecf5ff;
    border-color: #d9ecff;
    color: #409eff;
  }
  
  &:deep(.el-tag--info) {
    background-color: #f4f4f5;
    border-color: #e9e9eb;
    color: #909399;
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 