<template>
  <div class="evaluation-container">
    <div class="page-header">
      <h2>客服评价</h2>
      <p class="sub-title">您的反馈将帮助我们提升服务质量</p>
    </div>
    
    <div class="evaluation-content">
      <el-card v-if="!currentSession" class="empty-state">
        <el-empty description="暂无待评价的会话" />
      </el-card>
      
      <el-card v-else class="evaluation-form-card">
        <div class="session-info">
          <div class="info-item">
            <span class="label">会话时间：</span>
            <span class="value">{{ formatDateTime(currentSession.createdAt) }}</span>
          </div>
          <div class="info-item">
            <span class="label">客服名称：</span>
            <span class="value">{{ currentSession.agentName || '客服人员' }}</span>
          </div>
        </div>
        
        <el-divider content-position="center">评价表单</el-divider>
        
        <el-form 
          ref="formRef" 
          :model="evaluationForm" 
          :rules="rules" 
          label-width="100px"
          class="evaluation-form"
        >
          <el-form-item label="服务满意度" prop="satisfaction">
            <el-rate
              v-model="evaluationForm.satisfaction"
              :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
              :texts="['很差', '一般', '满意', '很满意', '非常满意']"
              show-text
            />
          </el-form-item>
          
          <el-form-item label="问题解决度" prop="resolution">
            <el-radio-group v-model="evaluationForm.resolution">
              <el-radio :label="1">完全解决</el-radio>
              <el-radio :label="2">部分解决</el-radio>
              <el-radio :label="3">未解决</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="响应速度" prop="responseSpeed">
            <el-rate 
              v-model="evaluationForm.responseSpeed"
              :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
            />
          </el-form-item>
          
          <el-form-item label="专业程度" prop="professionalism">
            <el-rate 
              v-model="evaluationForm.professionalism"
              :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
            />
          </el-form-item>
          
          <el-form-item label="服务态度" prop="attitude">
            <el-rate 
              v-model="evaluationForm.attitude"
              :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
            />
          </el-form-item>
          
          <el-form-item label="评价内容" prop="comment">
            <el-input
              v-model="evaluationForm.comment"
              type="textarea"
              :rows="4"
              placeholder="请输入您的评价或建议（选填）"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="submitForm">提交评价</el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
    
    <div class="history-evaluations">
      <h3>历史评价</h3>
      
      <el-table
        v-loading="loading"
        :data="evaluationHistory"
        style="width: 100%"
        border
        stripe
      >
        <el-table-column prop="sessionId" label="会话ID" width="100" />
        <el-table-column label="会话时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.sessionTime) }}
          </template>
        </el-table-column>
        <el-table-column label="客服" width="120">
          <template #default="scope">
            {{ scope.row.agentName || '客服人员' }}
          </template>
        </el-table-column>
        <el-table-column label="满意度" width="150">
          <template #default="scope">
            <el-rate
              v-model="scope.row.satisfaction"
              disabled
              text-color="#ff9900"
            />
          </template>
        </el-table-column>
        <el-table-column label="问题解决度" width="120">
          <template #default="scope">
            <el-tag :type="getResolutionTagType(scope.row.resolution)">
              {{ getResolutionText(scope.row.resolution) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="comment" label="评价内容" show-overflow-tooltip />
        <el-table-column label="评价时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.createdAt) }}
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[5, 10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import moment from 'moment'
import { useUserStore } from '@/store/user'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 当前会话
const currentSession = ref(null)

// 表单数据
const formRef = ref(null)
const evaluationForm = reactive({
  sessionId: '',
  satisfaction: 5,
  resolution: 1,
  responseSpeed: 5,
  professionalism: 5,
  attitude: 5,
  comment: ''
})

// 表单校验规则
const rules = {
  satisfaction: [
    { required: true, message: '请选择满意度', trigger: 'change' }
  ],
  resolution: [
    { required: true, message: '请选择问题解决度', trigger: 'change' }
  ],
  responseSpeed: [
    { required: true, message: '请评价响应速度', trigger: 'change' }
  ],
  professionalism: [
    { required: true, message: '请评价专业程度', trigger: 'change' }
  ],
  attitude: [
    { required: true, message: '请评价服务态度', trigger: 'change' }
  ]
}

// 历史评价数据
const loading = ref(false)
const evaluationHistory = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 生命周期钩子
onMounted(() => {
  // 从localStorage恢复状态
  userStore.loadFromStorage()
  
  // 检查登录状态和用户角色
  if (!userStore.isLoggedIn || !userStore.isUser) {
    router.push('/login')
    return
  }
  
  // 从路由参数中获取会话ID
  const sessionId = route.query.id
  
  if (sessionId) {
    // 根据ID获取会话信息
    fetchSessionInfo(sessionId)
  }
  
  // 加载历史评价
  loadEvaluationHistory()
})

// 获取会话信息
const fetchSessionInfo = async (sessionId) => {
  try {
    // TODO: 调用实际API获取会话信息
    // const res = await getSessionInfo(sessionId)
    // if (res.code === 200) {
    //   currentSession.value = res.data
    //   evaluationForm.sessionId = sessionId
    // }
    
    // 模拟API响应
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    currentSession.value = {
      id: sessionId,
      agentId: 'A1001',
      agentName: '客服小王',
      createdAt: new Date(Date.now() - Math.random() * 86400000).toISOString(), // 最近24小时内
      endedAt: new Date().toISOString(),
      status: 0 // 已结束
    }
    
    evaluationForm.sessionId = sessionId
  } catch (error) {
    console.error('获取会话信息失败', error)
    ElMessage.error('获取会话信息失败')
  }
}

// 加载历史评价
const loadEvaluationHistory = async () => {
  loading.value = true
  try {
    // TODO: 调用实际API获取历史评价
    // const res = await getEvaluationHistory({
    //   pageNum: currentPage.value,
    //   pageSize: pageSize.value
    // })
    // if (res.code === 200) {
    //   evaluationHistory.value = res.data.items
    //   total.value = res.data.total
    // }
    
    // 模拟API响应
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 模拟数据
    const mockData = []
    const mockTotal = 25
    
    const displayedCount = Math.min(pageSize.value, mockTotal - (currentPage.value - 1) * pageSize.value)
    
    for (let i = 0; i < displayedCount; i++) {
      const index = (currentPage.value - 1) * pageSize.value + i
      const createdAt = new Date(Date.now() - index * 86400000 * 2 - Math.random() * 86400000).toISOString()
      const sessionTime = new Date(Date.parse(createdAt) - Math.random() * 3600000).toISOString()
      
      mockData.push({
        id: `E${10000 + index}`,
        sessionId: `S${20000 + index}`,
        userId: 'U1001',
        agentId: `A${1000 + Math.floor(Math.random() * 10)}`,
        agentName: ['客服小王', '客服小李', '客服小张'][Math.floor(Math.random() * 3)],
        satisfaction: Math.floor(Math.random() * 3) + 3, // 3-5分
        resolution: Math.floor(Math.random() * 3) + 1, // 1-3
        responseSpeed: Math.floor(Math.random() * 2) + 4, // 4-5分
        professionalism: Math.floor(Math.random() * 2) + 4, // 4-5分
        attitude: Math.floor(Math.random() * 2) + 4, // 4-5分
        comment: [
          '服务很好，问题得到解决',
          '客服很专业，解决了我的问题',
          '回复速度很快，态度很好',
          '非常满意，谢谢客服的帮助',
          ''
        ][Math.floor(Math.random() * 5)],
        sessionTime: sessionTime,
        createdAt: createdAt
      })
    }
    
    evaluationHistory.value = mockData
    total.value = mockTotal
  } catch (error) {
    console.error('加载历史评价失败', error)
    ElMessage.error('加载历史评价失败')
  } finally {
    loading.value = false
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // TODO: 调用实际API提交评价
    // const res = await submitEvaluation(evaluationForm)
    // if (res.code === 200) {
    //   ElMessage.success('评价提交成功')
    // }
    
    // 模拟API响应
    await new Promise(resolve => setTimeout(resolve, 500))
    ElMessage.success('评价提交成功')
    
    // 重置表单
    resetForm()
    
    // 重新加载历史评价
    currentPage.value = 1
    loadEvaluationHistory()
    
    // 清除当前会话
    currentSession.value = null
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  loadEvaluationHistory()
}

// 处理当前页变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  loadEvaluationHistory()
}

// 获取问题解决度标签类型
const getResolutionTagType = (resolution) => {
  switch (resolution) {
    case 1: return 'success'
    case 2: return 'warning'
    case 3: return 'danger'
    default: return 'info'
  }
}

// 获取问题解决度文本
const getResolutionText = (resolution) => {
  switch (resolution) {
    case 1: return '完全解决'
    case 2: return '部分解决'
    case 3: return '未解决'
    default: return '未知'
  }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return moment(dateTime).format('YYYY-MM-DD HH:mm:ss')
}
</script>

<style lang="scss" scoped>
.evaluation-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0 0 10px 0;
      font-size: 24px;
      color: var(--el-text-color-primary);
    }
    
    .sub-title {
      margin: 0;
      color: var(--el-text-color-secondary);
      font-size: 14px;
    }
  }
  
  .evaluation-content {
    margin-bottom: 30px;
    
    .empty-state {
      min-height: 200px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    .evaluation-form-card {
      .session-info {
        margin-bottom: 20px;
        
        .info-item {
          margin-bottom: 10px;
          
          .label {
            font-weight: bold;
            color: var(--el-text-color-secondary);
            margin-right: 10px;
          }
          
          .value {
            color: var(--el-text-color-primary);
          }
        }
      }
      
      .evaluation-form {
        max-width: 600px;
        margin: 0 auto;
      }
    }
  }
  
  .history-evaluations {
    h3 {
      margin-top: 0;
      margin-bottom: 20px;
      font-size: 18px;
      color: var(--el-text-color-primary);
    }
    
    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style> 