<template>
  <div class="user-layout">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="logo">
        <h1>客服系统</h1>
      </div>
      
      <div class="right-menu">
        <el-dropdown trigger="click" @command="handleCommand">
          <div class="avatar-container">
            <el-avatar :size="30" :src="userInfo?.avatar || defaultAvatar">{{ userInfo?.nickname?.substring(0, 1) || '用' }}</el-avatar>
            <span class="nickname">{{ userInfo?.nickname || '用户' }}</span>
            <el-icon><CaretBottom /></el-icon>
          </div>
          
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人中心</el-dropdown-item>
              <el-dropdown-item command="history">历史会话</el-dropdown-item>
              <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>
    
    <!-- 主体内容 -->
    <main class="main-content">
      <router-view />
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { CaretBottom } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/user'
import { useChatStore } from '@/store/chat'

// 默认头像
const defaultAvatar = '/default-avatar.png'

// 路由实例
const router = useRouter()

// 用户信息store
const userStore = useUserStore()
const chatStore = useChatStore()

// 计算属性：用户信息
const userInfo = computed(() => userStore.userInfo)

// 生命周期钩子
onMounted(() => {
  // 从localStorage恢复状态
  userStore.loadFromStorage()
  
  // 如果没有登录，跳转到登录页
  if (!userStore.isLoggedIn) {
    router.push('/login')
    return
  }
  
  // 如果不是用户角色，跳转到登录页
  if (!userStore.isUser) {
    router.push('/login')
    return
  }
  
  // 初始化WebSocket连接
  chatStore.initWebSocket()
})

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/user/profile')
      break
    case 'history':
      router.push('/user/history')
      break
    case 'logout':
      confirmLogout()
      break
  }
}

// 确认退出登录
const confirmLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 清理聊天状态
    chatStore.clearState()
    
    // 退出登录
    userStore.logout()
    
    // 跳转到登录页
    router.push('/login')
  }).catch(() => {})
}
</script>

<style lang="scss" scoped>
.user-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  height: 60px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  position: relative;
  z-index: 10;
  
  .logo {
    h1 {
      margin: 0;
      color: var(--primary-color);
      font-size: 18px;
    }
  }
  
  .right-menu {
    display: flex;
    align-items: center;
    
    .avatar-container {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 5px;
      border-radius: 4px;
      
      &:hover {
        background-color: #f6f6f6;
      }
      
      .nickname {
        margin: 0 8px;
        font-size: 14px;
      }
    }
  }
}

.main-content {
  flex: 1;
  overflow: hidden;
  background-color: var(--background-color);
}

@media (max-width: 768px) {
  .header {
    padding: 0 15px;
  }
  
  .right-menu {
    .avatar-container {
      .nickname {
        display: none;
      }
    }
  }
}
</style> 