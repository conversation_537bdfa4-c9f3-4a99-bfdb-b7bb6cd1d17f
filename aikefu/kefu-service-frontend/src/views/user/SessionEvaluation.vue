<template>
  <div class="evaluation-container">
    <el-card class="evaluation-card">
      <template #header>
        <div class="evaluation-header">
          <span>会话评价</span>
          <el-tag type="info" v-if="evaluated">已评价</el-tag>
        </div>
      </template>
      
      <div class="evaluation-body" v-if="!evaluated">
        <div class="evaluation-item">
          <div class="item-label">服务满意度</div>
          <div class="item-content">
            <el-rate
              v-model="satisfactionLevel"
              :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
              :texts="['很不满意', '不满意', '一般', '满意', '非常满意']"
              show-text
            />
          </div>
        </div>
        
        <div class="evaluation-item">
          <div class="item-label">问题是否解决</div>
          <div class="item-content">
            <el-radio-group v-model="isSolved">
              <el-radio :label="1">已解决</el-radio>
              <el-radio :label="0">未解决</el-radio>
            </el-radio-group>
          </div>
        </div>
        
        <div class="evaluation-item">
          <div class="item-label">反馈意见</div>
          <div class="item-content">
            <el-input
              v-model="feedbackContent"
              type="textarea"
              :rows="3"
              placeholder="请输入您的反馈意见（选填）"
              resize="none"
            />
          </div>
        </div>
        
        <div class="evaluation-item">
          <div class="item-label">您对我们的建议</div>
          <div class="item-content">
            <el-input
              v-model="userSuggestion"
              type="textarea"
              :rows="3"
              placeholder="请输入对我们服务的建议（选填）"
              resize="none"
            />
          </div>
        </div>
        
        <div class="evaluation-actions">
          <el-button type="primary" @click="submitEvaluation" :disabled="!canSubmit">提交评价</el-button>
          <el-button @click="skipEvaluation">暂不评价</el-button>
        </div>
      </div>
      
      <div class="evaluation-body" v-else>
        <el-result
          icon="success"
          title="感谢您的评价"
          sub-title="您的反馈是我们持续改进的动力"
        >
          <template #extra>
            <el-button type="primary" @click="startNewChat">开始新会话</el-button>
          </template>
        </el-result>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { evaluateSession } from '@/api/chat'

const props = defineProps({
  sessionId: {
    type: [Number, String],
    required: true
  }
})

const emit = defineEmits(['evaluationComplete', 'skipEvaluation', 'startNewChat'])

// 评价数据
const satisfactionLevel = ref(5)  // 默认5星
const isSolved = ref(1)  // 默认已解决
const feedbackContent = ref('')
const userSuggestion = ref('')
const evaluated = ref(false)
const evaluating = ref(false)

// 是否可以提交
const canSubmit = computed(() => {
  return satisfactionLevel.value > 0 && !evaluating.value
})

// 提交评价
const submitEvaluation = async () => {
  if (!canSubmit.value) return
  
  evaluating.value = true
  
  try {
    const response = await evaluateSession(props.sessionId, {
      satisfactionLevel: satisfactionLevel.value,
      feedbackContent: feedbackContent.value,
      userSuggestion: userSuggestion.value,
      isSolved: isSolved.value
    })
    
    if (response.code === 200) {
      ElMessage.success('评价提交成功，感谢您的反馈')
      evaluated.value = true
      emit('evaluationComplete')
    } else {
      ElMessage.error(response.message || '评价提交失败')
    }
  } catch (error) {
    console.error('提交评价失败', error)
    ElMessage.error('评价提交失败，请稍后重试')
  } finally {
    evaluating.value = false
  }
}

// 跳过评价
const skipEvaluation = () => {
  emit('skipEvaluation')
}

// 开始新会话
const startNewChat = () => {
  emit('startNewChat')
}
</script>

<style scoped>
.evaluation-container {
  max-width: 600px;
  margin: 20px auto;
}

.evaluation-card {
  border-radius: 8px;
}

.evaluation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

.evaluation-body {
  padding: 10px 0;
}

.evaluation-item {
  margin-bottom: 20px;
}

.item-label {
  font-weight: 500;
  margin-bottom: 8px;
  color: #606266;
}

.item-content {
  padding-left: 10px;
}

.evaluation-actions {
  margin-top: 30px;
  display: flex;
  justify-content: center;
  gap: 16px;
}
</style> 