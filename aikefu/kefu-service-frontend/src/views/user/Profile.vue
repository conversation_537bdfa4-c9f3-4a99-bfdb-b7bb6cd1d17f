<template>
  <div class="profile-container">
    <el-card class="profile-card">
      <template #header>
        <div class="card-header">
          <h3>个人资料</h3>
          <el-button type="primary" @click="handleEdit" v-if="!isEditing">编辑</el-button>
          <div v-else>
            <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
            <el-button @click="cancelEdit">取消</el-button>
          </div>
        </div>
      </template>
      
      <div class="profile-content">
        <div class="avatar-container">
          <el-avatar :size="100" :src="form.avatar || defaultAvatar">
            {{ form.nickname ? form.nickname.substring(0, 1) : '用' }}
          </el-avatar>
          <el-upload
            v-if="isEditing"
            class="avatar-upload"
            action=""
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleAvatarChange"
            accept="image/*"
          >
            <el-button size="small" type="primary">更换头像</el-button>
          </el-upload>
        </div>
        
        <el-form 
          ref="profileForm"
          :model="form"
          :rules="rules"
          label-width="80px"
          :disabled="!isEditing"
        >
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="form.phone" disabled />
          </el-form-item>
          
          <el-form-item label="昵称" prop="nickname">
            <el-input v-model="form.nickname" placeholder="请输入昵称" />
          </el-form-item>
          
          <el-form-item label="性别" prop="gender">
            <el-select v-model="form.gender" placeholder="请选择性别" style="width: 100%">
              <el-option label="男" :value="1" />
              <el-option label="女" :value="2" />
              <el-option label="保密" :value="0" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="form.email" placeholder="请输入邮箱" />
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    
    <el-card class="security-card">
      <template #header>
        <div class="card-header">
          <h3>安全设置</h3>
        </div>
      </template>
      
      <div class="security-content">
        <div class="security-item">
          <div class="security-info">
            <div class="security-title">账户密码</div>
            <div class="security-desc">用于保护账户信息安全</div>
          </div>
          <el-button type="primary" plain @click="showPasswordDialog">修改密码</el-button>
        </div>
      </div>
    </el-card>
    
    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="passwordDialogVisible"
      title="修改密码"
      width="400px"
    >
      <el-form 
        ref="passwordForm"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="当前密码" prop="oldPassword">
          <el-input 
            v-model="passwordForm.oldPassword"
            type="password"
            show-password
            placeholder="请输入当前密码"
          />
        </el-form-item>
        
        <el-form-item label="新密码" prop="newPassword">
          <el-input 
            v-model="passwordForm.newPassword"
            type="password"
            show-password
            placeholder="请输入新密码"
          />
        </el-form-item>
        
        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input 
            v-model="passwordForm.confirmPassword"
            type="password"
            show-password
            placeholder="请再次输入新密码"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="changePassword" :loading="changingPassword">确认修改</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/store/user'
import { updateUser } from '@/api/user'
import { useRouter } from 'vue-router'

// 默认头像
const defaultAvatar = '/default-avatar.png'

// 用户store
const userStore = useUserStore()

// 计算属性：用户信息
const userInfo = computed(() => userStore.userInfo)

// 是否处于编辑状态
const isEditing = ref(false)

// 表单数据和备份
const initialForm = ref({})
const form = reactive({
  id: '',
  phone: '',
  nickname: '',
  avatar: '',
  gender: 0,
  email: ''
})

// 表单校验规则
const rules = {
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 10, message: '昵称长度应为2-10个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 密码表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码验证器
const validatePass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入新密码'))
  } else {
    if (passwordForm.confirmPassword !== '') {
      passwordForm$.value?.validateField('confirmPassword')
    }
    callback()
  }
}

// 确认密码验证器
const validateConfirmPass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入新密码'))
  } else if (value !== passwordForm.newPassword) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

// 密码表单校验规则
const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, validator: validatePass, trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应为6-20位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPass, trigger: 'blur' }
  ]
}

// 表单引用
const profileForm = ref(null)
const passwordForm$ = ref(null)

// 状态变量
const saving = ref(false)
const passwordDialogVisible = ref(false)
const changingPassword = ref(false)

// 路由
const router = useRouter()

// 生命周期钩子
onMounted(() => {
  // 从localStorage恢复状态
  userStore.loadFromStorage()
  
  // 检查登录状态和用户角色
  if (!userStore.isLoggedIn || !userStore.isUser) {
    router.push('/login')
    return
  }
  
  // 初始化表单数据
  initFormData()
})

// 初始化表单数据
const initFormData = () => {
  if (!userInfo.value) return
  
  Object.assign(form, {
    id: userInfo.value.id,
    phone: userInfo.value.phone,
    nickname: userInfo.value.nickname || '',
    avatar: userInfo.value.avatar || '',
    gender: userInfo.value.gender || 0,
    email: userInfo.value.email || ''
  })
  
  // 备份初始表单数据
  initialForm.value = JSON.parse(JSON.stringify(form))
}

// 处理编辑按钮点击
const handleEdit = () => {
  isEditing.value = true
}

// 取消编辑
const cancelEdit = () => {
  ElMessageBox.confirm('确定要取消编辑吗？未保存的修改将会丢失', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 恢复初始数据
    Object.assign(form, initialForm.value)
    isEditing.value = false
  }).catch(() => {})
}

// 保存个人资料
const handleSave = async () => {
  if (!profileForm.value) return
  
  await profileForm.value.validate(async valid => {
    if (!valid) return
    
    saving.value = true
    
    try {
      // 构建更新数据
      const updateData = {
        nickname: form.nickname,
        gender: form.gender,
        email: form.email,
        avatar: form.avatar
      }
      
      // 调用API更新用户信息
      const res = await updateUser(form.id, updateData)
      
      if (res.code === 200) {
        ElMessage.success('保存成功')
        
        // 更新Store中的用户信息
        userStore.setUserInfo(res.data)
        
        // 更新备份数据
        initialForm.value = JSON.parse(JSON.stringify(form))
        
        // 退出编辑模式
        isEditing.value = false
      } else {
        ElMessage.error(res.message || '保存失败')
      }
    } catch (error) {
      console.error('保存个人资料失败', error)
      ElMessage.error('保存失败，请稍后重试')
    } finally {
      saving.value = false
    }
  })
}

// 显示修改密码对话框
const showPasswordDialog = () => {
  // 重置密码表单
  passwordForm.oldPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  
  passwordDialogVisible.value = true
}

// 修改密码
const changePassword = async () => {
  if (!passwordForm$.value) return
  
  await passwordForm$.value.validate(async valid => {
    if (!valid) return
    
    changingPassword.value = true
    
    try {
      // TODO: 调用修改密码API
      // const res = await changePassword({
      //   userId: form.id,
      //   oldPassword: passwordForm.oldPassword,
      //   newPassword: passwordForm.newPassword
      // })
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      ElMessage.success('密码修改成功')
      
      // 关闭对话框
      passwordDialogVisible.value = false
    } catch (error) {
      console.error('修改密码失败', error)
      ElMessage.error('修改密码失败，请稍后重试')
    } finally {
      changingPassword.value = false
    }
  })
}

// 处理头像更改
const handleAvatarChange = (file) => {
  if (!file || !file.raw) return
  
  // 校验文件类型和大小
  const isImage = file.raw.type.startsWith('image/')
  const isLt2M = file.raw.size / 1024 / 1024 < 2
  
  if (!isImage) {
    ElMessage.error('只能上传图片文件')
    return
  }
  
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB')
    return
  }
  
  // 创建FormData上传头像
  const formData = new FormData()
  formData.append('file', file.raw)
  formData.append('userId', form.id)
  
  // TODO: 调用上传头像API
  // 模拟上传成功
  const reader = new FileReader()
  reader.readAsDataURL(file.raw)
  reader.onload = () => {
    form.avatar = reader.result
  }
}
</script>

<style lang="scss" scoped>
.profile-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.profile-card, .security-card {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 18px;
    color: var(--primary-text-color);
  }
}

.profile-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  
  .avatar-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
  }
  
  .el-form {
    width: 100%;
    max-width: 400px;
  }
}

.security-content {
  .security-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color-lighter);
    
    &:last-child {
      border-bottom: none;
    }
    
    .security-info {
      .security-title {
        font-size: 16px;
        color: var(--primary-text-color);
        margin-bottom: 5px;
      }
      
      .security-desc {
        font-size: 14px;
        color: var(--secondary-text-color);
      }
    }
  }
}

@media (max-width: 768px) {
  .profile-container {
    padding: 10px;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style> 