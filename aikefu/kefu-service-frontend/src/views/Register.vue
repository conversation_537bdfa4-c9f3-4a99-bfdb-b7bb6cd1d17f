<template>
  <div class="register-container">
    <div class="register-box card-shadow rounded">
      <h2 class="title">用户注册</h2>
      
      <el-form ref="registerForm" :model="form" :rules="rules" label-width="0" status-icon>
        <el-form-item prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号">
            <template #prefix>
              <el-icon><Phone /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item prop="nickname">
          <el-input v-model="form.nickname" placeholder="请输入昵称">
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input v-model="form.password" placeholder="请设置密码" type="password" show-password>
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item prop="confirmPassword">
          <el-input v-model="form.confirmPassword" placeholder="请确认密码" type="password" show-password>
            <template #prefix>
              <el-icon><Key /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :loading="loading" class="register-button" @click="handleRegister">注册</el-button>
        </el-form-item>
        
        <div class="form-footer">
          <span>已有账号？</span>
          <router-link to="/login" class="login-link">立即登录</router-link>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Phone, Lock, User, Key } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/user'

// 路由
const router = useRouter()

// 用户store
const userStore = useUserStore()

// 加载状态
const loading = ref(false)

// 注册表单
const form = reactive({
  phone: '',
  nickname: '',
  password: '',
  confirmPassword: ''
})

// 密码验证器
const validatePass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else {
    if (form.confirmPassword !== '') {
      registerForm.value?.validateField('confirmPassword')
    }
    callback()
  }
}

// 确认密码验证器
const validateConfirmPass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== form.password) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

// 表单校验规则
const rules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 10, message: '昵称长度应为2-10个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, validator: validatePass, trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应为6-20位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPass, trigger: 'blur' }
  ]
}

// 表单引用
const registerForm = ref(null)

// 处理注册
const handleRegister = async () => {
  if (!registerForm.value) return
  
  await registerForm.value.validate(async valid => {
    if (!valid) return
    
    loading.value = true
    
    try {
      // 移除确认密码字段
      const { confirmPassword, ...registerData } = form
      
      // 调用注册API
      const res = await userStore.register(registerData)
      
      ElMessage({
        type: 'success',
        message: '注册成功，即将跳转到登录页'
      })
      
      // 跳转到登录页
      setTimeout(() => {
        router.push('/login')
      }, 1500)
    } catch (error) {
      ElMessage.error(error.message || '注册失败，请稍后重试')
    } finally {
      loading.value = false
    }
  })
}
</script>

<style lang="scss" scoped>
.register-container {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.register-box {
  width: 380px;
  padding: 30px;
  background-color: white;
  
  .title {
    font-size: 24px;
    text-align: center;
    margin-bottom: 30px;
    color: var(--primary-color);
  }
  
  .register-button {
    width: 100%;
    margin-top: 10px;
  }
  
  .form-footer {
    margin-top: 20px;
    text-align: center;
    
    .login-link {
      margin-left: 5px;
      color: var(--primary-color);
    }
  }
}

@media (max-width: 480px) {
  .register-box {
    width: 90%;
    max-width: 380px;
    padding: 20px;
  }
}
</style> 