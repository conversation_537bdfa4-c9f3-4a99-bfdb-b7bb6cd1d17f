/* 新消息高亮样式 */
.highlight-new-message {
  animation: highlight-flash 2s ease-in-out;
  position: relative;
}

@keyframes highlight-flash {
  0% {
    background-color: rgba(0, 123, 255, 0.1);
  }
  50% {
    background-color: rgba(0, 123, 255, 0.2);
  }
  100% {
    background-color: transparent;
  }
}

.highlight-new-message::after {
  content: "";
  position: absolute;
  top: 0;
  right: 10px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #f56c6c;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.8;
  }
}

/* 轮询模式指示器 */
.polling-mode-indicator {
  display: inline-block;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: #e6a23c;
  color: white;
  margin-left: 5px;
}

.websocket-mode-indicator {
  display: inline-block;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: #67c23a;
  color: white;
  margin-left: 5px;
} 