import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import './styles/main.scss'
import { initUserActivityMonitor } from './utils/userActivity'

const app = createApp(App)
const pinia = createPinia()

app.use(router)
app.use(pinia)
app.use(ElementPlus)

// 初始化用户活动监测
initUserActivityMonitor()

// 初始化WebSocket连接（如果用户已登录）
import { useUserStore } from './store/user'
import { useChatStore } from './store/chat'

// 应用挂载后初始化WebSocket
app.mount('#app')

// 从localStorage恢复用户状态并初始化WebSocket连接
const userStore = useUserStore(pinia)
const chatStore = useChatStore(pinia)

// 加载用户信息
userStore.loadFromStorage()

// 如果用户已登录，则立即初始化WebSocket连接
if (userStore.isLoggedIn && userStore.token) {
  console.log('用户已登录，初始化全局WebSocket连接')
  chatStore.initWebSocket()
  
  // 设置WebSocket断开后的重连逻辑
  // 当WebSocket断开连接后，立即启动HTTP轮询，并每5秒尝试重新连接WebSocket
  // 这部分逻辑已经在chat.js的startPolling方法中实现
  
  // 检查并监控连接状态
  const checkConnectionStatus = () => {
    if (!chatStore.connected && !chatStore.isPolling) {
      console.log('检测到既无WebSocket连接也无HTTP轮询，启动连接...')
      chatStore.initWebSocket()
      
      // 如果WebSocket连接失败，则启动HTTP轮询
      setTimeout(() => {
        if (!chatStore.connected) {
          console.log('WebSocket连接失败，启动HTTP轮询')
          chatStore.startPolling()
        }
      }, 3000)
    }
  }
  
  // 每30秒检查一次连接状态，确保至少有一种连接方式在工作
  setInterval(checkConnectionStatus, 30000)
}

// 导出Vue实例，方便其他地方使用
export { app, pinia } 