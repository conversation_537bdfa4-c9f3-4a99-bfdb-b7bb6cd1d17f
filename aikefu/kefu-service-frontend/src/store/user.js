import { defineStore } from 'pinia'
import { login as userLogin, register as userRegister, getUserInfo } from '@/api/user'
import { login as agentLogin, ssoLogin, getAgentInfo, updateAgentStatus, updateAgentStatusBulk, checkIsAdmin } from '@/api/agent'
import { useChatStore } from '@/store/chat'

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null,
    token: null,
    role: '' // 'user', 'agent' 或 'admin'
  }),
  
  getters: {
    isLoggedIn: (state) => !!state.token,
    isUser: (state) => state.role === 'user',
    isAgent: (state) => state.role === 'agent' || state.role === 'admin',
    isAdmin: (state) => state.role === 'admin',
    userId: (state) => state.userInfo?.id,
    agentId: (state) => state.userInfo?.id,
    userName: (state) => {
      if (state.role === 'user') {
        return state.userInfo?.nickname || state.userInfo?.phone || '用户'
      } else if (state.role === 'agent' || state.role === 'admin') {
        return state.userInfo?.name || state.userInfo?.agentNo || '客服'
      }
      return '未登录'
    }
  },
  
  actions: {
    setUserInfo(info) {
      this.userInfo = info
      // 根据登录信息类型设置角色
      if (info?.agentNo) {
        // 默认设置为客服角色
        this.role = 'agent'
      } else if (info?.phone) {
        this.role = 'user'
      }
      localStorage.setItem('userInfo', JSON.stringify(info))
      localStorage.setItem('role', this.role)
    },
    
    setToken(token) {
      this.token = token
      localStorage.setItem('token', token)
    },
    
    setRole(role) {
      this.role = role
      localStorage.setItem('role', role)
    },
    
    loadFromStorage() {
      try {
        const userInfo = localStorage.getItem('userInfo')
        const token = localStorage.getItem('token')
        const role = localStorage.getItem('role')
        
        if (userInfo) {
          this.userInfo = JSON.parse(userInfo)
        }
        if (token) {
          this.token = token
        }
        if (role) {
          this.role = role
        }
      } catch (error) {
        console.error('从localStorage加载状态失败:', error)
      }
    },
    
    // 客服登录
    async agentLogin(agentInfo) {
      try {
        const res = await agentLogin(agentInfo)
        if (res.code === 200) {
          const { agent, token } = res.data
          this.setUserInfo(agent)
          this.setToken(token)
          
          // 判断客服类型，如果是系统管理员(agentType=3)，设置为admin角色
          if (agent.agentType === 3) {
            this.role = 'admin'
            localStorage.setItem('role', 'admin')
          } else {
            // 普通客服
            this.role = 'agent'
            localStorage.setItem('role', 'agent')
          }
          
          // 登录成功后立即初始化WebSocket连接
          const chatStore = useChatStore()
          chatStore.initWebSocket()
          
          return Promise.resolve(res)
        }
        return Promise.reject(res)
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // SSO登录
    async ssoLogin(token) {
      try {
        const res = await ssoLogin({ token })
        if (res.code === 200) {
          const { agent, token: jwtToken } = res.data
          this.setUserInfo(agent)
          this.setToken(jwtToken)
          
          // 判断客服类型，如果是系统管理员(agentType=3)，设置为admin角色
          if (agent.agentType === 3) {
            this.role = 'admin'
            localStorage.setItem('role', 'admin')
          } else {
            // 普通客服
            this.role = 'agent'
            localStorage.setItem('role', 'agent')
          }
          
          // 登录成功后初始化WebSocket连接
          const chatStore = useChatStore()
          chatStore.initWebSocket()
          
          return Promise.resolve(res)
        }
        return Promise.reject(res)
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 检查是否为系统管理员
    async checkIsAdmin() {
      if (!this.userInfo?.id) return false
      
      try {
        const res = await checkIsAdmin(this.userInfo.id)
        if (res.code === 200 && res.data === true) {
          this.role = 'admin'
          localStorage.setItem('role', 'admin')
          return true
        }
        return false
      } catch (error) {
        console.error('检查管理员权限失败:', error)
        return false
      }
    },
    
    // 用户登录
    async userLogin(userInfo) {
      try {
        const res = await userLogin(userInfo)
        if (res.code === 200) {
          const { user, token } = res.data
          this.setUserInfo(user)
          this.setToken(token)
          
          // 登录成功后立即初始化WebSocket连接
          const chatStore = useChatStore()
          chatStore.initWebSocket()
          
          return Promise.resolve(res)
        }
        return Promise.reject(res)
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 用户注册
    async register(userInfo) {
      try {
        const res = await userRegister(userInfo)
        return Promise.resolve(res)
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 获取用户信息
    async getUserInfo(id) {
      try {
        const res = await getUserInfo(id)
        if (res.code === 200) {
          // 新的返回格式是数组，第一个元素是用户信息
          if (Array.isArray(res.data)) {
            this.setUserInfo(res.data[0])
          } else {
            // 兼容旧格式
            this.setUserInfo(res.data)
          }
        }
        return Promise.resolve(res)
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 获取客服信息
    async getAgentInfo(id) {
      try {
        const res = await getAgentInfo(id)
        if (res.code === 200) {
          this.setUserInfo(res.data)
        }
        return Promise.resolve(res)
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 客服上下线（使用原API，路径方式）
    async updateAgentStatus(id, status) {
      try {
        const res = await updateAgentStatus(id, status)
        if (res.code === 200) {
          // 更新状态
          this.userInfo.status = status
          // 更新localStorage
          localStorage.setItem('userInfo', JSON.stringify(this.userInfo))
        }
        return Promise.resolve(res)
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 客服上下线（使用新API，数据方式）
    async updateAgentStatusBulk(id, status) {
      try {
        const res = await updateAgentStatusBulk(id, status)
        if (res.code === 200) {
          // 更新状态
          this.userInfo.status = status
          // 更新localStorage
          localStorage.setItem('userInfo', JSON.stringify(this.userInfo))
        }
        return Promise.resolve(res)
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 登出
    logout() {
      // 如果是客服，先更新状态为离线
      if (this.role === 'agent' && this.userInfo?.id) {
        this.updateAgentStatus(this.userInfo.id, 0).catch(error => {
          console.error('Failed to update agent status:', error)
        })
      }
      
      // 清除状态
      this.userInfo = null
      this.token = null
      this.role = ''
      
      // 清除localStorage
      localStorage.removeItem('userInfo')
      localStorage.removeItem('token')
      localStorage.removeItem('role')
    }
  }
}) 