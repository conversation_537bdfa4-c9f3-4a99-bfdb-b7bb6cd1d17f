import { defineStore } from 'pinia'
import {
    createSession,
    getSessionsByUserId as getConversationList,
    getSessionById as getConversationById,
    sendMessage,
    getMessagesBySessionId as getMessageHistory,
    assignAgentManually as transferAgent,
    closeSession,
    createAISession,
    getMessagesByPage,
    updateMessageStatus,
    updateReadStatus
} from '@/api/chat'
import { useUserStore } from './user'

export const useChatStore = defineStore('chat', {
    state: () => ({
        conversations: [],
        currentConversation: null,
        messages: [],
        loading: false,
        unreadCount: 0,
        ws: null,
        connected: false,
        reconnectAttempts: 0,
        maxReconnectAttempts: 5000, // 最大重连次数，设置很大使其实际上不限制
        reconnectTimeout: null,
        heartbeatInterval: null,
        currentSession: null,
        isInChatPage: false, // 标记是否在聊天页面
        pollingInterval: null,
        isPolling: false,
        pollingFailCount: 0,
        pollingSuccessCount: 0
    }),

    getters: {
        currentConversationId: (state) => state.currentConversation?.id,
        hasActiveConversation: (state) => !!state.currentConversation,
        sortedConversations: (state) => {
            return [...state.conversations].sort((a, b) => {
                // 未读消息优先
                if (a.unreadCount > 0 && b.unreadCount === 0) return -1
                if (a.unreadCount === 0 && b.unreadCount > 0) return 1

                // 最近消息时间排序
                const aTime = a.lastMessageTime ? new Date(a.lastMessageTime).getTime() : 0
                const bTime = b.lastMessageTime ? new Date(b.lastMessageTime).getTime() : 0
                return bTime - aTime
            })
        }
    },

    actions: {
        // 设置当前是否在聊天页面
        setInChatPage(status) {
            const wasInChatPage = this.isInChatPage
            this.isInChatPage = status
            console.log(`当前${status ? '进入' : '离开'}聊天页面，WebSocket状态:`, this.connected ? '已连接' : '未连接')

            // 如果状态没有变化，不做任何处理
            if (wasInChatPage === status) {
                console.log('聊天页面状态未变化，不做额外处理')
                return
            }

            // 如果进入聊天页面且未连接，则初始化WebSocket
            // 移除限制，允许在任何页面都保持连接
            if (!this.connected) {
                console.log('当前未连接WebSocket，初始化连接')
                this.initWebSocket()
            }
        },

        // 关闭WebSocket连接
        closeWebSocket() {
            console.log('关闭WebSocket连接，当前连接状态:', this.connected ? '已连接' : '未连接')
            if (this.ws) {
                try {
                    this.ws.close(1000, "正常关闭") // 使用1000正常关闭码和说明
                    console.log('WebSocket关闭命令已发送')
                } catch (e) {
                    console.error('关闭WebSocket时出错:', e)
                }
                this.ws = null
            }
            this.connected = false
            this.stopHeartbeat()

            // 清除可能的重连定时器
            if (this.reconnectTimeout) {
                clearTimeout(this.reconnectTimeout)
                this.reconnectTimeout = null
            }
        },

        initWebSocket() {
            const userStore = useUserStore()
            if (!userStore.isLoggedIn || !userStore.token) {
                console.warn('用户未登录，不初始化WebSocket连接，登录状态:', userStore.isLoggedIn, '，token:', userStore.token ? '存在' : '不存在')
                return
            }

            // 如果已经连接，不重新初始化
            if (this.connected && this.ws && this.ws.readyState === WebSocket.OPEN) {
                console.log('WebSocket已连接，不需要重新初始化')
                return
            }

            // 如果正在连接中，等待连接完成
            if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
                console.log('WebSocket正在连接中，等待连接完成...')
                return
            }

            // 如果已经存在连接但状态不是OPEN，先关闭
            if (this.ws) {
                console.log('存在旧的WebSocket连接，先关闭')
                try {
                    this.ws.close()
                } catch (e) {
                    console.error('关闭旧WebSocket连接时出错:', e)
                }
                this.ws = null
                this.connected = false
            }

            try {
                // 根据当前网页协议自动选择WebSocket协议
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'

                // 获取主机名，如果是在移动设备上，尤其需要确保不使用localhost
                const host = window.location.host

                // 优先使用环境变量中的WebSocket URL，如果没有则使用当前域名构建
                let wsUrl = import.meta.env.VITE_WS_URL
                if (!wsUrl) {
                    // 构建基于当前域名的WebSocket URL
                    wsUrl = `${protocol}//${host}`
                    console.log('未找到VITE_WS_URL环境变量，使用当前域名构建WebSocket URL:', wsUrl)
                }

                const userType = userStore.isUser ? 'user' : 'agent'
                const userId = userStore.userId || userStore.agentId

                // 构建WebSocket URL
                const wsFullUrl = `${wsUrl}/ws/${userType}/${userId}?token=${userStore.token}`
                console.log('尝试连接WebSocket:', wsFullUrl)

                // 设置连接超时
                const connectionTimeout = setTimeout(() => {
                    if (this.ws && this.ws.readyState !== WebSocket.OPEN) {
                        console.error('WebSocket连接超时，当前状态:', this.ws.readyState)
                        try {
                            this.ws.close()
                        } catch (e) {
                            console.error('关闭超时WebSocket时出错:', e)
                        }
                        this.ws = null
                        this.connected = false

                        // WebSocket连接失败时，启动HTTP轮询
                        this.startPolling()
                    }
                }, 10000) // 10秒超时

                this.ws = new WebSocket(wsFullUrl)
                console.log('WebSocket实例已创建, readyState:', this.ws.readyState)

                this.ws.onopen = () => {
                    clearTimeout(connectionTimeout) // 清除连接超时
                    console.log('WebSocket连接已建立')
                    this.connected = true
                    this.reconnectAttempts = 0
                    this.startHeartbeat()

                    // 发送身份认证消息
                    this.ws.send(JSON.stringify({
                        type: 3, // 系统通知类型
                        from: userType === 'user' ? 0 : 1,
                        senderId: Number(userId),
                        content: 'auth',
                        data: {
                            token: userStore.token,
                            userType: userType,
                            userId: userId
                        }
                    }))

                    // 如果是客服端，立即请求更新会话列表
                    if (userStore.isAgent) {
                        this.fetchConversations()
                    }

                    // 如果之前在使用HTTP轮询，现在可以停止了
                    if (this.pollingInterval) {
                        console.log('WebSocket连接已建立，停止HTTP轮询')
                        this.stopPolling()
                    }
                }

                this.ws.onmessage = (event) => {
                    try {
                        console.log('收到WebSocket原始消息:', event.data.substring(0, 100) + (event.data.length > 100 ? '...' : ''))
                        const data = JSON.parse(event.data)
                        console.log('解析后的消息:', {
                            type: data.type,
                            from: data.from,
                            sessionId: data.sessionId,
                            id: data.id,
                            contentPreview: data.content ? data.content.substring(0, 50) + (data.content.length > 50 ? '...' : '') : null
                        })

                        if (data.type === 6) {
                            // 心跳响应，不做特殊处理
                            console.log('收到心跳响应')
                            return
                        }

                        // 处理其他类型的消息
                        this.handleMessage(data)
                    } catch (error) {
                        console.error('处理WebSocket消息失败:', error, '原始消息:', event.data)
                    }
                }

                this.ws.onclose = (event) => {
                    clearTimeout(connectionTimeout) // 清除连接超时
                    console.log('WebSocket连接已关闭', event.code, event.reason)
                    this.connected = false
                    this.stopHeartbeat()

                    // WebSocket断开后，立即启动HTTP轮询
                    this.startPolling()
                }

                this.ws.onerror = (error) => {
                    console.error('WebSocket连接错误:', error)
                    this.connected = false
                    // 当发生错误时，不要在这里直接关闭连接，让onclose事件处理
                }
            } catch (error) {
                console.error('初始化WebSocket失败:', error)
                this.connected = false
                // WebSocket初始化失败，立即启动HTTP轮询
                this.startPolling()
            }
        },

        startHeartbeat() {
            this.stopHeartbeat() // 先清除可能存在的心跳

            // 每30秒发送一次心跳
            this.heartbeatInterval = setInterval(() => {
                if (this.connected && this.ws && this.ws.readyState === WebSocket.OPEN) {
                    try {
                        this.ws.send(JSON.stringify({
                            type: 6, // 心跳类型
                            timestamp: new Date().getTime()
                        }))
                        console.log('发送心跳')
                    } catch (e) {
                        console.error('发送心跳失败:', e)
                        // 如果发送失败，尝试重连
                        this.closeWebSocket()
                        this.reconnect()
                    }
                } else {
                    console.warn('心跳检测到WebSocket未连接，尝试重连')
                    this.closeWebSocket()
                    this.reconnect()
                }
            }, 30000)
        },

        stopHeartbeat() {
            if (this.heartbeatInterval) {
                clearInterval(this.heartbeatInterval)
                this.heartbeatInterval = null
            }
        },

        reconnect() {
            // 取消现有的重连定时器
            if (this.reconnectTimeout) {
                clearTimeout(this.reconnectTimeout)
                this.reconnectTimeout = null
            }

            // 增加重连尝试次数
            this.reconnectAttempts++
            console.log(`尝试重新连接WebSocket (第${this.reconnectAttempts}次)`)

            // 直接尝试初始化WebSocket连接
            this.initWebSocket()
        },

        handleMessage(message) {
            // 基本检查，确保消息有效
            if (!message || typeof message !== 'object') {
                console.warn('收到无效消息:', message)
                return
            }

            try {
                console.log('处理收到的消息:', message)

                // 检查是否为图片消息
                if (message.type === 2 || message.msgType === 1) {
                    console.log('检测到图片消息:', message)
                    // 确保图片消息的type和msgType属性正确
                    message.type = 2
                    message.msgType = 1
                }

                // 标准化消息格式，确保一致性
                const standardizedMessage = this.standardizeMessage(message)

                // 检查消息是否已存在于消息列表，避免重复
                const msgExistsInList = this.messages.some(m => m.id === standardizedMessage.id)

                // 使用布尔值标记是否为新消息
                const isNewMessage = !msgExistsInList

                // 如果消息不存在，添加到消息列表中，即使当前不在这个会话页面
                if (isNewMessage) {
                    this.messages.push(standardizedMessage)
                    console.log('新消息已添加到消息列表，当前消息列表长度:', this.messages.length)
                } else {
                    console.log('消息已存在于列表中，不重复添加:', standardizedMessage.id)
                }

                // 无论消息属于哪个会话，都先分发事件到UI层，让组件决定是否显示
                // 添加一个标记指示这是否是新消息
                window.dispatchEvent(new CustomEvent('chat-message-received', {
                    detail: {
                        ...standardizedMessage,
                        isNewMessage: isNewMessage
                    }
                }))

                console.log('已分发消息接收事件:', standardizedMessage.sessionId, standardizedMessage.content)

                // 处理特定类型的消息
                if (standardizedMessage.type === 4) {
                    // 用户上线通知
                    console.log('用户上线:', standardizedMessage.senderId)
                } else if (standardizedMessage.type === 5) {
                    // 用户下线通知
                    console.log('用户下线:', standardizedMessage.senderId)
                } else if (standardizedMessage.type === 3) {
                    // 系统通知类消息
                    console.log('系统通知:', standardizedMessage.content)

                    // 检查是否是转接人工的通知
                    if (standardizedMessage.data && standardizedMessage.data.transferType === 'toHuman') {
                        console.log('检测到转接人工客服通知:', standardizedMessage)

                        // 触发转接人工客服通知事件，但添加标记来区分是"转接成功"的消息
                        window.dispatchEvent(new CustomEvent('chat-transfer-notification', {
                            detail: {
                                sessionId: standardizedMessage.sessionId,
                                content: standardizedMessage.content,
                                agentId: standardizedMessage.data.agentId,
                                timestamp: standardizedMessage.timestamp || new Date().getTime(),
                                isTransferSuccess: true // 标记为转接成功的消息
                            }
                        }))

                        // 更新当前会话的客服类型
                        if (this.currentConversation && this.currentConversation.id === standardizedMessage.sessionId) {
                            this.currentConversation.currentAgentType = 1; // 设置为人工客服类型
                            this.currentConversation.transferRequested = 1; // 设置为已请求转接状态

                            // 刷新当前会话详情
                            this.refreshCurrentConversation();
                        }
                    }
                    // 检查是否是给客服的转接请求通知
                    else if (standardizedMessage.data && standardizedMessage.data.transferType === 'requestAgent') {
                        console.log('检测到给客服的转接请求通知:', standardizedMessage)

                        // 触发给客服的转接通知事件，添加目标客服ID
                        window.dispatchEvent(new CustomEvent('chat-transfer-notification', {
                            detail: {
                                sessionId: standardizedMessage.sessionId,
                                content: standardizedMessage.content,
                                userId: standardizedMessage.data.userId,
                                // 添加目标客服ID字段，如果后端指定了特定客服
                                targetAgentId: standardizedMessage.data.targetAgentId || null,
                                timestamp: standardizedMessage.timestamp || new Date().getTime(),
                                isTransferRequest: true // 标记为转接请求类型
                            }
                        }))

                        // 触发新的请求人工客服通知事件
                        console.log('准备分发agent-request-notification事件')
                        const eventDetail = {
                            sessionId: standardizedMessage.sessionId,
                            content: standardizedMessage.content,
                            data: standardizedMessage.data,
                            // 确保目标客服ID也包含在事件详情中
                            targetAgentId: standardizedMessage.data.targetAgentId || null,
                            timestamp: standardizedMessage.timestamp || new Date().getTime()
                        }
                        console.log('agent-request-notification事件详情:', eventDetail)

                        try {
                            window.dispatchEvent(new CustomEvent('agent-request-notification', {
                                detail: eventDetail
                            }))
                            console.log('agent-request-notification事件已分发')

                            // 直接调用全局函数显示弹窗 - 新增代码
                            if (window.showAgentRequestAlert) {
                                console.log('尝试直接调用全局函数显示弹窗')
                                window.showAgentRequestAlert(eventDetail)
                            } else {
                                console.log('全局函数未找到，尝试触发全局事件')
                                // 尝试使用非标准但可能有效的方式直接设置组件状态
                                const showAlertEvent = new CustomEvent('show-waiting-alert', {
                                    detail: {
                                        show: true,
                                        session: {
                                            id: eventDetail.sessionId,
                                            userId: eventDetail.data.userId,
                                            lastMessage: eventDetail.content,
                                            userNickname: `访客${eventDetail.data.userId}`,
                                            waitingTime: 0
                                        }
                                    }
                                })
                                window.dispatchEvent(showAlertEvent)
                            }
                        } catch (e) {
                            console.error('分发agent-request-notification事件或显示弹窗失败:', e)
                        }

                        // 立即播放提示音效提醒客服
                        try {
                            // 使用相对路径并添加备选路径
                            const audio = new Audio(import.meta.env.BASE_URL + 'audio/notification.mp3')
                            audio.volume = 0.7
                            audio.play().catch(e => {
                                console.warn('播放转接通知音效失败:', e)
                                // 尝试备用路径
                                const backupAudio = new Audio('/audio/notification.mp3')
                                backupAudio.volume = 0.7
                                backupAudio.play().catch(err => console.error('备用音频播放失败:', err))
                            })
                        } catch (error) {
                            console.error('播放提示音失败:', error)
                        }

                        // 刷新会话列表，确保新转接的会话显示在列表中
                        this.fetchConversations();
                    }
                } else if (standardizedMessage.type === 1 || standardizedMessage.type === 2) {
                    // 文本或图片消息
                    const isFromUser = standardizedMessage.from === 0

                    if (isFromUser) {
                        console.log('客服收到用户消息:', standardizedMessage)

                        // 触发消息提醒事件
                        window.dispatchEvent(new CustomEvent('chat-notification', {
                            detail: {
                                sessionId: standardizedMessage.sessionId,
                                content: standardizedMessage.content,
                                senderId: standardizedMessage.senderId,
                                senderName: standardizedMessage.senderName || '访客' + standardizedMessage.senderId,
                                timestamp: standardizedMessage.timestamp || new Date().getTime(),
                                from: standardizedMessage.from,
                                id: standardizedMessage.id
                            }
                        }))

                        // 额外记录图片消息信息
                        if (standardizedMessage.type === 2 || standardizedMessage.msgType === 1) {
                            console.log('客服收到用户图片消息，URL:', standardizedMessage.content)
                        }

                        // 会话ID转为数字进行比较，避免字符串与数字比较不匹配
                        const msgSessionId = Number(standardizedMessage.sessionId)
                        const currentConvId = Number(this.currentConversationId)

                        // 记录会话ID用于调试
                        console.log('会话ID比较:', {
                            msgSessionId,
                            currentConvId,
                            conversationsCount: this.conversations.length,
                            match: msgSessionId === currentConvId
                        });

                        // 先检查当前会话ID是否匹配
                        if (msgSessionId === currentConvId) {
                            console.log('消息属于当前会话，标记为已读');

                            // 消息属于当前会话，立即标记为已读
                            if (isNewMessage) {
                                this.safeMarkMessageAsRead(standardizedMessage.id);
                            }
                        }

                        // 更新会话列表中的信息，不论是否是当前会话
                        // 确保使用数字ID进行比较
                        const sessionIndex = this.conversations.findIndex(c => Number(c.id) === msgSessionId)

                        if (sessionIndex !== -1) {
                            // 找到对应会话，更新会话信息
                            console.log('找到对应会话，更新会话信息')
                            this.conversations[sessionIndex].lastMessage = standardizedMessage.content
                            this.conversations[sessionIndex].lastActiveTime = new Date().toISOString()

                            // 只有当不是当前会话时，才增加未读计数
                            if (msgSessionId !== currentConvId) {
                                this.conversations[sessionIndex].unreadCount = (this.conversations[sessionIndex].unreadCount || 0) + 1
                            }

                            // 触发会话更新事件
                            window.dispatchEvent(new CustomEvent('chat-conversation-updated', {
                                detail: this.conversations[sessionIndex]
                            }))
                        } else {
                            // 会话不在列表中，异步刷新会话列表
                            console.log('在会话列表中未找到对应会话，异步刷新会话列表')
                            this.fetchConversations().then(() => {
                                console.log('会话列表已刷新')

                                // 不要重新触发消息事件，避免重复处理
                                // 只更新会话状态
                                const updatedIndex = this.conversations.findIndex(c => Number(c.id) === msgSessionId)
                                if (updatedIndex !== -1) {
                                    console.log('刷新后找到对应会话，更新会话状态')

                                    // 更新会话列表显示
                                    window.dispatchEvent(new CustomEvent('chat-conversation-updated', {
                                        detail: this.conversations[updatedIndex]
                                    }))

                                    // 如果是当前会话，确保消息标记为已读
                                    if (msgSessionId === currentConvId && isNewMessage) {
                                        this.safeMarkMessageAsRead(standardizedMessage.id)
                                    }
                                }
                            })
                        }
                    }
                }
            } catch (error) {
                console.error('处理消息出错:', error)
            }
        },

        // 安全地标记消息为已读，处理错误情况
        async safeMarkMessageAsRead(messageId) {
            if (!messageId) {
                console.warn('消息ID为空，无法标记为已读')
                return false
            }

            try {
                console.log('标记消息为已读:', messageId)
                // 先更新本地状态，确保UI响应
                const msgIndex = this.messages.findIndex(m => m.id === messageId)
                if (msgIndex !== -1) {
                    this.messages[msgIndex].isRead = 1
                }

                // 获取消息所属的会话ID
                const sessionId = this.currentConversationId
                if (!sessionId) {
                    console.warn('当前没有活动会话，无法更新消息状态')
                    return false
                }

                console.log('正在调用updateReadStatus API, 会话ID:', sessionId, '发送者类型:', 0)

                // 异步调用API，不阻塞UI
                updateReadStatus(sessionId, 0).then(res => { // 0表示用户消息
                    if (res.code === 200) {
                        console.log('会话消息已标记为已读，API返回:', res)
                        return true
                    } else {
                        console.warn('API返回标记消息已读失败:', res.message)
                        return false
                    }
                }).catch(error => {
                    console.error('标记消息已读出错，但不影响UI:', error)
                    return false
                })

                return true
            } catch (error) {
                console.error('标记消息已读过程出错:', error)
                return false
            }
        },

        // 标准化消息格式，确保一致性
        standardizeMessage(message) {
            // 复制消息对象，避免直接修改原对象
            const standardizedMsg = { ...message }

            // 确保基本字段存在
            if (standardizedMsg.type === undefined && standardizedMsg.msgType !== undefined) {
                standardizedMsg.type = standardizedMsg.msgType === 0 ? 1 : 2
            }

            if (standardizedMsg.from === undefined && standardizedMsg.senderType !== undefined) {
                standardizedMsg.from = standardizedMsg.senderType
            } else if (standardizedMsg.from !== undefined && standardizedMsg.senderType === undefined) {
                standardizedMsg.senderType = standardizedMsg.from
            }

            // 确保时间格式正确
            if (!standardizedMsg.createdAt) {
                standardizedMsg.createdAt = new Date().toISOString()
            }

            // 确保状态字段存在
            if (standardizedMsg.status === undefined) {
                standardizedMsg.status = 1 // 默认已发送状态
            }

            // 标记为已标准化
            standardizedMsg._standardized = true

            return standardizedMsg
        },

        sendMessage(message) {
            if (!this.connected || !this.ws || this.ws.readyState !== WebSocket.OPEN) {
                console.error('WebSocket未连接，无法发送消息')
                return Promise.reject(new Error('WebSocket未连接'))
            }

            return new Promise((resolve, reject) => {
                try {
                    this.ws.send(JSON.stringify(message))
                    resolve()
                } catch (error) {
                    console.error('发送消息失败:', error)
                    reject(error)
                }
            })
        },

        // 获取会话列表
        async fetchConversations() {
            const userStore = useUserStore()
            this.loading = true

            try {
                let res
                if (userStore.isUser) {
                    // 用户获取自己的会话列表
                    res = await getConversationList(userStore.userId)
                } else if (userStore.isAgent) {
                    // 客服获取分配给自己的会话列表
                    res = await getConversationList(userStore.agentId)
                }

                if (res.code === 200) {
                    this.conversations = res.data
                    // 计算总未读数
                    this.unreadCount = this.conversations.reduce((sum, conv) => sum + (conv.unreadCount || 0), 0)
                }

                return Promise.resolve(res)
            } catch (error) {
                return Promise.reject(error)
            } finally {
                this.loading = false
            }
        },

        // 创建新会话
        async createNewConversation(data) {
            const userStore = useUserStore()

            try {
                // 用户创建会话需要提供用户ID
                if (userStore.isUser && !data.userId) {
                    data.userId = userStore.userId
                }

                // 验证userId是否存在且有效
                if (!data.userId) {
                    console.error('创建会话失败: 缺少用户ID')
                    return Promise.reject(new Error('缺少用户ID'))
                }

                console.log('发送创建会话请求，参数:', data)

                // 默认使用AI客服会话，除非明确要求使用人工客服
                let res;
                if (data.assignHuman === true) {
                    // 明确要求人工客服
                    res = await createSession({ ...data, assignHuman: true })
                } else {
                    // 默认使用AI客服
                    res = await createAISession(data)
                }

                console.log('创建会话响应:', res)

                if (res.code === 200) {
                    // 添加到会话列表
                    this.conversations.unshift(res.data)
                    // 设置为当前会话
                    this.setCurrentConversation(res.data)
                }

                return Promise.resolve(res)
            } catch (error) {
                console.error('创建会话错误:', error)
                if (error.response) {
                    console.error('服务器响应:', error.response.data)
                }
                return Promise.reject(error)
            }
        },

        // 设置当前会话
        async setCurrentConversation(conversation) {
            // 如果传入的是会话ID，需要先获取会话详情
            if (typeof conversation === 'string' || typeof conversation === 'number') {
                try {
                    const res = await getConversationById(conversation)
                    if (res.code === 200) {
                        this.currentConversation = res.data
                    } else {
                        return Promise.reject(res)
                    }
                } catch (error) {
                    return Promise.reject(error)
                }
            } else {
                this.currentConversation = conversation
            }

            // 清空消息列表，准备加载新的消息历史
            this.messages = []

            // 加载消息历史
            this.fetchMessageHistory()

            // 如果有未读消息，标记为已读
            const index = this.conversations.findIndex(conv => conv.id === this.currentConversation.id)
            if (index !== -1 && this.conversations[index].unreadCount > 0) {
                this.unreadCount -= this.conversations[index].unreadCount
                this.conversations[index].unreadCount = 0
                // 这里还可以调用后端API标记为已读
            }
        },

        // 获取消息历史
        async fetchMessageHistory() {
            if (!this.currentConversationId) return

            this.loading = true
            console.log('开始获取消息历史, 会话ID:', this.currentConversationId)

            try {
                const res = await getMessageHistory(this.currentConversationId)
                console.log('获取消息历史响应:', res)

                if (res.code === 200) {
                    // 检查返回的消息数据
                    console.log('消息历史数据条数:', res.data.length)

                    // 将消息转换为前端需要的格式
                    const messages = res.data.map(msg => {
                        // 记录每条消息的详细信息，帮助调试
                        console.log('消息数据:', {
                            id: msg.id,
                            content: msg.content && msg.content.length > 30 ? msg.content.substring(0, 30) + '...' : msg.content,
                            senderType: msg.senderType,
                            msgType: msg.msgType
                        })

                        return msg
                    })

                    // 更新消息列表
                    this.messages = messages
                    console.log('更新后的messages数组长度:', this.messages.length)
                } else {
                    console.error('获取消息历史失败:', res.message)
                }

                return Promise.resolve(res)
            } catch (error) {
                console.error('获取消息历史异常:', error)
                return Promise.reject(error)
            } finally {
                this.loading = false
            }
        },

        // 转接客服
        async transferToAgent(agentId) {
            if (!this.currentConversationId) return Promise.reject('没有选择会话')

            try {
                const res = await transferAgent(this.currentConversationId, agentId)

                if (res.code === 200) {
                    // 更新当前会话信息
                    this.currentConversation.agentId = agentId
                    this.currentConversation.agentName = res.data.agentName

                    // 更新会话列表
                    const index = this.conversations.findIndex(conv => conv.id === this.currentConversationId)
                    if (index !== -1) {
                        this.conversations[index].agentId = agentId
                        this.conversations[index].agentName = res.data.agentName
                    }

                    // 添加系统消息到消息列表
                    this.messages.push({
                        id: 'system-' + Date.now(),
                        conversationId: this.currentConversationId,
                        content: `会话已转接给客服 ${res.data.agentName}`,
                        type: 'system',
                        createdAt: new Date().toISOString()
                    })
                }

                return Promise.resolve(res)
            } catch (error) {
                return Promise.reject(error)
            }
        },

        // 关闭会话
        async closeConversation(data) {
            if (!data.conversationId) return Promise.reject('会话ID不能为空')

            try {
                // 调用API关闭会话
                const res = await closeSession(data.conversationId, data.closedBy === 'user' ? 0 : 1)

                if (res.code === 200) {
                    // 如果是当前会话，更新状态
                    if (this.currentConversationId === data.conversationId) {
                        this.currentConversation.status = 2 // 已关闭

                        // 添加系统消息
                        this.messages.push({
                            id: 'system-' + Date.now(),
                            conversationId: data.conversationId,
                            content: `会话已结束`,
                            type: 'system',
                            createdAt: new Date().toISOString()
                        })
                    }

                    // 更新会话列表中的状态
                    const index = this.conversations.findIndex(conv => conv.id === data.conversationId)
                    if (index !== -1) {
                        this.conversations[index].status = 2
                        this.conversations[index].endTime = new Date().toISOString()
                    }
                }

                return Promise.resolve(res)
            } catch (error) {
                return Promise.reject(error)
            }
        },

        // 清空当前会话
        clearCurrentConversation() {
            this.currentConversation = null
            this.messages = []
        },

        // 清理状态（用于退出登录时）
        clearState() {
            // 关闭WebSocket连接
            if (this.ws) {
                this.ws.close()
                this.ws = null
            }

            // 停止心跳和重连
            this.stopHeartbeat()
            if (this.reconnectTimeout) {
                clearTimeout(this.reconnectTimeout)
            }

            // 重置状态
            this.connected = false
            this.reconnectAttempts = 0
            this.messages = []
            this.currentSession = null
        },

        // 发送文本消息
        async sendTextMessage(content, receiverId, skipLocalAdd = false) {
            const userStore = useUserStore()
            if (!this.currentConversationId) {
                console.error('没有活动的会话')
                return Promise.reject(new Error('没有活动的会话'))
            }

            // 记录连接状态
            console.log('发送消息前连接状态:', {
                webSocketConnected: this.connected,
                isPolling: this.isPolling,
                readyState: this.ws?.readyState,
                userAgent: navigator.userAgent
            })

            // 如果用户是普通用户，但未指定接收者ID，则查找当前会话的客服ID
            // 如果是客服，但未指定接收者ID，则使用当前会话的用户ID
            let actualReceiverId = receiverId
            if (!actualReceiverId) {
                if (userStore.isUser) {
                    // 如果当前会话有客服，使用客服ID，否则留空让后端处理
                    actualReceiverId = this.currentConversation?.agentId || 0
                } else {
                    // 如果当前会话有用户，使用用户ID
                    actualReceiverId = this.currentConversation?.userId || 0
                }
            }
            var datasource = localStorage.getItem('datasource');
            var scene = localStorage.getItem('scene');
            var collection_name = localStorage.getItem('collection_name');
            var channel = localStorage.getItem('channel');

            // 创建消息对象
            const message = {
                type: 1, // 文本消息
                from: userStore.isUser ? 0 : 1, // 0-用户发送，1-客服发送
                senderId: Number(userStore.isUser ? userStore.userId : userStore.agentId),
                receiverId: Number(actualReceiverId), // 确保是数字类型
                sessionId: Number(this.currentConversationId), // 确保是数字类型
                content: content,
                timestamp: Date.now(),
                scene: scene || '默认应用场景',
                datasource: datasource || '默认数据源',
                collectionName: collection_name || '默认查询知识库集合',
                channel: channel||'Android'
            }

            // 不论使用WebSocket还是HTTP API，先添加临时消息到本地
            if (!skipLocalAdd) {
                const localMessage = {
                    ...message,
                    id: 'local-' + Date.now(),
                    createdAt: new Date().toISOString(),
                    status: 0 // 发送中状态
                }
                this.messages.push(localMessage)
            }

            try {
                // 1. 如果WebSocket已连接，优先使用WebSocket发送
                if (this.connected && this.ws?.readyState === WebSocket.OPEN) {
                    try {
                        await this.sendMessage(message)
                        console.log('WebSocket消息发送成功')

                        // 更新本地消息状态为已发送
                        if (!skipLocalAdd) {
                            const localMsgIndex = this.messages.findIndex(msg => msg.id === 'local-' + message.timestamp)
                            if (localMsgIndex !== -1) {
                                this.messages[localMsgIndex].status = 1 // 已发送
                            }
                        }

                        return Promise.resolve({ success: true, mode: 'websocket' })
                    } catch (wsError) {
                        console.error('WebSocket发送失败，尝试使用HTTP API:', wsError)
                        throw wsError // 继续抛出异常，进入HTTP备选方案
                    }
                }

                // 2. 如果WebSocket未连接，或发送失败，使用HTTP API
                console.log('使用HTTP API发送消息')
                const res = await sendMessage({
                    sessionId: this.currentConversationId,
                    content: content,
                    msgType: 0, // 0=文本消息
                    senderType: userStore.isUser ? 0 : 1, // 0=用户, 1=客服
                    isRead: 0,
                    channel: channel,
                    scene: scene || '默认应用场景',
                    datasource: datasource || '默认数据源',
                    collectionName: collection_name || '默认查询知识库集合'
                })

                console.log('HTTP API消息发送结果:', res)

                // 如果通过HTTP API发送成功，更新本地消息状态
                if (res.code === 200 && !skipLocalAdd) {
                    const localMsgIndex = this.messages.findIndex(msg => msg.id === 'local-' + message.timestamp)
                    if (localMsgIndex !== -1) {
                        // 更新消息状态和ID
                        this.messages[localMsgIndex].status = 1 // 已发送
                        this.messages[localMsgIndex].id = res.data.id || this.messages[localMsgIndex].id
                    }
                }

                // 如果当前正在使用HTTP轮询，立即执行一次轮询获取新消息
                if (this.isPolling) {
                    setTimeout(() => this.performPolling(), 1000)
                }

                return Promise.resolve({ success: true, mode: 'http' })
            } catch (error) {
                console.error('发送消息失败(两种方式均尝试):', error)

                // 更新本地消息状态为发送失败
                if (!skipLocalAdd) {
                    const localMsgIndex = this.messages.findIndex(msg => msg.id === 'local-' + message.timestamp)
                    if (localMsgIndex !== -1) {
                        this.messages[localMsgIndex].status = -1 // 发送失败
                    }
                }

                return Promise.reject(error)
            }
        },

        // 发送图片消息
        async sendImageMessage(formData, receiverId) {
            const userStore = useUserStore()
            if (!this.currentConversationId) {
                console.error('没有活动的会话')
                return Promise.reject(new Error('没有活动的会话'))
            }

            try {
                // 修改为使用后台上传接口
                const res = await sendMessage(formData, true) // 第二个参数true表示文件上传

                if (res.code !== 200) {
                    return Promise.reject(new Error(res.message || '图片上传失败'))
                }

                // 获取图片URL
                const imageUrl = res.data.url

                // 如果用户是普通用户，但未指定接收者ID，则查找当前会话的客服ID
                // 如果是客服，但未指定接收者ID，则使用当前会话的用户ID
                let actualReceiverId = receiverId
                if (!actualReceiverId) {
                    if (userStore.isUser) {
                        // 如果当前会话有客服，使用客服ID，否则留空让后端处理
                        actualReceiverId = this.currentConversation?.agentId || 0
                    } else {
                        // 如果当前会话有用户，使用用户ID
                        actualReceiverId = this.currentConversation?.userId || 0
                    }
                }

                // 使用WebSocket发送消息通知
                const message = {
                    type: 2, // 图片消息
                    from: userStore.isUser ? 0 : 1,
                    senderId: Number(userStore.isUser ? userStore.userId : userStore.agentId),
                    receiverId: Number(actualReceiverId), // 确保是数字类型
                    sessionId: Number(this.currentConversationId), // 确保是数字类型
                    content: imageUrl,
                    timestamp: Date.now(),
                    scene: scene || '默认应用场景',
                    datasource: datasource || '默认数据源',
                    collectionName: collection_name || '默认查询知识库集合',
                    channel: channel||'Android'
                }

                // 始终尝试通过WebSocket发送通知
                if (this.connected && this.ws?.readyState === WebSocket.OPEN) {
                    await this.sendMessage(message)
                } else {
                    console.warn('WebSocket未连接，图片已上传但无法发送WebSocket通知')
                    // 不抛出异常，因为图片已经上传成功
                }

                // 返回上传的图片信息
                return Promise.resolve(res)
            } catch (error) {
                console.error('发送图片消息失败:', error)
                return Promise.reject(error)
            }
        },

        // 加载消息历史的别名方法
        async loadMessages(sessionId, limit = 100) {
            try {
                console.log(`开始加载会话消息, 会话ID: ${sessionId}, 限制: ${limit}条`);

                // 保存原有消息数组，以便在出错时恢复
                const originalMessages = [...this.messages];

                const res = await getMessagesByPage(sessionId, 1, limit);
                console.log(`消息加载API返回: 状态=${res.code}, 响应数据=`, res.data);

                if (res.code === 200) {
                    // 检查返回的数据是否有效
                    if (!res.data) {
                        console.error('API返回数据为空:', res.data);
                        return; // 如果数据无效，保留原有消息，不进行替换
                    }

                    let messagesData = [];

                    // 处理不同的返回格式
                    if (Array.isArray(res.data)) {
                        // 直接是数组格式
                        console.log('API返回数组格式数据，长度:', res.data.length);
                        messagesData = res.data;
                    } else if (res.data.records && Array.isArray(res.data.records)) {
                        // 分页格式 {records: []}
                        console.log('API返回分页格式数据，长度:', res.data.records.length);
                        messagesData = res.data.records;
                    } else {
                        console.error('API返回数据格式异常:', res.data);
                        return; // 未知格式，保留原有消息
                    }

                    // 检查是否为空数组，避免清空已有消息
                    if (messagesData.length === 0 && this.messages.length > 0) {
                        console.warn('API返回空消息数组，但当前已有消息，保留现有消息');
                        return;
                    }

                    this.messages = messagesData;
                    // 按时间顺序排序消息
                    this.messages.sort((a, b) => {
                        const timeA = new Date(a.createdAt || a.timestamp).getTime();
                        const timeB = new Date(b.createdAt || b.timestamp).getTime();
                        return timeA - timeB;
                    });

                    console.log(`消息加载完成，共${this.messages.length}条消息`);
                } else {
                    console.error('加载消息失败，API返回非200状态:', res);
                }
            } catch (error) {
                console.error('加载消息异常:', error);
                // 错误处理：恢复原有消息，避免UI显示空白
                if (this.messages.length === 0 && originalMessages && originalMessages.length > 0) {
                    console.warn('由于出错，恢复原有消息数组');
                    this.messages = originalMessages;
                }
            }
        },

        // 刷新当前会话信息
        async refreshCurrentConversation() {
            if (!this.currentConversation || !this.currentConversation.id) {
                return
            }

            try {
                const response = await getConversationById(this.currentConversation.id)

                if (response.code === 200) {
                    // 更新会话信息
                    this.currentConversation = response.data

                    // 更新会话列表中对应的会话
                    const index = this.conversations.findIndex(conv => conv.id === this.currentConversation.id)
                    if (index !== -1) {
                        this.conversations[index] = response.data
                    }
                }

                return response
            } catch (error) {
                console.error('刷新会话信息失败', error)
                return { code: 500, message: '刷新会话信息失败' }
            }
        },

        // 添加HTTP轮询作为备份方案 - 增强版
        startPolling() {
            // 先清除已有的轮询
            if (this.pollingInterval) this.stopPolling()

            // 记录轮询启动
            console.log('启动HTTP轮询作为备份方案')

            // 添加一个状态标记，表示正在使用HTTP轮询
            this.isPolling = true

            // 立即执行一次轮询
            this.performPolling()

            // 设置定时轮询 - 每5秒轮询一次新消息
            this.pollingInterval = setInterval(() => {
                this.performPolling()

                // 每次轮询同时尝试重新连接WebSocket
                if (!this.connected) {
                    console.log('轮询期间尝试重新连接WebSocket')
                    this.reconnect()
                }
            }, 5000)

            // 发出轮询模式通知
            window.dispatchEvent(new CustomEvent('chat-polling-started', {
                detail: { timestamp: Date.now() }
            }))
        },

        // 执行一次轮询
        async performPolling() {
            if (!this.isInChatPage || !this.currentConversationId) {
                // 如果不在聊天页面或没有当前会话，停止轮询
                this.stopPolling()
                return
            }

            try {
                console.log('执行HTTP轮询获取消息:', this.currentConversationId)
                const res = await getMessageHistory(this.currentConversationId)

                if (res.code === 200 && Array.isArray(res.data)) {
                    // 更新当前会话的消息，避免重复
                    const newMessages = res.data.filter(msg =>
                        !this.messages.some(existingMsg => existingMsg.id === msg.id)
                    )

                    if (newMessages.length > 0) {
                        console.log('通过HTTP轮询发现新消息:', newMessages.length)
                        // 标准化消息格式
                        const normalizedMessages = newMessages.map(msg => {
                            if (msg.from === undefined && msg.senderType !== undefined) {
                                msg.from = msg.senderType
                            } else if (msg.from !== undefined && msg.senderType === undefined) {
                                msg.senderType = msg.from
                            }
                            return msg
                        })

                        // 添加到消息列表
                        this.messages.push(...normalizedMessages)

                        // 触发消息接收事件，通知UI更新
                        window.dispatchEvent(new CustomEvent('chat-message-received', {
                            detail: normalizedMessages[normalizedMessages.length - 1]
                        }))

                        // 更新最后一条消息时间
                        if (this.currentConversation && normalizedMessages.length > 0) {
                            const lastMsg = normalizedMessages[normalizedMessages.length - 1]
                            this.currentConversation.lastMessage = lastMsg.type === 1 || lastMsg.msgType === 0
                                ? lastMsg.content
                                : '[图片]'
                            this.currentConversation.lastActiveTime = lastMsg.createdAt || new Date().toISOString()
                        }
                    } else {
                        console.log('HTTP轮询未发现新消息')
                    }

                    // 每次轮询也检查会话状态
                    if (this.currentConversation) {
                        try {
                            const sessionRes = await getConversationById(this.currentConversation.id)
                            if (sessionRes.code === 200 && sessionRes.data) {
                                // 更新会话状态，如果有变化
                                if (sessionRes.data.status !== this.currentConversation.status) {
                                    console.log('会话状态已更新:', sessionRes.data.status)
                                    this.currentConversation.status = sessionRes.data.status
                                }
                            }
                        } catch (error) {
                            console.warn('轮询获取会话状态失败:', error)
                        }
                    }
                }

                // 轮询成功，重置失败计数
                this.pollingFailCount = 0

                // 每10次成功的轮询，尝试重新建立WebSocket连接
                this.pollingSuccessCount = (this.pollingSuccessCount || 0) + 1
                if (this.pollingSuccessCount >= 10) {
                    console.log('轮询模式运行10次，尝试恢复WebSocket连接')
                    this.pollingSuccessCount = 0

                    // 尝试恢复WebSocket连接，但不立即停止轮询
                    // WebSocket连接成功后会自动停止轮询
                    this.initWebSocket()
                }
            } catch (error) {
                console.error('HTTP轮询失败:', error)

                // 失败次数累计，如果连续失败超过3次，尝试重新建立WebSocket连接
                this.pollingFailCount = (this.pollingFailCount || 0) + 1
                if (this.pollingFailCount >= 3) {
                    console.log('HTTP轮询连续失败3次，尝试恢复WebSocket连接')
                    this.pollingFailCount = 0

                    // 在尝试重新建立WebSocket之前先停止轮询
                    this.stopPolling()

                    // 尝试重新建立WebSocket连接
                    this.connected = false
                    this.initWebSocket()
                }
            }
        },

        stopPolling() {
            if (this.pollingInterval) {
                clearInterval(this.pollingInterval)
                this.pollingInterval = null
                this.isPolling = false
                this.pollingFailCount = 0
                this.pollingSuccessCount = 0
                console.log('HTTP轮询已停止')

                // 发出轮询停止通知
                window.dispatchEvent(new CustomEvent('chat-polling-stopped', {
                    detail: { timestamp: Date.now() }
                }))
            }
        },

        // 现有代码中增加这个方法，作为主动切换WebSocket/HTTP轮询的方法
        toggleConnectionMode() {
            if (this.connected) {
                console.log('手动切换到HTTP轮询模式')
                this.closeWebSocket()
                this.startPolling()
                return 'polling'
            } else if (this.isPolling) {
                console.log('手动切换到WebSocket模式')
                this.stopPolling()
                this.initWebSocket()
                return 'websocket'
            } else {
                // 两者都未连接时，优先尝试WebSocket
                console.log('两种连接都未建立，优先尝试WebSocket')
                this.initWebSocket()

                // 如果3秒后WebSocket还未连接，自动启动HTTP轮询
                setTimeout(() => {
                    if (!this.connected && this.currentConversationId) {
                        console.log('WebSocket未能成功连接，自动启动HTTP轮询')
                        this.startPolling()
                    }
                }, 3000)

                return 'auto'
            }
        },

        // 检查当前连接状态
        getConnectionStatus() {
            return {
                isWebSocketConnected: this.connected,
                isPolling: this.isPolling,
                wsReadyState: this.ws ? this.ws.readyState : -1,
                reconnectAttempts: this.reconnectAttempts,
                currentMode: this.connected ? 'websocket' : (this.isPolling ? 'polling' : 'none')
            }
        },

        // 自动选择最佳连接方式
        useOptimalConnectionMode() {
            // 如果WebSocket已连接，使用WebSocket
            if (this.connected) {
                console.log('使用已连接的WebSocket')
                return 'websocket'
            }

            // 修改连接逻辑：即使正在轮询，也尝试恢复WebSocket连接
            if (this.isPolling) {
                const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
                const hasSlowConnection = navigator.connection && (
                    navigator.connection.type === 'cellular' ||
                    navigator.connection.downlink < 1
                )

                if (isMobile && hasSlowConnection) {
                    console.log('检测到移动设备+慢速连接，继续使用HTTP轮询')
                    return 'polling'
                }

                // 先尝试停止轮询，重新建立WebSocket连接
                console.log('尝试从轮询模式恢复WebSocket连接')
                this.stopPolling()
                this.initWebSocket()

                // 设置超时：如果5秒内WebSocket没有连接成功，恢复轮询
                setTimeout(() => {
                    if (!this.connected && this.currentConversationId) {
                        console.log('WebSocket恢复连接失败，重新启动HTTP轮询')
                        this.startPolling()
                    }
                }, 5000)

                return 'switching' // 表示正在从轮询切换到WebSocket
            }

            // 如果都未连接，优先尝试WebSocket
            console.log('优先尝试WebSocket连接')
            this.initWebSocket()

            // 设置备用方案：如果WebSocket连接失败，再使用HTTP轮询
            setTimeout(() => {
                if (!this.connected && this.currentConversationId) {
                    console.log('WebSocket连接未成功，切换到HTTP轮询')
                    this.startPolling()
                }
            }, 5000)

            return 'connecting'
        },

        // 添加标记消息为已读的方法
        async markMessageAsRead(messageId) {
            if (!messageId) {
                console.warn('消息ID为空，无法标记为已读')
                return
            }

            try {
                console.log('标记消息为已读:', messageId)

                // 获取会话ID
                const sessionId = this.currentConversationId
                if (!sessionId) {
                    console.warn('当前无活动会话，无法标记消息已读')
                    return false
                }

                console.log('调用updateReadStatus API更新会话中所有消息的已读状态, 会话ID:', sessionId, '发送者类型:', 0)

                // 调用更新会话中所有消息的已读状态API
                const res = await updateReadStatus(sessionId, 0) // 0表示用户发送的消息

                if (res.code === 200) {
                    console.log('会话中的消息已标记为已读, API返回:', res)

                    // 更新本地消息状态
                    const userMessages = this.messages.filter(m => m.from === 0 || m.senderType === 0)
                    console.log('更新本地消息状态, 用户消息数量:', userMessages.length)
                    userMessages.forEach(msg => {
                        msg.isRead = 1
                    })

                    return true
                } else {
                    console.warn('标记会话消息已读失败:', res.message || res.msg)
                    return false
                }
            } catch (error) {
                console.error('标记消息已读出错:', error)
                return false
            }
        }
    }
}) 