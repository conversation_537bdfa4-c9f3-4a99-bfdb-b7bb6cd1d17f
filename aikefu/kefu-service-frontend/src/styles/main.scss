/* 全局样式 */
:root {
  --primary-color: #409EFF;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  --primary-text-color: #303133;
  --regular-text-color: #606266;
  --secondary-text-color: #909399;
  --placeholder-text-color: #C0C4CC;
  --border-color-base: #DCDFE6;
  --border-color-light: #E4E7ED;
  --border-color-lighter: #EBEEF5;
  --border-color-extra-light: #F2F6FC;
  --background-color: #F5F7FA;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
  color: var(--primary-text-color);
  font-size: 14px;
  background-color: var(--background-color);
}

html, body, #app {
  height: 100%;
  width: 100%;
}

a {
  color: var(--primary-color);
  text-decoration: none;
}

.page-container {
  padding: 20px;
  box-sizing: border-box;
}

.full-height {
  height: 100%;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-center {
  text-align: center;
}

.card-shadow {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.rounded {
  border-radius: 4px;
}

.p-20 {
  padding: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.text-primary {
  color: var(--primary-color);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-danger {
  color: var(--danger-color);
}

.text-info {
  color: var(--info-color);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #999;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }
} 