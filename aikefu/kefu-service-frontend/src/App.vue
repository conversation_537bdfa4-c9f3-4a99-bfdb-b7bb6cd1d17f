<template>
  <div class="app-container">
    <router-view v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
    <MessageNotification
      v-model:show="showNotification"
      :user-name="notificationData.userName"
      :session-id="notificationData.sessionId"
      :message-content="notificationData.content"
      @close="handleNotificationClose"
    />
    <WaitingSessionAlert 
      :show="showWaitingAlert" 
      :waitingSessions="pendingSessionsForAlert" 
      @close="handleCloseWaitingAlert" 
      @view-sessions="handleViewWaitingSessions"
    />
  </div>
</template>

<script setup>
// App.vue 作为根组件, 主要负责路由视图的渲染
import { ref, onMounted, onUnmounted, h } from 'vue'
import { ElNotification } from 'element-plus'
import { useRouter } from 'vue-router'
import { useUserStore } from './store/user'
import MessageNotification from './components/MessageNotification.vue'
import WaitingSessionAlert from './components/WaitingSessionAlert.vue'

const router = useRouter()
const userStore = useUserStore()

// 新消息通知相关
const showNotification = ref(false)
const notificationData = ref({
  userName: '',
  sessionId: '',
  content: ''
})

// 待处理会话提醒相关
const showWaitingAlert = ref(false)
const pendingSessionsForAlert = ref([])

// 页面可见性和标题提示相关
const isPageVisible = ref(true)
const originalTitle = ref(document.title)
const hasUnreadMessages = ref(false)

// 音频上下文
let audioContext = null;
// 音频缓存
const audioBufferCache = {};

// 初始化音频上下文
const initAudioContext = () => {
  try {
    // 如果已存在，则返回
    if (audioContext) return audioContext;
    
    // 创建音频上下文（处理兼容性）
    const AudioContext = window.AudioContext || window.webkitAudioContext;
    if (!AudioContext) {
      console.warn('当前浏览器不支持 Web Audio API');
      return null;
    }
    
    audioContext = new AudioContext();
    return audioContext;
  } catch (error) {
    console.error('初始化音频上下文失败:', error);
    return null;
  }
};

// 加载音频文件
const loadAudio = async (url) => {
  try {
    // 检查缓存
    if (audioBufferCache[url]) {
      return audioBufferCache[url];
    }
    
    // 确保音频上下文已初始化
    const context = initAudioContext();
    if (!context) return null;
    
    // 获取音频文件
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error('加载音频文件失败');
    }
    
    // 解码音频数据
    const arrayBuffer = await response.arrayBuffer();
    const audioBuffer = await context.decodeAudioData(arrayBuffer);
    
    // 缓存解码后的音频
    audioBufferCache[url] = audioBuffer;
    return audioBuffer;
  } catch (error) {
    console.error('加载音频文件失败:', error);
    return null;
  }
};

// 播放音频缓冲区
const playAudioBuffer = (audioBuffer) => {
  try {
    if (!audioBuffer) return false;
    
    // 确保音频上下文已初始化
    const context = initAudioContext();
    if (!context) return false;
    
    // 创建音源节点
    const source = context.createBufferSource();
    source.buffer = audioBuffer;
    
    // 创建增益节点，可以控制音量
    const gainNode = context.createGain();
    gainNode.gain.value = 1;
    
    // 连接节点
    source.connect(gainNode);
    gainNode.connect(context.destination);
    
    // 播放
    source.start(0);
    return true;
  } catch (error) {
    console.error('播放音频失败:', error);
    return false;
  }
};

// 播放转接通知提示音
const playNotificationSound = async () => {
  try {
    // 尝试使用 Web Audio API 播放音频
    const audioUrl = import.meta.env.BASE_URL + 'audio/notification-2.mp3';
    const audioBuffer = await loadAudio(audioUrl);
    
    if (audioBuffer) {
      console.log('使用 Web Audio API 播放通知音效');
      playAudioBuffer(audioBuffer);
    } else {
      // 回退到传统方法
      console.log('回退到 Audio 对象播放通知音效');
      const audio = new Audio(audioUrl);
      audio.volume = 1;
      audio.play().catch(e => {
        console.log('播放通知音效失败:', e);
        // 尝试备用路径
        const backupAudio = new Audio('/audio/notification.mp3');
        backupAudio.volume = 1;
        backupAudio.play().catch(err => console.error('备用音频播放失败:', err));
      });
    }
  } catch (error) {
    console.log('播放提示音失败:', error);
  }
};

// 播放新消息提示音
const playMessageSound = async () => {
  try {
    // 尝试使用 Web Audio API 播放音频
    const audioUrl = import.meta.env.BASE_URL + 'audio/message-2.mp3';
    const audioBuffer = await loadAudio(audioUrl);
    
    if (audioBuffer) {
      console.log('使用 Web Audio API 播放消息音效');
      playAudioBuffer(audioBuffer);
    } else {
      // 回退到传统方法
      console.log('回退到 Audio 对象播放消息音效');
      const audio = new Audio(audioUrl);
      audio.volume = 1;
      audio.play().catch(e => {
        console.log('播放消息音效失败:', e);
        // 尝试备用路径
        const backupAudio = new Audio('/audio/message-2.mp3');
        backupAudio.volume = 1;
        backupAudio.play().catch(err => console.error('备用消息音频播放失败:', err));
      });
    }
  } catch (error) {
    console.log('播放消息提示音失败:', error);
  }
};

// 处理新消息通知
const handleNotification = (event) => {
  const { sessionId, content, senderName } = event.detail
  
  // 直接使用 ElNotification
  ElNotification({
    title: '新消息',
    message: h('div', { class: 'notification-content' }, [
      h('div', { class: 'user-info' }, [
        h('span', { class: 'user-name' }, senderName),
        h('span', { class: 'session-id' }, `#${sessionId}`)
      ]),
      h('div', { class: 'message-content' }, content)
    ]),
    type: 'info',
    duration: 4500,
    position: 'bottom-right'
  })
  
  // 将收到的消息保存到localStorage供Dashboard页面的MessageCarousel使用
  saveLatestMessage(event.detail)
}

// 全局保存最新消息到localStorage
const saveLatestMessage = (messageData) => {
  try {
    // 从localStorage获取现有消息
    let storedMessages = []
    const savedMessages = localStorage.getItem('dashboard_latest_messages')
    if (savedMessages) {
      storedMessages = JSON.parse(savedMessages)
    }
    
    // 检查消息是否已存在
    const existingIndex = storedMessages.findIndex(msg => msg.id === messageData.id)
    if (existingIndex === -1) {
      // 创建标准消息格式
      const messageObj = {
        sessionId: messageData.sessionId,
        content: messageData.content,
        senderId: messageData.senderId,
        senderName: messageData.senderName || ('访客' + messageData.senderId),
        timestamp: messageData.timestamp || Date.now(),
        avatar: messageData.avatar || '',
        id: messageData.id || ('msg-' + Date.now())
      }
      
      // 添加到数组开头
      storedMessages.unshift(messageObj)
      
      // 限制存储数量，最多保留100条消息
      const storageLimit = 100
      if (storedMessages.length > storageLimit) {
        storedMessages = storedMessages.slice(0, storageLimit)
      }
      
      // 保存回localStorage
      localStorage.setItem('dashboard_latest_messages', JSON.stringify(storedMessages))
      console.log('已将新消息保存到localStorage', messageObj)
    }
  } catch (error) {
    console.error('保存最新消息到localStorage失败:', error)
  }
}

// 处理收到聊天消息
const handleChatMessageReceived = (event) => {
  // 保存接收到的所有聊天消息到localStorage
  saveLatestMessage(event.detail)
  
  // 设置有未读消息状态
  hasUnreadMessages.value = true
  
  // 如果页面不可见，则播放音效、更新标题、显示浏览器通知
  if (!isPageVisible.value) {
    // 播放消息提示音
    playMessageSound()
    
    // 更新页面标题提示
    updatePageTitle()
    
    // 显示浏览器通知
    const messageData = event.detail
    const senderName = messageData.senderName || `访客${messageData.senderId}`
    showBrowserNotification(
      '新消息',
      `${senderName}: ${messageData.content}`,
      { tag: `session-${messageData.sessionId}` }
    )
  }
}

// 处理关闭消息通知
const handleNotificationClose = () => {
  showNotification.value = false
}

// 处理人工客服请求通知
const handleAgentRequestNotification = (event) => {
  console.log('App.vue 收到请求人工客服通知:', event.detail)
  
  // 只有客服角色才显示提醒
  if (!userStore.isAgent) {
    console.log('当前用户不是客服，不显示待处理会话提醒')
    return
  }
  
  // 新增：检查是否指定了目标客服ID
  if (event.detail.targetAgentId && event.detail.targetAgentId !== userStore.userInfo.id) {
    console.log('转接请求指定了其他客服，当前客服ID:', userStore.userInfo.id, '目标客服ID:', event.detail.targetAgentId)
    return; // 不是发给当前客服的请求，直接返回
  }
  
  // 获取会话信息
  const sessionId = event.detail.sessionId
  const userId = event.detail.data?.userId
  const content = event.detail.content
  
  // 创建待处理会话对象
  const pendingSession = {
    id: sessionId,
    userId: userId,
    userNickname: `访客${userId}`,
    lastMessage: content,
    waitingTime: 0, // 初始等待时间为0
    status: 0 // 待处理状态
  }
  
  // 显示待处理会话提醒
  pendingSessionsForAlert.value = [pendingSession]
  showWaitingAlert.value = true
  
  // 播放提示音
  playNotificationSound()
}

// 处理关闭待处理会话提醒
const handleCloseWaitingAlert = () => {
  showWaitingAlert.value = false
}

// 处理查看待处理会话
const handleViewWaitingSessions = () => {
  showWaitingAlert.value = false
  // 跳转到客服聊天页面的待处理会话选项卡
  router.push('/agent/chat?tab=waiting')
}

// 更新页面标题提示
const updatePageTitle = () => {
  if (!isPageVisible.value && hasUnreadMessages.value) {
    document.title = `【新消息】${originalTitle.value}`
  }
}

// 恢复原始页面标题
const restorePageTitle = () => {
  document.title = originalTitle.value
  hasUnreadMessages.value = false
}

// 处理页面可见性变化
const handleVisibilityChange = () => {
  isPageVisible.value = !document.hidden
  if (isPageVisible.value) {
    // 页面变为可见时，恢复标题
    restorePageTitle()
  } else {
    // 页面变为不可见时，如果有未读消息，更新标题
    if (hasUnreadMessages.value) {
      updatePageTitle()
    }
  }
}

// 请求浏览器通知权限
const requestNotificationPermission = async () => {
  if ('Notification' in window) {
    try {
      const permission = await Notification.requestPermission()
      console.log('浏览器通知权限状态:', permission)
      return permission === 'granted'
    } catch (error) {
      console.log('请求通知权限失败:', error)
      return false
    }
  }
  return false
}

// 显示浏览器原生通知
const showBrowserNotification = (title, message, options = {}) => {
  if ('Notification' in window && Notification.permission === 'granted' && !isPageVisible.value) {
    try {
      const notification = new Notification(title, {
        body: message,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: 'chat-message', // 同一标签的通知会替换旧的
        requireInteraction: false,
        ...options
      })
      
      // 点击通知时聚焦到页面
      notification.onclick = () => {
        window.focus()
        notification.close()
      }
      
      // 5秒后自动关闭
      setTimeout(() => notification.close(), 5000)
      
      console.log('显示浏览器通知成功')
    } catch (error) {
      console.log('显示浏览器通知失败:', error)
    }
  }
}

// 处理显示弹窗事件
const handleShowWaitingAlert = (event) => {
  if (!userStore.isAgent) return
  
  console.log('App.vue 收到show-waiting-alert事件:', event.detail)
  if (event.detail && event.detail.show) {
    if (event.detail.session) {
      pendingSessionsForAlert.value = [event.detail.session]
    }
    showWaitingAlert.value = true
    playNotificationSound()
  }
}

onMounted(() => {
  // 初始化音频上下文并预加载音频文件
  initAudioContext();
  // 预加载常用的音频文件
  loadAudio(import.meta.env.BASE_URL + 'audio/notification-2.mp3').catch(() => {
    loadAudio('/audio/notification.mp3'); // 如果主路径加载失败，尝试备用路径
  });
  loadAudio(import.meta.env.BASE_URL + 'audio/message-2.mp3').catch(() => {
    loadAudio('/audio/message-2.mp3'); // 如果主路径加载失败，尝试备用路径
  });

  // 添加事件监听
  window.addEventListener('chat-notification', handleNotification)
  window.addEventListener('agent-request-notification', handleAgentRequestNotification)
  window.addEventListener('show-waiting-alert', handleShowWaitingAlert)
  // 添加聊天消息接收事件监听
  window.addEventListener('chat-message-received', handleChatMessageReceived)
  
  // 添加页面可见性变化监听
  document.addEventListener('visibilitychange', handleVisibilityChange)
  
  // 初始化页面可见性状态
  isPageVisible.value = !document.hidden
  
  // 请求浏览器通知权限
  requestNotificationPermission()
  
  // 注册全局接口用于手动触发弹窗
  window.showAgentRequestAlert = (eventDetail) => {
    if (!userStore.isAgent) return
    
    // 检查是否指定了目标客服ID，如果指定了且不是当前客服，则不显示弹窗
    if (eventDetail.targetAgentId && eventDetail.targetAgentId !== userStore.userInfo.id) {
      console.log('转接请求指定了其他客服，当前客服ID:', userStore.userInfo.id, '目标客服ID:', eventDetail.targetAgentId)
      return; // 不是发给当前客服的请求，直接返回
    }
    
    if (eventDetail && eventDetail.data) {
      // 创建会话对象
      const session = {
        id: eventDetail.sessionId,
        // userId: eventDetail.data.userId,
        lastMessage: eventDetail.content,
        userNickname: `${eventDetail.data.userId}`,
        waitingTime: 0
      }
      
      pendingSessionsForAlert.value = [session]
      showWaitingAlert.value = true
      playNotificationSound()
    }
  }
})

onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('chat-notification', handleNotification)
  window.removeEventListener('agent-request-notification', handleAgentRequestNotification)
  window.removeEventListener('show-waiting-alert', handleShowWaitingAlert)
  // 移除聊天消息接收事件监听
  window.removeEventListener('chat-message-received', handleChatMessageReceived)
  
  // 移除页面可见性变化监听
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  
  // 删除全局接口
  delete window.showAgentRequestAlert
})
</script>

<style lang="scss">
.app-container {
  height: 100%;
  width: 100%;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

:deep(.notification-content) {
  padding: 5px 0;

  .user-info {
    margin-bottom: 5px;

    .user-name {
      font-weight: bold;
      margin-right: 8px;
    }

    .session-id {
      color: #909399;
      font-size: 12px;
    }
  }

  .message-content {
    color: #606266;
    font-size: 14px;
    word-break: break-all;
  }
}
</style> 