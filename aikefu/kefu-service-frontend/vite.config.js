import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  build: {
    outDir: 'chat-service',
    assetsDir: 'assets',
    rollupOptions: {
      output: {
        entryFileNames: '[name].js',
        chunkFileNames: '[name]-[hash].js',
        assetFileNames: '[name]-[hash].[ext]'
      }
    }
  },
  server: {
    host: '0.0.0.0', // 允许外部访问
    port: 3000,
    allowedHosts: [
      "xd.cpolar.cn", // 允许通过 Cpolar 的域名访问
      "localhost",
      "127.0.0.1"
    ],
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        // target: 'https://weixn.cpolar.cn',
        changeOrigin: true,
        secure: false
      },
      '/ws': {
        target: 'ws://localhost:8080',
        // target: 'ws://weixn.cpolar.cn',
        ws: true,
        changeOrigin: true
      },
      '/chat': {
        // target: 'http://127.0.0.1:8000',
        target: 'https://berhomellm.cpolar.cn',
        changeOrigin: true,
        secure: false
      }
    }
  },
  preview: {
    port: 3000,
    host: '0.0.0.0'
  }
}) 