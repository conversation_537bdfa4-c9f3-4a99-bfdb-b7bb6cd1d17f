# 签收图片定时上传

📝 **编辑人**：张德炜  
🕒 **更新时间**：2025年6月19日  
📦 **所属项目**：仓库签收图片上传

## 项目介绍

仓库签收图片上传助手是一款专为物流签收图片设计的自动化上传工具，可将本地图片文件定时自动上传至阿里云OSS存储服务。
软件以系统托盘方式运行，提供简便的配置界面，能够定时扫描指定目录并将符合命名规则的图片文件上传至云端存储。

## 功能特点

- **系统托盘运行**：程序启动后最小化到系统托盘，不占用桌面空间
- **定时自动扫描**：按设定的时间间隔自动扫描指定目录
- **智能文件解析**：自动解析文件名中的物流单号和日期信息
- **云端自动分类**：根据解析信息将文件存储到OSS特定路径
- **上传状态标记**：成功上传的文件自动重命名添加"已上传_"前缀
- **开机自启动**：支持Windows开机自动启动
- **完整日志记录**：详细记录所有操作和异常情况

## 安装说明

1. 联系工作人员获取项目文件
2. 双击运行`熊洞科技-签收图片上传工具.exe`即可启动程序
3. 首次启动会自动创建默认配置文件和日志目录

## 用户操作指南

### 基本使用

1. 双击运行程序，程序会自动最小化到系统托盘（屏幕右下角）
2. 程序会按照配置的时间间隔自动扫描指定目录并上传图片
3. 成功上传的图片将被重命名为"已上传_原文件名"

### 系统托盘菜单

在系统托盘区域找到图片上传图标，右键点击可以看到以下菜单选项：

- **运行状态**：显示当前程序运行状态
- **设置**：包含以下子菜单
  - **修改扫描目录**：修改要扫描的根目录路径
  - **修改扫描间隔**：修改自动扫描的时间间隔
- **开机启动**：启用或禁用开机自动运行功能
- **显示日志文件夹**：打开日志文件所在的文件夹
- **退出**：退出程序

### 上传后图片访问URL格式

上传到阿里云OSS后的图片可以通过特定格式的URL访问。格式如下：

```
https://file.juranguanjia.com/ftpFiles/日期/日期小时/物流单号.扩展名
```

其中各部分说明：
- **日期**：格式为YYYYMMDD（年月日），例如20250617
- **日期小时**：格式为YYYYMMDDHH（年月日时），例如2025061715
- **物流单号**：纯物流单号（不包含后缀），例如JD1234568876
- **扩展名**：原文件扩展名，如.jpg、.jpeg、.png

例如，对于文件名为`JDX040486038611-1-1-_2025-06-18 091236120.jpg`的图片，上传后的访问URL为：
```
https://file.juranguanjia.com/ftpFiles/20250618/2025061809/JDX040486038611.jpg
```

同事可以根据图片文件名中的日期时间和物流单号，按照上述格式构造图片访问链接。

### 配置文件说明

配置文件位于程序所在目录下的`config.ini`，可以通过以下两种方式修改：

1. 通过系统托盘菜单的"设置"选项修改
2. 直接编辑`config.ini`文件（修改后需要重启程序生效）

配置项说明：

- **root_path**：图片扫描根目录，使用正斜杠"/"作为路径分隔符
- **scan_interval**：扫描间隔（分钟），程序每隔多少分钟扫描一次指定目录

> 注意：修改配置后，需要通过系统托盘的"退出"菜单退出程序，然后重新启动程序使新设置生效。

### 图片命名规则

为确保图片能被正确识别和上传，请按以下格式命名图片文件：

```
物流单号[-后缀]_YYYY-MM-DD HHMMSS.扩展名
```

例如：
- `JDX040566571860_2025-06-18 075136816.jpg`
- `JDX040566571860-1-1-_2025-06-18 075136816.jpg`
- `JD1234568876-1-1_2025-06-17 151234.jpg`

其中：
- 物流单号：以字母开头的订单编号（如JD开头的京东订单号）
- 后缀：如"-1-1"（可选）
- 日期时间：格式为YYYY-MM-DD HHMMSS（年-月-日 时分秒）
- 扩展名：支持jpg、jpeg、png格式

## 技术实现

### 整体架构

OSS上传助手基于Python编写，采用以下关键技术和库：

- **oss2**：阿里云OSS Python SDK，用于文件上传
- **pystray**：实现系统托盘功能
- **schedule**：实现定时任务调度
- **pathlib**：处理文件路径
- **logging**：日志记录功能
- **configparser**：配置文件读写

### 主要模块及功能

1. **配置管理模块**
   - 读取、创建和保存配置文件
   - 提供默认配置
   - 配置更新和验证

2. **文件扫描模块**
   - 扫描指定目录下的图片文件
   - 筛选当天和前两天的文件夹
   - 识别未上传的图片文件

3. **文件解析模块**
   - 从文件名中提取物流单号和日期时间信息
   - 构造OSS对象存储路径
   - 处理各种格式的文件名

4. **上传处理模块**
   - 执行文件上传操作
   - 处理上传成功和失败情况
   - 重命名已上传文件
   - 生成文件访问URL

5. **系统托盘模块**
   - 创建和管理系统托盘图标
   - 处理菜单操作
   - 提供用户交互界面

6. **日志管理模块**
   - 记录程序运行和错误信息
   - 按日期分割日志文件
   - 控制日志保留天数

### 关键流程

1. **启动流程**
   ```
   加载配置 -> 创建系统托盘 -> 启动定时任务 -> 执行首次扫描
   ```

2. **扫描上传流程**
   ```
   扫描目录 -> 识别图片 -> 解析文件名 -> 上传至OSS -> 重命名文件
   ```

3. **文件名解析流程**
   ```
   提取物流单号 -> 提取日期时间 -> 构造OSS路径 -> 执行上传
   ```

### 安全性考虑

- 物流单号和日期解析采用正则表达式严格匹配
- 上传失败不会影响程序整体运行
- 文件重命名异常处理机制，避免因文件已存在导致程序错误
- 日志文件自动轮转，避免日志文件过大

### 容错机制

- 当目标文件夹不存在时自动创建
- 当配置文件不存在时自动创建默认配置
- 当重命名文件存在冲突时，添加时间戳创建唯一文件名
- 上传或重命名失败时记录详细日志

## 常见问题

1. **问题**：程序启动后在系统托盘中看不到图标
   **解决**：检查系统托盘区是否被折叠，尝试重启程序或检查系统托盘设置

2. **问题**：文件上传失败
   **解决**：
   - 检查网络连接是否正常
   - 检查图片文件名是否符合规范
   - 查看日志文件了解具体错误信息

3. **问题**：修改配置后没有生效
   **解决**：修改配置后需要退出程序并重新启动

4. **问题**：找不到日志文件
   **解决**：日志文件位于程序目录下的logs文件夹中

## 系统要求

- 操作系统：Windows 7/8/10/11
- 网络：需要连接互联网以上传文件到OSS

