#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
版本号更新辅助脚本
使用方法:
    python update_version.py [major|minor|patch]
    例如: python update_version.py patch (增加修订号)
    例如: python update_version.py minor (增加次版本号)
    例如: python update_version.py major (增加主版本号)
    
    不带参数运行将显示当前版本号

版本号格式: major.minor.patch.build
例如: 1.0.1.0 表示 主版本号.次版本号.修订号.构建号
"""

import os
import sys
import re
import datetime

VERSION_FILE = "version_info.txt"

def read_current_version():
    """读取当前版本号"""
    try:
        with open(VERSION_FILE, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 查找 filevers 版本号
            file_version_match = re.search(r'filevers=\((\d+),\s*(\d+),\s*(\d+),\s*(\d+)\)', content)
            if not file_version_match:
                print(f"无法在 {VERSION_FILE} 中找到文件版本号")
                return None
                
            # 查找 prodvers 版本号
            prod_version_match = re.search(r'prodvers=\((\d+),\s*(\d+),\s*(\d+),\s*(\d+)\)', content)
            if not prod_version_match:
                print(f"无法在 {VERSION_FILE} 中找到产品版本号")
                return None
                
            # 查找字符串版本号
            file_version_str_match = re.search(r'StringStruct\(u\'FileVersion\',\s*u\'([\d\.]+)\'\)', content)
            prod_version_str_match = re.search(r'StringStruct\(u\'ProductVersion\',\s*u\'([\d\.]+)\'\)', content)
            
            if not file_version_str_match or not prod_version_str_match:
                print(f"无法在 {VERSION_FILE} 中找到版本号字符串")
                return None
                
            # 返回各个版本号组件
            file_vers = tuple(map(int, file_version_match.groups()))
            prod_vers = tuple(map(int, prod_version_match.groups()))
            file_vers_str = file_version_str_match.group(1)
            prod_vers_str = prod_version_str_match.group(1)
            
            return {
                'file_vers': file_vers,
                'prod_vers': prod_vers,
                'file_vers_str': file_vers_str,
                'prod_vers_str': prod_vers_str
            }
            
    except Exception as e:
        print(f"读取版本信息时出错: {e}")
        return None

def update_version(version_type):
    """更新版本号"""
    current = read_current_version()
    if not current:
        print(f"未找到当前版本信息，请确保 {VERSION_FILE} 文件存在且格式正确")
        return False
        
    # 解构当前版本号
    major, minor, patch, build = current['file_vers']
    
    # 根据版本类型更新
    if version_type == "major":
        major += 1
        minor = 0
        patch = 0
        build = 0
    elif version_type == "minor":
        minor += 1
        patch = 0
        build = 0
    elif version_type == "patch":
        patch += 1
        build = 0
    elif version_type == "build":
        build += 1
    else:
        print(f"无效的版本类型: {version_type}")
        return False
        
    # 新版本号
    new_vers = (major, minor, patch, build)
    new_vers_str = f"{major}.{minor}.{patch}"
    
    try:
        # 读取原始文件内容
        with open(VERSION_FILE, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 替换各个版本号
        content = re.sub(r'filevers=\(\d+,\s*\d+,\s*\d+,\s*\d+\)', f'filevers={new_vers}', content)
        content = re.sub(r'prodvers=\(\d+,\s*\d+,\s*\d+,\s*\d+\)', f'prodvers={new_vers}', content)
        content = re.sub(r'StringStruct\(u\'FileVersion\',\s*u\'[\d\.]+\'\)', f'StringStruct(u\'FileVersion\', u\'{new_vers_str}\')', content)
        content = re.sub(r'StringStruct\(u\'ProductVersion\',\s*u\'[\d\.]+\'\)', f'StringStruct(u\'ProductVersion\', u\'{new_vers_str}\')', content)
        
        # 写回文件
        with open(VERSION_FILE, 'w', encoding='utf-8') as f:
            f.write(content)
            
        print(f"成功更新版本号: {current['file_vers_str']} -> {new_vers_str}")
        return True
        
    except Exception as e:
        print(f"更新版本号时出错: {e}")
        return False

def display_version():
    """显示当前版本号"""
    current = read_current_version()
    if not current:
        print(f"未找到当前版本信息，请确保 {VERSION_FILE} 文件存在且格式正确")
        return
        
    print("当前版本信息:")
    print(f"文件版本: {current['file_vers_str']} ({current['file_vers']})")
    print(f"产品版本: {current['prod_vers_str']} ({current['prod_vers']})")
    print("\n使用方法:")
    print("  python update_version.py patch  # 增加修订号")
    print("  python update_version.py minor  # 增加次版本号")
    print("  python update_version.py major  # 增加主版本号")
    print("  python update_version.py build  # 增加构建号")

if __name__ == "__main__":
    # 检查版本文件是否存在
    if not os.path.exists(VERSION_FILE):
        print(f"错误: 版本文件 {VERSION_FILE} 不存在")
        sys.exit(1)
        
    # 解析命令行参数
    if len(sys.argv) < 2:
        display_version()
    else:
        version_type = sys.argv[1].lower()
        if version_type in ["major", "minor", "patch", "build"]:
            if update_version(version_type):
                print("版本号已更新，请重新打包应用程序")
        else:
            print(f"无效的版本类型: {version_type}")
            print("有效的版本类型: major, minor, patch, build")
            sys.exit(1) 