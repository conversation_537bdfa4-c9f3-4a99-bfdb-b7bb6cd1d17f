# Context
Filename: 全景相机功能扩展任务.md
Created On: 2025-07-16
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
在现有的签收图片上传工具基础上，增加对全景相机照片的支持：
1. 保持原有的 "D:/Image/Ori/" 目录功能不变
2. 新增支持 "D:/Image/Panorama/" 全景相机照片目录
3. 全景相机照片在上传到OSS时，需要在订单编号后加上 "_Panorama" 标识
4. 例如：JDX041379468474_Panorama.jpg

# Project Overview
这是一个基于Python的签收图片自动上传工具，使用阿里云OSS存储服务。程序以系统托盘方式运行，定时扫描指定目录并上传符合命名规则的图片文件。

核心功能模块：
- 配置管理：通过config.ini管理扫描目录和间隔
- 文件扫描：扫描指定目录下的图片文件
- 文件解析：从文件名提取物流单号和时间信息
- OSS上传：上传文件到阿里云OSS并重命名本地文件
- 系统托盘：提供用户交互界面

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## 当前代码结构分析

### 1. 配置系统
- `local_config` 字典包含 `root_path` 和 `scan_interval`
- 当前只支持单一根目录配置
- 配置通过 `config.ini` 文件管理

### 2. 核心函数分析

#### parse_filename() 函数
- 解析文件名格式：`物流单号[-后缀]_YYYY-MM-DD HHMMSS.扩展名`
- 返回：tracking_number, date_digits, hour_digits, pure_tracking_number
- 使用正则表达式：`r'([A-Z0-9][A-Z0-9\-]*)_(\d{4}-\d{2}-\d{2})\s*(\d{6})'`

#### upload_file() 函数
- 构造OSS路径：`ftpFiles/{date_digits}/{hour_digits}/{pure_tracking_number}{ext}`
- 上传成功后重命名文件为 "已上传_" 前缀

#### scan_directory() 函数
- 扫描 `local_config['root_path']` 目录
- 只处理当天和前两天的日期文件夹
- 查找 jpg、jpeg、png 格式图片
- 跳过已上传文件（"已上传_" 前缀）

### 3. 需要修改的关键点
1. 配置系统需要支持多个扫描目录
2. upload_file() 需要根据来源目录决定是否添加 "_Panorama" 后缀
3. scan_directory() 需要扫描多个目录
4. 需要在OSS路径中区分普通照片和全景照片

### 4. 文件命名规则
- 当前支持：JDX040566571860-1-1-_2025-06-18 075136816.jpg
- 全景需要：JDX041379468474_Panorama.jpg（在OSS中）
- 本地文件名保持原有格式不变

# Proposed Solution (Populated by INNOVATE mode)

## 选定方案：配置文件扩展方案

### 核心设计思路
1. **配置扩展**：在config.ini中新增panorama_path配置项
2. **向后兼容**：保持原有root_path配置不变，确保现有用户无需修改
3. **目录类型识别**：通过文件路径判断照片类型（普通/全景）
4. **OSS路径区分**：全景照片在文件名后添加"_Panorama"后缀

### 实现优势
- 配置灵活，用户可自定义全景目录路径
- 保持现有配置结构一致性
- 易于扩展更多目录类型
- 向后兼容，现有用户无需更改配置

# Implementation Plan (Generated by PLAN mode)

## 详细修改计划

### 1. 配置系统扩展
**文件**: 熊洞科技-签收图片上传工具.py
**修改内容**:
- 扩展 `local_config` 字典，添加 `panorama_path` 配置项
- 修改 `load_config()` 函数，支持读取全景目录配置
- 修改 `save_config()` 函数，支持保存全景目录配置
- 更新配置文件注释，说明新增配置项

### 2. 文件上传逻辑增强
**文件**: 熊洞科技-签收图片上传工具.py
**修改内容**:
- 修改 `upload_file()` 函数签名，添加 `is_panorama` 参数
- 在OSS路径构造时，根据 `is_panorama` 参数决定是否添加 "_Panorama" 后缀
- 更新函数文档说明

### 3. 目录扫描逻辑重构
**文件**: 熊洞科技-签收图片上传工具.py
**修改内容**:
- 重构 `scan_directory()` 函数，支持扫描多个目录
- 创建新的辅助函数 `scan_single_directory()` 处理单个目录扫描
- 添加目录类型识别逻辑
- 更新日志输出，区分不同目录的扫描结果

### 4. 系统托盘界面更新
**文件**: 熊洞科技-签收图片上传工具.py
**修改内容**:
- 更新托盘菜单中的配置修改功能
- 在配置文件打开时提供全景目录配置说明

## 实施检查清单

Implementation Checklist:
1. ✅ 扩展local_config字典，添加panorama_path配置项（默认值："D:/Image/Panorama/"）
2. ✅ 修改load_config()函数，支持读取panorama_path配置
3. ✅ 修改save_config()函数，支持保存panorama_path配置并添加相关注释
4. ✅ 修改upload_file()函数签名，添加is_panorama参数
5. ✅ 在upload_file()中实现全景照片OSS路径构造逻辑（添加_Panorama后缀）
6. ✅ 创建scan_single_directory()辅助函数，处理单个目录的扫描逻辑
7. ✅ 重构scan_directory()函数，支持扫描普通和全景两个目录
8. ✅ 更新相关函数的文档注释，说明新增功能
9. ✅ 更新run_scheduler()和run_console_mode()函数中的目录检查逻辑
10. ✅ 更新系统托盘界面的配置修改功能，提供全景目录配置说明

# Current Execution Step (Updated by EXECUTE mode when starting a step)
> 已完成所有核心功能实现，准备进入测试验证阶段

# Task Progress (Appended by EXECUTE mode after each step completion)
*   2025-07-16 当前时间
    *   Step: 1-10 核心功能实现
    *   Modifications:
        - 扩展local_config字典，添加panorama_path配置项
        - 修改load_config()和save_config()函数支持全景目录配置
        - 修改upload_file()函数，添加is_panorama参数和OSS路径构造逻辑
        - 创建scan_single_directory()辅助函数
        - 重构scan_directory()函数支持双目录扫描
        - 更新run_scheduler()和run_console_mode()函数的目录检查逻辑
        - 更新系统托盘界面配置说明
        - 更新函数文档注释
    *   Change Summary: 成功实现全景相机照片支持功能，保持向后兼容性
    *   Reason: 执行全景相机功能扩展计划
    *   Blockers: 无
    *   Status: 待用户确认

