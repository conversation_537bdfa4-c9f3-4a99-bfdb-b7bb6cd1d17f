#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
精简版 PyInstaller 打包配置文件
"""

import os
import sys
from pathlib import Path
from PyInstaller.__main__ import run

if __name__ == "__main__":
    # 确保在正确的目录
    script_path = Path("熊洞科技-签收图片上传工具.py")
    if not script_path.exists():
        print(f"错误: 找不到'{script_path}'文件!")
        sys.exit(1)
        
    # 获取图标路径
    icon_path = Path("icon.ico")
    icon_option = []
    if icon_path.exists():
        icon_option = ["--icon", str(icon_path)]
    else:
        print("警告: 找不到图标文件 'icon.ico'，将使用默认图标")
    
    # 检查版本信息文件
    version_file = Path("version_info.txt")
    version_option = []
    if version_file.exists():
        version_option = ["--version-file", str(version_file)]
        print(f"找到版本信息文件 '{version_file}'，将应用到 exe 文件")
    else:
        print(f"警告: 找不到版本信息文件 '{version_file}'，exe 将没有版本信息")
    
    # 打包命令 - 精简版
    pyinstaller_cmd = [
        "熊洞科技-签收图片上传工具.py",         # 主脚本
        "--name=熊洞科技-签收图片上传工具",     # 可执行文件名
        "--noconsole",           # 无控制台窗口
        "--windowed",            # 创建窗口应用程序
        "--onefile",             # 打包成单个文件
        "--clean",               # 清理临时文件
        "--add-data=icon.ico;.",  # 包含图标文件
        # 排除不需要的大型库
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        "--exclude-module=pandas",
        "--exclude-module=scipy",
        "--exclude-module=torch",
        "--exclude-module=sklearn",
        "--exclude-module=torchvision",
        "--exclude-module=tensorflow",
        "--exclude-module=numba",
        "--exclude-module=av",
        "--exclude-module=librosa",
        "--exclude-module=transformers",
        "--exclude-module=pyarrow",
        *icon_option,            # 图标选项
        *version_option,         # 版本信息选项
    ]
    
    print(f"开始精简打包...")
    print(f"命令: {' '.join(pyinstaller_cmd)}")
    
    # 执行打包
    try:
        run(pyinstaller_cmd)
        print(f"打包完成，输出文件在: {os.path.abspath('dist')}")
        print("在dist目录中找到'熊洞科技-签收图片上传工具.exe'文件")
    except Exception as e:
        print(f"打包过程中发生错误: {e}") 