# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    # filevers 和 prodvers 应该始终是包含四个项的元组：(1, 2, 3, 4)
    # 分别设置为文件版本和产品版本的四个部分
    filevers=(1, 0, 2, 0),
    prodvers=(1, 0, 2, 0),
    # 包含位标志
    mask=0x3f,
    # 包含位标志
    flags=0x0,
    # 操作系统
    OS=0x40004,
    # 文件类型
    fileType=0x1,
    # 文件子类型
    subtype=0x0,
    # 创建日期
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'080404b0',
        [StringStruct(u'CompanyName', u'熊洞科技'),
        StringStruct(u'FileDescription', u'熊洞科技-签收图片上传工具1.1.0'),
        StringStruct(u'FileVersion', u'1.1.0'),
        StringStruct(u'InternalName', u'熊洞科技-签收图片上传工具1.1.0'),
        StringStruct(u'LegalCopyright', u'Copyright (C) 2025 熊洞科技'),
        StringStruct(u'OriginalFilename', u'熊洞科技-签收图片上传工具1.1.0.exe'),
        StringStruct(u'ProductName', u'熊洞科技-签收图片上传工具1.1.0'),
        StringStruct(u'ProductVersion', u'1.1.0')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
) 