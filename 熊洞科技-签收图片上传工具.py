# -*- coding: utf-8 -*-
import os
import glob
import re
import time
import logging
import datetime
import schedule
from pathlib import Path
from logging.handlers import TimedRotatingFileHandler
import sys
import pystray
from PIL import Image, ImageDraw
import threading
import winreg
import configparser

import oss2

def get_program_directory():
    """
    获取程序所在目录的绝对路径
    """
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe文件
        return os.path.dirname(sys.executable)
    else:
        # 如果是python脚本
        return os.path.dirname(os.path.abspath(__file__))

def initialize_working_directory():
    """
    初始化工作目录，确保程序在正确的目录下运行
    """
    program_dir = get_program_directory()
    current_dir = os.getcwd()

    # 如果当前工作目录不是程序所在目录，则切换到程序目录
    if os.path.abspath(current_dir) != os.path.abspath(program_dir):
        try:
            os.chdir(program_dir)
            print(f"工作目录已切换到: {program_dir}")
        except Exception as e:
            print(f"切换工作目录失败: {str(e)}")
            print(f"当前目录: {current_dir}")
            print(f"程序目录: {program_dir}")

    return program_dir

# OSS 配置信息（来自你的 info）
oss_info = {
    "endpoint": "http://oss-cn-beijing.aliyuncs.com",   # 改为外网 endpoint
    "keyid": "LTAIR4PwcCd6ZFAQ",
    "keysecret": "zk4KPaeVNW9M252PSzIfneKhy29ox9",
    "bucketname": "juranfile",
    "filehost": "https://file.juranguanjia.com"
}

# 本地配置
local_config = {
    "root_path": "D:/Image/Ori/",        # 配置签收图片根目录，可根据需要修改
    "panorama_path": "D:/Image/Panorama/", # 配置全景相机图片根目录，可根据需要修改
    "scan_interval": 15,                 # 扫描间隔，单位分钟
}

# 加载配置文件
def load_config():
    """
    从config.ini加载配置
    """
    global local_config
    config_path = Path("config.ini")
    if config_path.exists():
        try:
            config = configparser.ConfigParser()
            config.read("config.ini", encoding="utf-8")
            if "设置" in config:
                if "root_path" in config["设置"]:
                    local_config["root_path"] = config["设置"]["root_path"]
                if "panorama_path" in config["设置"]:
                    local_config["panorama_path"] = config["设置"]["panorama_path"]
                if "scan_interval" in config["设置"]:
                    local_config["scan_interval"] = int(config["设置"]["scan_interval"])
            logger.info(f"已从配置文件加载设置: 签收图片目录={local_config['root_path']}, 全景图片目录={local_config['panorama_path']}, 扫描间隔={local_config['scan_interval']}分钟")
        except Exception as e:
            logger.error(f"读取配置文件错误: {str(e)}")
    else:
        # 如果配置文件不存在，创建默认配置
        try:
            save_config()
            logger.info("已创建默认配置文件")
        except Exception as e:
            logger.error(f"创建配置文件错误: {str(e)}")

def save_config():
    """
    保存配置到config.ini
    """
    try:
        config = configparser.ConfigParser()
        config["设置"] = {
            "root_path": local_config["root_path"],
            "panorama_path": local_config["panorama_path"],
            "scan_interval": str(local_config["scan_interval"])
        }
        
        # 写入带有详细注释的配置文件
        with open("config.ini", "w", encoding="utf-8") as configfile:
            config.write(configfile)
            
        # 重新打开文件添加注释（因为ConfigParser不支持保留注释）
        with open("config.ini", "r", encoding="utf-8") as file:
            content = file.readlines()
            
        # 在第一行后添加详细注释
        detailed_content = [content[0],
                           "#############################################################\n",
                           "#                       配置参数说明                         #\n",
                           "#############################################################\n",
                           "# 签收签收图片扫描目录 - 请使用正斜杠\"/\"\n"]

        # 找到各配置项并添加注释
        for i, line in enumerate(content):
            if "root_path" in line:
                detailed_content.append(line)
                detailed_content.append("\n")
                detailed_content.append("# 全景相机图片扫描目录 - 请使用正斜杠\"/\"\n")
            elif "panorama_path" in line:
                detailed_content.append(line)
                detailed_content.append("\n")
                detailed_content.append("# 扫描间隔(分钟) - 程序每隔多少分钟扫描一次指定目录\n")
            elif "scan_interval" in line:
                detailed_content.append(line)
                detailed_content.append("\n")
                detailed_content.append("#############################################################\n")
                detailed_content.append("# 修改完成后请保存此文件，然后通过系统托盘的\"退出\"菜单项,重新打开软件使新设置生效\n")
                detailed_content.append("#############################################################\n")
        
        # 将详细注释写回文件
        with open("config.ini", "w", encoding="utf-8") as file:
            file.writelines(detailed_content)
            
        return True
    except Exception as e:
        logger.error(f"保存配置文件错误: {str(e)}")
        return False

# 初始化工作目录（在配置日志之前）
program_dir = initialize_working_directory()

# 创建logs目录（使用程序目录的绝对路径）
logs_dir = Path(program_dir) / "logs"
if not logs_dir.exists():
    try:
        logs_dir.mkdir(exist_ok=True)
        print(f"创建日志目录: {logs_dir}")
    except Exception as e:
        print(f"创建日志目录失败: {str(e)}")
        # 如果无法在程序目录创建日志，尝试在用户临时目录创建
        try:
            import tempfile
            logs_dir = Path(tempfile.gettempdir()) / "熊洞科技签收图片上传工具_logs"
            logs_dir.mkdir(exist_ok=True)
            print(f"已在临时目录创建日志目录: {logs_dir}")
        except Exception as e2:
            print(f"在临时目录创建日志目录也失败: {str(e2)}")
            logs_dir = Path(".")  # 最后的备选方案

# 配置日志
# 使用TimedRotatingFileHandler按天轮转日志，保留最近7天
log_file_path = logs_dir / "oss_upload.log"
file_handler = TimedRotatingFileHandler(
    filename=str(log_file_path),
    when='midnight',  # 每天午夜切换新日志文件
    interval=1,       # 间隔为1天
    backupCount=7,    # 保留7个备份文件
    encoding='utf-8'
)
file_handler.suffix = "%Y-%m-%d.log"  # 日志文件后缀名

# 标准输出处理器
stream_handler = logging.StreamHandler()

# 配置日志格式和处理器
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        file_handler,
        stream_handler
    ]
)
logger = logging.getLogger('oss_uploader')

# 初始化 Auth
auth = oss2.AuthV4(oss_info['keyid'], oss_info['keysecret'])

# 设置 endpoint 和 region
endpoint = oss_info['endpoint']
region = "cn-beijing"  # 手动设置更安全

# 初始化 Bucket
bucket = oss2.Bucket(auth, endpoint, oss_info['bucketname'], region=region)

def parse_filename(filename):
    """
    解析文件名，提取物流单号和时间信息
    文件名格式例如: JD1234568876-1-1_2025-06-17 151234.jpg 或 JDX040566571860-1-1-_2025-06-18 075136816.jpg
    返回: 物流单号(如JD1234568876), 日期(如20250617), 小时(如2025061715), 纯物流单号(如JD1234568876)
    """
    try:
        # 移除文件扩展名
        base_name = os.path.splitext(filename)[0]
        
        # 使用正则表达式匹配物流单号和日期时间
        # 修改后的正则可兼容末尾带有短横线"-"的物流单号格式
        pattern = r'([A-Z0-9][A-Z0-9\-]*)_(\d{4}-\d{2}-\d{2})\s*(\d{6})'
        match = re.search(pattern, base_name)
        
        if match:
            tracking_number = match.group(1)  # 完整物流单号 (可能包含 -1-1 或 -1-1-)
            date_str = match.group(2)         # 日期，格式如 2025-06-17
            time_str = match.group(3)         # 时间，格式如 151234
            
            # 提取日期数字 (20250617)
            date_digits = date_str.replace('-', '')
            
            # 提取小时 (2025061715)
            hour_digits = date_digits + time_str[:2]
            
            # 提取纯物流单号 (去除 -1-1 等后缀)
            pure_tracking_number = tracking_number.split('-')[0]
            
            return tracking_number, date_digits, hour_digits, pure_tracking_number
        else:
            logger.error(f"文件名格式不匹配: {filename}")
            return None, None, None, None
            
    except Exception as e:
        logger.error(f"解析文件名异常: {filename}, 错误: {str(e)}")
        return None, None, None, None

def scan_single_directory(root_path, directory_type="签收"):
    """
    扫描单个目录，查找需要上传的图片
    root_path: 要扫描的根目录路径
    directory_type: 目录类型，用于日志显示（"签收" 或 "全景"）
    返回: (上传成功数量, 跳过数量)
    """
    # 使用Path对象处理路径，确保路径分隔符的一致性
    root_path = Path(root_path)
    logger.info(f"开始扫描{directory_type}图片目录: {root_path}")

    # 检查目录是否存在
    if not root_path.exists():
        logger.warning(f"{directory_type}图片目录不存在: {root_path}")
        return 0, 0

    # 获取当前日期和前两天日期
    today = datetime.datetime.now()
    target_dates = []

    # 生成当天和前两天的日期字符串，格式为YYYY-MM-DD
    for days_delta in range(3):  # 0=今天, 1=昨天, 2=前天
        date = today - datetime.timedelta(days=days_delta)
        date_str = date.strftime("%Y-%m-%d")
        target_dates.append(date_str)

    logger.info(f"将处理{directory_type}目录中以下日期文件夹: {', '.join(target_dates)}")

    # 查找格式为 YYYY-MM-DD 的日期文件夹
    date_folders = []
    try:
        # 使用Path的glob方法查找所有日期文件夹
        all_date_pattern = root_path.glob("[0-9][0-9][0-9][0-9]-[0-9][0-9]-[0-9][0-9]")
        all_date_folders = list(all_date_pattern)

        # 筛选当天和前两天的文件夹
        for folder in all_date_folders:
            if folder.name in target_dates:
                date_folders.append(folder)

    except Exception as e:
        logger.error(f"扫描{directory_type}目录异常: {str(e)}")

    if not date_folders:
        logger.info(f"未找到{directory_type}目录中的目标日期文件夹 {target_dates}，请确认根目录路径: {root_path}")
        return 0, 0

    logger.info(f"发现{directory_type}目录中 {len(date_folders)} 个目标日期文件夹: {[folder.name for folder in date_folders]}")

    # 遍历每个日期文件夹
    uploaded_count = 0
    skipped_count = 0
    is_panorama = (directory_type == "全景")

    for date_folder in date_folders:
        folder_name = date_folder.name
        logger.info(f"处理{directory_type}文件夹: {folder_name}")

        # 查找所有图片文件 (jpg, jpeg, png)
        image_files = []

        try:
            # Windows系统下文件扩展名不区分大小写，使用一个模式可以匹配所有情况
            # 使用set避免重复
            image_paths = set()

            # 使用Path的glob方法查找图片文件，使用小写扩展名即可
            for ext in ['jpg', 'jpeg', 'png']:
                # 将Path对象转换为字符串，以便于比较和去重
                for img_path in date_folder.glob(f"*.{ext}"):
                    image_paths.add(str(img_path))

            # 转回Path对象
            image_files = [Path(path) for path in image_paths]

        except Exception as e:
            logger.error(f"查找{directory_type}图片文件异常: {str(e)}")

        logger.info(f"在{directory_type}文件夹 {folder_name} 中发现 {len(image_files)} 个图片文件")

        # 筛选掉已经上传过的图片 (文件名以"已上传_"开头)
        new_images = [f for f in image_files if not f.name.startswith("已上传_")]

        logger.info(f"在{directory_type}文件夹 {folder_name} 中发现 {len(new_images)} 个新图片文件需要上传")

        # 上传每个新图片
        for image_path in new_images:
            # 将Path对象转换为字符串，确保路径格式正确
            image_path_str = str(image_path)
            if upload_file(image_path_str, is_panorama):
                uploaded_count += 1
            else:
                skipped_count += 1

    logger.info(f"{directory_type}目录扫描完成. 成功上传: {uploaded_count}, 跳过: {skipped_count}")
    return uploaded_count, skipped_count

def upload_file(file_path, is_panorama=False):
    """
    上传单个文件到OSS
    file_path: 文件完整路径
    is_panorama: 是否为全景相机照片，默认为False
    """
    try:
        # 使用Path对象处理路径
        file_path = Path(file_path)
        
        # 确保文件存在
        if not file_path.exists():
            logger.error(f"文件不存在: {file_path}")
            return False
            
        # 获取文件名
        filename = file_path.name
        logger.info(f"处理文件: {filename}")
        
        # 解析文件名
        tracking_number, date_digits, hour_digits, pure_tracking_number = parse_filename(filename)
        if not all([tracking_number, date_digits, hour_digits, pure_tracking_number]):
            logger.warning(f"跳过文件 {filename}: 文件名格式不符合要求")
            return False
        
        # 获取文件扩展名
        ext = file_path.suffix

        # 构造OSS对象路径，全景照片添加_Panorama后缀
        if is_panorama:
            # 全景照片格式: ftpFiles/20250617/2025061715/JD1234568876_Panorama.jpg
            oss_object_key = f"ftpFiles/{date_digits}/{hour_digits}/{pure_tracking_number}_Panorama{ext}"
        else:
            # 签收照片格式: ftpFiles/20250617/2025061715/JD1234568876.jpg
            oss_object_key = f"ftpFiles/{date_digits}/{hour_digits}/{pure_tracking_number}{ext}"
        
        # 上传文件
        with open(str(file_path), 'rb') as fileobj:
            result = bucket.put_object(oss_object_key, fileobj)
        
        # 检查上传状态
        if result.status == 200:
            logger.info(f"上传成功: {filename} -> {oss_object_key}")
            
            # 文件访问URL
            file_url = f"{oss_info['filehost']}/{oss_object_key}"
            logger.info(f"文件访问地址: {file_url}")
            
            # 重命名本地文件为"已上传_xxxx"
            dir_path = file_path.parent
            new_filename = f"已上传_{filename}"
            new_file_path = dir_path / new_filename
            
            try:
                # 检查目标文件是否已经存在
                if new_file_path.exists():
                    # 如果存在，添加时间戳创建唯一文件名
                    timestamp = time.strftime("%Y%m%d%H%M%S")
                    name_parts = os.path.splitext(filename)
                    new_filename = f"已上传_{name_parts[0]}_{timestamp}{name_parts[1]}"
                    new_file_path = dir_path / new_filename
                    logger.info(f"目标文件已存在，使用时间戳创建唯一文件名: {new_filename}")
                
                file_path.rename(new_file_path)
                logger.info(f"文件已重命名: {filename} -> {new_filename}")
                return True
            except Exception as e:
                logger.error(f"重命名文件失败: {filename}, 错误: {str(e)}")
                return True  # 上传成功但重命名失败，仍然返回True
        else:
            logger.error(f"上传失败: {filename}, 状态码: {result.status}")
            return False
            
    except Exception as e:
        logger.error(f"上传文件异常: {file_path}, 错误: {str(e)}")
        return False

def scan_directory():
    """
    扫描本地目录，查找需要上传的图片

    功能说明：
    - 支持扫描普通签收图片目录 (root_path) 和全景相机图片目录 (panorama_path)
    - 只处理当天和前两天的文件夹（格式：YYYY-MM-DD）
    - 全景相机照片在OSS中会自动添加"_Panorama"后缀进行区分
    - 跳过已上传的文件（文件名以"已上传_"开头）

    返回：
        tuple: (总上传成功数量, 总跳过数量)
    """
    logger.info("=== 开始扫描所有配置的图片目录 ===")

    total_uploaded = 0
    total_skipped = 0

    # 扫描签收图片目录
    normal_uploaded, normal_skipped = scan_single_directory(local_config['root_path'], "签收")
    total_uploaded += normal_uploaded
    total_skipped += normal_skipped

    # 扫描全景相机图片目录
    panorama_uploaded, panorama_skipped = scan_single_directory(local_config['panorama_path'], "全景")
    total_uploaded += panorama_uploaded
    total_skipped += panorama_skipped

    logger.info(f"=== 所有目录扫描完成 ===")
    logger.info(f"总计 - 成功上传: {total_uploaded}, 跳过: {total_skipped}")
    logger.info(f"详细 - 签收图片: 上传{normal_uploaded}个, 跳过{normal_skipped}个")
    logger.info(f"详细 - 全景图片: 上传{panorama_uploaded}个, 跳过{panorama_skipped}个")

    return total_uploaded, total_skipped

def setup_scheduler():
    """
    设置定时任务调度器
    """
    logger.info(f"设置定时任务，每 {local_config['scan_interval']} 分钟扫描一次")
    schedule.every(local_config['scan_interval']).minutes.do(scan_directory)
    
    # 立即执行一次
    logger.info("立即执行首次扫描")
    scan_directory()
    
    # 持续运行定时任务
    logger.info("定时任务已开始，按 Ctrl+C 停止程序")
    try:
        while True:
            schedule.run_pending()
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("程序已停止")
    except Exception as e:
        logger.error(f"定时任务异常: {str(e)}")
        raise

def test_parse_filename():
    """
    测试函数，用于验证解析文件名功能
    包含普通照片和全景照片的测试用例
    """
    test_filenames = [
        "JDX040566571860-1-1-_2025-06-18 075136816.jpg",
        "JDX040566571860666-8-1-90-_2025-06-18 075136816.jpg",# 新格式
        "JD1234568876-1-1_2025-06-17 151234.jpg",        # 旧格式
        "JD9876543210_2025-06-18 103045.jpg"             # 简单格式
    ]

    print("===== 测试文件名解析功能 =====")
    for test_file in test_filenames:
        result = parse_filename(test_file)
        if result[0]:
            print(f"文件: {test_file}")
            print(f"  - 物流单号: {result[0]}")
            print(f"  - 日期: {result[1]}")
            print(f"  - 小时: {result[2]}")
            print(f"  - 纯物流单号: {result[3]}")
            print(f"  - 普通照片OSS路径: ftpFiles/{result[1]}/{result[2]}/{result[3]}.jpg")
            print(f"  - 全景照片OSS路径: ftpFiles/{result[1]}/{result[2]}/{result[3]}_Panorama.jpg")
            print()
        else:
            print(f"无法解析文件名: {test_file}")
            print()

def create_image(width, height, color1, color2):
    """
    创建一个简单的图标图像
    """
    image = Image.new('RGB', (width, height), color1)
    dc = ImageDraw.Draw(image)
    dc.rectangle(
        (width // 2, 0, width, height // 2),
        fill=color2)
    dc.rectangle(
        (0, height // 2, width // 2, height),
        fill=color2)
    return image

def setup_autostart(enable=True):
    """
    设置开机自动启动
    enable: True表示启用自启动，False表示禁用自启动
    """
    app_name = "签收图片上传工具"
    # 获取当前可执行文件路径和工作目录
    program_dir = get_program_directory()

    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe，使用cmd命令设置工作目录
        app_path = f'cmd /c "cd /d "{program_dir}" && "{sys.executable}""'
    else:
        # 如果是python脚本
        app_path = f'cmd /c "cd /d "{program_dir}" && "{sys.executable}" "{os.path.abspath(__file__)}""'

    key_path = r'Software\Microsoft\Windows\CurrentVersion\Run'
    try:
        # 打开注册表项
        key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_SET_VALUE | winreg.KEY_QUERY_VALUE)
        if enable:
            # 设置开机启动
            winreg.SetValueEx(key, app_name, 0, winreg.REG_SZ, app_path)
            logger.info(f"已设置开机启动: {app_path}")
            logger.info(f"工作目录将设置为: {program_dir}")
        else:
            # 删除开机启动项
            try:
                winreg.DeleteValue(key, app_name)
                logger.info("已禁用开机启动")
            except FileNotFoundError:
                pass  # 如果不存在该启动项，忽略错误
        winreg.CloseKey(key)
        return True
    except Exception as e:
        logger.error(f"设置开机启动失败: {str(e)}")
        return False

class OssUploader:
    """
    签收图片上传工具 - 系统托盘应用类
    """
    def __init__(self):
        self.is_running = False
        self.scheduler_thread = None
        # 创建系统托盘图标
        self.icon = self.create_tray_icon()
        
    def create_tray_icon(self):
        """
        创建系统托盘图标
        """
        # 加载图标文件
        try:
            if Path("icon.ico").exists():
                image = Image.open("icon.ico")
            else:
                # 创建图标图像 (蓝色)
                image = create_image(64, 64, 'blue', 'white')
        except Exception as e:
            logger.error(f"加载图标失败: {str(e)}")
            image = create_image(64, 64, 'blue', 'white')
        
        # 创建系统托盘菜单项
        menu = (
            pystray.MenuItem('运行状态: 正在运行', self.show_status),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem('设置', pystray.Menu(
                pystray.MenuItem('修改扫描目录', self.change_scan_directory),
                pystray.MenuItem('修改扫描间隔', self.change_scan_interval),
            )),
            pystray.MenuItem('开机启动', self.toggle_autostart, checked=self.is_autostart_enabled),
            pystray.MenuItem('显示日志文件夹', self.open_log_folder),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem('退出', self.exit_app)
        )
        
        # 创建系统托盘图标
        icon = pystray.Icon("oss_uploader", image, "签收图片上传", menu)
        return icon
    
    def change_scan_directory(self, icon, item):
        """
        修改扫描目录 - 简化为直接打开配置文件
        """
        try:
            # 直接打开配置文件让用户编辑
            import subprocess
            subprocess.Popen(f'notepad config.ini')
            logger.info(f"已打开配置文件 config.ini，请修改 root_path 和 panorama_path 参数")

            # 用系统托盘通知提醒用户
            if icon:
                icon.notify("请在打开的记事本中修改目录参数：\nroot_path - 普通签收图片目录\npanorama_path - 全景相机图片目录\n修改后保存文件并重启程序", "修改扫描目录")

        except Exception as e:
            logger.error(f"打开配置文件失败: {str(e)}")
            # 备选方案
            try:
                os.startfile("config.ini")
            except:
                logger.error("无法打开配置文件")
    
    def change_scan_interval(self, icon, item):
        """
        修改扫描间隔 - 简化为直接打开配置文件
        """
        try:
            # 直接打开配置文件让用户编辑
            import subprocess
            subprocess.Popen(f'notepad config.ini')
            logger.info(f"已打开配置文件 config.ini，请修改 scan_interval 参数")
            
            # 用系统托盘通知提醒用户
            if icon:
                icon.notify("请在打开的记事本中修改 scan_interval 参数\n修改后保存文件并'重启服务'", "修改扫描间隔")
                
        except Exception as e:
            logger.error(f"打开配置文件失败: {str(e)}")
            # 备选方案
            try:
                os.startfile("config.ini")
            except:
                logger.error("无法打开配置文件")
    
    def is_autostart_enabled(self, item):
        """
        检查是否启用了开机自启动
        """
        try:
            key_path = r'Software\Microsoft\Windows\CurrentVersion\Run'
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_READ)
            try:
                winreg.QueryValueEx(key, "签收图片上传工具")
                return True
            except FileNotFoundError:
                return False
            finally:
                winreg.CloseKey(key)
        except:
            return False
    
    def show_status(self, icon, item):
        """
        显示当前状态
        """
        logger.info("程序运行中...")
        pass
    
    def toggle_autostart(self, icon, item):
        """
        切换开机自启动状态
        """
        current_status = self.is_autostart_enabled(item)
        setup_autostart(not current_status)  # 切换状态
        
    def open_log_folder(self, icon, item):
        """
        打开日志文件夹
        """
        log_path = str(logs_dir)  # 使用全局的logs_dir变量
        if os.path.exists(log_path):
            os.startfile(log_path)
            logger.info(f"已打开日志文件夹: {log_path}")
        else:
            logger.error(f"日志文件夹不存在: {log_path}")

    
    def start_service(self):
        """
        启动服务
        """
        if self.is_running:
            return
        
        self.is_running = True
        # 启动定时任务线程
        self.scheduler_thread = threading.Thread(target=self.run_scheduler, daemon=True)
        self.scheduler_thread.start()
        logger.info("服务已启动")
        
    def stop_service(self):
        """
        停止服务
        """
        self.is_running = False
        if self.scheduler_thread:
            # 等待线程结束
            if self.scheduler_thread.is_alive():
                logger.info("正在停止服务...")
                # 由于线程设置了daemon=True，主线程结束时会自动终止
            self.scheduler_thread = None
        logger.info("服务已停止")
        
    def run_scheduler(self):
        """
        运行定时任务
        """
        try:
            # 初始化日志
            logger.info("=== 熊洞科技 -- 仓储签收图片上传程序启动 ===")
            
            # 标准化本地目录路径
            local_config['root_path'] = str(Path(local_config['root_path']).resolve())
            local_config['panorama_path'] = str(Path(local_config['panorama_path']).resolve())
            logger.info(f"签收图片目录: {local_config['root_path']}")
            logger.info(f"全景图片目录: {local_config['panorama_path']}")
            logger.info(f"OSS存储桶: {oss_info['bucketname']}")

            # 检查并创建目录
            for dir_type, dir_path in [("签收图片", local_config['root_path']), ("全景图片", local_config['panorama_path'])]:
                path_obj = Path(dir_path)
                if not path_obj.exists():
                    logger.warning(f"{dir_type}目录不存在: {path_obj}")
                    logger.info(f"正在尝试创建{dir_type}目录...")
                    try:
                        path_obj.mkdir(parents=True, exist_ok=True)
                        logger.info(f"已创建{dir_type}目录: {path_obj}")
                    except Exception as e:
                        logger.error(f"创建{dir_type}目录失败: {str(e)}")
                        # 不返回，继续处理其他目录
            
            # 设置定时任务
            logger.info(f"设置定时任务，每 {local_config['scan_interval']} 分钟扫描一次")
            schedule.every(local_config['scan_interval']).minutes.do(scan_directory)
            
            # 立即执行一次
            logger.info("立即执行首次扫描")
            scan_directory()
            
            # 持续运行定时任务
            logger.info("定时任务已开始")
            while self.is_running:
                schedule.run_pending()
                time.sleep(1)
                
        except Exception as e:
            logger.error(f"定时任务异常: {str(e)}")
            
    def exit_app(self, icon, item):
        """
        退出应用
        """
        self.stop_service()
        icon.stop()
        
    def run(self):
        """
        运行应用
        """
        # 启动服务
        self.start_service()
        # 运行系统托盘图标
        self.icon.run()

def main():
    """
    主函数
    """
    try:
        # 工作目录和日志已在模块加载时初始化
        logger.info("=== 熊洞科技 -- 签收图片上传工具启动 ===")
        logger.info(f"程序目录: {get_program_directory()}")
        logger.info(f"工作目录: {os.getcwd()}")
        logger.info(f"日志目录: {logs_dir}")

        # 加载配置文件
        load_config()

        # 默认启用开机自启动 (只在第一次运行时设置)
        if getattr(sys, 'frozen', False):  # 只在exe模式下设置开机启动
            setup_autostart(True)

        # 创建并运行系统托盘应用
        app = OssUploader()
        app.run()

    except Exception as e:
        logger.error(f"程序启动异常: {str(e)}")
        # 如果系统托盘不可用，则回退到控制台模式
        logger.info("回退到控制台模式运行...")
        run_console_mode()


def run_console_mode():
    """
    控制台模式运行 (当系统托盘模式不可用时使用)
    """
    logger.info("=== 熊洞科技 -- 仓储签收图片上传程序启动 ===")
    
    # 加载配置文件
    load_config()
    
    # 标准化本地目录路径
    local_config['root_path'] = str(Path(local_config['root_path']).resolve())
    local_config['panorama_path'] = str(Path(local_config['panorama_path']).resolve())
    logger.info(f"签收图片目录: {local_config['root_path']}")
    logger.info(f"全景图片目录: {local_config['panorama_path']}")
    logger.info(f"OSS存储桶: {oss_info['bucketname']}")

    # 检查并创建目录
    for dir_type, dir_path in [("签收图片", local_config['root_path']), ("全景图片", local_config['panorama_path'])]:
        path_obj = Path(dir_path)
        if not path_obj.exists():
            logger.warning(f"{dir_type}目录不存在: {path_obj}")
            logger.info(f"正在尝试创建{dir_type}目录...")
            try:
                path_obj.mkdir(parents=True, exist_ok=True)
                logger.info(f"已创建{dir_type}目录: {path_obj}")
            except Exception as e:
                logger.error(f"创建{dir_type}目录失败: {str(e)}")
                # 不返回，继续处理其他目录

    # 启动定时任务
    setup_scheduler()


if __name__ == "__main__":
    main()