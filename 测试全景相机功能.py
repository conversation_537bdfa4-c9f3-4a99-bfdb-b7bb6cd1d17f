# -*- coding: utf-8 -*-
"""
全景相机功能测试脚本
用于验证新增的全景相机照片上传功能
"""

import os
import sys
from pathlib import Path

# 添加主程序路径到系统路径
sys.path.append('.')

# 导入主程序模块
try:
    from 熊洞科技_签收图片上传工具 import (
        local_config, 
        parse_filename, 
        upload_file,
        scan_single_directory,
        test_parse_filename
    )
    print("✅ 成功导入主程序模块")
except ImportError as e:
    print(f"❌ 导入主程序模块失败: {e}")
    sys.exit(1)

def test_config():
    """测试配置项"""
    print("\n=== 测试配置项 ===")
    print(f"普通图片目录: {local_config.get('root_path', '未配置')}")
    print(f"全景图片目录: {local_config.get('panorama_path', '未配置')}")
    print(f"扫描间隔: {local_config.get('scan_interval', '未配置')} 分钟")
    
    # 检查配置项是否存在
    required_keys = ['root_path', 'panorama_path', 'scan_interval']
    missing_keys = [key for key in required_keys if key not in local_config]
    
    if missing_keys:
        print(f"❌ 缺少配置项: {missing_keys}")
        return False
    else:
        print("✅ 所有必需的配置项都存在")
        return True

def test_oss_path_construction():
    """测试OSS路径构造"""
    print("\n=== 测试OSS路径构造 ===")
    
    # 模拟文件名解析结果
    test_cases = [
        {
            "filename": "JDX041379468474-1-1_2025-07-16 143000.jpg",
            "expected_normal": "ftpFiles/20250716/2025071614/JDX041379468474.jpg",
            "expected_panorama": "ftpFiles/20250716/2025071614/JDX041379468474_Panorama.jpg"
        }
    ]
    
    for case in test_cases:
        print(f"\n测试文件: {case['filename']}")
        result = parse_filename(case['filename'])
        
        if result[0]:  # 如果解析成功
            tracking_number, date_digits, hour_digits, pure_tracking_number = result
            
            # 构造OSS路径
            ext = ".jpg"
            normal_path = f"ftpFiles/{date_digits}/{hour_digits}/{pure_tracking_number}{ext}"
            panorama_path = f"ftpFiles/{date_digits}/{hour_digits}/{pure_tracking_number}_Panorama{ext}"
            
            print(f"  解析结果:")
            print(f"    物流单号: {tracking_number}")
            print(f"    日期: {date_digits}")
            print(f"    小时: {hour_digits}")
            print(f"    纯物流单号: {pure_tracking_number}")
            
            print(f"  OSS路径:")
            print(f"    普通照片: {normal_path}")
            print(f"    全景照片: {panorama_path}")
            
            # 验证路径是否符合预期
            if normal_path == case['expected_normal'] and panorama_path == case['expected_panorama']:
                print("  ✅ OSS路径构造正确")
            else:
                print("  ❌ OSS路径构造错误")
                print(f"    期望普通路径: {case['expected_normal']}")
                print(f"    期望全景路径: {case['expected_panorama']}")
        else:
            print("  ❌ 文件名解析失败")

def test_directory_structure():
    """测试目录结构"""
    print("\n=== 测试目录结构 ===")
    
    directories = [
        ("普通图片目录", local_config.get('root_path')),
        ("全景图片目录", local_config.get('panorama_path'))
    ]
    
    for dir_name, dir_path in directories:
        if dir_path:
            path_obj = Path(dir_path)
            if path_obj.exists():
                print(f"✅ {dir_name} 存在: {dir_path}")
            else:
                print(f"⚠️  {dir_name} 不存在: {dir_path}")
                print(f"   程序运行时会自动创建此目录")
        else:
            print(f"❌ {dir_name} 未配置")

def main():
    """主测试函数"""
    print("🚀 开始全景相机功能测试")
    print("=" * 50)
    
    # 测试配置
    config_ok = test_config()
    
    if config_ok:
        # 测试OSS路径构造
        test_oss_path_construction()
        
        # 测试目录结构
        test_directory_structure()
        
        # 运行原有的文件名解析测试
        print("\n=== 运行文件名解析测试 ===")
        test_parse_filename()
        
        print("\n" + "=" * 50)
        print("🎉 全景相机功能测试完成")
        print("\n📋 功能说明:")
        print("1. 普通签收图片存放在:", local_config.get('root_path'))
        print("2. 全景相机图片存放在:", local_config.get('panorama_path'))
        print("3. 全景照片在OSS中会自动添加'_Panorama'后缀")
        print("4. 两种类型的照片都会按照相同的日期文件夹结构组织")
        print("5. 程序会同时扫描两个目录并分别处理")
        
    else:
        print("\n❌ 配置测试失败，请检查程序配置")

if __name__ == "__main__":
    main()
